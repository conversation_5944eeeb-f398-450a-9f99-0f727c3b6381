import "../modal/modal";
export declare const TripRePaymentTemplate: (autoFillOrderCode: boolean, request: any, uri_searchBox: string, language: string, _agent: string, _termsUrl: string, _isLoading: boolean, _agree: boolean, _isSubmit: boolean, _isShowDetailsTrip: boolean, _orderDetails: any, _inforAirports: any[], _pricePaxInfor: any[], _servicePrice: number, _sumPrice: number, _paymentMethod: string, _banks: any[], _bankNote: string, _transferContent: string, _cashInfo: any, _currencySymbol: string, _convertedVND: number, showDetailsTrip: () => void, setPaymentMethod: (method: string) => void, selectBank: (bank: any) => void, setAgree: (agree: boolean) => void, onPayment: () => void, handleLanguageChange: (value: string) => void, showLanguageSelect: boolean) => import("lit-html").TemplateResult<1>;
