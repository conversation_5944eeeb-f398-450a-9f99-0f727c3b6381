{"version": 3, "file": "index.js", "sources": ["../../node_modules/tslib/tslib.es6.js", "../../node_modules/@lit/reactive-element/css-tag.js", "../../node_modules/@lit/reactive-element/reactive-element.js", "../../node_modules/lit-html/lit-html.js", "../../node_modules/lit-element/lit-element.js", "../../node_modules/@lit/reactive-element/decorators/custom-element.js", "../../node_modules/@lit/reactive-element/decorators/property.js", "../../node_modules/@lit/reactive-element/decorators/state.js", "../../src/utils/dateUtils.ts", "../../src/environments/environment.ts", "../../src/components/trip-available/trip-available-template.ts", "../../node_modules/@fingerprintjs/fingerprintjs/dist/fp.esm.js", "../../src/utils/deviceUtils.ts", "../../src/services/CryptoService.ts", "../../src/services/FlightService.ts", "../../src/services/WorldServices.ts", "../../src/interface/DefaultColors.ts", "../../src/services/ColorService.ts", "../../src/components/trip-available/trip-available.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\r\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\r\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nvar ownKeys = function(o) {\r\n    ownKeys = Object.getOwnPropertyNames || function (o) {\r\n        var ar = [];\r\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\r\n        return ar;\r\n    };\r\n    return ownKeys(o);\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose, inner;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n            if (async) inner = dispose;\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    var r, s = 0;\r\n    function next() {\r\n        while (r = env.stack.pop()) {\r\n            try {\r\n                if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\r\n                if (r.dispose) {\r\n                    var result = r.dispose.call(r.value);\r\n                    if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n                }\r\n                else s |= 1;\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\r\n    if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\r\n        return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\r\n            return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\r\n        });\r\n    }\r\n    return path;\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __esDecorate: __esDecorate,\r\n    __runInitializers: __runInitializers,\r\n    __propKey: __propKey,\r\n    __setFunctionName: __setFunctionName,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n    __rewriteRelativeImportExtension: __rewriteRelativeImportExtension,\r\n};\r\n", "/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t=globalThis,e=t.ShadowRoot&&(void 0===t.ShadyCSS||t.ShadyCSS.nativeShadow)&&\"adoptedStyleSheets\"in Document.prototype&&\"replace\"in CSSStyleSheet.prototype,s=Symbol(),o=new WeakMap;class n{constructor(t,e,o){if(this._$cssResult$=!0,o!==s)throw Error(\"CSSResult is not constructable. Use `unsafeCSS` or `css` instead.\");this.cssText=t,this.t=e}get styleSheet(){let t=this.o;const s=this.t;if(e&&void 0===t){const e=void 0!==s&&1===s.length;e&&(t=o.get(s)),void 0===t&&((this.o=t=new CSSStyleSheet).replaceSync(this.cssText),e&&o.set(s,t))}return t}toString(){return this.cssText}}const r=t=>new n(\"string\"==typeof t?t:t+\"\",void 0,s),i=(t,...e)=>{const o=1===t.length?t[0]:e.reduce(((e,s,o)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if(\"number\"==typeof t)return t;throw Error(\"Value passed to 'css' function must be a 'css' function result: \"+t+\". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.\")})(s)+t[o+1]),t[0]);return new n(o,t,s)},S=(s,o)=>{if(e)s.adoptedStyleSheets=o.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet));else for(const e of o){const o=document.createElement(\"style\"),n=t.litNonce;void 0!==n&&o.setAttribute(\"nonce\",n),o.textContent=e.cssText,s.appendChild(o)}},c=e?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e=\"\";for(const s of t.cssRules)e+=s.cssText;return r(e)})(t):t;export{n as CSSResult,S as adoptStyles,i as css,c as getCompatibleStyle,e as supportsAdoptingStyleSheets,r as unsafeCSS};\n//# sourceMappingURL=css-tag.js.map\n", "import{getCompatibleStyle as t,adoptStyles as s}from\"./css-tag.js\";export{CSSResult,adoptStyles,css,getCompatibleStyle,supportsAdoptingStyleSheets,unsafeCSS}from\"./css-tag.js\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */const{is:i,defineProperty:e,getOwnPropertyDescriptor:r,getOwnPropertyNames:h,getOwnPropertySymbols:o,getPrototypeOf:n}=Object,a=globalThis,c=a.trustedTypes,l=c?c.emptyScript:\"\",p=a.reactiveElementPolyfillSupport,d=(t,s)=>t,u={toAttribute(t,s){switch(s){case Boolean:t=t?l:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,s){let i=t;switch(s){case Boolean:i=null!==t;break;case Number:i=null===t?null:Number(t);break;case Object:case Array:try{i=JSON.parse(t)}catch(t){i=null}}return i}},f=(t,s)=>!i(t,s),y={attribute:!0,type:String,converter:u,reflect:!1,hasChanged:f};Symbol.metadata??=Symbol(\"metadata\"),a.litPropertyMetadata??=new WeakMap;class b extends HTMLElement{static addInitializer(t){this._$Ei(),(this.l??=[]).push(t)}static get observedAttributes(){return this.finalize(),this._$Eh&&[...this._$Eh.keys()]}static createProperty(t,s=y){if(s.state&&(s.attribute=!1),this._$Ei(),this.elementProperties.set(t,s),!s.noAccessor){const i=Symbol(),r=this.getPropertyDescriptor(t,i,s);void 0!==r&&e(this.prototype,t,r)}}static getPropertyDescriptor(t,s,i){const{get:e,set:h}=r(this.prototype,t)??{get(){return this[s]},set(t){this[s]=t}};return{get(){return e?.call(this)},set(s){const r=e?.call(this);h.call(this,s),this.requestUpdate(t,r,i)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)??y}static _$Ei(){if(this.hasOwnProperty(d(\"elementProperties\")))return;const t=n(this);t.finalize(),void 0!==t.l&&(this.l=[...t.l]),this.elementProperties=new Map(t.elementProperties)}static finalize(){if(this.hasOwnProperty(d(\"finalized\")))return;if(this.finalized=!0,this._$Ei(),this.hasOwnProperty(d(\"properties\"))){const t=this.properties,s=[...h(t),...o(t)];for(const i of s)this.createProperty(i,t[i])}const t=this[Symbol.metadata];if(null!==t){const s=litPropertyMetadata.get(t);if(void 0!==s)for(const[t,i]of s)this.elementProperties.set(t,i)}this._$Eh=new Map;for(const[t,s]of this.elementProperties){const i=this._$Eu(t,s);void 0!==i&&this._$Eh.set(i,t)}this.elementStyles=this.finalizeStyles(this.styles)}static finalizeStyles(s){const i=[];if(Array.isArray(s)){const e=new Set(s.flat(1/0).reverse());for(const s of e)i.unshift(t(s))}else void 0!==s&&i.push(t(s));return i}static _$Eu(t,s){const i=s.attribute;return!1===i?void 0:\"string\"==typeof i?i:\"string\"==typeof t?t.toLowerCase():void 0}constructor(){super(),this._$Ep=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Em=null,this._$Ev()}_$Ev(){this._$ES=new Promise((t=>this.enableUpdating=t)),this._$AL=new Map,this._$E_(),this.requestUpdate(),this.constructor.l?.forEach((t=>t(this)))}addController(t){(this._$EO??=new Set).add(t),void 0!==this.renderRoot&&this.isConnected&&t.hostConnected?.()}removeController(t){this._$EO?.delete(t)}_$E_(){const t=new Map,s=this.constructor.elementProperties;for(const i of s.keys())this.hasOwnProperty(i)&&(t.set(i,this[i]),delete this[i]);t.size>0&&(this._$Ep=t)}createRenderRoot(){const t=this.shadowRoot??this.attachShadow(this.constructor.shadowRootOptions);return s(t,this.constructor.elementStyles),t}connectedCallback(){this.renderRoot??=this.createRenderRoot(),this.enableUpdating(!0),this._$EO?.forEach((t=>t.hostConnected?.()))}enableUpdating(t){}disconnectedCallback(){this._$EO?.forEach((t=>t.hostDisconnected?.()))}attributeChangedCallback(t,s,i){this._$AK(t,i)}_$EC(t,s){const i=this.constructor.elementProperties.get(t),e=this.constructor._$Eu(t,i);if(void 0!==e&&!0===i.reflect){const r=(void 0!==i.converter?.toAttribute?i.converter:u).toAttribute(s,i.type);this._$Em=t,null==r?this.removeAttribute(e):this.setAttribute(e,r),this._$Em=null}}_$AK(t,s){const i=this.constructor,e=i._$Eh.get(t);if(void 0!==e&&this._$Em!==e){const t=i.getPropertyOptions(e),r=\"function\"==typeof t.converter?{fromAttribute:t.converter}:void 0!==t.converter?.fromAttribute?t.converter:u;this._$Em=e,this[e]=r.fromAttribute(s,t.type),this._$Em=null}}requestUpdate(t,s,i){if(void 0!==t){if(i??=this.constructor.getPropertyOptions(t),!(i.hasChanged??f)(this[t],s))return;this.P(t,s,i)}!1===this.isUpdatePending&&(this._$ES=this._$ET())}P(t,s,i){this._$AL.has(t)||this._$AL.set(t,s),!0===i.reflect&&this._$Em!==t&&(this._$Ej??=new Set).add(t)}async _$ET(){this.isUpdatePending=!0;try{await this._$ES}catch(t){Promise.reject(t)}const t=this.scheduleUpdate();return null!=t&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){if(!this.isUpdatePending)return;if(!this.hasUpdated){if(this.renderRoot??=this.createRenderRoot(),this._$Ep){for(const[t,s]of this._$Ep)this[t]=s;this._$Ep=void 0}const t=this.constructor.elementProperties;if(t.size>0)for(const[s,i]of t)!0!==i.wrapped||this._$AL.has(s)||void 0===this[s]||this.P(s,this[s],i)}let t=!1;const s=this._$AL;try{t=this.shouldUpdate(s),t?(this.willUpdate(s),this._$EO?.forEach((t=>t.hostUpdate?.())),this.update(s)):this._$EU()}catch(s){throw t=!1,this._$EU(),s}t&&this._$AE(s)}willUpdate(t){}_$AE(t){this._$EO?.forEach((t=>t.hostUpdated?.())),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$EU(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$ES}shouldUpdate(t){return!0}update(t){this._$Ej&&=this._$Ej.forEach((t=>this._$EC(t,this[t]))),this._$EU()}updated(t){}firstUpdated(t){}}b.elementStyles=[],b.shadowRootOptions={mode:\"open\"},b[d(\"elementProperties\")]=new Map,b[d(\"finalized\")]=new Map,p?.({ReactiveElement:b}),(a.reactiveElementVersions??=[]).push(\"2.0.4\");export{b as ReactiveElement,u as defaultConverter,f as notEqual};\n//# sourceMappingURL=reactive-element.js.map\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t=globalThis,i=t.trustedTypes,s=i?i.createPolicy(\"lit-html\",{createHTML:t=>t}):void 0,e=\"$lit$\",h=`lit$${Math.random().toFixed(9).slice(2)}$`,o=\"?\"+h,n=`<${o}>`,r=document,l=()=>r.createComment(\"\"),c=t=>null===t||\"object\"!=typeof t&&\"function\"!=typeof t,a=Array.isArray,u=t=>a(t)||\"function\"==typeof t?.[Symbol.iterator],d=\"[ \\t\\n\\f\\r]\",f=/<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g,v=/-->/g,_=/>/g,m=RegExp(`>|${d}(?:([^\\\\s\"'>=/]+)(${d}*=${d}*(?:[^ \\t\\n\\f\\r\"'\\`<>=]|(\"|')|))|$)`,\"g\"),p=/'/g,g=/\"/g,$=/^(?:script|style|textarea|title)$/i,y=t=>(i,...s)=>({_$litType$:t,strings:i,values:s}),x=y(1),b=y(2),w=y(3),T=Symbol.for(\"lit-noChange\"),E=Symbol.for(\"lit-nothing\"),A=new WeakMap,C=r.createTreeWalker(r,129);function P(t,i){if(!a(t)||!t.hasOwnProperty(\"raw\"))throw Error(\"invalid template strings array\");return void 0!==s?s.createHTML(i):i}const V=(t,i)=>{const s=t.length-1,o=[];let r,l=2===i?\"<svg>\":3===i?\"<math>\":\"\",c=f;for(let i=0;i<s;i++){const s=t[i];let a,u,d=-1,y=0;for(;y<s.length&&(c.lastIndex=y,u=c.exec(s),null!==u);)y=c.lastIndex,c===f?\"!--\"===u[1]?c=v:void 0!==u[1]?c=_:void 0!==u[2]?($.test(u[2])&&(r=RegExp(\"</\"+u[2],\"g\")),c=m):void 0!==u[3]&&(c=m):c===m?\">\"===u[0]?(c=r??f,d=-1):void 0===u[1]?d=-2:(d=c.lastIndex-u[2].length,a=u[1],c=void 0===u[3]?m:'\"'===u[3]?g:p):c===g||c===p?c=m:c===v||c===_?c=f:(c=m,r=void 0);const x=c===m&&t[i+1].startsWith(\"/>\")?\" \":\"\";l+=c===f?s+n:d>=0?(o.push(a),s.slice(0,d)+e+s.slice(d)+h+x):s+h+(-2===d?i:x)}return[P(t,l+(t[s]||\"<?>\")+(2===i?\"</svg>\":3===i?\"</math>\":\"\")),o]};class N{constructor({strings:t,_$litType$:s},n){let r;this.parts=[];let c=0,a=0;const u=t.length-1,d=this.parts,[f,v]=V(t,s);if(this.el=N.createElement(f,n),C.currentNode=this.el.content,2===s||3===s){const t=this.el.content.firstChild;t.replaceWith(...t.childNodes)}for(;null!==(r=C.nextNode())&&d.length<u;){if(1===r.nodeType){if(r.hasAttributes())for(const t of r.getAttributeNames())if(t.endsWith(e)){const i=v[a++],s=r.getAttribute(t).split(h),e=/([.?@])?(.*)/.exec(i);d.push({type:1,index:c,name:e[2],strings:s,ctor:\".\"===e[1]?H:\"?\"===e[1]?I:\"@\"===e[1]?L:k}),r.removeAttribute(t)}else t.startsWith(h)&&(d.push({type:6,index:c}),r.removeAttribute(t));if($.test(r.tagName)){const t=r.textContent.split(h),s=t.length-1;if(s>0){r.textContent=i?i.emptyScript:\"\";for(let i=0;i<s;i++)r.append(t[i],l()),C.nextNode(),d.push({type:2,index:++c});r.append(t[s],l())}}}else if(8===r.nodeType)if(r.data===o)d.push({type:2,index:c});else{let t=-1;for(;-1!==(t=r.data.indexOf(h,t+1));)d.push({type:7,index:c}),t+=h.length-1}c++}}static createElement(t,i){const s=r.createElement(\"template\");return s.innerHTML=t,s}}function S(t,i,s=t,e){if(i===T)return i;let h=void 0!==e?s._$Co?.[e]:s._$Cl;const o=c(i)?void 0:i._$litDirective$;return h?.constructor!==o&&(h?._$AO?.(!1),void 0===o?h=void 0:(h=new o(t),h._$AT(t,s,e)),void 0!==e?(s._$Co??=[])[e]=h:s._$Cl=h),void 0!==h&&(i=S(t,h._$AS(t,i.values),h,e)),i}class M{constructor(t,i){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=i}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(t){const{el:{content:i},parts:s}=this._$AD,e=(t?.creationScope??r).importNode(i,!0);C.currentNode=e;let h=C.nextNode(),o=0,n=0,l=s[0];for(;void 0!==l;){if(o===l.index){let i;2===l.type?i=new R(h,h.nextSibling,this,t):1===l.type?i=new l.ctor(h,l.name,l.strings,this,t):6===l.type&&(i=new z(h,this,t)),this._$AV.push(i),l=s[++n]}o!==l?.index&&(h=C.nextNode(),o++)}return C.currentNode=r,e}p(t){let i=0;for(const s of this._$AV)void 0!==s&&(void 0!==s.strings?(s._$AI(t,s,i),i+=s.strings.length-2):s._$AI(t[i])),i++}}class R{get _$AU(){return this._$AM?._$AU??this._$Cv}constructor(t,i,s,e){this.type=2,this._$AH=E,this._$AN=void 0,this._$AA=t,this._$AB=i,this._$AM=s,this.options=e,this._$Cv=e?.isConnected??!0}get parentNode(){let t=this._$AA.parentNode;const i=this._$AM;return void 0!==i&&11===t?.nodeType&&(t=i.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,i=this){t=S(this,t,i),c(t)?t===E||null==t||\"\"===t?(this._$AH!==E&&this._$AR(),this._$AH=E):t!==this._$AH&&t!==T&&this._(t):void 0!==t._$litType$?this.$(t):void 0!==t.nodeType?this.T(t):u(t)?this.k(t):this._(t)}O(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}T(t){this._$AH!==t&&(this._$AR(),this._$AH=this.O(t))}_(t){this._$AH!==E&&c(this._$AH)?this._$AA.nextSibling.data=t:this.T(r.createTextNode(t)),this._$AH=t}$(t){const{values:i,_$litType$:s}=t,e=\"number\"==typeof s?this._$AC(t):(void 0===s.el&&(s.el=N.createElement(P(s.h,s.h[0]),this.options)),s);if(this._$AH?._$AD===e)this._$AH.p(i);else{const t=new M(e,this),s=t.u(this.options);t.p(i),this.T(s),this._$AH=t}}_$AC(t){let i=A.get(t.strings);return void 0===i&&A.set(t.strings,i=new N(t)),i}k(t){a(this._$AH)||(this._$AH=[],this._$AR());const i=this._$AH;let s,e=0;for(const h of t)e===i.length?i.push(s=new R(this.O(l()),this.O(l()),this,this.options)):s=i[e],s._$AI(h),e++;e<i.length&&(this._$AR(s&&s._$AB.nextSibling,e),i.length=e)}_$AR(t=this._$AA.nextSibling,i){for(this._$AP?.(!1,!0,i);t&&t!==this._$AB;){const i=t.nextSibling;t.remove(),t=i}}setConnected(t){void 0===this._$AM&&(this._$Cv=t,this._$AP?.(t))}}class k{get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}constructor(t,i,s,e,h){this.type=1,this._$AH=E,this._$AN=void 0,this.element=t,this.name=i,this._$AM=e,this.options=h,s.length>2||\"\"!==s[0]||\"\"!==s[1]?(this._$AH=Array(s.length-1).fill(new String),this.strings=s):this._$AH=E}_$AI(t,i=this,s,e){const h=this.strings;let o=!1;if(void 0===h)t=S(this,t,i,0),o=!c(t)||t!==this._$AH&&t!==T,o&&(this._$AH=t);else{const e=t;let n,r;for(t=h[0],n=0;n<h.length-1;n++)r=S(this,e[s+n],i,n),r===T&&(r=this._$AH[n]),o||=!c(r)||r!==this._$AH[n],r===E?t=E:t!==E&&(t+=(r??\"\")+h[n+1]),this._$AH[n]=r}o&&!e&&this.j(t)}j(t){t===E?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,t??\"\")}}class H extends k{constructor(){super(...arguments),this.type=3}j(t){this.element[this.name]=t===E?void 0:t}}class I extends k{constructor(){super(...arguments),this.type=4}j(t){this.element.toggleAttribute(this.name,!!t&&t!==E)}}class L extends k{constructor(t,i,s,e,h){super(t,i,s,e,h),this.type=5}_$AI(t,i=this){if((t=S(this,t,i,0)??E)===T)return;const s=this._$AH,e=t===E&&s!==E||t.capture!==s.capture||t.once!==s.once||t.passive!==s.passive,h=t!==E&&(s===E||e);e&&this.element.removeEventListener(this.name,this,s),h&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){\"function\"==typeof this._$AH?this._$AH.call(this.options?.host??this.element,t):this._$AH.handleEvent(t)}}class z{constructor(t,i,s){this.element=t,this.type=6,this._$AN=void 0,this._$AM=i,this.options=s}get _$AU(){return this._$AM._$AU}_$AI(t){S(this,t)}}const Z={M:e,P:h,A:o,C:1,L:V,R:M,D:u,V:S,I:R,H:k,N:I,U:L,B:H,F:z},j=t.litHtmlPolyfillSupport;j?.(N,R),(t.litHtmlVersions??=[]).push(\"3.2.1\");const B=(t,i,s)=>{const e=s?.renderBefore??i;let h=e._$litPart$;if(void 0===h){const t=s?.renderBefore??null;e._$litPart$=h=new R(i.insertBefore(l(),t),t,void 0,s??{})}return h._$AI(t),h};export{Z as _$LH,x as html,w as mathml,T as noChange,E as nothing,B as render,b as svg};\n//# sourceMappingURL=lit-html.js.map\n", "import{ReactiveElement as t}from\"@lit/reactive-element\";export*from\"@lit/reactive-element\";import{render as e,noChange as s}from\"lit-html\";export*from\"lit-html\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */class r extends t{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){const t=super.createRenderRoot();return this.renderOptions.renderBefore??=t.firstChild,t}update(t){const s=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Do=e(s,this.renderRoot,this.renderOptions)}connectedCallback(){super.connectedCallback(),this._$Do?.setConnected(!0)}disconnectedCallback(){super.disconnectedCallback(),this._$Do?.setConnected(!1)}render(){return s}}r._$litElement$=!0,r[\"finalized\"]=!0,globalThis.litElementHydrateSupport?.({LitElement:r});const i=globalThis.litElementPolyfillSupport;i?.({LitElement:r});const o={_$AK:(t,e,s)=>{t._$AK(e,s)},_$AL:t=>t._$AL};(globalThis.litElementVersions??=[]).push(\"4.1.1\");export{r as LitElement,o as _$LE};\n//# sourceMappingURL=lit-element.js.map\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t=t=>(e,o)=>{void 0!==o?o.addInitializer((()=>{customElements.define(t,e)})):customElements.define(t,e)};export{t as customElement};\n//# sourceMappingURL=custom-element.js.map\n", "import{defaultConverter as t,notEqual as e}from\"../reactive-element.js\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */const o={attribute:!0,type:String,converter:t,reflect:!1,hasChanged:e},r=(t=o,e,r)=>{const{kind:n,metadata:i}=r;let s=globalThis.litPropertyMetadata.get(i);if(void 0===s&&globalThis.litPropertyMetadata.set(i,s=new Map),s.set(r.name,t),\"accessor\"===n){const{name:o}=r;return{set(r){const n=e.get.call(this);e.set.call(this,r),this.requestUpdate(o,n,t)},init(e){return void 0!==e&&this.P(o,void 0,t),e}}}if(\"setter\"===n){const{name:o}=r;return function(r){const n=this[o];e.call(this,r),this.requestUpdate(o,n,t)}}throw Error(\"Unsupported decorator location: \"+n)};function n(t){return(e,o)=>\"object\"==typeof o?r(t,e,o):((t,e,o)=>{const r=e.hasOwnProperty(o);return e.constructor.createProperty(o,r?{...t,wrapped:!0}:t),r?Object.getOwnPropertyDescriptor(e,o):void 0})(t,e,o)}export{n as property,r as standardProperty};\n//# sourceMappingURL=property.js.map\n", "import{property as t}from\"./property.js\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */function r(r){return t({...r,state:!0,attribute:!1})}export{r as state};\n//# sourceMappingURL=state.js.map\n", "/**\n * Chuyển đổi đối tượng Date thành chuỗi định dạng yyyy-MM-dd\n * @param date - Đ<PERSON>i tượng Date cần chuyển đổi\n * @returns Chuỗi định dạng yyyy-MM-dd\n */\nexport const formatDate = (date) => {\n    const year = date.getFullYear();\n    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Thêm số 0 ở đầu nếu cần\n    const day = date.getDate().toString().padStart(2, '0'); // Thêm số 0 ở đầu nếu cần\n    return `${year}-${month}-${day}`; // Định dạng yyyy-MM-dd\n};\nexport function formatDateTo_ddMMyyyy(date, language) {\n    if (!date || date === undefined) {\n        return null;\n    }\n    var date1 = new Date(date);\n    if (language === 'vi') {\n        // Tr<PERSON> về dạng dd/MM/yyyy\n        return date1.toLocaleDateString('vi-VN', {\n            day: '2-digit',\n            month: '2-digit',\n            year: 'numeric'\n        });\n    }\n    // Trả về dạng 11 Jul, 2025\n    const day = date1.getDate().toString().padStart(2, '0');\n    const month = date1.toLocaleString('en-US', { month: 'short' }); // Jul\n    const year = date1.getFullYear();\n    return `${day} ${month}, ${year}`;\n}\nexport function getDurationByArray(legs) {\n    if (legs == null)\n        return '';\n    var duration = 0;\n    var departure = new Date(legs[0].DepartureDate);\n    var arrival = new Date(legs[legs.length - 1].ArrivalDate);\n    duration = arrival.getTime() - departure.getTime();\n    var hours = Math.floor(duration / 3600000);\n    var minutes = Math.floor((duration % 3600000) / 60000);\n    return hours.toString().padStart(2, '0') + 'h' + minutes.toString().padStart(2, '0');\n}\nexport function formatDateToString(date, language) {\n    if (!date)\n        return null;\n    let day, month, year;\n    // Trường hợp là đối tượng Date\n    if (date instanceof Date) {\n        day = date.getDate();\n        month = date.getMonth() + 1;\n        year = date.getFullYear();\n    }\n    // Trường hợp là object có day/month/year\n    else if (typeof date === 'object' && ('day' in date || 'month' in date || 'year' in date)) {\n        day = date.day || 1;\n        month = date.month || 1;\n        year = date.year || 2000;\n    }\n    // Trường hợp là string có thể parse được\n    else if (typeof date === 'string') {\n        const parsed = new Date(date);\n        if (isNaN(parsed.getTime()))\n            return null;\n        day = parsed.getDate();\n        month = parsed.getMonth() + 1;\n        year = parsed.getFullYear();\n    }\n    else {\n        return null;\n    }\n    const dd = day.toString().padStart(2, '0');\n    const mm = month.toString().padStart(2, '0');\n    const yyyy = year.toString();\n    // Trả về theo ngôn ngữ\n    return language === 'vi' ? `${dd}/${mm}/${yyyy}` : `${mm}/${dd}/${yyyy}`;\n}\nexport function validatePhone(phone) {\n    return phone.match(/^[0-9]{6,12}$/) ? true : false;\n}\nexport function validateEmail(email) {\n    return email.match(/^([\\w.%+-]+)@([\\w-]+\\.)+([\\w]{2,})$/i) ? true : false;\n}\nexport function getTimeFromDateTime(dateTime) {\n    const date = new Date(dateTime);\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n}\nexport function convertDurationToHour(duration) {\n    const hours = Math.floor(duration / 60).toString().padStart(2, '0');\n    const minutes = (duration % 60).toString().padStart(2, '0');\n    return `${hours}h${minutes}`;\n}\nexport function getDuration(leg) {\n    if (leg == null)\n        return '';\n    var departure = new Date(leg.DepartureDate);\n    var arrival = new Date(leg.ArrivalDate);\n    var duration = arrival.getTime() - departure.getTime();\n    var hours = Math.floor(duration / 3600000);\n    var minutes = Math.floor((duration % 3600000) / 60000);\n    return hours.toString().padStart(2, '0') + 'h' + minutes.toString().padStart(2, '0');\n}\nexport function formatddMMyyyy(date) {\n    if (date == null)\n        return '';\n    var dateObj = new Date(date);\n    return dateObj.getDate().toString().padStart(2, '0') + '/' + ((dateObj.getMonth() + 1).toString().padStart(2, '0')) + '/' + dateObj.getFullYear();\n}\nexport function getDayInWeek(date) {\n    const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];\n    const dateObj = new Date(date);\n    return days[dateObj.getDay()];\n}\nexport function getFlights(legs) {\n    return legs?.map(leg => leg.OperatingAirlines + leg.FlightNumber).join(' - ');\n}\nexport function getDirect(legs, language) {\n    return legs.length > 1 ? (language === 'vi' ? \"Nhiều chặng\" : \"Multiple stops\") : (language === 'vi' ? \"Bay thẳng\" : \"Direct flight\");\n}\nexport function getDurarionLeg(leg) {\n    var departure = new Date(leg.DepartureDate);\n    var arrival = new Date(leg.ArrivalDate);\n    var duration = (arrival.getTime() - departure.getTime()) / 60000;\n    return convertDurationToHour(duration);\n}\nexport function getPassengerDescription(searchTripRequest, paxType, language) {\n    if (!searchTripRequest) {\n        return '';\n    }\n    switch (paxType) {\n        case 'ADT':\n            return `${searchTripRequest?.Adult} x ${language === 'vi' ? 'Người lớn' : 'Adult'}`;\n        case 'CHD':\n            return `${searchTripRequest?.Child} x ${language === 'vi' ? 'Trẻ em' : 'Child'}`;\n        case 'INF':\n            return `${searchTripRequest?.Infant} x ${language === 'vi' ? 'Em bé' : 'Infant'}`;\n        default:\n            return '';\n    }\n}\nexport function getPassengerDescriptionV2(paxType, ADT = 0, CHD = 0, INF = 0, language) {\n    switch (paxType) {\n        case 'ADT':\n            return `${ADT} x ${language === 'vi' ? 'Người lớn' : 'Adult'}`;\n        case 'CHD':\n            return `${CHD} x ${language === 'vi' ? 'Trẻ em' : 'Child'}`;\n        case 'INF':\n            return `${INF} x ${language === 'vi' ? 'Em bé' : 'Infant'}`;\n        default:\n            return '';\n    }\n}\nexport function getTypePassenger(passenger, passengers, language) {\n    var type = '';\n    switch (passenger.type) {\n        case 'adult':\n            type = language === 'vi' ? 'Người lớn' : 'Adult';\n            break;\n        case 'child':\n            type = language === 'vi' ? 'Trẻ em' : 'Child';\n            break;\n        case 'infant':\n            type = language === 'vi' ? 'Em bé' : 'Infant';\n            break;\n    }\n    //get index of passenger in type\n    var indexPassenger = passengers.filter(p => p.type === passenger.type).indexOf(passenger);\n    var result = `${type} ${indexPassenger + 1}`;\n    return result;\n}\nexport function formatNumber(value, convertedVND, language) {\n    if (value === null || value === undefined)\n        return '';\n    const result = language === 'vi' ? value : value / convertedVND;\n    if (language === 'vi' || convertedVND === 1) {\n        return Math.round(result).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, '.');\n    }\n    else {\n        const [integerPart, decimalPart] = result.toFixed(2).split('.');\n        const formattedInteger = integerPart.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n        return `${formattedInteger}.${decimalPart}`;\n    }\n}\nexport function range(start, end) {\n    if (start > end) {\n        return [];\n    }\n    return Array(end - start + 1).fill(0).map((_, idx) => start + idx);\n}\nexport function getDayOfWeek(date, language) {\n    const dayOfWeek = new Date(date).getDay();\n    return language === 'vi' ? ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'][dayOfWeek] : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayOfWeek];\n}\nexport function formatDate_ddMM(date, language) {\n    const day = date.getDate().toString().padStart(2, '0');\n    if (language === 'vi') {\n        const monthStr = (date.getMonth() + 1).toString().padStart(2, '0');\n        return `${day}/${monthStr}`;\n    }\n    const monthStr = date.toLocaleString('en', { month: 'short' });\n    return `${day} ${monthStr}`;\n}\nexport function formatDateS(dateString, language) {\n    var date = new Date(dateString);\n    return formatDateTypeDate(date, language);\n}\nexport function formatDateTypeDate(date, language) {\n    const daysOfWeek = language === 'vi'\n        ? [\"Chủ nhật\", \"Thứ hai\", \"Thứ ba\", \"Thứ tư\", \"Thứ năm\", \"Thứ sáu\", \"Thứ bảy\"]\n        : [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"];\n    const dayOfWeek = daysOfWeek[date.getDay()];\n    if (!dayOfWeek) {\n        return '';\n    }\n    const day = date.getDate().toString().padStart(2, '0');\n    const year = date.getFullYear();\n    if (language === 'vi') {\n        const monthStr = (date.getMonth() + 1).toString().padStart(2, '0');\n        return `${dayOfWeek}, ${day}/${monthStr}/${year}`;\n    }\n    // Format for English: Thursday, 31 July 2023\n    const monthStr = date.toLocaleString('en', { month: 'long' });\n    return `${dayOfWeek}, ${day} ${monthStr} ${year}`;\n}\nexport function getFareType(bookingInfos) {\n    return bookingInfos.map(bookingInfo => bookingInfo.FareType || bookingInfo.CabinName).join(' - ');\n}\nexport function debounce(func, wait) {\n    let timeout;\n    return (...args) => {\n        clearTimeout(timeout);\n        timeout = window.setTimeout(() => func(...args), wait);\n    };\n}\n", "export const environment = {\n    production: true,\n    // apiUrl: 'https://api.ngocmaitravel.vn',\n    // apiUrl: 'https://************',\n    // apiUrl: 'https://test01.ngocmaitravel.vn',\n    apiUrl: 'https://abi-ota.nmbooking.vn',\n    // apiUrl: 'https://localhost:7065',\n    publicKey: \"LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0NCk1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBeC9DT1ZhTWpQMGt5c0RiRlhkZkhyLzhQOEg5bEsveVoNCis2NERvZWdTSERSeENqb0NQMGxMZEtqb2VMa1hWaWE4UTBzQ3dzU1VIeHhIc3lkeWVhY0tUN0hHWG1WbFBaQXlJbHh1RjRPc0ZvWkoNCmEzQUVrSTdPMEpBeFFrcmFENzdURGJoeHBzSHdKUmtnWEI4aVR5NzQwOS83NjQ2VForcGdGTzMvSVlVT0M2SEplMEIvdkVadzkzeTkNCitodGlWa0ZtZEorQ2JkeGZyaEZrWHU5ZGRWeWptTENRODFIOTRJV2xOdm5PdlFHTkNpNWNHenpIZTVGTldpVy9PRGZrNkRKN0tJUUINCmlieUovVVoxdnJlKzExTytPWGZ0bkwwcHFybThvTnE5dURneWZQUW1US1Rxb0pDaGl6ZEFFTVlyNDVsQ3UybUJJNmVtVFhJWWVJYlANCm9uenZpUUlEQVFBQg0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tDQo=\",\n};\n", "import { html } from \"lit\";\nimport { convertDurationToHour, formatDateTo_ddMMyyyy, formatDateToString, formatddMMyyyy, formatNumber, getDayInWeek, getDuration, getDurationByArray, getTimeFromDateTime } from \"../../utils/dateUtils\";\nimport { environment } from \"../../environments/environment\";\nconst apiUrl = environment.apiUrl;\nexport const TripAvailableTemplate = (autoFillOrderCode, language, uri_searchBox, showLanguageSelect, isLoading = false, orderAvailable = null, isNotValid = false, orderDetails = null, inforAirports = [], bankSelected = '', errorString = '', formSubmitted = false, orderCode = '', contact = '', request = null, _PaymentNote, __NoteModel, _currencySymbol, _convertedVND, handlers = {}) => {\n    console.log('payment note template', _PaymentNote);\n    console.log('note model', __NoteModel);\n    return html `\r\n    ${isLoading ? html `\r\n        <div class=\"static\">\r\n            <div class=\"loader-container\">\r\n                <span class=\"loader\"></span>\r\n            <img src=\"${apiUrl}/assets/img/background/trip_loading2.gif\"/>\r\n                <span class=\"loadidng-vertical\"> ${language === 'vi' ? `Đang kiểm tra đặt chỗ của bạn, vui lòng chờ trong giây lát,...` : `We are checking your booking. Please wait a moment...`}</span>\r\n            </div>\r\n        </div>\r\n    ` : !orderAvailable ? html `\r\n        <div class=\"contents bg-cover bg-center  bg-blur\">\r\n            <div class=\"relative flex justify-center min-h-[50vh] p-4  bg-no-repeat bg-cover bg-center\">\r\n                <div class=\"absolute top-0 right-0 w-full h-full z-10 opacity-15 \">\r\n                </div>\r\n                <div class=\"relative z-20 max-w-7xl h-full w-full mx-auto my-auto flex justify-end items-center\">\r\n                    <div class=\"rounded-lg border bg-white text-gray-800 shadow-lg w-full max-w-md\" >\r\n                        <div class=\"flex flex-col p-6 space-y-1\">\r\n                            <h3 class=\"tracking-tight text-2xl font-bold text-center text-nmt-600\">${language === 'vi' ? `Đặt Chỗ Của Bạn` : `Check Your Booking`} </h3>\r\n                            <p class=\"text-sm text-gray-600 text-center\">${language === 'vi' ? `Nhập thông tin để kiểm tra đặt chỗ của bạn` : `Enter your booking information to check your booking`}</p>\r\n                            ${errorString ? html `\r\n                                <p class=\"flex items-center text-sm text-red-800 rounded-lg dark:text-red-400 text-center\">\r\n                                    ${errorString}\r\n                                </p>\r\n                            ` : ''}\r\n                        </div>\r\n                        <form class=\"h-full\" @submit=${(e) => handlers.onSubmitForm?.(e) || e.preventDefault()}>\r\n                            <div class=\"p-6 pt-0 space-y-4\">\r\n                                <div class=\"space-y-2\">\r\n                                    <input \r\n                                        .value=${orderCode}\r\n                                        @input=${handlers.onOrderCodeChange || (() => { })}\r\n                                        class=\"flex h-12 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-nmt-500\"\r\n                                        placeholder=\"${language === 'vi' ? `Nhập mã đơn hàng` : `Enter your order code`}\" \r\n                                        required=\"\">\r\n                                    ${formSubmitted && !orderCode ? html `\r\n                                        <div class=\"flex items-center text-sm text-red-800 rounded-lg dark:text-red-400\" role=\"alert\">\r\n                                            <svg class=\"flex-shrink-0 inline w-4 h-4 me-3\" aria-hidden=\"true\"\r\n                                                xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                                <path d=\"M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z\" />\r\n                                            </svg>\r\n                                            <div>${language === 'vi' ? `Bạn cần nhập mã đơn hàng.` : `You need to enter your order code.`}</div>\r\n                                        </div>\r\n                                    ` : ''}\r\n                                </div>\r\n                                <div class=\"space-y-2\">\r\n                                    <input \r\n                                        .value=${contact}\r\n                                        @input=${handlers.onContactChange || (() => { })}\r\n                                        class=\"flex h-12 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-nmt-500\"\r\n                                        placeholder=\"${language === 'vi' ? `Nhập số điện thoại hoặc email đặt hàng` : `Enter your phone number or email`}\"\r\n                                        type=\"text\">\r\n                                    ${formSubmitted && !contact ? html `\r\n                                        <div class=\"flex items-center text-sm text-red-800 rounded-lg dark:text-red-400\" role=\"alert\">\r\n                                            <svg class=\"flex-shrink-0 inline w-4 h-4 me-3\" aria-hidden=\"true\"\r\n                                                xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                                <path d=\"M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z\" />\r\n                                            </svg>\r\n                                            <div>${language === 'vi' ? `Bạn cần nhập số điện thoại hoặc email đặt hàng.` : `You need to enter your phone number or email.`}</div>\r\n                                        </div>\r\n                                    ` : ''}\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"flex items-center p-6 pt-0\">\r\n                                <button\r\n                                    class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-nmt-500 bg-nmt-600 text-white hover:bg-nmt-700 h-12 px-4 py-2 w-full\"\r\n                                    type=\"submit\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"\r\n                                        fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                        stroke-linejoin=\"round\" class=\"lucide lucide-search mr-2 h-4 w-4\">\r\n                                        <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\r\n                                        <path d=\"m21 21-4.3-4.3\"></path>\r\n                                    </svg>\r\n                                    ${language === 'vi' ? `Kiểm Tra Đặt Chỗ` : `Check Your Booking`}\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    ` : html `\r\n        <div class=\"!visible transition-opacity duration-150 text-foreground !opacity-100 dark:bg-gray-700\">\r\n            <div class=\"container mx-auto px-4 py-8 max-w-6xl\">\r\n                <section class=\"text-gray-600 body-font\">\r\n                    <div class=\"container md:px-5 mx-auto\">\r\n                        ${isLoading ? html `\r\n                            <div class=\"flex flex-col text-center w-full md:mb-10\">\r\n                                <h1 class=\"inline-block mb-2 text-3xl font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                    ${language === 'vi' ? `Đang tìm đơn hàng của bạn` : `Searching for your order`}\r\n                                </h1>\r\n                                <p class=\"text-gray-500 dark:text-gray-400\">${language === 'vi' ? `Vui lòng chờ trong giây lát...` : `Please wait a moment...`}</p>\r\n                            </div>\r\n                        ` : isNotValid ? html `\r\n                            <div class=\"grid md:grid-cols-5 grid-cols-1 md:gap-8 mt-6\">\r\n                                <div class=\"col-span-3\">\r\n                                    <div class=\"w-full overflow-x-auto\">\r\n                                            <div class=\"flex justify-between items-center mb-2\">\r\n                                <h1 class=\"inline-block  text-xl font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                ${language === 'vi' ? `Chi Tiết Đơn Hàng` : `Order Details`}</h1>\r\n\r\n                                <div class=\"flex justify-end items-center  \">\r\n                                    ${showLanguageSelect ? html `\r\n                                    <select id=\"language\" \r\n                                        class=\" text-sm  bg-gray-200 rounded-lg px-2 py-1  border-none focus:outline-none text-gray-600 \"\r\n                                        .value=${language}\r\n                                        @change=${(e) => handlers.handleLanguageChange(e.target.value)}\r\n                                    >\r\n                                        <option style=\"background-color: #f0f0f0; color: black;\" value=\"en\">English</option>\r\n                                        <option style=\"background-color: #f0f0f0; color: black;\" value=\"vi\">Tiếng Việt</option>\r\n                                    </select>\r\n                                    ` : ''}\r\n                                </div>\r\n                            </div>\r\n                                        <div class=\"relative overflow-x-auto shadow border border-gray-100 rounded-lg\">\r\n                                            <table class=\"w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 shadow\">\r\n                                                <tbody>\r\n                                                    <tr class=\"bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700\">\r\n                                                        <td scope=\"row\" class=\"md:ps-6 ps-2 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white\">\r\n                                                            ${language === 'vi' ? `Mã đơn hàng` : `Order Code`}\r\n                                                        </td>\r\n                                                        <td class=\"md:px-6 px-2 py-2\">\r\n                                                            <span class=\"font-extrabold text-lg text-nmt-500\">${orderAvailable?.OrderCode}</span>\r\n                                                        </td>\r\n                                                    </tr>\r\n                                                    <tr class=\"bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700\">\r\n                                                        <td scope=\"row\" class=\"md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white\">\r\n                                                            ${language === 'vi' ? `Ngày đặt` : `Order date`}\r\n                                                        </td>\r\n                                                        <td class=\"md:px-6 px-2 py-2\">\r\n                                                            <span class=\"text-base text-gray-700 dark:text-gray-400\">${orderAvailable?.TimeCreate}</span>\r\n                                                        </td>\r\n                                                    </tr>\r\n                                                    <tr class=\"bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700\">\r\n                                                        <td scope=\"row\" class=\"md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white\">\r\n                                                            ${language === 'vi' ? `Tình trạng` : `Status`}\r\n                                                        </td>\r\n                                                        <td class=\"md:px-6 px-2 py-2\">\r\n                                                            <span class=\"text-base text-gray-700 dark:text-gray-400\">\r\n                                                                ${orderAvailable?.Status === 0 ? html `\r\n                                                                    <span class=\"text-red-600\">${language === 'vi' ? `Chờ thanh toán` : `Pending payment`}</span>\r\n                                                                    <button @click=${handlers.rePayment || (() => { })}\r\n                                                                        class=\"h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-[#fb6340] to-[#fbb140] hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm md:px-6 px-3 md:py-3 py-1.5 text-center me-2\">\r\n                                                                        ${language === 'vi' ? `Thanh toán ngay` : `Pay now`}\r\n                                                                    </button>\r\n                                                                ` : orderAvailable?.Status === 1 ? html `\r\n                                                                    <span class=\"text-green-600\">${language === 'vi' ? `Đã thanh toán` : `Paid`}</span>\r\n                                                                ` : orderAvailable?.Status === -1 ? html `\r\n                                                                    <span class=\"text-red-600\">${language === 'vi' ? `Đã hủy` : `Cancelled`}</span>\r\n                                                                ` : ''}\r\n                                                            </span>\r\n                                                        </td>\r\n                                                    </tr>\r\n                                                    <tr class=\"bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700\">\r\n                                                        <td scope=\"row\" class=\"md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white\">\r\n                                                            ${language === 'vi' ? `Giá trị đơn hàng` : `Order value`}\r\n                                                        </td>\r\n                                                        <td class=\"md:px-6 px-2 py-2\">\r\n                                                            <span class=\"font-extrabold text-lg text-nmt-500\">${formatNumber(orderDetails?.totalPrice, _convertedVND, language)} ${_currencySymbol}</span>\r\n                                                        </td>\r\n                                                    </tr>\r\n                                                    <tr class=\"bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700\">\r\n                                                        <td scope=\"row\" class=\"md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white\">\r\n                                                            ${language === 'vi' ? `Hình thức thanh toán` : `Payment method`}\r\n                                                        </td>\r\n                                                        <td class=\"md:px-6 px-2 py-2\">\r\n                                                             ${orderAvailable?.PaymentMethod.includes('bank-transfer') ? html `\r\n                                            <div class=\"flex-1\">\r\n                                                    <label for=\"bank-transfer\"\r\n                                                        class=\"items-center  cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\"\r\n                                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                            stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                            stroke-linejoin=\"round\"\r\n                                                            class=\"mr-2 h-5 w-5 text-nmt-600 inline-flex\">\r\n                                                            <rect width=\"20\" height=\"16\" x=\"2\" y=\"6\" rx=\"2\"></rect>\r\n                                                            <path d=\"M12 3v10\"></path>\r\n                                                            <path d=\"m5 9 7-4 7 4\"></path>\r\n                                                        </svg>\r\n                                                        ${language === 'vi' ? `Chuyển Khoản Ngân Hàng` : `Bank Transfer`}\r\n                                                    </label>\r\n                                                    <p class=\"text-sm text-gray-500 mt-1\">${language === 'vi' ? `Thanh toán trực tiếp từ tài khoản ngân hàng của bạn` : `Pay directly from your bank account`}</p>\r\n\r\n                                                    <div class=\"mt-4 space-y-4 pt-3 border-t border-nmt-100\">\r\n                                                        <div class=\"bg-nmt-100 p-3 rounded-md text-sm\">\r\n                                                            <p class=\"font-medium\">${language === 'vi' ? `Thông tin chuyển khoản` : `Transfer information`}</p>\r\n                                                        </div>\r\n\r\n                                                        <div class=\"space-y-3\">\r\n                                                            <div class=\"grid grid-cols-3 gap-2 text-sm\">\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? `Chủ Tài Khoản:` : `Account Holder:`}</div>\r\n                                                                <div class=\"col-span-2 font-medium\">\r\n                                                                    ${_PaymentNote?.banksInfo[0]?.accountHolder}\r\n                                                                </div>\r\n\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? `Ngân hàng:` : `Bank:`}</div>\r\n                                                                <div class=\"col-span-2 font-medium\"> ${_PaymentNote?.banksInfo[0]?.bankName} </div>\r\n\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? `Chi nhánh:` : `Branch:`}</div>\r\n                                                                <div class=\"col-span-2 font-medium\"> ${_PaymentNote?.banksInfo[0]?.branch}</div>\r\n\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? `Số tài khoản:` : `Account Number:`}</div>\r\n                                                                <div class=\"col-span-2 font-medium\"> ${_PaymentNote?.banksInfo[0]?.accountNumber}</div>\r\n\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? `Nội dung CK:` : `Transfer Content:`}</div>\r\n                                                                <div class=\"col-span-2 font-medium\">${_PaymentNote?.transferContent} ${autoFillOrderCode ? orderAvailable?.OrderCode : ''}</div>\r\n                                                                \r\n                                                                ${_PaymentNote?.banksInfo[0]?.qrImageUrl ? html `\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? 'QR Code:' : 'QR Code:'}</div>\r\n                                                                <div class=\"col-span-2 font-medium\">\r\n                                                                    <img src=\"${_PaymentNote?.banksInfo[0]?.qrImageUrl}\" alt=\"${_PaymentNote?.banksInfo[0]?.bankName}\" class=\"h-36 rounded-md bg-gray-100 shadow-md\" />\r\n                                                                </div>\r\n                                                                ` : ''}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div\r\n                                                            class=\"bg-nmt-50 border border-nmt-200 p-3 rounded-md mt-4\">\r\n                                                            <p class=\"text-sm font-medium text-nmt-800\">${language === 'vi' ? `Hướng dẫn xác nhận thanh toán:` : `Payment confirmation instructions:`}</p>\r\n                                                            <p class=\"text-sm mt-2\"> ${_PaymentNote?.note}</p>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            ` : orderAvailable?.PaymentMethod.includes('cash') ? html `\r\n                                            <div class=\"flex-1\">\r\n                                                    <label for=\"cash\"\r\n                                                        class=\"items-center cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\"\r\n                                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                            stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                            stroke-linejoin=\"round\"\r\n                                                            class=\"mr-2 h-5 w-5 text-nmt-600 inline-flex\">\r\n                                                            <rect width=\"20\" height=\"12\" x=\"2\" y=\"6\" rx=\"2\"></rect>\r\n                                                            <circle cx=\"12\" cy=\"12\" r=\"2\"></circle>\r\n                                                            <path d=\"M6 12h.01M18 12h.01\"></path>\r\n                                                        </svg>\r\n                                                        ${language === 'vi' ? `Thanh Toán Tiền Mặt` : `Cash Payment`}\r\n                                                    </label>\r\n                                                    <p class=\"text-sm text-gray-500 mt-1\">${language === 'vi' ? `Thanh toán trực tiếp bằng tiền mặt tại quầy` : `Pay in cash at the counter`}</p>\r\n\r\n                                                    <div class=\"mt-4 space-y-4 pt-3 border-t border-nmt-100\">\r\n                                                        <div class=\"bg-nmt-100 p-3 rounded-md text-sm\">\r\n                                                            <p>${language === 'vi' ? `Thông tin thanh toán tiền mặt:` : `Cash payment information:`}</p>\r\n                                                        </div>\r\n\r\n                                                        <div class=\"space-y-3\">\r\n                                                            <div class=\"flex items-start gap-2\">\r\n                                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\"\r\n                                                                    height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\"\r\n                                                                    stroke=\"currentColor\" stroke-width=\"2\"\r\n                                                                    stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                                    class=\"mt-0.5 text-nmt-600 min-w-5\">\r\n                                                                    <path\r\n                                                                        d=\"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z\">\r\n                                                                    </path>\r\n                                                                    <circle cx=\"12\" cy=\"10\" r=\"3\"></circle>\r\n                                                                </svg>\r\n                                                                <div>\r\n                                                                    <p class=\"font-medium\">${language === 'vi' ? `Địa điểm thanh toán:` : `Payment location:`}</p>\r\n                                                                    <p class=\"text-sm text-gray-600\">${language === 'vi' ? `Quầy vé tại văn phòng đại lý của chúng tôi:` : `Ticket counter at our agency's office:`}\r\n                                                                         <span class=\"font-medium text-gray-800\">\r\n                                                                        ${_PaymentNote?.paymentAddress}\r\n                                                                    </span>\r\n                                                                    </p>\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                            <div class=\"flex items-start gap-2\">\r\n                                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\"\r\n                                                                    height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\"\r\n                                                                    stroke=\"currentColor\" stroke-width=\"2\"\r\n                                                                    stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                                    class=\"mt-0.5 text-nmt-600\">\r\n                                                                    <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n                                                                    <polyline points=\"12 6 12 12 16 14\"></polyline>\r\n                                                                </svg>\r\n                                                                <div>\r\n                                                                    <p class=\"font-medium\">${language === 'vi' ? `Thời gian:` : `Time:`}</p>\r\n                                                                    <ul class=\"list-disc pl-6 space-y-1\">\r\n                                                                        <li>${_PaymentNote?.paymentDeadline}</li>\r\n                                                                        <li>${_PaymentNote?.workingHours}</li>\r\n                                                                    </ul>\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                            <div class=\"flex items-start gap-2\">\r\n                                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\"\r\n                                                                    height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\"\r\n                                                                    stroke=\"currentColor\" stroke-width=\"2\"\r\n                                                                    stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                                    class=\"mt-0.5 text-nmt-600\">\r\n                                                                    <rect width=\"18\" height=\"18\" x=\"3\" y=\"3\" rx=\"2\"\r\n                                                                        ry=\"2\">\r\n                                                                    </rect>\r\n                                                                    <line x1=\"9\" x2=\"15\" y1=\"9\" y2=\"9\"></line>\r\n                                                                    <line x1=\"9\" x2=\"15\" y1=\"15\" y2=\"15\"></line>\r\n                                                                </svg>\r\n                                                                <div>\r\n                                                                    <p class=\"font-medium\">${language === 'vi' ? `Giấy tờ cần mang theo:` : `Documents to bring:`}</p>\r\n                                                                    <p class=\"text-sm text-gray-600\">\r\n                                                                    ${_PaymentNote?.note}\r\n                                                                    </p>\r\n                                                                </div>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            ` : orderAvailable?.PaymentMethod?.includes('e-wallet') ? html `\r\n                                                <div class=\"flex-1\">\r\n                                                    <label for=\"e-wallet\"\r\n                                                        class=\"items-center cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\"\r\n                                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                            stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                            stroke-linejoin=\"round\"\r\n                                                            class=\"mr-2 h-5 w-5 text-nmt-600 inline-flex\">\r\n                                                            <rect width=\"20\" height=\"12\" x=\"2\" y=\"6\" rx=\"2\"></rect>\r\n                                                            <circle cx=\"12\" cy=\"12\" r=\"2\"></circle>\r\n                                                            <path d=\"M6 12h.01M18 12h.01\"></path>\r\n                                                        </svg>\r\n                                                        ${language === 'vi' ? `Thanh Toán Ví Điện Tử` : `E-Wallet Payment`}\r\n                                                    </label>\r\n                                                    <p class=\"text-sm text-gray-500 mt-1\">${language === 'vi' ? `Thanh toán trực tuyến bằng ví điện tử ${orderAvailable?.PaymentMethod?.replace('e-wallet', '')}` : `Pay online using your e-wallet ${orderAvailable?.PaymentMethod?.replace('e-wallet', '')}`}</p>\r\n                                                </div>\r\n                                            ` : orderAvailable?.PaymentMethod === 'credit-card' ? html `\r\n                                            <div class=\"flex-1\">\r\n                                                    <label for=\"credit-card\"\r\n                                                        class=\"items-center cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\"\r\n                                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                            stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                            stroke-linejoin=\"round\"\r\n                                                            class=\"mr-2 h-5 w-5 text-nmt-600 inline-flex\">\r\n                                                            <rect width=\"20\" height=\"14\" x=\"2\" y=\"5\" rx=\"2\"></rect>\r\n                                                            <line x1=\"2\" x2=\"22\" y1=\"10\" y2=\"10\"></line>\r\n                                                        </svg>\r\n                                                        ${language === 'vi' ? `Thanh Toán Thẻ Tín Dụng` : `Credit Card Payment`}\r\n                                                    </label>\r\n                                                    <p class=\"text-sm text-gray-500 mt-1\">${language === 'vi' ? `Thanh toán trực tuyến bằng thẻ tín dụng` : `Pay online using credit card`}</p>\r\n                                                </div>\r\n                                            ` : ``}\r\n                                                        </td>\r\n                                                    </tr>\r\n                                                </tbody>\r\n                                            </table>\r\n                                        </div>\r\n                                    </div>\r\n                                    <!-- Customer information section -->\r\n                                    <div class=\"mt-10\">\r\n                                        <h1 class=\"inline-block mb-2 text-xl font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                            ${language === 'vi' ? `Thông Tin Liên Hệ` : `Contact Information`}</h1>\r\n                                        <div class=\"flex flex-col space-y-2 w-fit\">\r\n                                            <h2 class=\"gap-2 text-base tracking-tight text-gray-600 dark:text-white grid grid-cols-2\">\r\n                                                <span class=\"font-bold whitespace-nowrap\">${language === 'vi' ? `Họ và tên` : `Full name`} </span>\r\n                                                <span class=\"text-gray-500 dark:text-white font-semibold\">: ${orderAvailable?.CustomerName}</span>\r\n                                            </h2>\r\n                                            <h2 class=\"gap-2 text-base tracking-tight text-gray-600 dark:text-white grid grid-cols-2\">\r\n                                                <span class=\"font-bold whitespace-nowrap\">${language === 'vi' ? `Số điện thoại` : `Phone number`} </span>\r\n                                                <span class=\"text-gray-500 dark:text-white font-semibold\">: ${orderAvailable?.PhoneNumber}</span>\r\n                                            </h2>\r\n                                            <h2 class=\"gap-2 text-base tracking-tight text-gray-600 dark:text-white grid grid-cols-2\">\r\n                                                <span class=\"font-bold whitespace-nowrap\">Email </span>\r\n                                                <span class=\"text-gray-500 dark:text-white font-semibold inline-flex\">: ${orderAvailable?.Email}</span>\r\n                                            </h2>\r\n                                        </div>\r\n                                    </div>\r\n                                    <!-- Start List Passenger -->\r\n                                     <div class=\"w-full overflow-x-auto mt-10\">\r\n                            <h1\r\n                                class=\"inline-block mb-2 text-xl font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                ${language === 'vi' ? `Danh Sách Khách` : `Passenger List`}</h1>\r\n                            <div class=\"relative overflow-x-auto shadow dark:shadow-lg sm:rounded-lg\">\r\n                                <table class=\"w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400\">\r\n                                    <thead\r\n                                        class=\"text-xs text-gray-700 uppercase bg-gray-200 dark:bg-gray-800 dark:text-gray-400\">\r\n                                        <tr>\r\n                                            <th scope=\"col\" class=\"px-6 py-3\">\r\n                                                ${language === 'vi' ? `Họ và tên` : `Full name`}\r\n                                            </th>\r\n                                            <th scope=\"col\" class=\"px-6 py-3\">\r\n                                                ${language === 'vi' ? `Ngày sinh` : `Date of birth`}\r\n                                            </th>\r\n                                            <th scope=\"col\" class=\"px-6 py-3\">\r\n                                                ${language === 'vi' ? `Giới tính` : `Gender`}\r\n                                            </th>\r\n                                        </tr>\r\n                                    </thead>\r\n                                    <tbody>\r\n                                    ${orderDetails?.paxList.map((pax) => html `\r\n                                    <tr\r\n                                            class=\"odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\">\r\n                                            <td scope=\"row\"\r\n                                                class=\"md:px-6 px-2 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white\">\r\n                                                <div>\r\n                                                    ${pax?.fullname}\r\n                                                </div>\r\n                                                ${pax?.withInfant ? html `\r\n                                                <div>\r\n                                                    <span class=\"text-xs text-red-500 \">\r\n                                                        * ${language === 'vi' ? `Em bé` : `Infant`}: ${pax?.withInfant?.fullname} - ${pax?.withInfant?.birthday.day + '/' + pax?.withInfant?.birthday.month + '/' + pax?.withInfant?.birthday.year} - \r\n                                                    ${pax?.withInfant?.gender === 'MSTR' ? language === 'vi' ? `Bé trai` : `Boy` : pax?.withInfant?.gender === 'MISS' ? language === 'vi' ? `Bé gái` : `Girl` : language === 'vi' ? `Khác` : `Other`}\r\n                                                    \r\n                                                </div>\r\n                                                ` : ``}\r\n                                                <div>\r\n                                                ${pax.baggages.map((baggage) => html `\r\n                                                ${baggage?.SsrCode ? html `\r\n                                                <div class=\"text-xs text-gray-500 dark:text-gray-400\">${baggage?.type} - ${baggage?.WeightBag} KG</div>\r\n                                                ` : ``}\r\n                                                `)}\r\n                                                </div>\r\n                                            </td>\r\n                                            <td class=\"md:px-6 px-2 py-2\">\r\n                                                ${formatDateToString(pax?.birthday, language)}\r\n                                            </td>\r\n                                            <td class=\"md:px-6 px-2 py-2\">\r\n                                                ${pax.gender === 'MR' ? language === 'vi' ? `Nam` : `Male` : pax.gender === 'MRS' ? language === 'vi' ? `Nữ` : `Female` : language === 'vi' ? `Khác` : `Other`}\r\n                                            </td>\r\n                                        </tr>\r\n                                    `)}\r\n                                        \r\n                                    </tbody>\r\n                                </table>\r\n                            </div>\r\n\r\n                        </div>\r\n                                    <!-- End List Passenger -->\r\n                                </div>\r\n                                <div class=\"col-span-2 relative max-md:mt-4\">\r\n                        <div class=\"sticky top-24\">\r\n                            <div class=\"border-2 border-dashed border-nmt-500 rounded-lg md:p-4 p-2\">\r\n                                <h1\r\n                                    class=\"w-full inline-block text-center text-xl font-extrabold tracking-tight text-nmt-500  dark:text-white\">\r\n                                    ${language === 'vi' ? `THÔNG TIN CHUYẾN BAY` : `FLIGHT INFORMATION`}</h1>\r\n                                <div>\r\n                                    <h2\r\n                                        class=\"mt-4 flex gap-2  text-base tracking-tight text-gray-600  dark:text-white\">\r\n                                        <span class=\"font-extrabold\">${language === 'vi' ? `Điểm Khởi Hành:` : `Departure point:`}</span> <strong\r\n                                            class=\"text-gray-500 dark:text-white font-semibold\">\r\n                                            ${inforAirports[orderAvailable?.Depart]?.cityName}\r\n                                            (${orderAvailable?.Depart})\r\n                                        </strong>\r\n                                    </h2>\r\n                                    <h2 class=\"mt-2  gap-2  text-base tracking-tight text-gray-600  dark:text-white\">\r\n                                        <span class=\"font-extrabold  whitespace-nowrap\">${language === 'vi' ? `Điểm Đến:` : `Arrival point:`} </span>\r\n                                        <span class=\" text-gray-500 dark:text-white font-semibold\">\r\n\r\n                                            ${inforAirports[orderAvailable?.Arrival]?.cityName}\r\n                                            (${orderAvailable?.Arrival})</span>\r\n                                    </h2>\r\n                                    <h2\r\n                                        class=\"mt-2 flex gap-2  text-base tracking-tight text-gray-600  dark:text-white\">\r\n                                        <span class=\"font-extrabold inline-flex whitespace-nowrap\">${language === 'vi' ? `Ngày khởi hành:` : `Departure date:`}</span>\r\n                                        <span class=\"inline-flex text-gray-500 dark:text-white font-semibold\">\r\n                                            ${orderAvailable?.DepartDate}\r\n                                        </span>\r\n                                    </h2>\r\n                                    ${orderAvailable?.ReturnDate ? html `\r\n                                    <h2 class=\"mt-2 flex gap-2  text-base tracking-tight text-gray-600  dark:text-white\">\r\n                                        <span class=\"font-extrabold inline-flex whitespace-nowrap\">${language === 'vi' ? `Ngày trở về:` : `Return date:`}</span>\r\n                                        <span class=\"inline-flex text-gray-500 dark:text-white font-semibold\">\r\n                                            ${orderAvailable?.ReturnDate}\r\n                                        </span>\r\n                                    </h2>\r\n                                    ` : ``}\r\n                                    \r\n                                    <h2\r\n                                        class=\"mt-2 flex flex-col gap-2  text-base tracking-tight text-gray-600  dark:text-white\">\r\n                                        <span class=\"font-extrabold inline-flex whitespace-nowrap\">${language === 'vi' ? `Chi tiết chuyến bay:` : `Flight details:`}</span>\r\n                                        ${orderDetails.full?.InventoriesSelected.length > 0 ? html `\r\n                                        <div class=\"w-full space-y-10\">\r\n                                        ${orderDetails.full?.InventoriesSelected.map((itinerarySelected, index) => html `\r\n                                        <div class=\"w-full bg-gray-100 my-4 \">\r\n                                                <!-- start flight infor -->\r\n                                                <div class=\"bg-white rounded-e-lg rounded-bl-lg \">\r\n                                                    <div class=\"py-[2px] flex gap-2\">\r\n                                                        <button\r\n                                                            class=\"h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-md text-sm px-2 py-1 text-center me-2\">\r\n                                                            <span>\r\n                                                            ${orderDetails.full?.InventoriesSelected.length > 1 ? html `${index % 2 === 1 ? language === 'vi' ? `Chiều về` : `Return` : language === 'vi' ? `Chiều đi` : `Departure`}` : ``}\r\n                                                            </span>\r\n                                                        </button>\r\n\r\n                                                        <span>\r\n                                                            ${formatDateTo_ddMMyyyy(itinerarySelected.segment.Legs[0]?.DepartureDate, language)}\r\n                                                            |\r\n                                                            ${language === 'vi' ? `Thời gian bay` : `Flight time`}\r\n                                                            ${getDurationByArray(itinerarySelected.segment.Legs)}\r\n                                                        </span>\r\n                                                    </div>\r\n                                                    <div class=\"w-full\">\r\n                                                    ${itinerarySelected.segment.Legs.map((leg, $index) => html `\r\n                                                    ${index > 0 ? html `\r\n                                                    <div\r\n                                                            class=\"relative flex flex-1 flex-row items-center justify-start min-h-[50px] w-full  before:content-[''] before:absolute before:border-l-[4px] before:border-dotted before:border-[#dadfe6] before:h-[30%] before:w-0 before:start-[68px] before:top-0 before:inline-block after:content-[''] after:absolute after:border-l-[4px] after:border-dotted after:border-[#dadfe6] after:h-[30%] after:w-0 after:start-[68px] after:top-[70%] after:inline-block\">\r\n                                                            <div class=\"flex py-4 ps-[80px] w-full\">\r\n                                                                <div\r\n                                                                    class=\"flex flex-row items-center justify-start w-full\">\r\n                                                                    <div\r\n                                                                        class=\"w-full text-sm py-2 px-1 bg-gray-100 rounded-lg \">\r\n                                                                        ${language === 'vi' ? `Trung chuyển tại` : `Transit at`}\r\n                                                                        ${inforAirports[leg.DepartureCode]?.cityName}\r\n                                                                        ${convertDurationToHour(itinerarySelected.segment.Legs[$index].StopTime)}\r\n\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </div>\r\n                                                            <!-- icon -->\r\n                                                            <div\r\n                                                                class=\"absolute inline-block start-[62.5px] top-[calc(50%-8px)]\">\r\n                                                                <svg class=\"w-4 h-4 text-[#acb4bf] dark:text-white\"\r\n                                                                    aria-hidden=\"true\"\r\n                                                                    xmlns=\"http://www.w3.org/2000/svg\" width=\"24\"\r\n                                                                    height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                                                    <path stroke=\"currentColor\" stroke-linecap=\"round\"\r\n                                                                        stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                                                                        d=\"M17.651 7.65a7.131 7.131 0 0 0-12.68 3.15M18.001 4v4h-4m-7.652 8.35a7.13 7.13 0 0 0 12.68-3.15M6 20v-4h4\" />\r\n                                                                </svg>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    ` : ``}\r\n                                                    <div class=\"flex flex-col items-start overflow-hidden relative\">\r\n                                                            <div>\r\n                                                                <div class=\"flex flex-row items-center justify-start\">\r\n                                                                    <div\r\n                                                                        class=\"flex flex-col items-center text-[#0f294d] text-[16px]  leading-[24px] me-[43px] whitespace-nowrap w-[45px] font-extrabold  \">\r\n                                                                        <span>\r\n                                                                            ${getTimeFromDateTime(leg?.DepartureDate)}\r\n                                                                        </span>\r\n                                                                    </div>\r\n                                                                    <span\r\n                                                                        class=\"text-[#0f294d] text-[16px] font-semibold leading-[24px]\">\r\n                                                                        <span>(${leg?.DepartureCode})</span>\r\n                                                                        ${inforAirports[leg?.DepartureCode]?.name}\r\n                                                                    </span>\r\n                                                                </div>\r\n\r\n                                                                <!-- class=\"flex flex-1 flex-row items-center justify-start min-h-[50px] w-full  before:content-[''] before:absolute before:bg-[#dadfe6] before:rounded-[2px] before:w-[4px] before:start-[68px] before:inline-block before:h-[32%] before:top-[5%] after:content-[''] after:absolute after:bg-[#dadfe6] after:rounded-[2px] after:w-[4px] after:h-[35%] after:start-[68px] after:bottom-[5%] after:inline-block\"> -->\r\n                                                                <div\r\n                                                                    class=\"flex flex-1 flex-row items-center justify-start min-h-[50px] w-full  before:content-[''] before:absolute before:bg-[#dadfe6] before:rounded-[2px] before:w-[4px] before:start-[68px] before:inline-block before:h-[80%] before:top-[10%] \">\r\n                                                                    <div\r\n                                                                        class=\"flex flex-row-reverse flex-wrap-reverse items-center justify-end w-[97px] overflow-hidden \">\r\n                                                                        <div\r\n                                                                            class=\"flex items-center justify-center bg-white  h-[24px]\">\r\n                                                                            <img src=\"${apiUrl}/assets/img/airlines/${leg?.OperatingAirlines}.png\"\r\n                                                                                class=\"h-[22px] pe-7 max-w-[86px]\">\r\n                                                                        </div>\r\n                                                                    </div>\r\n\r\n                                                                    <div class=\"flex md:py-4 py-3\">\r\n                                                                        <div\r\n                                                                            class=\"flex flex-row items-center justify-start\">\r\n                                                                            <span\r\n                                                                                class=\"md:text-sm text-xs text-[#8592a6]\">\r\n                                                                                ${leg?.OperatingAirlinesName}${leg?.OperatingAirlines + leg?.FlightNumber}${itinerarySelected.inventorySelected?.BookingInfos[$index]?.FareType || itinerarySelected.inventorySelected?.BookingInfos[$index]?.CabinName}\r\n                                                                            </span>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n\r\n                                                                <div class=\"flex flex-row items-center justify-start\">\r\n                                                                    <div\r\n                                                                        class=\"flex flex-col items-center text-[#0f294d] font-extrabold text-[16px]  leading-[24px] me-[43px] whitespace-nowrap w-[45px]\">\r\n                                                                        <span>${getTimeFromDateTime(leg?.ArrivalDate)}</span>\r\n                                                                    </div>\r\n                                                                    <span\r\n                                                                        class=\"text-nmt-600 text-[16px] font-semibold leading-[24px]\">\r\n                                                                        <span>(${leg?.ArrivalCode})</span>\r\n                                                                        ${inforAirports[leg?.ArrivalCode]?.name}\r\n                                                                    </span>\r\n                                                                </div>\r\n                                                            </div>\r\n                                                        </div>\r\n\r\n                                                    `)}\r\n                                                    </div>\r\n                                                </div>\r\n                                                <!-- end flight infor -->\r\n                                            </div>\r\n                                        `)}\r\n                                        </div>\r\n                                        ` : html ``}\r\n                                    </h2>\r\n\r\n\r\n                                    <h2\r\n                                        class=\"mt-2  gap-2 text-center  text-base tracking-tight text-nmt-500  dark:text-white font-extrabold  whitespace-nowrap\">\r\n                                        --------OOOOO-------\r\n                                    </h2>\r\n                                </div>\r\n                            </div>\r\n\r\n\r\n                        </div>\r\n                    </div>\r\n                            </div>\r\n                            ${orderDetails.full?.InventoriesSelected.length > 0 ? html `\r\n                <div class=\"w-full space-y-10 my-8\">\r\n                ${orderDetails.full?.InventoriesSelected.map((itinerarySelected, index) => html `\r\n                ${itinerarySelected.segment.Legs.map((leg, $index) => html `\r\n                <div class=\"space-y-4  bg-card mb-8  \">\r\n                        <div class=\"max-md:overflow-x-scroll w-auto h-max  max-md:overflow-y-hidden max-md:pb-2\">\r\n                            <div class=\"md:w-full w-max m-auto \">\r\n                                <div class=\"grid grid-cols-10 rounded-lg  relative \">\r\n                                    <div class=\"col-span-4 shadow-lg  relative rounded-s-lg \">\r\n                                        <div\r\n                                            class=\"w-full h-[37px] flex justify-between items-center  md:px-8 px-4 py-1 bg-gradient-to-l from-nmt-600 via-nmt-600 to-nmt-300 rounded-tl-lg\">\r\n                                            <span class=\"text-white md:text-lg text-base font-extrabold line-clamp-1\">\r\n                                                <svg class=\"fill-white w-6 h-6 inline-block\"\r\n                                                    xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 640 512\">\r\n                                                    <path\r\n                                                        d=\"M381 114.9L186.1 41.8c-16.7-6.2-35.2-5.3-51.1 2.7L89.1 67.4C78 73 77.2 88.5 87.6 95.2l146.9 94.5L136 240 77.8 214.1c-8.7-3.9-18.8-3.7-27.3 .6L18.3 230.8c-9.3 4.7-11.8 16.8-5 24.7l73.1 85.3c6.1 7.1 15 11.2 24.3 11.2l137.7 0c5 0 9.9-1.2 14.3-3.4L535.6 212.2c46.5-23.3 82.5-63.3 100.8-112C645.9 75 627.2 48 600.2 48l-57.4 0c-20.2 0-40.2 4.8-58.2 14L381 114.9zM0 480c0 17.7 14.3 32 32 32l576 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 448c-17.7 0-32 14.3-32 32z\" />\r\n                                                </svg>\r\n                                                <span class=\"ms-2\">${inforAirports[leg.DepartureCode]?.cityName} - ${inforAirports[leg.ArrivalCode]?.cityName}</span>\r\n                                            </span>\r\n                                        </div>\r\n                                        <div\r\n                                            class=\" flex flex-col justify-start items-center text-gray-800 bg-white  rounded-bl-lg pb-4\">\r\n                                            <div class=\"w-full h-12 flex justify-center items-center mt-4\">\r\n                                                <img src=\"${apiUrl}/assets/img/airlines/${leg.OperatingAirlines}.png\" class=\"h-full w-auto\">\r\n                                            </div>\r\n                                            <div\r\n                                                class=\"w-full flex flex-col justify-start items-start mt-2 space-y-2 md:px-8 px-4\">\r\n                                                <div class=\"flex flex-col \">\r\n                                                    <span class=\"text-xs font-semibold text-gray-700\">${language === 'vi' ? `HÃNG CHUYÊN CHỞ/ CARRIER` : `CARRIER`}</span>\r\n                                                    <span\r\n                                                        class=\"text-base font-bold uppercase\">${leg.OperatingAirlinesName}</span>\r\n                                                </div>\r\n\r\n                                                <div class=\"flex flex-col \">\r\n                                                    <span class=\"text-xs font-semibold text-gray-700\">${language === 'vi' ? `SỐ HIỆU/ FLIGHT` : `FLIGHT`}</span>\r\n                                                    <span class=\"text-base font-bold\">\r\n                                                        ${leg.OperatingAirlines + leg.FlightNumber}\r\n                                                    </span>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"col-span-6 relative  flex flex-row rounded-br-lg\">\r\n                                        <div class=\"w-3.5 min-w-3.5 h-full m-auto  bg-transparent z-20 \">\r\n                                            <div class=\"w-3.5 h-3 bg-nmt-600 mask-top-circle-cut\">\r\n                                            </div>\r\n                                            <div\r\n                                                class=\"w-3.5 h-[calc(100%-1.5rem)] bg-white  flex justify-center items-center relative z-10\">\r\n                                                <div\r\n                                                    style=\"background-image: linear-gradient(to bottom, #fff 50%, #ea580c 50%) !important; width: 1px; height: 100%; position: relative; background-size: 100% 10px;\">\r\n                                                    <div\r\n                                                        class=\"absolute md:top-0 -top-[1px] w-6 -left-2 md:h-[25px] h-[26px] bg-nmt-600\">\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div class=\"w-3.5 h-3 bg-white mask-bottom-circle-cut\">\r\n                                            </div>\r\n                                        </div>\r\n                                        <div\r\n                                            class=\"w-full h-full shadow-lg flex justify-between items-center cursor-pointer relative z-10 rounded-br-lg\">\r\n                                            <div\r\n                                                class=\"w-full h-full text-center cursor-pointer bg-white rounded-br-lg flex flex-col \">\r\n                                                <div class=\"w-full  text-center cursor-pointer\">\r\n                                                    <div\r\n                                                        class=\"w-full flex justify-end items-center h-[37px]  md:px-8 px-4 py-1  bg-gradient-to-l from-nmt-700 via-nmt-700 to-nmt-600 rounded-tr-lg\">\r\n                                                        <span class=\"text-white md:text-lg text-base font-extrabold\">\r\n                                                            (${getDayInWeek(leg.DepartureDate)}) - ${formatddMMyyyy(leg.DepartureDate)}\r\n                                                        </span>\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div\r\n                                                    class=\"flex justify-center items-center w-full h-full px-4 gap-2 my-4\">\r\n                                                    <div class=\"flex flex-col justify-start items-start\">\r\n                                                        <strong class=\"text-3xl font-extrabold text-nmt-600\">\r\n                                                            ${getTimeFromDateTime(leg.DepartureDate)}\r\n                                                        </strong>\r\n                                                        <strong\r\n                                                            class=\"md:text-base text-sm font-semibold text-gray-600\">\r\n                                                            ${formatddMMyyyy(leg.DepartureDate)}\r\n                                                        </strong>\r\n                                                        <strong class=\"text-lg font-bold text-gray-800 text-nowrap\">\r\n                                                            ${leg.DepartureCode + ' - ' + inforAirports[leg.DepartureCode]?.cityName}\r\n                                                        </strong>\r\n                                                        <strong class=\"md:text-base text-sm font-semibold text-gray-600\">\r\n                                                            ${language === 'vi' ? `Nhà ga:` : `Terminal:`} ${leg.DepartureTerminal || '-'}\r\n                                                        </strong>\r\n                                                    </div>\r\n                                                    <div class=\"w-full flex-col justify-center items-center\">\r\n                                                        <div class=\"w-full text-lg text-center font-semibold -mb-2\">\r\n                                                            ${leg.Equipment}\r\n                                                        </div>\r\n                                                        <div class=\"w-full flex justify-center items-center md:px-6\">\r\n                                                            <div class=\"w-full h-[3px] rounded-full bg-nmt-600 \">\r\n                                                            </div>\r\n                                                            <svg xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                                class=\"w-6 h-6 fill-nmt-600 inline-block ml-[1px]\"\r\n                                                                viewBox=\"0 0 576 512\">\r\n                                                                <path\r\n                                                                    d=\"M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z\" />\r\n                                                            </svg>\r\n                                                        </div>\r\n                                                        <div class=\"w-full text-lg text-center font-semibold -mt-2\">\r\n                                                            ${getDuration(leg)}\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div class=\"flex flex-col justify-end items-end\">\r\n                                                        <strong class=\"text-3xl font-bold \">\r\n                                                            ${getTimeFromDateTime(leg.ArrivalDate)}\r\n                                                        </strong>\r\n                                                        <strong\r\n                                                            class=\"md:text-base text-sm font-semibold text-gray-700\">\r\n                                                            ${formatddMMyyyy(leg.ArrivalDate)}\r\n                                                        </strong>\r\n                                                        <strong class=\"text-lg  font-bold text-gray-800 text-nowrap\">\r\n                                                            ${inforAirports[leg.ArrivalCode]?.cityName + ' - ' + leg.ArrivalCode}\r\n                                                        </strong>\r\n                                                        <strong class=\"md:text-base text-sm font-semibold text-gray-700\">\r\n                                                            ${language === 'vi' ? `Nhà ga:` : `Terminal:`} ${leg.ArrivalTerminal || '-'}\r\n                                                        </strong>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <div class=\"w-full rounded-br-lg\">\r\n                                                    <div\r\n                                                        style=\"width: 100%; height: 1px; background-image: linear-gradient(to right, transparent 50%, #fb7740 50%); background-size: 10px 100%;\">\r\n                                                    </div>\r\n                                                    <div\r\n                                                        class=\"w-full rounded-br-lg flex justify-between items-center px-4 pb-2\">\r\n                                                        <span>\r\n                                                            <span>${language === 'vi' ? `Hành lý xách tay:` : `Carry-on baggage:`} </span>\r\n                                                            ${itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandBaggage > 1 && itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandWeightBag !== 0 ? html `\r\n                                                            <strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandBaggage}x</strong>\r\n                                                            ` : ``}\r\n                                                            <strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandWeightBag}KG</strong>\r\n                                                        </span>\r\n                                                        <span>\r\n                                                            <span>${language === 'vi' ? `Hành lý ký gửi:` : `Checked baggage:`} </span>\r\n                                                            ${itinerarySelected.inventorySelected?.BookingInfos[$index]?.BagPieces > 1 && itinerarySelected.inventorySelected?.BookingInfos[$index]?.WeightBag !== 0 ? html `\r\n                                                            <strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.BagPieces}x</strong>\r\n                                                            ` : ``}\r\n                                                            ${itinerarySelected.inventorySelected?.BookingInfos[$index]?.WeightBag === 0 ? html `\r\n                                                            <span>${language === 'vi' ? `Không bao gồm` : `Not included`}</span>\r\n                                                            ` : html `<strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.WeightBag}KG</strong>`}\r\n                                                        </span>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                    <!-- end bottom -->\r\n\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                `)}\r\n                `)}\r\n                </div>\r\n                ` : html ``}\r\n                <div class=\" bg-white border shadow-md rounded-lg overflow-hidden\">\r\n\r\n                    <div class=\"px-6 pt-6 space-y-2 \">\r\n                        <div class=\"space-y-2 text-sm\">\r\n                            <h2 class=\"text-lg font-semibold text-nmt-700 border-b border-nmt-200 pb-2\">${language === 'vi' ? `Thời gian check-in` : `Check-in time`}</h2>\r\n                            <ul class=\"list-disc pl-5 space-y-1\">\r\n                                <li>${language === 'vi' ? `Quý khách vui lòng tới sân bay trước` : `Please arrive at the airport before`} \r\n                                    <span class=\"font-medium text-nmt-600\">${__NoteModel?.TimeCheckIn?.Domestic}</span> \r\n                                    (${language === 'vi' ? `nội địa` : `domestic`}), \r\n                                    hoặc <span class=\"font-medium text-nmt-600\">${__NoteModel?.TimeCheckIn?.International}</span> \r\n                                    (${language === 'vi' ? `quốc tế` : `international`}) \r\n                                    ${language === 'vi' ? `để làm thủ tục check-in` : `to complete check-in procedures`}.</li>\r\n                            </ul>\r\n                        </div>\r\n                        <div class=\"space-y-2\">\r\n                            <h2 class=\"text-lg font-semibold text-nmt-700 border-b border-nmt-200 pb-2\">${language === 'vi' ? `Giấy tờ tùy thân` : `Identity documents`}</h2>\r\n                            <ul class=\"list-disc pl-5 space-y-1 text-sm\">\r\n                                ${__NoteModel?.IdentityDocuments?.map((identityDoc) => html `<li>${identityDoc.value}</li>`)}\r\n                            </ul>\r\n                        </div>\r\n                        <div class=\"space-y-2\">\r\n                            <h2 class=\"text-lg font-semibold text-nmt-700 border-b border-nmt-200 pb-2\">${language === 'vi' ? `Quy định đặc biệt` : `Special rules`}</h2>\r\n                            <ul class=\"list-disc pl-5 space-y-2 text-sm\">\r\n                            ${__NoteModel?.SpecialRules?.map((specialRule) => html `<li>${specialRule.value}</li>`)}\r\n                            </ul>\r\n                        </div>\r\n                        <p class=\"text-nmt-800 font-bold text-lg text-center pb-4\">${language === 'vi' ? `CHÚC QUÝ KHÁCH CÓ CHUYẾN BAY TỐT ĐẸP!` : `HAVE A GOOD TRIP!`}</p>\r\n\r\n                    </div>\r\n    \r\n                </div>\r\n                        ` : html `\r\n                            <div class=\"flex flex-col text-center w-full md:mb-10\">\r\n                                <h1 class=\"inline-block mb-2 text-3xl font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                    ${language === 'vi' ? `KHÔNG TÌM THẤY ĐƠN HÀNG` : `ORDER NOT FOUND`}\r\n                                    <div class=\"italic text-lg font-normal\">${language === 'vi' ? `(Vui lòng kiểm tra lại mã đơn hàng)` : `(Please check the order code)`}</div>\r\n                                </h1>\r\n                            </div>\r\n                        `}\r\n                    </div>\r\n                </section>\r\n            </div>\r\n        </div>\r\n    `}\r\n`;\n};\n", "/**\n * FingerprintJS v4.6.1 - Copyright (c) FingerprintJS, Inc, 2025 (https://fingerprint.com)\n *\n * Licensed under Business Source License 1.1 https://mariadb.com/bsl11/\n * Licensor: FingerprintJS, Inc.\n * Licensed Work: FingerprintJS browser fingerprinting library\n * Additional Use Grant: None\n * Change Date: Four years from first release for the specific version.\n * Change License: MIT, text at https://opensource.org/license/mit/ with the following copyright notice:\n * Copyright 2015-present FingerprintJS, Inc.\n */\n\nimport { __awaiter, __generator, __assign, __spreadArray } from 'tslib';\n\nvar version = \"4.6.1\";\n\nfunction wait(durationMs, resolveWith) {\n    return new Promise(function (resolve) { return setTimeout(resolve, durationMs, resolveWith); });\n}\n/**\n * Allows asynchronous actions and microtasks to happen.\n */\nfunction releaseEventLoop() {\n    // Don't use setTimeout because Chrome throttles it in some cases causing very long agent execution:\n    // https://stackoverflow.com/a/6032591/1118709\n    // https://github.com/chromium/chromium/commit/0295dd09496330f3a9103ef7e543fa9b6050409b\n    // Reusing a MessageChannel object gives no noticeable benefits\n    return new Promise(function (resolve) {\n        var channel = new MessageChannel();\n        channel.port1.onmessage = function () { return resolve(); };\n        channel.port2.postMessage(null);\n    });\n}\nfunction requestIdleCallbackIfAvailable(fallbackTimeout, deadlineTimeout) {\n    if (deadlineTimeout === void 0) { deadlineTimeout = Infinity; }\n    var requestIdleCallback = window.requestIdleCallback;\n    if (requestIdleCallback) {\n        // The function `requestIdleCallback` loses the binding to `window` here.\n        // `globalThis` isn't always equal `window` (see https://github.com/fingerprintjs/fingerprintjs/issues/683).\n        // Therefore, an error can occur. `call(window,` prevents the error.\n        return new Promise(function (resolve) { return requestIdleCallback.call(window, function () { return resolve(); }, { timeout: deadlineTimeout }); });\n    }\n    else {\n        return wait(Math.min(fallbackTimeout, deadlineTimeout));\n    }\n}\nfunction isPromise(value) {\n    return !!value && typeof value.then === 'function';\n}\n/**\n * Calls a maybe asynchronous function without creating microtasks when the function is synchronous.\n * Catches errors in both cases.\n *\n * If just you run a code like this:\n * ```\n * console.time('Action duration')\n * await action()\n * console.timeEnd('Action duration')\n * ```\n * The synchronous function time can be measured incorrectly because another microtask may run before the `await`\n * returns the control back to the code.\n */\nfunction awaitIfAsync(action, callback) {\n    try {\n        var returnedValue = action();\n        if (isPromise(returnedValue)) {\n            returnedValue.then(function (result) { return callback(true, result); }, function (error) { return callback(false, error); });\n        }\n        else {\n            callback(true, returnedValue);\n        }\n    }\n    catch (error) {\n        callback(false, error);\n    }\n}\n/**\n * If you run many synchronous tasks without using this function, the JS main loop will be busy and asynchronous tasks\n * (e.g. completing a network request, rendering the page) won't be able to happen.\n * This function allows running many synchronous tasks such way that asynchronous tasks can run too in background.\n */\nfunction mapWithBreaks(items, callback, loopReleaseInterval) {\n    if (loopReleaseInterval === void 0) { loopReleaseInterval = 16; }\n    return __awaiter(this, void 0, void 0, function () {\n        var results, lastLoopReleaseTime, i, now;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    results = Array(items.length);\n                    lastLoopReleaseTime = Date.now();\n                    i = 0;\n                    _a.label = 1;\n                case 1:\n                    if (!(i < items.length)) return [3 /*break*/, 4];\n                    results[i] = callback(items[i], i);\n                    now = Date.now();\n                    if (!(now >= lastLoopReleaseTime + loopReleaseInterval)) return [3 /*break*/, 3];\n                    lastLoopReleaseTime = now;\n                    return [4 /*yield*/, releaseEventLoop()];\n                case 2:\n                    _a.sent();\n                    _a.label = 3;\n                case 3:\n                    ++i;\n                    return [3 /*break*/, 1];\n                case 4: return [2 /*return*/, results];\n            }\n        });\n    });\n}\n/**\n * Makes the given promise never emit an unhandled promise rejection console warning.\n * The promise will still pass errors to the next promises.\n * Returns the input promise for convenience.\n *\n * Otherwise, promise emits a console warning unless it has a `catch` listener.\n */\nfunction suppressUnhandledRejectionWarning(promise) {\n    promise.then(undefined, function () { return undefined; });\n    return promise;\n}\n\n/*\n * This file contains functions to work with pure data only (no browser features, DOM, side effects, etc).\n */\n/**\n * Does the same as Array.prototype.includes but has better typing\n */\nfunction includes(haystack, needle) {\n    for (var i = 0, l = haystack.length; i < l; ++i) {\n        if (haystack[i] === needle) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * Like `!includes()` but with proper typing\n */\nfunction excludes(haystack, needle) {\n    return !includes(haystack, needle);\n}\n/**\n * Be careful, NaN can return\n */\nfunction toInt(value) {\n    return parseInt(value);\n}\n/**\n * Be careful, NaN can return\n */\nfunction toFloat(value) {\n    return parseFloat(value);\n}\nfunction replaceNaN(value, replacement) {\n    return typeof value === 'number' && isNaN(value) ? replacement : value;\n}\nfunction countTruthy(values) {\n    return values.reduce(function (sum, value) { return sum + (value ? 1 : 0); }, 0);\n}\nfunction round(value, base) {\n    if (base === void 0) { base = 1; }\n    if (Math.abs(base) >= 1) {\n        return Math.round(value / base) * base;\n    }\n    else {\n        // Sometimes when a number is multiplied by a small number, precision is lost,\n        // for example 1234 * 0.0001 === 0.12340000000000001, and it's more precise divide: 1234 / (1 / 0.0001) === 0.1234.\n        var counterBase = 1 / base;\n        return Math.round(value * counterBase) / counterBase;\n    }\n}\n/**\n * Parses a CSS selector into tag name with HTML attributes.\n * Only single element selector are supported (without operators like space, +, >, etc).\n *\n * Multiple values can be returned for each attribute. You decide how to handle them.\n */\nfunction parseSimpleCssSelector(selector) {\n    var _a, _b;\n    var errorMessage = \"Unexpected syntax '\".concat(selector, \"'\");\n    var tagMatch = /^\\s*([a-z-]*)(.*)$/i.exec(selector);\n    var tag = tagMatch[1] || undefined;\n    var attributes = {};\n    var partsRegex = /([.:#][\\w-]+|\\[.+?\\])/gi;\n    var addAttribute = function (name, value) {\n        attributes[name] = attributes[name] || [];\n        attributes[name].push(value);\n    };\n    for (;;) {\n        var match = partsRegex.exec(tagMatch[2]);\n        if (!match) {\n            break;\n        }\n        var part = match[0];\n        switch (part[0]) {\n            case '.':\n                addAttribute('class', part.slice(1));\n                break;\n            case '#':\n                addAttribute('id', part.slice(1));\n                break;\n            case '[': {\n                var attributeMatch = /^\\[([\\w-]+)([~|^$*]?=(\"(.*?)\"|([\\w-]+)))?(\\s+[is])?\\]$/.exec(part);\n                if (attributeMatch) {\n                    addAttribute(attributeMatch[1], (_b = (_a = attributeMatch[4]) !== null && _a !== void 0 ? _a : attributeMatch[5]) !== null && _b !== void 0 ? _b : '');\n                }\n                else {\n                    throw new Error(errorMessage);\n                }\n                break;\n            }\n            default:\n                throw new Error(errorMessage);\n        }\n    }\n    return [tag, attributes];\n}\n/**\n * Converts a string to UTF8 bytes\n */\nfunction getUTF8Bytes(input) {\n    // Benchmark: https://jsbench.me/b6klaaxgwq/1\n    // If you want to just count bytes, see solutions at https://jsbench.me/ehklab415e/1\n    var result = new Uint8Array(input.length);\n    for (var i = 0; i < input.length; i++) {\n        // `charCode` is faster than encoding, so we prefer that when it's possible\n        var charCode = input.charCodeAt(i);\n        // In case of non-ASCII symbols we use proper encoding\n        if (charCode > 127) {\n            return new TextEncoder().encode(input);\n        }\n        result[i] = charCode;\n    }\n    return result;\n}\n\n/*\n * Based on https://github.com/karanlyons/murmurHash3.js/blob/a33d0723127e2e5415056c455f8aed2451ace208/murmurHash3.js\n */\n/**\n * Adds two 64-bit values (provided as tuples of 32-bit values)\n * and updates (mutates) first value to write the result\n */\nfunction x64Add(m, n) {\n    var m0 = m[0] >>> 16, m1 = m[0] & 0xffff, m2 = m[1] >>> 16, m3 = m[1] & 0xffff;\n    var n0 = n[0] >>> 16, n1 = n[0] & 0xffff, n2 = n[1] >>> 16, n3 = n[1] & 0xffff;\n    var o0 = 0, o1 = 0, o2 = 0, o3 = 0;\n    o3 += m3 + n3;\n    o2 += o3 >>> 16;\n    o3 &= 0xffff;\n    o2 += m2 + n2;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o1 += m1 + n1;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o0 += m0 + n0;\n    o0 &= 0xffff;\n    m[0] = (o0 << 16) | o1;\n    m[1] = (o2 << 16) | o3;\n}\n/**\n * Multiplies two 64-bit values (provided as tuples of 32-bit values)\n * and updates (mutates) first value to write the result\n */\nfunction x64Multiply(m, n) {\n    var m0 = m[0] >>> 16, m1 = m[0] & 0xffff, m2 = m[1] >>> 16, m3 = m[1] & 0xffff;\n    var n0 = n[0] >>> 16, n1 = n[0] & 0xffff, n2 = n[1] >>> 16, n3 = n[1] & 0xffff;\n    var o0 = 0, o1 = 0, o2 = 0, o3 = 0;\n    o3 += m3 * n3;\n    o2 += o3 >>> 16;\n    o3 &= 0xffff;\n    o2 += m2 * n3;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o2 += m3 * n2;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o1 += m1 * n3;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o1 += m2 * n2;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o1 += m3 * n1;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o0 += m0 * n3 + m1 * n2 + m2 * n1 + m3 * n0;\n    o0 &= 0xffff;\n    m[0] = (o0 << 16) | o1;\n    m[1] = (o2 << 16) | o3;\n}\n/**\n * Provides left rotation of the given int64 value (provided as tuple of two int32)\n * by given number of bits. Result is written back to the value\n */\nfunction x64Rotl(m, bits) {\n    var m0 = m[0];\n    bits %= 64;\n    if (bits === 32) {\n        m[0] = m[1];\n        m[1] = m0;\n    }\n    else if (bits < 32) {\n        m[0] = (m0 << bits) | (m[1] >>> (32 - bits));\n        m[1] = (m[1] << bits) | (m0 >>> (32 - bits));\n    }\n    else {\n        bits -= 32;\n        m[0] = (m[1] << bits) | (m0 >>> (32 - bits));\n        m[1] = (m0 << bits) | (m[1] >>> (32 - bits));\n    }\n}\n/**\n * Provides a left shift of the given int32 value (provided as tuple of [0, int32])\n * by given number of bits. Result is written back to the value\n */\nfunction x64LeftShift(m, bits) {\n    bits %= 64;\n    if (bits === 0) {\n        return;\n    }\n    else if (bits < 32) {\n        m[0] = m[1] >>> (32 - bits);\n        m[1] = m[1] << bits;\n    }\n    else {\n        m[0] = m[1] << (bits - 32);\n        m[1] = 0;\n    }\n}\n/**\n * Provides a XOR of the given int64 values(provided as tuple of two int32).\n * Result is written back to the first value\n */\nfunction x64Xor(m, n) {\n    m[0] ^= n[0];\n    m[1] ^= n[1];\n}\nvar F1 = [0xff51afd7, 0xed558ccd];\nvar F2 = [0xc4ceb9fe, 0x1a85ec53];\n/**\n * Calculates murmurHash3's final x64 mix of that block and writes result back to the input value.\n * (`[0, h[0] >>> 1]` is a 33 bit unsigned right shift. This is the\n * only place where we need to right shift 64bit ints.)\n */\nfunction x64Fmix(h) {\n    var shifted = [0, h[0] >>> 1];\n    x64Xor(h, shifted);\n    x64Multiply(h, F1);\n    shifted[1] = h[0] >>> 1;\n    x64Xor(h, shifted);\n    x64Multiply(h, F2);\n    shifted[1] = h[0] >>> 1;\n    x64Xor(h, shifted);\n}\nvar C1 = [0x87c37b91, 0x114253d5];\nvar C2 = [0x4cf5ad43, 0x2745937f];\nvar M$1 = [0, 5];\nvar N1 = [0, 0x52dce729];\nvar N2 = [0, 0x38495ab5];\n/**\n * Given a string and an optional seed as an int, returns a 128 bit\n * hash using the x64 flavor of MurmurHash3, as an unsigned hex.\n * All internal functions mutates passed value to achieve minimal memory allocations and GC load\n *\n * Benchmark https://jsbench.me/p4lkpaoabi/1\n */\nfunction x64hash128(input, seed) {\n    var key = getUTF8Bytes(input);\n    seed = seed || 0;\n    var length = [0, key.length];\n    var remainder = length[1] % 16;\n    var bytes = length[1] - remainder;\n    var h1 = [0, seed];\n    var h2 = [0, seed];\n    var k1 = [0, 0];\n    var k2 = [0, 0];\n    var i;\n    for (i = 0; i < bytes; i = i + 16) {\n        k1[0] = key[i + 4] | (key[i + 5] << 8) | (key[i + 6] << 16) | (key[i + 7] << 24);\n        k1[1] = key[i] | (key[i + 1] << 8) | (key[i + 2] << 16) | (key[i + 3] << 24);\n        k2[0] = key[i + 12] | (key[i + 13] << 8) | (key[i + 14] << 16) | (key[i + 15] << 24);\n        k2[1] = key[i + 8] | (key[i + 9] << 8) | (key[i + 10] << 16) | (key[i + 11] << 24);\n        x64Multiply(k1, C1);\n        x64Rotl(k1, 31);\n        x64Multiply(k1, C2);\n        x64Xor(h1, k1);\n        x64Rotl(h1, 27);\n        x64Add(h1, h2);\n        x64Multiply(h1, M$1);\n        x64Add(h1, N1);\n        x64Multiply(k2, C2);\n        x64Rotl(k2, 33);\n        x64Multiply(k2, C1);\n        x64Xor(h2, k2);\n        x64Rotl(h2, 31);\n        x64Add(h2, h1);\n        x64Multiply(h2, M$1);\n        x64Add(h2, N2);\n    }\n    k1[0] = 0;\n    k1[1] = 0;\n    k2[0] = 0;\n    k2[1] = 0;\n    var val = [0, 0];\n    switch (remainder) {\n        case 15:\n            val[1] = key[i + 14];\n            x64LeftShift(val, 48);\n            x64Xor(k2, val);\n        // fallthrough\n        case 14:\n            val[1] = key[i + 13];\n            x64LeftShift(val, 40);\n            x64Xor(k2, val);\n        // fallthrough\n        case 13:\n            val[1] = key[i + 12];\n            x64LeftShift(val, 32);\n            x64Xor(k2, val);\n        // fallthrough\n        case 12:\n            val[1] = key[i + 11];\n            x64LeftShift(val, 24);\n            x64Xor(k2, val);\n        // fallthrough\n        case 11:\n            val[1] = key[i + 10];\n            x64LeftShift(val, 16);\n            x64Xor(k2, val);\n        // fallthrough\n        case 10:\n            val[1] = key[i + 9];\n            x64LeftShift(val, 8);\n            x64Xor(k2, val);\n        // fallthrough\n        case 9:\n            val[1] = key[i + 8];\n            x64Xor(k2, val);\n            x64Multiply(k2, C2);\n            x64Rotl(k2, 33);\n            x64Multiply(k2, C1);\n            x64Xor(h2, k2);\n        // fallthrough\n        case 8:\n            val[1] = key[i + 7];\n            x64LeftShift(val, 56);\n            x64Xor(k1, val);\n        // fallthrough\n        case 7:\n            val[1] = key[i + 6];\n            x64LeftShift(val, 48);\n            x64Xor(k1, val);\n        // fallthrough\n        case 6:\n            val[1] = key[i + 5];\n            x64LeftShift(val, 40);\n            x64Xor(k1, val);\n        // fallthrough\n        case 5:\n            val[1] = key[i + 4];\n            x64LeftShift(val, 32);\n            x64Xor(k1, val);\n        // fallthrough\n        case 4:\n            val[1] = key[i + 3];\n            x64LeftShift(val, 24);\n            x64Xor(k1, val);\n        // fallthrough\n        case 3:\n            val[1] = key[i + 2];\n            x64LeftShift(val, 16);\n            x64Xor(k1, val);\n        // fallthrough\n        case 2:\n            val[1] = key[i + 1];\n            x64LeftShift(val, 8);\n            x64Xor(k1, val);\n        // fallthrough\n        case 1:\n            val[1] = key[i];\n            x64Xor(k1, val);\n            x64Multiply(k1, C1);\n            x64Rotl(k1, 31);\n            x64Multiply(k1, C2);\n            x64Xor(h1, k1);\n        // fallthrough\n    }\n    x64Xor(h1, length);\n    x64Xor(h2, length);\n    x64Add(h1, h2);\n    x64Add(h2, h1);\n    x64Fmix(h1);\n    x64Fmix(h2);\n    x64Add(h1, h2);\n    x64Add(h2, h1);\n    return (('00000000' + (h1[0] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h1[1] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h2[0] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h2[1] >>> 0).toString(16)).slice(-8));\n}\n\n/**\n * Converts an error object to a plain object that can be used with `JSON.stringify`.\n * If you just run `JSON.stringify(error)`, you'll get `'{}'`.\n */\nfunction errorToObject(error) {\n    var _a;\n    return __assign({ name: error.name, message: error.message, stack: (_a = error.stack) === null || _a === void 0 ? void 0 : _a.split('\\n') }, error);\n}\nfunction isFunctionNative(func) {\n    return /^function\\s.*?\\{\\s*\\[native code]\\s*}$/.test(String(func));\n}\n\nfunction isFinalResultLoaded(loadResult) {\n    return typeof loadResult !== 'function';\n}\n/**\n * Loads the given entropy source. Returns a function that gets an entropy component from the source.\n *\n * The result is returned synchronously to prevent `loadSources` from\n * waiting for one source to load before getting the components from the other sources.\n */\nfunction loadSource(source, sourceOptions) {\n    var sourceLoadPromise = suppressUnhandledRejectionWarning(new Promise(function (resolveLoad) {\n        var loadStartTime = Date.now();\n        // `awaitIfAsync` is used instead of just `await` in order to measure the duration of synchronous sources\n        // correctly (other microtasks won't affect the duration).\n        awaitIfAsync(source.bind(null, sourceOptions), function () {\n            var loadArgs = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                loadArgs[_i] = arguments[_i];\n            }\n            var loadDuration = Date.now() - loadStartTime;\n            // Source loading failed\n            if (!loadArgs[0]) {\n                return resolveLoad(function () { return ({ error: loadArgs[1], duration: loadDuration }); });\n            }\n            var loadResult = loadArgs[1];\n            // Source loaded with the final result\n            if (isFinalResultLoaded(loadResult)) {\n                return resolveLoad(function () { return ({ value: loadResult, duration: loadDuration }); });\n            }\n            // Source loaded with \"get\" stage\n            resolveLoad(function () {\n                return new Promise(function (resolveGet) {\n                    var getStartTime = Date.now();\n                    awaitIfAsync(loadResult, function () {\n                        var getArgs = [];\n                        for (var _i = 0; _i < arguments.length; _i++) {\n                            getArgs[_i] = arguments[_i];\n                        }\n                        var duration = loadDuration + Date.now() - getStartTime;\n                        // Source getting failed\n                        if (!getArgs[0]) {\n                            return resolveGet({ error: getArgs[1], duration: duration });\n                        }\n                        // Source getting succeeded\n                        resolveGet({ value: getArgs[1], duration: duration });\n                    });\n                });\n            });\n        });\n    }));\n    return function getComponent() {\n        return sourceLoadPromise.then(function (finalizeSource) { return finalizeSource(); });\n    };\n}\n/**\n * Loads the given entropy sources. Returns a function that collects the entropy components.\n *\n * The result is returned synchronously in order to allow start getting the components\n * before the sources are loaded completely.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction loadSources(sources, sourceOptions, excludeSources, loopReleaseInterval) {\n    var includedSources = Object.keys(sources).filter(function (sourceKey) { return excludes(excludeSources, sourceKey); });\n    // Using `mapWithBreaks` allows asynchronous sources to complete between synchronous sources\n    // and measure the duration correctly\n    var sourceGettersPromise = suppressUnhandledRejectionWarning(mapWithBreaks(includedSources, function (sourceKey) { return loadSource(sources[sourceKey], sourceOptions); }, loopReleaseInterval));\n    return function getComponents() {\n        return __awaiter(this, void 0, void 0, function () {\n            var sourceGetters, componentPromises, componentArray, components, index;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, sourceGettersPromise];\n                    case 1:\n                        sourceGetters = _a.sent();\n                        return [4 /*yield*/, mapWithBreaks(sourceGetters, function (sourceGetter) { return suppressUnhandledRejectionWarning(sourceGetter()); }, loopReleaseInterval)];\n                    case 2:\n                        componentPromises = _a.sent();\n                        return [4 /*yield*/, Promise.all(componentPromises)\n                            // Keeping the component keys order the same as the source keys order\n                        ];\n                    case 3:\n                        componentArray = _a.sent();\n                        components = {};\n                        for (index = 0; index < includedSources.length; ++index) {\n                            components[includedSources[index]] = componentArray[index];\n                        }\n                        return [2 /*return*/, components];\n                }\n            });\n        });\n    };\n}\n/**\n * Modifies an entropy source by transforming its returned value with the given function.\n * Keeps the source properties: sync/async, 1/2 stages.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction transformSource(source, transformValue) {\n    var transformLoadResult = function (loadResult) {\n        if (isFinalResultLoaded(loadResult)) {\n            return transformValue(loadResult);\n        }\n        return function () {\n            var getResult = loadResult();\n            if (isPromise(getResult)) {\n                return getResult.then(transformValue);\n            }\n            return transformValue(getResult);\n        };\n    };\n    return function (options) {\n        var loadResult = source(options);\n        if (isPromise(loadResult)) {\n            return loadResult.then(transformLoadResult);\n        }\n        return transformLoadResult(loadResult);\n    };\n}\n\n/*\n * Functions to help with features that vary through browsers\n */\n/**\n * Checks whether the browser is based on Trident (the Internet Explorer engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isTrident() {\n    var w = window;\n    var n = navigator;\n    // The properties are checked to be in IE 10, IE 11 and not to be in other browsers in October 2020\n    return (countTruthy([\n        'MSCSSMatrix' in w,\n        'msSetImmediate' in w,\n        'msIndexedDB' in w,\n        'msMaxTouchPoints' in n,\n        'msPointerEnabled' in n,\n    ]) >= 4);\n}\n/**\n * Checks whether the browser is based on EdgeHTML (the pre-Chromium Edge engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isEdgeHTML() {\n    // Based on research in October 2020\n    var w = window;\n    var n = navigator;\n    return (countTruthy(['msWriteProfilerMark' in w, 'MSStream' in w, 'msLaunchUri' in n, 'msSaveBlob' in n]) >= 3 &&\n        !isTrident());\n}\n/**\n * Checks whether the browser is based on Chromium without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isChromium() {\n    // Based on research in October 2020. Tested to detect Chromium 42-86.\n    var w = window;\n    var n = navigator;\n    return (countTruthy([\n        'webkitPersistentStorage' in n,\n        'webkitTemporaryStorage' in n,\n        (n.vendor || '').indexOf('Google') === 0,\n        'webkitResolveLocalFileSystemURL' in w,\n        'BatteryManager' in w,\n        'webkitMediaStream' in w,\n        'webkitSpeechGrammar' in w,\n    ]) >= 5);\n}\n/**\n * Checks whether the browser is based on mobile or desktop Safari without using user-agent.\n * All iOS browsers use WebKit (the Safari engine).\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isWebKit() {\n    // Based on research in August 2024\n    var w = window;\n    var n = navigator;\n    return (countTruthy([\n        'ApplePayError' in w,\n        'CSSPrimitiveValue' in w,\n        'Counter' in w,\n        n.vendor.indexOf('Apple') === 0,\n        'RGBColor' in w,\n        'WebKitMediaKeys' in w,\n    ]) >= 4);\n}\n/**\n * Checks whether this WebKit browser is a desktop browser.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isDesktopWebKit() {\n    // Checked in Safari and DuckDuckGo\n    var w = window;\n    var HTMLElement = w.HTMLElement, Document = w.Document;\n    return (countTruthy([\n        'safari' in w,\n        !('ongestureend' in w),\n        !('TouchEvent' in w),\n        !('orientation' in w),\n        HTMLElement && !('autocapitalize' in HTMLElement.prototype),\n        Document && 'pointerLockElement' in Document.prototype,\n    ]) >= 4);\n}\n/**\n * Checks whether this WebKit browser is Safari.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * Warning! The function works properly only for Safari version 15.4 and newer.\n */\nfunction isSafariWebKit() {\n    // Checked in Safari, Chrome, Firefox, Yandex, UC Browser, Opera, Edge and DuckDuckGo.\n    // iOS Safari and Chrome were checked on iOS 11-18. DuckDuckGo was checked on iOS 17-18 and macOS 14-15.\n    // Desktop Safari versions 12-18 were checked.\n    // The other browsers were checked on iOS 17 and 18; there was no chance to check them on the other OS versions.\n    var w = window;\n    return (\n    // Filters-out Chrome, Yandex, DuckDuckGo (macOS and iOS), Edge\n    isFunctionNative(w.print) &&\n        // Doesn't work in Safari < 15.4\n        String(w.browser) === '[object WebPageNamespace]');\n}\n/**\n * Checks whether the browser is based on Gecko (Firefox engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isGecko() {\n    var _a, _b;\n    var w = window;\n    // Based on research in September 2020\n    return (countTruthy([\n        'buildID' in navigator,\n        'MozAppearance' in ((_b = (_a = document.documentElement) === null || _a === void 0 ? void 0 : _a.style) !== null && _b !== void 0 ? _b : {}),\n        'onmozfullscreenchange' in w,\n        'mozInnerScreenX' in w,\n        'CSSMozDocumentRule' in w,\n        'CanvasCaptureMediaStream' in w,\n    ]) >= 4);\n}\n/**\n * Checks whether the browser is based on Chromium version ≥86 without using user-agent.\n * It doesn't check that the browser is based on Chromium, there is a separate function for this.\n */\nfunction isChromium86OrNewer() {\n    // Checked in Chrome 85 vs Chrome 86 both on desktop and Android. Checked in macOS Chrome 128, Android Chrome 127.\n    var w = window;\n    return (countTruthy([\n        !('MediaSettingsRange' in w),\n        'RTCEncodedAudioFrame' in w,\n        '' + w.Intl === '[object Intl]',\n        '' + w.Reflect === '[object Reflect]',\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on Chromium version ≥122 without using user-agent.\n * It doesn't check that the browser is based on Chromium, there is a separate function for this.\n */\nfunction isChromium122OrNewer() {\n    // Checked in Chrome 121 vs Chrome 122 and 129 both on desktop and Android\n    var w = window;\n    var URLPattern = w.URLPattern;\n    return (countTruthy([\n        'union' in Set.prototype,\n        'Iterator' in w,\n        URLPattern && 'hasRegExpGroups' in URLPattern.prototype,\n        'RGB8' in WebGLRenderingContext.prototype,\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on WebKit version ≥606 (Safari ≥12) without using user-agent.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * @see https://en.wikipedia.org/wiki/Safari_version_history#Release_history Safari-WebKit versions map\n */\nfunction isWebKit606OrNewer() {\n    // Checked in Safari 9–18\n    var w = window;\n    return (countTruthy([\n        'DOMRectList' in w,\n        'RTCPeerConnectionIceEvent' in w,\n        'SVGGeometryElement' in w,\n        'ontransitioncancel' in w,\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on WebKit version ≥616 (Safari ≥17) without using user-agent.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * @see https://developer.apple.com/documentation/safari-release-notes/safari-17-release-notes Safari 17 release notes\n * @see https://tauri.app/v1/references/webview-versions/#webkit-versions-in-safari Safari-WebKit versions map\n */\nfunction isWebKit616OrNewer() {\n    var w = window;\n    var n = navigator;\n    var CSS = w.CSS, HTMLButtonElement = w.HTMLButtonElement;\n    return (countTruthy([\n        !('getStorageUpdates' in n),\n        HTMLButtonElement && 'popover' in HTMLButtonElement.prototype,\n        'CSSCounterStyleRule' in w,\n        CSS.supports('font-size-adjust: ex-height 0.5'),\n        CSS.supports('text-transform: full-width'),\n    ]) >= 4);\n}\n/**\n * Checks whether the device is an iPad.\n * It doesn't check that the engine is WebKit and that the WebKit isn't desktop.\n */\nfunction isIPad() {\n    // Checked on:\n    // Safari on iPadOS (both mobile and desktop modes): 8, 11-18\n    // Chrome on iPadOS (both mobile and desktop modes): 11-18\n    // Safari on iOS (both mobile and desktop modes): 9-18\n    // Chrome on iOS (both mobile and desktop modes): 9-18\n    // Before iOS 13. Safari tampers the value in \"request desktop site\" mode since iOS 13.\n    if (navigator.platform === 'iPad') {\n        return true;\n    }\n    var s = screen;\n    var screenRatio = s.width / s.height;\n    return (countTruthy([\n        // Since iOS 13. Doesn't work in Chrome on iPadOS <15, but works in desktop mode.\n        'MediaSource' in window,\n        // Since iOS 12. Doesn't work in Chrome on iPadOS.\n        !!Element.prototype.webkitRequestFullscreen,\n        // iPhone 4S that runs iOS 9 matches this, but it is not supported\n        // Doesn't work in incognito mode of Safari ≥17 with split screen because of tracking prevention\n        screenRatio > 0.65 && screenRatio < 1.53,\n    ]) >= 2);\n}\n/**\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getFullscreenElement() {\n    var d = document;\n    return d.fullscreenElement || d.msFullscreenElement || d.mozFullScreenElement || d.webkitFullscreenElement || null;\n}\nfunction exitFullscreen() {\n    var d = document;\n    // `call` is required because the function throws an error without a proper \"this\" context\n    return (d.exitFullscreen || d.msExitFullscreen || d.mozCancelFullScreen || d.webkitExitFullscreen).call(d);\n}\n/**\n * Checks whether the device runs on Android without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isAndroid() {\n    var isItChromium = isChromium();\n    var isItGecko = isGecko();\n    var w = window;\n    var n = navigator;\n    var c = 'connection';\n    // Chrome removes all words \"Android\" from `navigator` when desktop version is requested\n    // Firefox keeps \"Android\" in `navigator.appVersion` when desktop version is requested\n    if (isItChromium) {\n        return (countTruthy([\n            !('SharedWorker' in w),\n            // `typechange` is deprecated, but it's still present on Android (tested on Chrome Mobile 117)\n            // Removal proposal https://bugs.chromium.org/p/chromium/issues/detail?id=699892\n            // Note: this expression returns true on ChromeOS, so additional detectors are required to avoid false-positives\n            n[c] && 'ontypechange' in n[c],\n            !('sinkId' in new Audio()),\n        ]) >= 2);\n    }\n    else if (isItGecko) {\n        return countTruthy(['onorientationchange' in w, 'orientation' in w, /android/i.test(n.appVersion)]) >= 2;\n    }\n    else {\n        // Only 2 browser engines are presented on Android.\n        // Actually, there is also Android 4.1 browser, but it's not worth detecting it at the moment.\n        return false;\n    }\n}\n/**\n * Checks whether the browser is Samsung Internet without using user-agent.\n * It doesn't check that the browser is based on Chromium, please use `isChromium` before using this function.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isSamsungInternet() {\n    // Checked in Samsung Internet 21, 25 and 27\n    var n = navigator;\n    var w = window;\n    var audioPrototype = Audio.prototype;\n    var visualViewport = w.visualViewport;\n    return (countTruthy([\n        'srLatency' in audioPrototype,\n        'srChannelCount' in audioPrototype,\n        'devicePosture' in n,\n        visualViewport && 'segments' in visualViewport,\n        'getTextInformation' in Image.prototype, // Not available in Samsung Internet 21\n    ]) >= 3);\n}\n\n/**\n * A deep description: https://fingerprint.com/blog/audio-fingerprinting/\n * Inspired by and based on https://github.com/cozylife/audio-fingerprint\n *\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * Audio signal is noised in private mode of Safari 17, so audio fingerprinting is skipped in Safari 17.\n */\nfunction getAudioFingerprint() {\n    if (doesBrowserPerformAntifingerprinting$1()) {\n        return -4 /* SpecialFingerprint.KnownForAntifingerprinting */;\n    }\n    return getUnstableAudioFingerprint();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableAudioFingerprint() {\n    var w = window;\n    var AudioContext = w.OfflineAudioContext || w.webkitOfflineAudioContext;\n    if (!AudioContext) {\n        return -2 /* SpecialFingerprint.NotSupported */;\n    }\n    // In some browsers, audio context always stays suspended unless the context is started in response to a user action\n    // (e.g. a click or a tap). It prevents audio fingerprint from being taken at an arbitrary moment of time.\n    // Such browsers are old and unpopular, so the audio fingerprinting is just skipped in them.\n    // See a similar case explanation at https://stackoverflow.com/questions/46363048/onaudioprocess-not-called-on-ios11#46534088\n    if (doesBrowserSuspendAudioContext()) {\n        return -1 /* SpecialFingerprint.KnownForSuspending */;\n    }\n    var hashFromIndex = 4500;\n    var hashToIndex = 5000;\n    var context = new AudioContext(1, hashToIndex, 44100);\n    var oscillator = context.createOscillator();\n    oscillator.type = 'triangle';\n    oscillator.frequency.value = 10000;\n    var compressor = context.createDynamicsCompressor();\n    compressor.threshold.value = -50;\n    compressor.knee.value = 40;\n    compressor.ratio.value = 12;\n    compressor.attack.value = 0;\n    compressor.release.value = 0.25;\n    oscillator.connect(compressor);\n    compressor.connect(context.destination);\n    oscillator.start(0);\n    var _a = startRenderingAudio(context), renderPromise = _a[0], finishRendering = _a[1];\n    // Suppresses the console error message in case when the fingerprint fails before requested\n    var fingerprintPromise = suppressUnhandledRejectionWarning(renderPromise.then(function (buffer) { return getHash(buffer.getChannelData(0).subarray(hashFromIndex)); }, function (error) {\n        if (error.name === \"timeout\" /* InnerErrorName.Timeout */ || error.name === \"suspended\" /* InnerErrorName.Suspended */) {\n            return -3 /* SpecialFingerprint.Timeout */;\n        }\n        throw error;\n    }));\n    return function () {\n        finishRendering();\n        return fingerprintPromise;\n    };\n}\n/**\n * Checks if the current browser is known for always suspending audio context\n */\nfunction doesBrowserSuspendAudioContext() {\n    // Mobile Safari 11 and older\n    return isWebKit() && !isDesktopWebKit() && !isWebKit606OrNewer();\n}\n/**\n * Checks if the current browser is known for applying anti-fingerprinting measures in all or some critical modes\n */\nfunction doesBrowserPerformAntifingerprinting$1() {\n    return (\n    // Safari ≥17\n    (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) ||\n        // Samsung Internet ≥26\n        (isChromium() && isSamsungInternet() && isChromium122OrNewer()));\n}\n/**\n * Starts rendering the audio context.\n * When the returned function is called, the render process starts finishing.\n */\nfunction startRenderingAudio(context) {\n    var renderTryMaxCount = 3;\n    var renderRetryDelay = 500;\n    var runningMaxAwaitTime = 500;\n    var runningSufficientTime = 5000;\n    var finalize = function () { return undefined; };\n    var resultPromise = new Promise(function (resolve, reject) {\n        var isFinalized = false;\n        var renderTryCount = 0;\n        var startedRunningAt = 0;\n        context.oncomplete = function (event) { return resolve(event.renderedBuffer); };\n        var startRunningTimeout = function () {\n            setTimeout(function () { return reject(makeInnerError(\"timeout\" /* InnerErrorName.Timeout */)); }, Math.min(runningMaxAwaitTime, startedRunningAt + runningSufficientTime - Date.now()));\n        };\n        var tryRender = function () {\n            try {\n                var renderingPromise = context.startRendering();\n                // `context.startRendering` has two APIs: Promise and callback, we check that it's really a promise just in case\n                if (isPromise(renderingPromise)) {\n                    // Suppresses all unhandled rejections in case of scheduled redundant retries after successful rendering\n                    suppressUnhandledRejectionWarning(renderingPromise);\n                }\n                switch (context.state) {\n                    case 'running':\n                        startedRunningAt = Date.now();\n                        if (isFinalized) {\n                            startRunningTimeout();\n                        }\n                        break;\n                    // Sometimes the audio context doesn't start after calling `startRendering` (in addition to the cases where\n                    // audio context doesn't start at all). A known case is starting an audio context when the browser tab is in\n                    // background on iPhone. Retries usually help in this case.\n                    case 'suspended':\n                        // The audio context can reject starting until the tab is in foreground. Long fingerprint duration\n                        // in background isn't a problem, therefore the retry attempts don't count in background. It can lead to\n                        // a situation when a fingerprint takes very long time and finishes successfully. FYI, the audio context\n                        // can be suspended when `document.hidden === false` and start running after a retry.\n                        if (!document.hidden) {\n                            renderTryCount++;\n                        }\n                        if (isFinalized && renderTryCount >= renderTryMaxCount) {\n                            reject(makeInnerError(\"suspended\" /* InnerErrorName.Suspended */));\n                        }\n                        else {\n                            setTimeout(tryRender, renderRetryDelay);\n                        }\n                        break;\n                }\n            }\n            catch (error) {\n                reject(error);\n            }\n        };\n        tryRender();\n        finalize = function () {\n            if (!isFinalized) {\n                isFinalized = true;\n                if (startedRunningAt > 0) {\n                    startRunningTimeout();\n                }\n            }\n        };\n    });\n    return [resultPromise, finalize];\n}\nfunction getHash(signal) {\n    var hash = 0;\n    for (var i = 0; i < signal.length; ++i) {\n        hash += Math.abs(signal[i]);\n    }\n    return hash;\n}\nfunction makeInnerError(name) {\n    var error = new Error(name);\n    error.name = name;\n    return error;\n}\n\n/**\n * Creates and keeps an invisible iframe while the given function runs.\n * The given function is called when the iframe is loaded and has a body.\n * The iframe allows to measure DOM sizes inside itself.\n *\n * Notice: passing an initial HTML code doesn't work in IE.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction withIframe(action, initialHtml, domPollInterval) {\n    var _a, _b, _c;\n    if (domPollInterval === void 0) { domPollInterval = 50; }\n    return __awaiter(this, void 0, void 0, function () {\n        var d, iframe;\n        return __generator(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    d = document;\n                    _d.label = 1;\n                case 1:\n                    if (!!d.body) return [3 /*break*/, 3];\n                    return [4 /*yield*/, wait(domPollInterval)];\n                case 2:\n                    _d.sent();\n                    return [3 /*break*/, 1];\n                case 3:\n                    iframe = d.createElement('iframe');\n                    _d.label = 4;\n                case 4:\n                    _d.trys.push([4, , 10, 11]);\n                    return [4 /*yield*/, new Promise(function (_resolve, _reject) {\n                            var isComplete = false;\n                            var resolve = function () {\n                                isComplete = true;\n                                _resolve();\n                            };\n                            var reject = function (error) {\n                                isComplete = true;\n                                _reject(error);\n                            };\n                            iframe.onload = resolve;\n                            iframe.onerror = reject;\n                            var style = iframe.style;\n                            style.setProperty('display', 'block', 'important'); // Required for browsers to calculate the layout\n                            style.position = 'absolute';\n                            style.top = '0';\n                            style.left = '0';\n                            style.visibility = 'hidden';\n                            if (initialHtml && 'srcdoc' in iframe) {\n                                iframe.srcdoc = initialHtml;\n                            }\n                            else {\n                                iframe.src = 'about:blank';\n                            }\n                            d.body.appendChild(iframe);\n                            // WebKit in WeChat doesn't fire the iframe's `onload` for some reason.\n                            // This code checks for the loading state manually.\n                            // See https://github.com/fingerprintjs/fingerprintjs/issues/645\n                            var checkReadyState = function () {\n                                var _a, _b;\n                                // The ready state may never become 'complete' in Firefox despite the 'load' event being fired.\n                                // So an infinite setTimeout loop can happen without this check.\n                                // See https://github.com/fingerprintjs/fingerprintjs/pull/716#issuecomment-986898796\n                                if (isComplete) {\n                                    return;\n                                }\n                                // Make sure iframe.contentWindow and iframe.contentWindow.document are both loaded\n                                // The contentWindow.document can miss in JSDOM (https://github.com/jsdom/jsdom).\n                                if (((_b = (_a = iframe.contentWindow) === null || _a === void 0 ? void 0 : _a.document) === null || _b === void 0 ? void 0 : _b.readyState) === 'complete') {\n                                    resolve();\n                                }\n                                else {\n                                    setTimeout(checkReadyState, 10);\n                                }\n                            };\n                            checkReadyState();\n                        })];\n                case 5:\n                    _d.sent();\n                    _d.label = 6;\n                case 6:\n                    if (!!((_b = (_a = iframe.contentWindow) === null || _a === void 0 ? void 0 : _a.document) === null || _b === void 0 ? void 0 : _b.body)) return [3 /*break*/, 8];\n                    return [4 /*yield*/, wait(domPollInterval)];\n                case 7:\n                    _d.sent();\n                    return [3 /*break*/, 6];\n                case 8: return [4 /*yield*/, action(iframe, iframe.contentWindow)];\n                case 9: return [2 /*return*/, _d.sent()];\n                case 10:\n                    (_c = iframe.parentNode) === null || _c === void 0 ? void 0 : _c.removeChild(iframe);\n                    return [7 /*endfinally*/];\n                case 11: return [2 /*return*/];\n            }\n        });\n    });\n}\n/**\n * Creates a DOM element that matches the given selector.\n * Only single element selector are supported (without operators like space, +, >, etc).\n */\nfunction selectorToElement(selector) {\n    var _a = parseSimpleCssSelector(selector), tag = _a[0], attributes = _a[1];\n    var element = document.createElement(tag !== null && tag !== void 0 ? tag : 'div');\n    for (var _i = 0, _b = Object.keys(attributes); _i < _b.length; _i++) {\n        var name_1 = _b[_i];\n        var value = attributes[name_1].join(' ');\n        // Changing the `style` attribute can cause a CSP error, therefore we change the `style.cssText` property.\n        // https://github.com/fingerprintjs/fingerprintjs/issues/733\n        if (name_1 === 'style') {\n            addStyleString(element.style, value);\n        }\n        else {\n            element.setAttribute(name_1, value);\n        }\n    }\n    return element;\n}\n/**\n * Adds CSS styles from a string in such a way that doesn't trigger a CSP warning (unsafe-inline or unsafe-eval)\n */\nfunction addStyleString(style, source) {\n    // We don't use `style.cssText` because browsers must block it when no `unsafe-eval` CSP is presented: https://csplite.com/csp145/#w3c_note\n    // Even though the browsers ignore this standard, we don't use `cssText` just in case.\n    for (var _i = 0, _a = source.split(';'); _i < _a.length; _i++) {\n        var property = _a[_i];\n        var match = /^\\s*([\\w-]+)\\s*:\\s*(.+?)(\\s*!([\\w-]+))?\\s*$/.exec(property);\n        if (match) {\n            var name_2 = match[1], value = match[2], priority = match[4];\n            style.setProperty(name_2, value, priority || ''); // The last argument can't be undefined in IE11\n        }\n    }\n}\n/**\n * Returns true if the code runs in an iframe, and any parent page's origin doesn't match the current origin\n */\nfunction isAnyParentCrossOrigin() {\n    var currentWindow = window;\n    for (;;) {\n        var parentWindow = currentWindow.parent;\n        if (!parentWindow || parentWindow === currentWindow) {\n            return false; // The top page is reached\n        }\n        try {\n            if (parentWindow.location.origin !== currentWindow.location.origin) {\n                return true;\n            }\n        }\n        catch (error) {\n            // The error is thrown when `origin` is accessed on `parentWindow.location` when the parent is cross-origin\n            if (error instanceof Error && error.name === 'SecurityError') {\n                return true;\n            }\n            throw error;\n        }\n        currentWindow = parentWindow;\n    }\n}\n\n// We use m or w because these two characters take up the maximum width.\n// And we use a LLi so that the same matching fonts can get separated.\nvar testString = 'mmMwWLliI0O&1';\n// We test using 48px font size, we may use any size. I guess larger the better.\nvar textSize = '48px';\n// A font will be compared against all the three default fonts.\n// And if for any default fonts it doesn't match, then that font is available.\nvar baseFonts = ['monospace', 'sans-serif', 'serif'];\nvar fontList = [\n    // This is android-specific font from \"Roboto\" family\n    'sans-serif-thin',\n    'ARNO PRO',\n    'Agency FB',\n    'Arabic Typesetting',\n    'Arial Unicode MS',\n    'AvantGarde Bk BT',\n    'BankGothic Md BT',\n    'Batang',\n    'Bitstream Vera Sans Mono',\n    'Calibri',\n    'Century',\n    'Century Gothic',\n    'Clarendon',\n    'EUROSTILE',\n    'Franklin Gothic',\n    'Futura Bk BT',\n    'Futura Md BT',\n    'GOTHAM',\n    'Gill Sans',\n    'HELV',\n    'Haettenschweiler',\n    'Helvetica Neue',\n    'Humanst521 BT',\n    'Leelawadee',\n    'Letter Gothic',\n    'Levenim MT',\n    'Lucida Bright',\n    'Lucida Sans',\n    'Menlo',\n    'MS Mincho',\n    'MS Outlook',\n    'MS Reference Specialty',\n    'MS UI Gothic',\n    'MT Extra',\n    'MYRIAD PRO',\n    'Marlett',\n    'Meiryo UI',\n    'Microsoft Uighur',\n    'Minion Pro',\n    'Monotype Corsiva',\n    'PMingLiU',\n    'Pristina',\n    'SCRIPTINA',\n    'Segoe UI Light',\n    'Serifa',\n    'SimHei',\n    'Small Fonts',\n    'Staccato222 BT',\n    'TRAJAN PRO',\n    'Univers CE 55 Medium',\n    'Vrinda',\n    'ZWAdobeF',\n];\n// kudos to http://www.lalit.org/lab/javascript-css-font-detect/\nfunction getFonts() {\n    var _this = this;\n    // Running the script in an iframe makes it not affect the page look and not be affected by the page CSS. See:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/592\n    // https://github.com/fingerprintjs/fingerprintjs/issues/628\n    return withIframe(function (_, _a) {\n        var document = _a.document;\n        return __awaiter(_this, void 0, void 0, function () {\n            var holder, spansContainer, defaultWidth, defaultHeight, createSpan, createSpanWithFonts, initializeBaseFontsSpans, initializeFontsSpans, isFontAvailable, baseFontsSpans, fontsSpans, index;\n            return __generator(this, function (_b) {\n                holder = document.body;\n                holder.style.fontSize = textSize;\n                spansContainer = document.createElement('div');\n                spansContainer.style.setProperty('visibility', 'hidden', 'important');\n                defaultWidth = {};\n                defaultHeight = {};\n                createSpan = function (fontFamily) {\n                    var span = document.createElement('span');\n                    var style = span.style;\n                    style.position = 'absolute';\n                    style.top = '0';\n                    style.left = '0';\n                    style.fontFamily = fontFamily;\n                    span.textContent = testString;\n                    spansContainer.appendChild(span);\n                    return span;\n                };\n                createSpanWithFonts = function (fontToDetect, baseFont) {\n                    return createSpan(\"'\".concat(fontToDetect, \"',\").concat(baseFont));\n                };\n                initializeBaseFontsSpans = function () {\n                    return baseFonts.map(createSpan);\n                };\n                initializeFontsSpans = function () {\n                    // Stores {fontName : [spans for that font]}\n                    var spans = {};\n                    var _loop_1 = function (font) {\n                        spans[font] = baseFonts.map(function (baseFont) { return createSpanWithFonts(font, baseFont); });\n                    };\n                    for (var _i = 0, fontList_1 = fontList; _i < fontList_1.length; _i++) {\n                        var font = fontList_1[_i];\n                        _loop_1(font);\n                    }\n                    return spans;\n                };\n                isFontAvailable = function (fontSpans) {\n                    return baseFonts.some(function (baseFont, baseFontIndex) {\n                        return fontSpans[baseFontIndex].offsetWidth !== defaultWidth[baseFont] ||\n                            fontSpans[baseFontIndex].offsetHeight !== defaultHeight[baseFont];\n                    });\n                };\n                baseFontsSpans = initializeBaseFontsSpans();\n                fontsSpans = initializeFontsSpans();\n                // add all the spans to the DOM\n                holder.appendChild(spansContainer);\n                // get the default width for the three base fonts\n                for (index = 0; index < baseFonts.length; index++) {\n                    defaultWidth[baseFonts[index]] = baseFontsSpans[index].offsetWidth; // width for the default font\n                    defaultHeight[baseFonts[index]] = baseFontsSpans[index].offsetHeight; // height for the default font\n                }\n                // check available fonts\n                return [2 /*return*/, fontList.filter(function (font) { return isFontAvailable(fontsSpans[font]); })];\n            });\n        });\n    });\n}\n\nfunction getPlugins() {\n    var rawPlugins = navigator.plugins;\n    if (!rawPlugins) {\n        return undefined;\n    }\n    var plugins = [];\n    // Safari 10 doesn't support iterating navigator.plugins with for...of\n    for (var i = 0; i < rawPlugins.length; ++i) {\n        var plugin = rawPlugins[i];\n        if (!plugin) {\n            continue;\n        }\n        var mimeTypes = [];\n        for (var j = 0; j < plugin.length; ++j) {\n            var mimeType = plugin[j];\n            mimeTypes.push({\n                type: mimeType.type,\n                suffixes: mimeType.suffixes,\n            });\n        }\n        plugins.push({\n            name: plugin.name,\n            description: plugin.description,\n            mimeTypes: mimeTypes,\n        });\n    }\n    return plugins;\n}\n\n/**\n * @see https://www.browserleaks.com/canvas#how-does-it-work\n *\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * Canvas image is noised in private mode of Safari 17, so image rendering is skipped in Safari 17.\n */\nfunction getCanvasFingerprint() {\n    return getUnstableCanvasFingerprint(doesBrowserPerformAntifingerprinting());\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableCanvasFingerprint(skipImages) {\n    var _a;\n    var winding = false;\n    var geometry;\n    var text;\n    var _b = makeCanvasContext(), canvas = _b[0], context = _b[1];\n    if (!isSupported(canvas, context)) {\n        geometry = text = \"unsupported\" /* ImageStatus.Unsupported */;\n    }\n    else {\n        winding = doesSupportWinding(context);\n        if (skipImages) {\n            geometry = text = \"skipped\" /* ImageStatus.Skipped */;\n        }\n        else {\n            _a = renderImages(canvas, context), geometry = _a[0], text = _a[1];\n        }\n    }\n    return { winding: winding, geometry: geometry, text: text };\n}\nfunction makeCanvasContext() {\n    var canvas = document.createElement('canvas');\n    canvas.width = 1;\n    canvas.height = 1;\n    return [canvas, canvas.getContext('2d')];\n}\nfunction isSupported(canvas, context) {\n    return !!(context && canvas.toDataURL);\n}\nfunction doesSupportWinding(context) {\n    // https://web.archive.org/web/20170825024655/http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // https://github.com/Modernizr/Modernizr/blob/master/feature-detects/canvas/winding.js\n    context.rect(0, 0, 10, 10);\n    context.rect(2, 2, 6, 6);\n    return !context.isPointInPath(5, 5, 'evenodd');\n}\nfunction renderImages(canvas, context) {\n    renderTextImage(canvas, context);\n    var textImage1 = canvasToString(canvas);\n    var textImage2 = canvasToString(canvas); // It's slightly faster to double-encode the text image\n    // Some browsers add a noise to the canvas: https://github.com/fingerprintjs/fingerprintjs/issues/791\n    // The canvas is excluded from the fingerprint in this case\n    if (textImage1 !== textImage2) {\n        return [\"unstable\" /* ImageStatus.Unstable */, \"unstable\" /* ImageStatus.Unstable */];\n    }\n    // Text is unstable:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/583\n    // https://github.com/fingerprintjs/fingerprintjs/issues/103\n    // Therefore it's extracted into a separate image.\n    renderGeometryImage(canvas, context);\n    var geometryImage = canvasToString(canvas);\n    return [geometryImage, textImage1];\n}\nfunction renderTextImage(canvas, context) {\n    // Resizing the canvas cleans it\n    canvas.width = 240;\n    canvas.height = 60;\n    context.textBaseline = 'alphabetic';\n    context.fillStyle = '#f60';\n    context.fillRect(100, 1, 62, 20);\n    context.fillStyle = '#069';\n    // It's important to use explicit built-in fonts in order to exclude the affect of font preferences\n    // (there is a separate entropy source for them).\n    context.font = '11pt \"Times New Roman\"';\n    // The choice of emojis has a gigantic impact on rendering performance (especially in FF).\n    // Some newer emojis cause it to slow down 50-200 times.\n    // There must be no text to the right of the emoji, see https://github.com/fingerprintjs/fingerprintjs/issues/574\n    // A bare emoji shouldn't be used because the canvas will change depending on the script encoding:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/66\n    // Escape sequence shouldn't be used too because Terser will turn it into a bare unicode.\n    var printedText = \"Cwm fjordbank gly \".concat(String.fromCharCode(55357, 56835) /* 😃 */);\n    context.fillText(printedText, 2, 15);\n    context.fillStyle = 'rgba(102, 204, 0, 0.2)';\n    context.font = '18pt Arial';\n    context.fillText(printedText, 4, 45);\n}\nfunction renderGeometryImage(canvas, context) {\n    // Resizing the canvas cleans it\n    canvas.width = 122;\n    canvas.height = 110;\n    // Canvas blending\n    // https://web.archive.org/web/**************/http://blogs.adobe.com/webplatform/2013/01/28/blending-features-in-canvas/\n    // http://jsfiddle.net/NDYV8/16/\n    context.globalCompositeOperation = 'multiply';\n    for (var _i = 0, _a = [\n        ['#f2f', 40, 40],\n        ['#2ff', 80, 40],\n        ['#ff2', 60, 80],\n    ]; _i < _a.length; _i++) {\n        var _b = _a[_i], color = _b[0], x = _b[1], y = _b[2];\n        context.fillStyle = color;\n        context.beginPath();\n        context.arc(x, y, 40, 0, Math.PI * 2, true);\n        context.closePath();\n        context.fill();\n    }\n    // Canvas winding\n    // https://web.archive.org/web/20130913061632/http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // http://jsfiddle.net/NDYV8/19/\n    context.fillStyle = '#f9c';\n    context.arc(60, 60, 60, 0, Math.PI * 2, true);\n    context.arc(60, 60, 20, 0, Math.PI * 2, true);\n    context.fill('evenodd');\n}\nfunction canvasToString(canvas) {\n    return canvas.toDataURL();\n}\n/**\n * Checks if the current browser is known for applying anti-fingerprinting measures in all or some critical modes\n */\nfunction doesBrowserPerformAntifingerprinting() {\n    // Safari 17\n    return isWebKit() && isWebKit616OrNewer() && isSafariWebKit();\n}\n\n/**\n * This is a crude and primitive touch screen detection. It's not possible to currently reliably detect the availability\n * of a touch screen with a JS, without actually subscribing to a touch event.\n *\n * @see http://www.stucox.com/blog/you-cant-detect-a-touchscreen/\n * @see https://github.com/Modernizr/Modernizr/issues/548\n */\nfunction getTouchSupport() {\n    var n = navigator;\n    var maxTouchPoints = 0;\n    var touchEvent;\n    if (n.maxTouchPoints !== undefined) {\n        maxTouchPoints = toInt(n.maxTouchPoints);\n    }\n    else if (n.msMaxTouchPoints !== undefined) {\n        maxTouchPoints = n.msMaxTouchPoints;\n    }\n    try {\n        document.createEvent('TouchEvent');\n        touchEvent = true;\n    }\n    catch (_a) {\n        touchEvent = false;\n    }\n    var touchStart = 'ontouchstart' in window;\n    return {\n        maxTouchPoints: maxTouchPoints,\n        touchEvent: touchEvent,\n        touchStart: touchStart,\n    };\n}\n\nfunction getOsCpu() {\n    return navigator.oscpu;\n}\n\nfunction getLanguages() {\n    var n = navigator;\n    var result = [];\n    var language = n.language || n.userLanguage || n.browserLanguage || n.systemLanguage;\n    if (language !== undefined) {\n        result.push([language]);\n    }\n    if (Array.isArray(n.languages)) {\n        // Starting from Chromium 86, there is only a single value in `navigator.language` in Incognito mode:\n        // the value of `navigator.language`. Therefore the value is ignored in this browser.\n        if (!(isChromium() && isChromium86OrNewer())) {\n            result.push(n.languages);\n        }\n    }\n    else if (typeof n.languages === 'string') {\n        var languages = n.languages;\n        if (languages) {\n            result.push(languages.split(','));\n        }\n    }\n    return result;\n}\n\nfunction getColorDepth() {\n    return window.screen.colorDepth;\n}\n\nfunction getDeviceMemory() {\n    // `navigator.deviceMemory` is a string containing a number in some unidentified cases\n    return replaceNaN(toFloat(navigator.deviceMemory), undefined);\n}\n\n/**\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * The window resolution is always the document size in private mode of Safari 17,\n * so the window resolution is not used in Safari 17.\n */\nfunction getScreenResolution() {\n    if (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) {\n        return undefined;\n    }\n    return getUnstableScreenResolution();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableScreenResolution() {\n    var s = screen;\n    // Some browsers return screen resolution as strings, e.g. \"1200\", instead of a number, e.g. 1200.\n    // I suspect it's done by certain plugins that randomize browser properties to prevent fingerprinting.\n    // Some browsers even return  screen resolution as not numbers.\n    var parseDimension = function (value) { return replaceNaN(toInt(value), null); };\n    var dimensions = [parseDimension(s.width), parseDimension(s.height)];\n    dimensions.sort().reverse();\n    return dimensions;\n}\n\nvar screenFrameCheckInterval = 2500;\nvar roundingPrecision = 10;\n// The type is readonly to protect from unwanted mutations\nvar screenFrameBackup;\nvar screenFrameSizeTimeoutId;\n/**\n * Starts watching the screen frame size. When a non-zero size appears, the size is saved and the watch is stopped.\n * Later, when `getScreenFrame` runs, it will return the saved non-zero size if the current size is null.\n *\n * This trick is required to mitigate the fact that the screen frame turns null in some cases.\n * See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n */\nfunction watchScreenFrame() {\n    if (screenFrameSizeTimeoutId !== undefined) {\n        return;\n    }\n    var checkScreenFrame = function () {\n        var frameSize = getCurrentScreenFrame();\n        if (isFrameSizeNull(frameSize)) {\n            screenFrameSizeTimeoutId = setTimeout(checkScreenFrame, screenFrameCheckInterval);\n        }\n        else {\n            screenFrameBackup = frameSize;\n            screenFrameSizeTimeoutId = undefined;\n        }\n    };\n    checkScreenFrame();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableScreenFrame() {\n    var _this = this;\n    watchScreenFrame();\n    return function () { return __awaiter(_this, void 0, void 0, function () {\n        var frameSize;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    frameSize = getCurrentScreenFrame();\n                    if (!isFrameSizeNull(frameSize)) return [3 /*break*/, 2];\n                    if (screenFrameBackup) {\n                        return [2 /*return*/, __spreadArray([], screenFrameBackup, true)];\n                    }\n                    if (!getFullscreenElement()) return [3 /*break*/, 2];\n                    // Some browsers set the screen frame to zero when programmatic fullscreen is on.\n                    // There is a chance of getting a non-zero frame after exiting the fullscreen.\n                    // See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n                    return [4 /*yield*/, exitFullscreen()];\n                case 1:\n                    // Some browsers set the screen frame to zero when programmatic fullscreen is on.\n                    // There is a chance of getting a non-zero frame after exiting the fullscreen.\n                    // See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n                    _a.sent();\n                    frameSize = getCurrentScreenFrame();\n                    _a.label = 2;\n                case 2:\n                    if (!isFrameSizeNull(frameSize)) {\n                        screenFrameBackup = frameSize;\n                    }\n                    return [2 /*return*/, frameSize];\n            }\n        });\n    }); };\n}\n/**\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n *\n * Sometimes the available screen resolution changes a bit, e.g. 1900x1440 → 1900x1439. A possible reason: macOS Dock\n * shrinks to fit more icons when there is too little space. The rounding is used to mitigate the difference.\n *\n * The frame width is always 0 in private mode of Safari 17, so the frame is not used in Safari 17.\n */\nfunction getScreenFrame() {\n    var _this = this;\n    if (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) {\n        return function () { return Promise.resolve(undefined); };\n    }\n    var screenFrameGetter = getUnstableScreenFrame();\n    return function () { return __awaiter(_this, void 0, void 0, function () {\n        var frameSize, processSize;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0: return [4 /*yield*/, screenFrameGetter()];\n                case 1:\n                    frameSize = _a.sent();\n                    processSize = function (sideSize) { return (sideSize === null ? null : round(sideSize, roundingPrecision)); };\n                    // It might look like I don't know about `for` and `map`.\n                    // In fact, such code is used to avoid TypeScript issues without using `as`.\n                    return [2 /*return*/, [processSize(frameSize[0]), processSize(frameSize[1]), processSize(frameSize[2]), processSize(frameSize[3])]];\n            }\n        });\n    }); };\n}\nfunction getCurrentScreenFrame() {\n    var s = screen;\n    // Some browsers return screen resolution as strings, e.g. \"1200\", instead of a number, e.g. 1200.\n    // I suspect it's done by certain plugins that randomize browser properties to prevent fingerprinting.\n    //\n    // Some browsers (IE, Edge ≤18) don't provide `screen.availLeft` and `screen.availTop`. The property values are\n    // replaced with 0 in such cases to not lose the entropy from `screen.availWidth` and `screen.availHeight`.\n    return [\n        replaceNaN(toFloat(s.availTop), null),\n        replaceNaN(toFloat(s.width) - toFloat(s.availWidth) - replaceNaN(toFloat(s.availLeft), 0), null),\n        replaceNaN(toFloat(s.height) - toFloat(s.availHeight) - replaceNaN(toFloat(s.availTop), 0), null),\n        replaceNaN(toFloat(s.availLeft), null),\n    ];\n}\nfunction isFrameSizeNull(frameSize) {\n    for (var i = 0; i < 4; ++i) {\n        if (frameSize[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction getHardwareConcurrency() {\n    // sometimes hardware concurrency is a string\n    return replaceNaN(toInt(navigator.hardwareConcurrency), undefined);\n}\n\nfunction getTimezone() {\n    var _a;\n    var DateTimeFormat = (_a = window.Intl) === null || _a === void 0 ? void 0 : _a.DateTimeFormat;\n    if (DateTimeFormat) {\n        var timezone = new DateTimeFormat().resolvedOptions().timeZone;\n        if (timezone) {\n            return timezone;\n        }\n    }\n    // For browsers that don't support timezone names\n    // The minus is intentional because the JS offset is opposite to the real offset\n    var offset = -getTimezoneOffset();\n    return \"UTC\".concat(offset >= 0 ? '+' : '').concat(offset);\n}\nfunction getTimezoneOffset() {\n    var currentYear = new Date().getFullYear();\n    // The timezone offset may change over time due to daylight saving time (DST) shifts.\n    // The non-DST timezone offset is used as the result timezone offset.\n    // Since the DST season differs in the northern and the southern hemispheres,\n    // both January and July timezones offsets are considered.\n    return Math.max(\n    // `getTimezoneOffset` returns a number as a string in some unidentified cases\n    toFloat(new Date(currentYear, 0, 1).getTimezoneOffset()), toFloat(new Date(currentYear, 6, 1).getTimezoneOffset()));\n}\n\nfunction getSessionStorage() {\n    try {\n        return !!window.sessionStorage;\n    }\n    catch (error) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\n// https://bugzilla.mozilla.org/show_bug.cgi?id=781447\nfunction getLocalStorage() {\n    try {\n        return !!window.localStorage;\n    }\n    catch (e) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\nfunction getIndexedDB() {\n    // IE and Edge don't allow accessing indexedDB in private mode, therefore IE and Edge will have different\n    // visitor identifier in normal and private modes.\n    if (isTrident() || isEdgeHTML()) {\n        return undefined;\n    }\n    try {\n        return !!window.indexedDB;\n    }\n    catch (e) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\nfunction getOpenDatabase() {\n    return !!window.openDatabase;\n}\n\nfunction getCpuClass() {\n    return navigator.cpuClass;\n}\n\nfunction getPlatform() {\n    // Android Chrome 86 and 87 and Android Firefox 80 and 84 don't mock the platform value when desktop mode is requested\n    var platform = navigator.platform;\n    // iOS mocks the platform value when desktop version is requested: https://github.com/fingerprintjs/fingerprintjs/issues/514\n    // iPad uses desktop mode by default since iOS 13\n    // The value is 'MacIntel' on M1 Macs\n    // The value is 'iPhone' on iPod Touch\n    if (platform === 'MacIntel') {\n        if (isWebKit() && !isDesktopWebKit()) {\n            return isIPad() ? 'iPad' : 'iPhone';\n        }\n    }\n    return platform;\n}\n\nfunction getVendor() {\n    return navigator.vendor || '';\n}\n\n/**\n * Checks for browser-specific (not engine specific) global variables to tell browsers with the same engine apart.\n * Only somewhat popular browsers are considered.\n */\nfunction getVendorFlavors() {\n    var flavors = [];\n    for (var _i = 0, _a = [\n        // Blink and some browsers on iOS\n        'chrome',\n        // Safari on macOS\n        'safari',\n        // Chrome on iOS (checked in 85 on 13 and 87 on 14)\n        '__crWeb',\n        '__gCrWeb',\n        // Yandex Browser on iOS, macOS and Android (checked in 21.2 on iOS 14, macOS and Android)\n        'yandex',\n        // Yandex Browser on iOS (checked in 21.2 on 14)\n        '__yb',\n        '__ybro',\n        // Firefox on iOS (checked in 32 on 14)\n        '__firefox__',\n        // Edge on iOS (checked in 46 on 14)\n        '__edgeTrackingPreventionStatistics',\n        'webkit',\n        // Opera Touch on iOS (checked in 2.6 on 14)\n        'oprt',\n        // Samsung Internet on Android (checked in 11.1)\n        'samsungAr',\n        // UC Browser on Android (checked in 12.10 and 13.0)\n        'ucweb',\n        'UCShellJava',\n        // Puffin on Android (checked in 9.0)\n        'puffinDevice',\n        // UC on iOS and Opera on Android have no specific global variables\n        // Edge for Android isn't checked\n    ]; _i < _a.length; _i++) {\n        var key = _a[_i];\n        var value = window[key];\n        if (value && typeof value === 'object') {\n            flavors.push(key);\n        }\n    }\n    return flavors.sort();\n}\n\n/**\n * navigator.cookieEnabled cannot detect custom or nuanced cookie blocking configurations. For example, when blocking\n * cookies via the Advanced Privacy Settings in IE9, it always returns true. And there have been issues in the past with\n * site-specific exceptions. Don't rely on it.\n *\n * @see https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cookies.js Taken from here\n */\nfunction areCookiesEnabled() {\n    var d = document;\n    // Taken from here: https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cookies.js\n    // navigator.cookieEnabled cannot detect custom or nuanced cookie blocking configurations. For example, when blocking\n    // cookies via the Advanced Privacy Settings in IE9, it always returns true. And there have been issues in the past\n    // with site-specific exceptions. Don't rely on it.\n    // try..catch because some in situations `document.cookie` is exposed but throws a\n    // SecurityError if you try to access it; e.g. documents created from data URIs\n    // or in sandboxed iframes (depending on flags/context)\n    try {\n        // Create cookie\n        d.cookie = 'cookietest=1; SameSite=Strict;';\n        var result = d.cookie.indexOf('cookietest=') !== -1;\n        // Delete cookie\n        d.cookie = 'cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT';\n        return result;\n    }\n    catch (e) {\n        return false;\n    }\n}\n\n/**\n * Only single element selector are supported (no operators like space, +, >, etc).\n * `embed` and `position: fixed;` will be considered as blocked anyway because it always has no offsetParent.\n * Avoid `iframe` and anything with `[src=]` because they produce excess HTTP requests.\n *\n * The \"inappropriate\" selectors are obfuscated. See https://github.com/fingerprintjs/fingerprintjs/issues/734.\n * A function is used instead of a plain object to help tree-shaking.\n *\n * The function code is generated automatically. See docs/content_blockers.md to learn how to make the list.\n */\nfunction getFilters() {\n    var fromB64 = atob; // Just for better minification\n    return {\n        abpIndo: [\n            '#Iklan-Melayang',\n            '#Kolom-Iklan-728',\n            '#SidebarIklan-wrapper',\n            '[title=\"ALIENBOLA\" i]',\n            fromB64('I0JveC1CYW5uZXItYWRz'),\n        ],\n        abpvn: ['.quangcao', '#mobileCatfish', fromB64('LmNsb3NlLWFkcw=='), '[id^=\"bn_bottom_fixed_\"]', '#pmadv'],\n        adBlockFinland: [\n            '.mainostila',\n            fromB64('LnNwb25zb3JpdA=='),\n            '.ylamainos',\n            fromB64('YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd'),\n        ],\n        adBlockPersian: [\n            '#navbar_notice_50',\n            '.kadr',\n            'TABLE[width=\"140px\"]',\n            '#divAgahi',\n            fromB64('YVtocmVmXj0iaHR0cDovL2cxLnYuZndtcm0ubmV0L2FkLyJd'),\n        ],\n        adBlockWarningRemoval: [\n            '#adblock-honeypot',\n            '.adblocker-root',\n            '.wp_adblock_detect',\n            fromB64('LmhlYWRlci1ibG9ja2VkLWFk'),\n            fromB64('I2FkX2Jsb2NrZXI='),\n        ],\n        adGuardAnnoyances: [\n            '.hs-sosyal',\n            '#cookieconsentdiv',\n            'div[class^=\"app_gdpr\"]',\n            '.as-oil',\n            '[data-cypress=\"soft-push-notification-modal\"]',\n        ],\n        adGuardBase: [\n            '.BetterJsPopOverlay',\n            fromB64('I2FkXzMwMFgyNTA='),\n            fromB64('I2Jhbm5lcmZsb2F0MjI='),\n            fromB64('I2NhbXBhaWduLWJhbm5lcg=='),\n            fromB64('I0FkLUNvbnRlbnQ='),\n        ],\n        adGuardChinese: [\n            fromB64('LlppX2FkX2FfSA=='),\n            fromB64('YVtocmVmKj0iLmh0aGJldDM0LmNvbSJd'),\n            '#widget-quan',\n            fromB64('YVtocmVmKj0iLzg0OTkyMDIwLnh5eiJd'),\n            fromB64('YVtocmVmKj0iLjE5NTZobC5jb20vIl0='),\n        ],\n        adGuardFrench: [\n            '#pavePub',\n            fromB64('LmFkLWRlc2t0b3AtcmVjdGFuZ2xl'),\n            '.mobile_adhesion',\n            '.widgetadv',\n            fromB64('LmFkc19iYW4='),\n        ],\n        adGuardGerman: ['aside[data-portal-id=\"leaderboard\"]'],\n        adGuardJapanese: [\n            '#kauli_yad_1',\n            fromB64('YVtocmVmXj0iaHR0cDovL2FkMi50cmFmZmljZ2F0ZS5uZXQvIl0='),\n            fromB64('Ll9wb3BJbl9pbmZpbml0ZV9hZA=='),\n            fromB64('LmFkZ29vZ2xl'),\n            fromB64('Ll9faXNib29zdFJldHVybkFk'),\n        ],\n        adGuardMobile: [\n            fromB64('YW1wLWF1dG8tYWRz'),\n            fromB64('LmFtcF9hZA=='),\n            'amp-embed[type=\"24smi\"]',\n            '#mgid_iframe1',\n            fromB64('I2FkX2ludmlld19hcmVh'),\n        ],\n        adGuardRussian: [\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZC5sZXRtZWFkcy5jb20vIl0='),\n            fromB64('LnJlY2xhbWE='),\n            'div[id^=\"smi2adblock\"]',\n            fromB64('ZGl2W2lkXj0iQWRGb3hfYmFubmVyXyJd'),\n            '#psyduckpockeball',\n        ],\n        adGuardSocial: [\n            fromB64('YVtocmVmXj0iLy93d3cuc3R1bWJsZXVwb24uY29tL3N1Ym1pdD91cmw9Il0='),\n            fromB64('YVtocmVmXj0iLy90ZWxlZ3JhbS5tZS9zaGFyZS91cmw/Il0='),\n            '.etsy-tweet',\n            '#inlineShare',\n            '.popup-social',\n        ],\n        adGuardSpanishPortuguese: ['#barraPublicidade', '#Publicidade', '#publiEspecial', '#queTooltip', '.cnt-publi'],\n        adGuardTrackingProtection: [\n            '#qoo-counter',\n            fromB64('YVtocmVmXj0iaHR0cDovL2NsaWNrLmhvdGxvZy5ydS8iXQ=='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2hpdGNvdW50ZXIucnUvdG9wL3N0YXQucGhwIl0='),\n            fromB64('YVtocmVmXj0iaHR0cDovL3RvcC5tYWlsLnJ1L2p1bXAiXQ=='),\n            '#top100counter',\n        ],\n        adGuardTurkish: [\n            '#backkapat',\n            fromB64('I3Jla2xhbWk='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2Fkc2Vydi5vbnRlay5jb20udHIvIl0='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2l6bGVuemkuY29tL2NhbXBhaWduLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cDovL3d3dy5pbnN0YWxsYWRzLm5ldC8iXQ=='),\n        ],\n        bulgarian: [fromB64('dGQjZnJlZW5ldF90YWJsZV9hZHM='), '#ea_intext_div', '.lapni-pop-over', '#xenium_hot_offers'],\n        easyList: [\n            '.yb-floorad',\n            fromB64('LndpZGdldF9wb19hZHNfd2lkZ2V0'),\n            fromB64('LnRyYWZmaWNqdW5reS1hZA=='),\n            '.textad_headline',\n            fromB64('LnNwb25zb3JlZC10ZXh0LWxpbmtz'),\n        ],\n        easyListChina: [\n            fromB64('LmFwcGd1aWRlLXdyYXBbb25jbGljayo9ImJjZWJvcy5jb20iXQ=='),\n            fromB64('LmZyb250cGFnZUFkdk0='),\n            '#taotaole',\n            '#aafoot.top_box',\n            '.cfa_popup',\n        ],\n        easyListCookie: [\n            '.ezmob-footer',\n            '.cc-CookieWarning',\n            '[data-cookie-number]',\n            fromB64('LmF3LWNvb2tpZS1iYW5uZXI='),\n            '.sygnal24-gdpr-modal-wrap',\n        ],\n        easyListCzechSlovak: [\n            '#onlajny-stickers',\n            fromB64('I3Jla2xhbW5pLWJveA=='),\n            fromB64('LnJla2xhbWEtbWVnYWJvYXJk'),\n            '.sklik',\n            fromB64('W2lkXj0ic2tsaWtSZWtsYW1hIl0='),\n        ],\n        easyListDutch: [\n            fromB64('I2FkdmVydGVudGll'),\n            fromB64('I3ZpcEFkbWFya3RCYW5uZXJCbG9jaw=='),\n            '.adstekst',\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly94bHR1YmUubmwvY2xpY2svIl0='),\n            '#semilo-lrectangle',\n        ],\n        easyListGermany: [\n            '#SSpotIMPopSlider',\n            fromB64('LnNwb25zb3JsaW5rZ3J1ZW4='),\n            fromB64('I3dlcmJ1bmdza3k='),\n            fromB64('I3Jla2xhbWUtcmVjaHRzLW1pdHRl'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9iZDc0Mi5jb20vIl0='),\n        ],\n        easyListItaly: [\n            fromB64('LmJveF9hZHZfYW5udW5jaQ=='),\n            '.sb-box-pubbliredazionale',\n            fromB64('YVtocmVmXj0iaHR0cDovL2FmZmlsaWF6aW9uaWFkcy5zbmFpLml0LyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZHNlcnZlci5odG1sLml0LyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZmZpbGlhemlvbmlhZHMuc25haS5pdC8iXQ=='),\n        ],\n        easyListLithuania: [\n            fromB64('LnJla2xhbW9zX3RhcnBhcw=='),\n            fromB64('LnJla2xhbW9zX251b3JvZG9z'),\n            fromB64('aW1nW2FsdD0iUmVrbGFtaW5pcyBza3lkZWxpcyJd'),\n            fromB64('aW1nW2FsdD0iRGVkaWt1b3RpLmx0IHNlcnZlcmlhaSJd'),\n            fromB64('aW1nW2FsdD0iSG9zdGluZ2FzIFNlcnZlcmlhaS5sdCJd'),\n        ],\n        estonian: [fromB64('QVtocmVmKj0iaHR0cDovL3BheTRyZXN1bHRzMjQuZXUiXQ==')],\n        fanboyAnnoyances: ['#ac-lre-player', '.navigate-to-top', '#subscribe_popup', '.newsletter_holder', '#back-top'],\n        fanboyAntiFacebook: ['.util-bar-module-firefly-visible'],\n        fanboyEnhancedTrackers: [\n            '.open.pushModal',\n            '#issuem-leaky-paywall-articles-zero-remaining-nag',\n            '#sovrn_container',\n            'div[class$=\"-hide\"][zoompage-fontsize][style=\"display: block;\"]',\n            '.BlockNag__Card',\n        ],\n        fanboySocial: ['#FollowUs', '#meteored_share', '#social_follow', '.article-sharer', '.community__social-desc'],\n        frellwitSwedish: [\n            fromB64('YVtocmVmKj0iY2FzaW5vcHJvLnNlIl1bdGFyZ2V0PSJfYmxhbmsiXQ=='),\n            fromB64('YVtocmVmKj0iZG9rdG9yLXNlLm9uZWxpbmsubWUiXQ=='),\n            'article.category-samarbete',\n            fromB64('ZGl2LmhvbGlkQWRz'),\n            'ul.adsmodern',\n        ],\n        greekAdBlock: [\n            fromB64('QVtocmVmKj0iYWRtYW4ub3RlbmV0LmdyL2NsaWNrPyJd'),\n            fromB64('QVtocmVmKj0iaHR0cDovL2F4aWFiYW5uZXJzLmV4b2R1cy5nci8iXQ=='),\n            fromB64('QVtocmVmKj0iaHR0cDovL2ludGVyYWN0aXZlLmZvcnRobmV0LmdyL2NsaWNrPyJd'),\n            'DIV.agores300',\n            'TABLE.advright',\n        ],\n        hungarian: [\n            '#cemp_doboz',\n            '.optimonk-iframe-container',\n            fromB64('LmFkX19tYWlu'),\n            fromB64('W2NsYXNzKj0iR29vZ2xlQWRzIl0='),\n            '#hirdetesek_box',\n        ],\n        iDontCareAboutCookies: [\n            '.alert-info[data-block-track*=\"CookieNotice\"]',\n            '.ModuleTemplateCookieIndicator',\n            '.o--cookies--container',\n            '#cookies-policy-sticky',\n            '#stickyCookieBar',\n        ],\n        icelandicAbp: [fromB64('QVtocmVmXj0iL2ZyYW1ld29yay9yZXNvdXJjZXMvZm9ybXMvYWRzLmFzcHgiXQ==')],\n        latvian: [\n            fromB64('YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiAxMjBweDsgaGVpZ2h0O' +\n                'iA0MHB4OyBvdmVyZmxvdzogaGlkZGVuOyBwb3NpdGlvbjogcmVsYXRpdmU7Il0='),\n            fromB64('YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6I' +\n                'DMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ=='),\n        ],\n        listKr: [\n            fromB64('YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0='),\n            fromB64('I2xpdmVyZUFkV3JhcHBlcg=='),\n            fromB64('YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=='),\n            fromB64('aW5zLmZhc3R2aWV3LWFk'),\n            '.revenue_unit_item.dable',\n        ],\n        listeAr: [\n            fromB64('LmdlbWluaUxCMUFk'),\n            '.right-and-left-sponsers',\n            fromB64('YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=='),\n            fromB64('YVtocmVmKj0iYm9vcmFxLm9yZyJd'),\n            fromB64('YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd'),\n        ],\n        listeFr: [\n            fromB64('YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=='),\n            fromB64('I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=='),\n            fromB64('YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0='),\n            '.site-pub-interstitiel',\n            'div[id^=\"crt-\"][data-criteo-id]',\n        ],\n        officialPolish: [\n            '#ceneo-placeholder-ceneo-12',\n            fromB64('W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=='),\n            fromB64('YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=='),\n            fromB64('ZGl2I3NrYXBpZWNfYWQ='),\n        ],\n        ro: [\n            fromB64('YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0='),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd'),\n            'a[href^=\"/url/\"]',\n        ],\n        ruAd: [\n            fromB64('YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd'),\n            fromB64('YVtocmVmKj0iLy91dGltZy5ydS8iXQ=='),\n            fromB64('YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0='),\n            '#pgeldiz',\n            '.yandex-rtb-block',\n        ],\n        thaiAds: [\n            'a[href*=macau-uta-popup]',\n            fromB64('I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=='),\n            fromB64('LmFkczMwMHM='),\n            '.bumq',\n            '.img-kosana',\n        ],\n        webAnnoyancesUltralist: [\n            '#mod-social-share-2',\n            '#social-tools',\n            fromB64('LmN0cGwtZnVsbGJhbm5lcg=='),\n            '.zergnet-recommend',\n            '.yt.btn-link.btn-md.btn',\n        ],\n    };\n}\n/**\n * The order of the returned array means nothing (it's always sorted alphabetically).\n *\n * Notice that the source is slightly unstable.\n * Safari provides a 2-taps way to disable all content blockers on a page temporarily.\n * Also content blockers can be disabled permanently for a domain, but it requires 4 taps.\n * So empty array shouldn't be treated as \"no blockers\", it should be treated as \"no signal\".\n * If you are a website owner, don't make your visitors want to disable content blockers.\n */\nfunction getDomBlockers(_a) {\n    var _b = _a === void 0 ? {} : _a, debug = _b.debug;\n    return __awaiter(this, void 0, void 0, function () {\n        var filters, filterNames, allSelectors, blockedSelectors, activeBlockers;\n        var _c;\n        return __generator(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    if (!isApplicable()) {\n                        return [2 /*return*/, undefined];\n                    }\n                    filters = getFilters();\n                    filterNames = Object.keys(filters);\n                    allSelectors = (_c = []).concat.apply(_c, filterNames.map(function (filterName) { return filters[filterName]; }));\n                    return [4 /*yield*/, getBlockedSelectors(allSelectors)];\n                case 1:\n                    blockedSelectors = _d.sent();\n                    if (debug) {\n                        printDebug(filters, blockedSelectors);\n                    }\n                    activeBlockers = filterNames.filter(function (filterName) {\n                        var selectors = filters[filterName];\n                        var blockedCount = countTruthy(selectors.map(function (selector) { return blockedSelectors[selector]; }));\n                        return blockedCount > selectors.length * 0.6;\n                    });\n                    activeBlockers.sort();\n                    return [2 /*return*/, activeBlockers];\n            }\n        });\n    });\n}\nfunction isApplicable() {\n    // Safari (desktop and mobile) and all Android browsers keep content blockers in both regular and private mode\n    return isWebKit() || isAndroid();\n}\nfunction getBlockedSelectors(selectors) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function () {\n        var d, root, elements, blockedSelectors, i, element, holder, i;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    d = document;\n                    root = d.createElement('div');\n                    elements = new Array(selectors.length);\n                    blockedSelectors = {} // Set() isn't used just in case somebody need older browser support\n                    ;\n                    forceShow(root);\n                    // First create all elements that can be blocked. If the DOM steps below are done in a single cycle,\n                    // browser will alternate tree modification and layout reading, that is very slow.\n                    for (i = 0; i < selectors.length; ++i) {\n                        element = selectorToElement(selectors[i]);\n                        if (element.tagName === 'DIALOG') {\n                            element.show();\n                        }\n                        holder = d.createElement('div') // Protects from unwanted effects of `+` and `~` selectors of filters\n                        ;\n                        forceShow(holder);\n                        holder.appendChild(element);\n                        root.appendChild(holder);\n                        elements[i] = element;\n                    }\n                    _b.label = 1;\n                case 1:\n                    if (!!d.body) return [3 /*break*/, 3];\n                    return [4 /*yield*/, wait(50)];\n                case 2:\n                    _b.sent();\n                    return [3 /*break*/, 1];\n                case 3:\n                    d.body.appendChild(root);\n                    try {\n                        // Then check which of the elements are blocked\n                        for (i = 0; i < selectors.length; ++i) {\n                            if (!elements[i].offsetParent) {\n                                blockedSelectors[selectors[i]] = true;\n                            }\n                        }\n                    }\n                    finally {\n                        // Then remove the elements\n                        (_a = root.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(root);\n                    }\n                    return [2 /*return*/, blockedSelectors];\n            }\n        });\n    });\n}\nfunction forceShow(element) {\n    element.style.setProperty('visibility', 'hidden', 'important');\n    element.style.setProperty('display', 'block', 'important');\n}\nfunction printDebug(filters, blockedSelectors) {\n    var message = 'DOM blockers debug:\\n```';\n    for (var _i = 0, _a = Object.keys(filters); _i < _a.length; _i++) {\n        var filterName = _a[_i];\n        message += \"\\n\".concat(filterName, \":\");\n        for (var _b = 0, _c = filters[filterName]; _b < _c.length; _b++) {\n            var selector = _c[_b];\n            message += \"\\n  \".concat(blockedSelectors[selector] ? '🚫' : '➡️', \" \").concat(selector);\n        }\n    }\n    // console.log is ok here because it's under a debug clause\n    // eslint-disable-next-line no-console\n    console.log(\"\".concat(message, \"\\n```\"));\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/color-gamut\n */\nfunction getColorGamut() {\n    // rec2020 includes p3 and p3 includes srgb\n    for (var _i = 0, _a = ['rec2020', 'p3', 'srgb']; _i < _a.length; _i++) {\n        var gamut = _a[_i];\n        if (matchMedia(\"(color-gamut: \".concat(gamut, \")\")).matches) {\n            return gamut;\n        }\n    }\n    return undefined;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/inverted-colors\n */\nfunction areColorsInverted() {\n    if (doesMatch$5('inverted')) {\n        return true;\n    }\n    if (doesMatch$5('none')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$5(value) {\n    return matchMedia(\"(inverted-colors: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/forced-colors\n */\nfunction areColorsForced() {\n    if (doesMatch$4('active')) {\n        return true;\n    }\n    if (doesMatch$4('none')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$4(value) {\n    return matchMedia(\"(forced-colors: \".concat(value, \")\")).matches;\n}\n\nvar maxValueToCheck = 100;\n/**\n * If the display is monochrome (e.g. black&white), the value will be ≥0 and will mean the number of bits per pixel.\n * If the display is not monochrome, the returned value will be 0.\n * If the browser doesn't support this feature, the returned value will be undefined.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/monochrome\n */\nfunction getMonochromeDepth() {\n    if (!matchMedia('(min-monochrome: 0)').matches) {\n        // The media feature isn't supported by the browser\n        return undefined;\n    }\n    // A variation of binary search algorithm can be used here.\n    // But since expected values are very small (≤10), there is no sense in adding the complexity.\n    for (var i = 0; i <= maxValueToCheck; ++i) {\n        if (matchMedia(\"(max-monochrome: \".concat(i, \")\")).matches) {\n            return i;\n        }\n    }\n    throw new Error('Too high value');\n}\n\n/**\n * @see https://www.w3.org/TR/mediaqueries-5/#prefers-contrast\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-contrast\n */\nfunction getContrastPreference() {\n    if (doesMatch$3('no-preference')) {\n        return 0 /* ContrastPreference.None */;\n    }\n    // The sources contradict on the keywords. Probably 'high' and 'low' will never be implemented.\n    // Need to check it when all browsers implement the feature.\n    if (doesMatch$3('high') || doesMatch$3('more')) {\n        return 1 /* ContrastPreference.More */;\n    }\n    if (doesMatch$3('low') || doesMatch$3('less')) {\n        return -1 /* ContrastPreference.Less */;\n    }\n    if (doesMatch$3('forced')) {\n        return 10 /* ContrastPreference.ForcedColors */;\n    }\n    return undefined;\n}\nfunction doesMatch$3(value) {\n    return matchMedia(\"(prefers-contrast: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-motion\n */\nfunction isMotionReduced() {\n    if (doesMatch$2('reduce')) {\n        return true;\n    }\n    if (doesMatch$2('no-preference')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$2(value) {\n    return matchMedia(\"(prefers-reduced-motion: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-transparency\n */\nfunction isTransparencyReduced() {\n    if (doesMatch$1('reduce')) {\n        return true;\n    }\n    if (doesMatch$1('no-preference')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$1(value) {\n    return matchMedia(\"(prefers-reduced-transparency: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://www.w3.org/TR/mediaqueries-5/#dynamic-range\n */\nfunction isHDR() {\n    if (doesMatch('high')) {\n        return true;\n    }\n    if (doesMatch('standard')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch(value) {\n    return matchMedia(\"(dynamic-range: \".concat(value, \")\")).matches;\n}\n\nvar M = Math; // To reduce the minified code size\nvar fallbackFn = function () { return 0; };\n/**\n * @see https://gitlab.torproject.org/legacy/trac/-/issues/13018\n * @see https://bugzilla.mozilla.org/show_bug.cgi?id=531915\n */\nfunction getMathFingerprint() {\n    // Native operations\n    var acos = M.acos || fallbackFn;\n    var acosh = M.acosh || fallbackFn;\n    var asin = M.asin || fallbackFn;\n    var asinh = M.asinh || fallbackFn;\n    var atanh = M.atanh || fallbackFn;\n    var atan = M.atan || fallbackFn;\n    var sin = M.sin || fallbackFn;\n    var sinh = M.sinh || fallbackFn;\n    var cos = M.cos || fallbackFn;\n    var cosh = M.cosh || fallbackFn;\n    var tan = M.tan || fallbackFn;\n    var tanh = M.tanh || fallbackFn;\n    var exp = M.exp || fallbackFn;\n    var expm1 = M.expm1 || fallbackFn;\n    var log1p = M.log1p || fallbackFn;\n    // Operation polyfills\n    var powPI = function (value) { return M.pow(M.PI, value); };\n    var acoshPf = function (value) { return M.log(value + M.sqrt(value * value - 1)); };\n    var asinhPf = function (value) { return M.log(value + M.sqrt(value * value + 1)); };\n    var atanhPf = function (value) { return M.log((1 + value) / (1 - value)) / 2; };\n    var sinhPf = function (value) { return M.exp(value) - 1 / M.exp(value) / 2; };\n    var coshPf = function (value) { return (M.exp(value) + 1 / M.exp(value)) / 2; };\n    var expm1Pf = function (value) { return M.exp(value) - 1; };\n    var tanhPf = function (value) { return (M.exp(2 * value) - 1) / (M.exp(2 * value) + 1); };\n    var log1pPf = function (value) { return M.log(1 + value); };\n    // Note: constant values are empirical\n    return {\n        acos: acos(0.123124234234234242),\n        acosh: acosh(1e308),\n        acoshPf: acoshPf(1e154),\n        asin: asin(0.123124234234234242),\n        asinh: asinh(1),\n        asinhPf: asinhPf(1),\n        atanh: atanh(0.5),\n        atanhPf: atanhPf(0.5),\n        atan: atan(0.5),\n        sin: sin(-1e300),\n        sinh: sinh(1),\n        sinhPf: sinhPf(1),\n        cos: cos(10.000000000123),\n        cosh: cosh(1),\n        coshPf: coshPf(1),\n        tan: tan(-1e300),\n        tanh: tanh(1),\n        tanhPf: tanhPf(1),\n        exp: exp(1),\n        expm1: expm1(1),\n        expm1Pf: expm1Pf(1),\n        log1p: log1p(10),\n        log1pPf: log1pPf(10),\n        powPI: powPI(-100),\n    };\n}\n\n/**\n * We use m or w because these two characters take up the maximum width.\n * Also there are a couple of ligatures.\n */\nvar defaultText = 'mmMwWLliI0fiflO&1';\n/**\n * Settings of text blocks to measure. The keys are random but persistent words.\n */\nvar presets = {\n    /**\n     * The default font. User can change it in desktop Chrome, desktop Firefox, IE 11,\n     * Android Chrome (but only when the size is ≥ than the default) and Android Firefox.\n     */\n    default: [],\n    /** OS font on macOS. User can change its size and weight. Applies after Safari restart. */\n    apple: [{ font: '-apple-system-body' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    serif: [{ fontFamily: 'serif' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    sans: [{ fontFamily: 'sans-serif' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    mono: [{ fontFamily: 'monospace' }],\n    /**\n     * Check the smallest allowed font size. User can change it in desktop Chrome, desktop Firefox and desktop Safari.\n     * The height can be 0 in Chrome on a retina display.\n     */\n    min: [{ fontSize: '1px' }],\n    /** Tells one OS from another in desktop Chrome. */\n    system: [{ fontFamily: 'system-ui' }],\n};\n/**\n * The result is a dictionary of the width of the text samples.\n * Heights aren't included because they give no extra entropy and are unstable.\n *\n * The result is very stable in IE 11, Edge 18 and Safari 14.\n * The result changes when the OS pixel density changes in Chromium 87. The real pixel density is required to solve,\n * but seems like it's impossible: https://stackoverflow.com/q/1713771/1118709.\n * The \"min\" and the \"mono\" (only on Windows) value may change when the page is zoomed in Firefox 87.\n */\nfunction getFontPreferences() {\n    return withNaturalFonts(function (document, container) {\n        var elements = {};\n        var sizes = {};\n        // First create all elements to measure. If the DOM steps below are done in a single cycle,\n        // browser will alternate tree modification and layout reading, that is very slow.\n        for (var _i = 0, _a = Object.keys(presets); _i < _a.length; _i++) {\n            var key = _a[_i];\n            var _b = presets[key], _c = _b[0], style = _c === void 0 ? {} : _c, _d = _b[1], text = _d === void 0 ? defaultText : _d;\n            var element = document.createElement('span');\n            element.textContent = text;\n            element.style.whiteSpace = 'nowrap';\n            for (var _e = 0, _f = Object.keys(style); _e < _f.length; _e++) {\n                var name_1 = _f[_e];\n                var value = style[name_1];\n                if (value !== undefined) {\n                    element.style[name_1] = value;\n                }\n            }\n            elements[key] = element;\n            container.append(document.createElement('br'), element);\n        }\n        // Then measure the created elements\n        for (var _g = 0, _h = Object.keys(presets); _g < _h.length; _g++) {\n            var key = _h[_g];\n            sizes[key] = elements[key].getBoundingClientRect().width;\n        }\n        return sizes;\n    });\n}\n/**\n * Creates a DOM environment that provides the most natural font available, including Android OS font.\n * Measurements of the elements are zoom-independent.\n * Don't put a content to measure inside an absolutely positioned element.\n */\nfunction withNaturalFonts(action, containerWidthPx) {\n    if (containerWidthPx === void 0) { containerWidthPx = 4000; }\n    /*\n     * Requirements for Android Chrome to apply the system font size to a text inside an iframe:\n     * - The iframe mustn't have a `display: none;` style;\n     * - The text mustn't be positioned absolutely;\n     * - The text block must be wide enough.\n     *   2560px on some devices in portrait orientation for the biggest font size option (32px);\n     * - There must be much enough text to form a few lines (I don't know the exact numbers);\n     * - The text must have the `text-size-adjust: none` style. Otherwise the text will scale in \"Desktop site\" mode;\n     *\n     * Requirements for Android Firefox to apply the system font size to a text inside an iframe:\n     * - The iframe document must have a header: `<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />`.\n     *   The only way to set it is to use the `srcdoc` attribute of the iframe;\n     * - The iframe content must get loaded before adding extra content with JavaScript;\n     *\n     * https://example.com as the iframe target always inherits Android font settings so it can be used as a reference.\n     *\n     * Observations on how page zoom affects the measurements:\n     * - macOS Safari 11.1, 12.1, 13.1, 14.0: zoom reset + offsetWidth = 100% reliable;\n     * - macOS Safari 11.1, 12.1, 13.1, 14.0: zoom reset + getBoundingClientRect = 100% reliable;\n     * - macOS Safari 14.0: offsetWidth = 5% fluctuation;\n     * - macOS Safari 14.0: getBoundingClientRect = 5% fluctuation;\n     * - iOS Safari 9, 10, 11.0, 12.0: haven't found a way to zoom a page (pinch doesn't change layout);\n     * - iOS Safari 13.1, 14.0: zoom reset + offsetWidth = 100% reliable;\n     * - iOS Safari 13.1, 14.0: zoom reset + getBoundingClientRect = 100% reliable;\n     * - iOS Safari 14.0: offsetWidth = 100% reliable;\n     * - iOS Safari 14.0: getBoundingClientRect = 100% reliable;\n     * - Chrome 42, 65, 80, 87: zoom 1/devicePixelRatio + offsetWidth = 1px fluctuation;\n     * - Chrome 42, 65, 80, 87: zoom 1/devicePixelRatio + getBoundingClientRect = 100% reliable;\n     * - Chrome 87: offsetWidth = 1px fluctuation;\n     * - Chrome 87: getBoundingClientRect = 0.7px fluctuation;\n     * - Firefox 48, 51: offsetWidth = 10% fluctuation;\n     * - Firefox 48, 51: getBoundingClientRect = 10% fluctuation;\n     * - Firefox 52, 53, 57, 62, 66, 67, 68, 71, 75, 80, 84: offsetWidth = width 100% reliable, height 10% fluctuation;\n     * - Firefox 52, 53, 57, 62, 66, 67, 68, 71, 75, 80, 84: getBoundingClientRect = width 100% reliable, height 10%\n     *   fluctuation;\n     * - Android Chrome 86: haven't found a way to zoom a page (pinch doesn't change layout);\n     * - Android Firefox 84: font size in accessibility settings changes all the CSS sizes, but offsetWidth and\n     *   getBoundingClientRect keep measuring with regular units, so the size reflects the font size setting and doesn't\n     *   fluctuate;\n     * - IE 11, Edge 18: zoom 1/devicePixelRatio + offsetWidth = 100% reliable;\n     * - IE 11, Edge 18: zoom 1/devicePixelRatio + getBoundingClientRect = reflects the zoom level;\n     * - IE 11, Edge 18: offsetWidth = 100% reliable;\n     * - IE 11, Edge 18: getBoundingClientRect = 100% reliable;\n     */\n    return withIframe(function (_, iframeWindow) {\n        var iframeDocument = iframeWindow.document;\n        var iframeBody = iframeDocument.body;\n        var bodyStyle = iframeBody.style;\n        bodyStyle.width = \"\".concat(containerWidthPx, \"px\");\n        bodyStyle.webkitTextSizeAdjust = bodyStyle.textSizeAdjust = 'none';\n        // See the big comment above\n        if (isChromium()) {\n            iframeBody.style.zoom = \"\".concat(1 / iframeWindow.devicePixelRatio);\n        }\n        else if (isWebKit()) {\n            iframeBody.style.zoom = 'reset';\n        }\n        // See the big comment above\n        var linesOfText = iframeDocument.createElement('div');\n        linesOfText.textContent = __spreadArray([], Array((containerWidthPx / 20) << 0), true).map(function () { return 'word'; }).join(' ');\n        iframeBody.appendChild(linesOfText);\n        return action(iframeDocument, iframeBody);\n    }, '<!doctype html><html><head><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">');\n}\n\nfunction isPdfViewerEnabled() {\n    return navigator.pdfViewerEnabled;\n}\n\n/**\n * Unlike most other architectures, on x86/x86-64 when floating-point instructions\n * have no NaN arguments, but produce NaN output, the output NaN has sign bit set.\n * We use it to distinguish x86/x86-64 from other architectures, by doing subtraction\n * of two infinities (must produce NaN per IEEE 754 standard).\n *\n * See https://codebrowser.bddppq.com/pytorch/pytorch/third_party/XNNPACK/src/init.c.html#79\n */\nfunction getArchitecture() {\n    var f = new Float32Array(1);\n    var u8 = new Uint8Array(f.buffer);\n    f[0] = Infinity;\n    f[0] = f[0] - f[0];\n    return u8[3];\n}\n\n/**\n * The return type is a union instead of the enum, because it's too challenging to embed the const enum into another\n * project. Turning it into a union is a simple and an elegant solution.\n */\nfunction getApplePayState() {\n    var ApplePaySession = window.ApplePaySession;\n    if (typeof (ApplePaySession === null || ApplePaySession === void 0 ? void 0 : ApplePaySession.canMakePayments) !== 'function') {\n        return -1 /* ApplePayState.NoAPI */;\n    }\n    if (willPrintConsoleError()) {\n        return -3 /* ApplePayState.NotAvailableInFrame */;\n    }\n    try {\n        return ApplePaySession.canMakePayments() ? 1 /* ApplePayState.Enabled */ : 0 /* ApplePayState.Disabled */;\n    }\n    catch (error) {\n        return getStateFromError(error);\n    }\n}\n/**\n * Starting from Safari 15 calling `ApplePaySession.canMakePayments()` produces this error message when FingerprintJS\n * runs in an iframe with a cross-origin parent page, and the iframe on that page has no allow=\"payment *\" attribute:\n *   Feature policy 'Payment' check failed for element with origin 'https://example.com' and allow attribute ''.\n * This function checks whether the error message is expected.\n *\n * We check for cross-origin parents, which is prone to false-positive results. Instead, we should check for allowed\n * feature/permission, but we can't because none of these API works in Safari yet:\n *   navigator.permissions.query({ name: ‘payment' })\n *   navigator.permissions.query({ name: ‘payment-handler' })\n *   document.featurePolicy\n */\nvar willPrintConsoleError = isAnyParentCrossOrigin;\nfunction getStateFromError(error) {\n    // See full expected error messages in the test\n    if (error instanceof Error && error.name === 'InvalidAccessError' && /\\bfrom\\b.*\\binsecure\\b/i.test(error.message)) {\n        return -2 /* ApplePayState.NotAvailableInInsecureContext */;\n    }\n    throw error;\n}\n\n/**\n * Checks whether the Safari's Privacy Preserving Ad Measurement setting is on.\n * The setting is on when the value is not undefined.\n * A.k.a. private click measurement, privacy-preserving ad attribution.\n *\n * Unfortunately, it doesn't work in mobile Safari.\n * Probably, it will start working in mobile Safari or stop working in desktop Safari later.\n * We've found no way to detect the setting state in mobile Safari. Help wanted.\n *\n * @see https://webkit.org/blog/11529/introducing-private-click-measurement-pcm/\n * @see https://developer.apple.com/videos/play/wwdc2021/10033\n */\nfunction getPrivateClickMeasurement() {\n    var _a;\n    var link = document.createElement('a');\n    var sourceId = (_a = link.attributionSourceId) !== null && _a !== void 0 ? _a : link.attributionsourceid;\n    return sourceId === undefined ? undefined : String(sourceId);\n}\n\n/** WebGl context is not available */\nvar STATUS_NO_GL_CONTEXT = -1;\n/** WebGL context `getParameter` method is not a function */\nvar STATUS_GET_PARAMETER_NOT_A_FUNCTION = -2;\nvar validContextParameters = new Set([\n    10752, 2849, 2884, 2885, 2886, 2928, 2929, 2930, 2931, 2932, 2960, 2961, 2962, 2963, 2964, 2965, 2966, 2967, 2968,\n    2978, 3024, 3042, 3088, 3089, 3106, 3107, 32773, 32777, 32777, 32823, 32824, 32936, 32937, 32938, 32939, 32968, 32969,\n    32970, 32971, 3317, 33170, 3333, 3379, 3386, 33901, 33902, 34016, 34024, 34076, 3408, 3410, 3411, 3412, 3413, 3414,\n    3415, 34467, 34816, 34817, 34818, 34819, 34877, 34921, 34930, 35660, 35661, 35724, 35738, 35739, 36003, 36004, 36005,\n    36347, 36348, 36349, 37440, 37441, 37443, 7936, 7937, 7938,\n    // SAMPLE_ALPHA_TO_COVERAGE (32926) and SAMPLE_COVERAGE (32928) are excluded because they trigger a console warning\n    // in IE, Chrome ≤ 59 and Safari ≤ 13 and give no entropy.\n]);\nvar validExtensionParams = new Set([\n    34047,\n    35723,\n    36063,\n    34852,\n    34853,\n    34854,\n    34229,\n    36392,\n    36795,\n    38449, // MAX_VIEWS_OVR\n]);\nvar shaderTypes = ['FRAGMENT_SHADER', 'VERTEX_SHADER'];\nvar precisionTypes = ['LOW_FLOAT', 'MEDIUM_FLOAT', 'HIGH_FLOAT', 'LOW_INT', 'MEDIUM_INT', 'HIGH_INT'];\nvar rendererInfoExtensionName = 'WEBGL_debug_renderer_info';\nvar polygonModeExtensionName = 'WEBGL_polygon_mode';\n/**\n * Gets the basic and simple WebGL parameters\n */\nfunction getWebGlBasics(_a) {\n    var _b, _c, _d, _e, _f, _g;\n    var cache = _a.cache;\n    var gl = getWebGLContext(cache);\n    if (!gl) {\n        return STATUS_NO_GL_CONTEXT;\n    }\n    if (!isValidParameterGetter(gl)) {\n        return STATUS_GET_PARAMETER_NOT_A_FUNCTION;\n    }\n    var debugExtension = shouldAvoidDebugRendererInfo() ? null : gl.getExtension(rendererInfoExtensionName);\n    return {\n        version: ((_b = gl.getParameter(gl.VERSION)) === null || _b === void 0 ? void 0 : _b.toString()) || '',\n        vendor: ((_c = gl.getParameter(gl.VENDOR)) === null || _c === void 0 ? void 0 : _c.toString()) || '',\n        vendorUnmasked: debugExtension ? (_d = gl.getParameter(debugExtension.UNMASKED_VENDOR_WEBGL)) === null || _d === void 0 ? void 0 : _d.toString() : '',\n        renderer: ((_e = gl.getParameter(gl.RENDERER)) === null || _e === void 0 ? void 0 : _e.toString()) || '',\n        rendererUnmasked: debugExtension ? (_f = gl.getParameter(debugExtension.UNMASKED_RENDERER_WEBGL)) === null || _f === void 0 ? void 0 : _f.toString() : '',\n        shadingLanguageVersion: ((_g = gl.getParameter(gl.SHADING_LANGUAGE_VERSION)) === null || _g === void 0 ? void 0 : _g.toString()) || '',\n    };\n}\n/**\n * Gets the advanced and massive WebGL parameters and extensions\n */\nfunction getWebGlExtensions(_a) {\n    var cache = _a.cache;\n    var gl = getWebGLContext(cache);\n    if (!gl) {\n        return STATUS_NO_GL_CONTEXT;\n    }\n    if (!isValidParameterGetter(gl)) {\n        return STATUS_GET_PARAMETER_NOT_A_FUNCTION;\n    }\n    var extensions = gl.getSupportedExtensions();\n    var contextAttributes = gl.getContextAttributes();\n    var unsupportedExtensions = [];\n    // Features\n    var attributes = [];\n    var parameters = [];\n    var extensionParameters = [];\n    var shaderPrecisions = [];\n    // Context attributes\n    if (contextAttributes) {\n        for (var _i = 0, _b = Object.keys(contextAttributes); _i < _b.length; _i++) {\n            var attributeName = _b[_i];\n            attributes.push(\"\".concat(attributeName, \"=\").concat(contextAttributes[attributeName]));\n        }\n    }\n    // Context parameters\n    var constants = getConstantsFromPrototype(gl);\n    for (var _c = 0, constants_1 = constants; _c < constants_1.length; _c++) {\n        var constant = constants_1[_c];\n        var code = gl[constant];\n        parameters.push(\"\".concat(constant, \"=\").concat(code).concat(validContextParameters.has(code) ? \"=\".concat(gl.getParameter(code)) : ''));\n    }\n    // Extension parameters\n    if (extensions) {\n        for (var _d = 0, extensions_1 = extensions; _d < extensions_1.length; _d++) {\n            var name_1 = extensions_1[_d];\n            if ((name_1 === rendererInfoExtensionName && shouldAvoidDebugRendererInfo()) ||\n                (name_1 === polygonModeExtensionName && shouldAvoidPolygonModeExtensions())) {\n                continue;\n            }\n            var extension = gl.getExtension(name_1);\n            if (!extension) {\n                unsupportedExtensions.push(name_1);\n                continue;\n            }\n            for (var _e = 0, _f = getConstantsFromPrototype(extension); _e < _f.length; _e++) {\n                var constant = _f[_e];\n                var code = extension[constant];\n                extensionParameters.push(\"\".concat(constant, \"=\").concat(code).concat(validExtensionParams.has(code) ? \"=\".concat(gl.getParameter(code)) : ''));\n            }\n        }\n    }\n    // Shader precision\n    for (var _g = 0, shaderTypes_1 = shaderTypes; _g < shaderTypes_1.length; _g++) {\n        var shaderType = shaderTypes_1[_g];\n        for (var _h = 0, precisionTypes_1 = precisionTypes; _h < precisionTypes_1.length; _h++) {\n            var precisionType = precisionTypes_1[_h];\n            var shaderPrecision = getShaderPrecision(gl, shaderType, precisionType);\n            shaderPrecisions.push(\"\".concat(shaderType, \".\").concat(precisionType, \"=\").concat(shaderPrecision.join(',')));\n        }\n    }\n    // Postprocess\n    extensionParameters.sort();\n    parameters.sort();\n    return {\n        contextAttributes: attributes,\n        parameters: parameters,\n        shaderPrecisions: shaderPrecisions,\n        extensions: extensions,\n        extensionParameters: extensionParameters,\n        unsupportedExtensions: unsupportedExtensions,\n    };\n}\n/**\n * This function usually takes the most time to execute in all the sources, therefore we cache its result.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getWebGLContext(cache) {\n    if (cache.webgl) {\n        return cache.webgl.context;\n    }\n    var canvas = document.createElement('canvas');\n    var context;\n    canvas.addEventListener('webglCreateContextError', function () { return (context = undefined); });\n    for (var _i = 0, _a = ['webgl', 'experimental-webgl']; _i < _a.length; _i++) {\n        var type = _a[_i];\n        try {\n            context = canvas.getContext(type);\n        }\n        catch (_b) {\n            // Ok, continue\n        }\n        if (context) {\n            break;\n        }\n    }\n    cache.webgl = { context: context };\n    return context;\n}\n/**\n * https://developer.mozilla.org/en-US/docs/Web/API/WebGLShaderPrecisionFormat\n * https://developer.mozilla.org/en-US/docs/Web/API/WebGLRenderingContext/getShaderPrecisionFormat\n * https://www.khronos.org/registry/webgl/specs/latest/1.0/#5.12\n */\nfunction getShaderPrecision(gl, shaderType, precisionType) {\n    var shaderPrecision = gl.getShaderPrecisionFormat(gl[shaderType], gl[precisionType]);\n    return shaderPrecision ? [shaderPrecision.rangeMin, shaderPrecision.rangeMax, shaderPrecision.precision] : [];\n}\nfunction getConstantsFromPrototype(obj) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    var keys = Object.keys(obj.__proto__);\n    return keys.filter(isConstantLike);\n}\nfunction isConstantLike(key) {\n    return typeof key === 'string' && !key.match(/[^A-Z0-9_x]/);\n}\n/**\n * Some browsers print a console warning when the WEBGL_debug_renderer_info extension is requested.\n * JS Agent aims to avoid printing messages to console, so we avoid this extension in that browsers.\n */\nfunction shouldAvoidDebugRendererInfo() {\n    return isGecko();\n}\n/**\n * Some browsers print a console warning when the WEBGL_polygon_mode extension is requested.\n * JS Agent aims to avoid printing messages to console, so we avoid this extension in that browsers.\n */\nfunction shouldAvoidPolygonModeExtensions() {\n    return isChromium() || isWebKit();\n}\n/**\n * Some unknown browsers have no `getParameter` method\n */\nfunction isValidParameterGetter(gl) {\n    return typeof gl.getParameter === 'function';\n}\n\nfunction getAudioContextBaseLatency() {\n    var _a;\n    // The signal emits warning in Chrome and Firefox, therefore it is enabled on Safari where it doesn't produce warning\n    // and on Android where it's less visible\n    var isAllowedPlatform = isAndroid() || isWebKit();\n    if (!isAllowedPlatform) {\n        return -2 /* SpecialFingerprint.Disabled */;\n    }\n    if (!window.AudioContext) {\n        return -1 /* SpecialFingerprint.NotSupported */;\n    }\n    return (_a = new AudioContext().baseLatency) !== null && _a !== void 0 ? _a : -1 /* SpecialFingerprint.NotSupported */;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/resolvedOptions\n *\n * The return type is a union instead of a const enum due to the difficulty of embedding const enums in other projects.\n * This makes integration simpler and more elegant.\n */\nfunction getDateTimeLocale() {\n    if (!window.Intl) {\n        return -1 /* Status.IntlAPINotSupported */;\n    }\n    var DateTimeFormat = window.Intl.DateTimeFormat;\n    if (!DateTimeFormat) {\n        return -2 /* Status.DateTimeFormatNotSupported */;\n    }\n    var locale = DateTimeFormat().resolvedOptions().locale;\n    if (!locale && locale !== '') {\n        return -3 /* Status.LocaleNotAvailable */;\n    }\n    return locale;\n}\n\n/**\n * The list of entropy sources used to make visitor identifiers.\n *\n * This value isn't restricted by Semantic Versioning, i.e. it may be changed without bumping minor or major version of\n * this package.\n *\n * Note: Rollup and Webpack are smart enough to remove unused properties of this object during tree-shaking, so there is\n * no need to export the sources individually.\n */\nvar sources = {\n    // READ FIRST:\n    // See https://github.com/fingerprintjs/fingerprintjs/blob/master/contributing.md#how-to-add-an-entropy-source\n    // to learn how entropy source works and how to make your own.\n    // The sources run in this exact order.\n    // The asynchronous sources are at the start to run in parallel with other sources.\n    fonts: getFonts,\n    domBlockers: getDomBlockers,\n    fontPreferences: getFontPreferences,\n    audio: getAudioFingerprint,\n    screenFrame: getScreenFrame,\n    canvas: getCanvasFingerprint,\n    osCpu: getOsCpu,\n    languages: getLanguages,\n    colorDepth: getColorDepth,\n    deviceMemory: getDeviceMemory,\n    screenResolution: getScreenResolution,\n    hardwareConcurrency: getHardwareConcurrency,\n    timezone: getTimezone,\n    sessionStorage: getSessionStorage,\n    localStorage: getLocalStorage,\n    indexedDB: getIndexedDB,\n    openDatabase: getOpenDatabase,\n    cpuClass: getCpuClass,\n    platform: getPlatform,\n    plugins: getPlugins,\n    touchSupport: getTouchSupport,\n    vendor: getVendor,\n    vendorFlavors: getVendorFlavors,\n    cookiesEnabled: areCookiesEnabled,\n    colorGamut: getColorGamut,\n    invertedColors: areColorsInverted,\n    forcedColors: areColorsForced,\n    monochrome: getMonochromeDepth,\n    contrast: getContrastPreference,\n    reducedMotion: isMotionReduced,\n    reducedTransparency: isTransparencyReduced,\n    hdr: isHDR,\n    math: getMathFingerprint,\n    pdfViewerEnabled: isPdfViewerEnabled,\n    architecture: getArchitecture,\n    applePay: getApplePayState,\n    privateClickMeasurement: getPrivateClickMeasurement,\n    audioBaseLatency: getAudioContextBaseLatency,\n    dateTimeLocale: getDateTimeLocale,\n    // Some sources can affect other sources (e.g. WebGL can affect canvas), so it's important to run these sources\n    // after other sources.\n    webGlBasics: getWebGlBasics,\n    webGlExtensions: getWebGlExtensions,\n};\n/**\n * Loads the built-in entropy sources.\n * Returns a function that collects the entropy components to make the visitor identifier.\n */\nfunction loadBuiltinSources(options) {\n    return loadSources(sources, options, []);\n}\n\nvar commentTemplate = '$ if upgrade to Pro: https://fpjs.dev/pro';\nfunction getConfidence(components) {\n    var openConfidenceScore = getOpenConfidenceScore(components);\n    var proConfidenceScore = deriveProConfidenceScore(openConfidenceScore);\n    return { score: openConfidenceScore, comment: commentTemplate.replace(/\\$/g, \"\".concat(proConfidenceScore)) };\n}\nfunction getOpenConfidenceScore(components) {\n    // In order to calculate the true probability of the visitor identifier being correct, we need to know the number of\n    // website visitors (the higher the number, the less the probability because the fingerprint entropy is limited).\n    // JS agent doesn't know the number of visitors, so we can only do an approximate assessment.\n    if (isAndroid()) {\n        return 0.4;\n    }\n    // Safari (mobile and desktop)\n    if (isWebKit()) {\n        return isDesktopWebKit() && !(isWebKit616OrNewer() && isSafariWebKit()) ? 0.5 : 0.3;\n    }\n    var platform = 'value' in components.platform ? components.platform.value : '';\n    // Windows\n    if (/^Win/.test(platform)) {\n        // The score is greater than on macOS because of the higher variety of devices running Windows.\n        // Chrome provides more entropy than Firefox according too\n        // https://netmarketshare.com/browser-market-share.aspx?options=%7B%22filter%22%3A%7B%22%24and%22%3A%5B%7B%22platform%22%3A%7B%22%24in%22%3A%5B%22Windows%22%5D%7D%7D%5D%7D%2C%22dateLabel%22%3A%22Trend%22%2C%22attributes%22%3A%22share%22%2C%22group%22%3A%22browser%22%2C%22sort%22%3A%7B%22share%22%3A-1%7D%2C%22id%22%3A%22browsersDesktop%22%2C%22dateInterval%22%3A%22Monthly%22%2C%22dateStart%22%3A%222019-11%22%2C%22dateEnd%22%3A%222020-10%22%2C%22segments%22%3A%22-1000%22%7D\n        // So we assign the same score to them.\n        return 0.6;\n    }\n    // macOS\n    if (/^Mac/.test(platform)) {\n        // Chrome provides more entropy than Safari and Safari provides more entropy than Firefox.\n        // Chrome is more popular than Safari and Safari is more popular than Firefox according to\n        // https://netmarketshare.com/browser-market-share.aspx?options=%7B%22filter%22%3A%7B%22%24and%22%3A%5B%7B%22platform%22%3A%7B%22%24in%22%3A%5B%22Mac%20OS%22%5D%7D%7D%5D%7D%2C%22dateLabel%22%3A%22Trend%22%2C%22attributes%22%3A%22share%22%2C%22group%22%3A%22browser%22%2C%22sort%22%3A%7B%22share%22%3A-1%7D%2C%22id%22%3A%22browsersDesktop%22%2C%22dateInterval%22%3A%22Monthly%22%2C%22dateStart%22%3A%222019-11%22%2C%22dateEnd%22%3A%222020-10%22%2C%22segments%22%3A%22-1000%22%7D\n        // So we assign the same score to them.\n        return 0.5;\n    }\n    // Another platform, e.g. a desktop Linux. It's rare, so it should be pretty unique.\n    return 0.7;\n}\nfunction deriveProConfidenceScore(openConfidenceScore) {\n    return round(0.99 + 0.01 * openConfidenceScore, 0.0001);\n}\n\nfunction componentsToCanonicalString(components) {\n    var result = '';\n    for (var _i = 0, _a = Object.keys(components).sort(); _i < _a.length; _i++) {\n        var componentKey = _a[_i];\n        var component = components[componentKey];\n        var value = 'error' in component ? 'error' : JSON.stringify(component.value);\n        result += \"\".concat(result ? '|' : '').concat(componentKey.replace(/([:|\\\\])/g, '\\\\$1'), \":\").concat(value);\n    }\n    return result;\n}\nfunction componentsToDebugString(components) {\n    return JSON.stringify(components, function (_key, value) {\n        if (value instanceof Error) {\n            return errorToObject(value);\n        }\n        return value;\n    }, 2);\n}\nfunction hashComponents(components) {\n    return x64hash128(componentsToCanonicalString(components));\n}\n/**\n * Makes a GetResult implementation that calculates the visitor id hash on demand.\n * Designed for optimisation.\n */\nfunction makeLazyGetResult(components) {\n    var visitorIdCache;\n    // This function runs very fast, so there is no need to make it lazy\n    var confidence = getConfidence(components);\n    // A plain class isn't used because its getters and setters aren't enumerable.\n    return {\n        get visitorId() {\n            if (visitorIdCache === undefined) {\n                visitorIdCache = hashComponents(this.components);\n            }\n            return visitorIdCache;\n        },\n        set visitorId(visitorId) {\n            visitorIdCache = visitorId;\n        },\n        confidence: confidence,\n        components: components,\n        version: version,\n    };\n}\n/**\n * A delay is required to ensure consistent entropy components.\n * See https://github.com/fingerprintjs/fingerprintjs/issues/254\n * and https://github.com/fingerprintjs/fingerprintjs/issues/307\n * and https://github.com/fingerprintjs/fingerprintjs/commit/945633e7c5f67ae38eb0fea37349712f0e669b18\n */\nfunction prepareForSources(delayFallback) {\n    if (delayFallback === void 0) { delayFallback = 50; }\n    // A proper deadline is unknown. Let it be twice the fallback timeout so that both cases have the same average time.\n    return requestIdleCallbackIfAvailable(delayFallback, delayFallback * 2);\n}\n/**\n * The function isn't exported from the index file to not allow to call it without `load()`.\n * The hiding gives more freedom for future non-breaking updates.\n *\n * A factory function is used instead of a class to shorten the attribute names in the minified code.\n * Native private class fields could've been used, but TypeScript doesn't allow them with `\"target\": \"es5\"`.\n */\nfunction makeAgent(getComponents, debug) {\n    var creationTime = Date.now();\n    return {\n        get: function (options) {\n            return __awaiter(this, void 0, void 0, function () {\n                var startTime, components, result;\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            startTime = Date.now();\n                            return [4 /*yield*/, getComponents()];\n                        case 1:\n                            components = _a.sent();\n                            result = makeLazyGetResult(components);\n                            if (debug || (options === null || options === void 0 ? void 0 : options.debug)) {\n                                // console.log is ok here because it's under a debug clause\n                                // eslint-disable-next-line no-console\n                                console.log(\"Copy the text below to get the debug data:\\n\\n```\\nversion: \".concat(result.version, \"\\nuserAgent: \").concat(navigator.userAgent, \"\\ntimeBetweenLoadAndGet: \").concat(startTime - creationTime, \"\\nvisitorId: \").concat(result.visitorId, \"\\ncomponents: \").concat(componentsToDebugString(components), \"\\n```\"));\n                            }\n                            return [2 /*return*/, result];\n                    }\n                });\n            });\n        },\n    };\n}\n/**\n * Sends an unpersonalized AJAX request to collect installation statistics\n */\nfunction monitor() {\n    // The FingerprintJS CDN (https://github.com/fingerprintjs/cdn) replaces `window.__fpjs_d_m` with `true`\n    if (window.__fpjs_d_m || Math.random() >= 0.001) {\n        return;\n    }\n    try {\n        var request = new XMLHttpRequest();\n        request.open('get', \"https://m1.openfpcdn.io/fingerprintjs/v\".concat(version, \"/npm-monitoring\"), true);\n        request.send();\n    }\n    catch (error) {\n        // console.error is ok here because it's an unexpected error handler\n        // eslint-disable-next-line no-console\n        console.error(error);\n    }\n}\n/**\n * Builds an instance of Agent and waits a delay required for a proper operation.\n */\nfunction load(options) {\n    var _a;\n    if (options === void 0) { options = {}; }\n    return __awaiter(this, void 0, void 0, function () {\n        var delayFallback, debug, getComponents;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    if ((_a = options.monitoring) !== null && _a !== void 0 ? _a : true) {\n                        monitor();\n                    }\n                    delayFallback = options.delayFallback, debug = options.debug;\n                    return [4 /*yield*/, prepareForSources(delayFallback)];\n                case 1:\n                    _b.sent();\n                    getComponents = loadBuiltinSources({ cache: {}, debug: debug });\n                    return [2 /*return*/, makeAgent(getComponents, debug)];\n            }\n        });\n    });\n}\n\n// The default export is a syntax sugar (`import * as FP from '...' → import FP from '...'`).\n// It should contain all the public exported values.\nvar index = { load: load, hashComponents: hashComponents, componentsToDebugString: componentsToDebugString };\n// The exports below are for private usage. They may change unexpectedly. Use them at your own risk.\n/** Not documented, out of Semantic Versioning, usage is at your own risk */\nvar murmurX64Hash128 = x64hash128;\n\nexport { componentsToDebugString, index as default, getFullscreenElement, getUnstableAudioFingerprint, getUnstableCanvasFingerprint, getUnstableScreenFrame, getUnstableScreenResolution, getWebGLContext, hashComponents, isAndroid, isChromium, isDesktopWebKit, isEdgeHTML, isGecko, isSamsungInternet, isTrident, isWebKit, load, loadSources, murmurX64Hash128, prepareForSources, sources, transformSource, withIframe };\n", "import FingerprintJS from '@fingerprintjs/fingerprintjs';\nexport async function getDeviceId() {\n    const fp = await FingerprintJS.load();\n    const result = await fp.get();\n    return result.visitorId; // Unique Device ID\n}\nexport async function fetchWithDeviceId(input, init) {\n    const deviceId = await getDeviceId();\n    // Convert Headers instance to a plain object if needed\n    let headers;\n    if (init?.headers instanceof Headers) {\n        headers = new Headers();\n        init.headers.forEach((value, key) => {\n            headers.set(key, value);\n        });\n    }\n    else {\n        headers = new Headers(init?.headers || {});\n    }\n    // Set the custom header\n    headers.set(\"X-Device-Id\", deviceId);\n    const modifiedInit = {\n        ...init,\n        headers,\n        credentials: \"include\", // Ensure credentials are sent if needed\n    };\n    return fetch(input, modifiedInit);\n}\nexport async function fetchWithDeviceIdandApiKey(input, init = {}, apiKey) {\n    const deviceId = await getDeviceId(); // Lấy deviceId\n    // Khởi tạo headers và đảm bảo giữ nguyên headers cũ nếu có\n    const headers = new Headers(init.headers);\n    headers.set(\"X-Device-Id\", deviceId);\n    headers.set(\"X-Api-Key\", apiKey); // Gửi API key trong request\n    // Tạo request mới với headers cập nhật\n    const modifiedInit = {\n        ...init,\n        headers,\n        credentials: \"include\", // Giữ cookies nếu cần\n    };\n    try {\n        const response = await fetch(input, modifiedInit);\n        return response;\n    }\n    catch (error) {\n        console.error('Fetch error:', error);\n        throw error;\n    }\n}\n", "import { environment } from \"../environments/environment\";\nimport { fetchWithDeviceId } from \"../utils/deviceUtils\";\nimport FingerprintJS from '@fingerprintjs/fingerprintjs';\nconst apiUrl = environment.apiUrl;\nconst publicKey = atob(environment.publicKey);\nexport class CryptoService {\n    constructor() {\n        this.keyPair = null;\n        this.encryptionKeyPair = null;\n    }\n    async gra() {\n        this.keyPair = await crypto.subtle.generateKey({\n            name: \"RSA-OAEP\",\n            modulusLength: 2048,\n            publicExponent: new Uint8Array([1, 0, 1]),\n            hash: \"SHA-256\"\n        }, true, [\"encrypt\", \"decrypt\"]);\n        const publicKey = await crypto.subtle.exportKey(\"spki\", this.keyPair.publicKey);\n        const privateKey = await crypto.subtle.exportKey(\"pkcs8\", this.keyPair.privateKey);\n        return {\n            publicKey: this.arrayBufferToPEM(publicKey, \"PUBLIC KEY\"),\n            privateKey: this.arrayBufferToPEM(privateKey, \"PRIVATE KEY\")\n        };\n    }\n    async ga() {\n        return await crypto.subtle.generateKey({ name: 'AES-GCM', length: 256 }, true, ['encrypt', 'decrypt']);\n    }\n    async ea(aesKey, data) {\n        const encoder = new TextEncoder();\n        const dataBuffer = encoder.encode(data);\n        const iv = crypto.getRandomValues(new Uint8Array(12));\n        const encryptedData = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, aesKey, dataBuffer);\n        return { encryptedData, iv };\n    }\n    async irpu(pem) {\n        const binaryDer = this.pemToArrayBuffer(pem);\n        return await crypto.subtle.importKey('spki', binaryDer, { name: 'RSA-OAEP', hash: 'SHA-256' }, true, ['encrypt']);\n    }\n    async irpr(pem) {\n        const binaryDer = this.pemToArrayBuffer(pem);\n        return await crypto.subtle.importKey('pkcs8', binaryDer, { name: 'RSA-OAEP', hash: 'SHA-256' }, true, ['decrypt']);\n    }\n    async era(publicKey, aesKey) {\n        const exportedAESKey = await crypto.subtle.exportKey('raw', aesKey);\n        return await crypto.subtle.encrypt({ name: 'RSA-OAEP' }, publicKey, exportedAESKey);\n    }\n    async dra(privateKey, encryptedData) {\n        return await crypto.subtle.decrypt({ name: 'RSA-OAEP' }, privateKey, encryptedData);\n    }\n    async he(publicKeyPem, data) {\n        const aesKey = await this.ga();\n        const { encryptedData, iv } = await this.ea(aesKey, data);\n        const publicKey = await this.irpu(publicKeyPem);\n        const encryptedAESKey = await this.era(publicKey, aesKey);\n        const combinedData = new Uint8Array(encryptedAESKey.byteLength + iv.byteLength + encryptedData.byteLength);\n        combinedData.set(new Uint8Array(encryptedAESKey), 0);\n        combinedData.set(iv, encryptedAESKey.byteLength);\n        combinedData.set(new Uint8Array(encryptedData), encryptedAESKey.byteLength + iv.byteLength);\n        return btoa(String.fromCharCode(...combinedData));\n    }\n    async hd(privateKeyBem, encryptedText) {\n        try {\n            const combinedData = Uint8Array.from(atob(encryptedText), c => c.charCodeAt(0));\n            const encryptedAesKey = combinedData.slice(0, 256);\n            const encryptedData = combinedData.slice(256, combinedData.length);\n            const privateKey = await this.irpr(privateKeyBem);\n            const aesKeyBuffer = await this.dra(privateKey, encryptedAesKey);\n            const decryptedData = await this.da(aesKeyBuffer, encryptedData);\n            return decryptedData;\n        }\n        catch (error) {\n            console.error(\"Error during decryption:\", error);\n            throw new Error(\"Decryption failed\");\n        }\n    }\n    bts(buffer) {\n        return btoa(String.fromCharCode(...new Uint8Array(buffer)));\n    }\n    async da(aesKeyBuffer, encryptedData) {\n        try {\n            const aesKey = await crypto.subtle.importKey(\"raw\", aesKeyBuffer, { name: \"AES-GCM\" }, false, [\"decrypt\"]);\n            const iv = encryptedData.slice(0, 12);\n            const tag = encryptedData.slice(12, 28);\n            const cipherText = encryptedData.slice(28);\n            const encryptedBuffer = new Uint8Array([...cipherText, ...tag]);\n            const decryptedBuffer = await crypto.subtle.decrypt({ name: \"AES-GCM\", iv: iv }, aesKey, encryptedBuffer);\n            return new TextDecoder().decode(decryptedBuffer);\n        }\n        catch (error) {\n            throw new Error(\"AES-GCM Decryption failed\");\n        }\n    }\n    async encrypt(publicKey, plainText) {\n        const key = await this.importPublicKey(publicKey);\n        const encryptedData = await crypto.subtle.encrypt({\n            name: \"RSA-OAEP\"\n        }, key, new TextEncoder().encode(plainText));\n        return this.arrayBufferToBase64(encryptedData);\n    }\n    async decrypt(privateKey, encryptedText) {\n        const key = await this.importPrivateKey(privateKey);\n        const decryptedData = await crypto.subtle.decrypt({\n            name: \"RSA-OAEP\"\n        }, key, this.base64ToArrayBuffer(encryptedText));\n        return new TextDecoder().decode(decryptedData);\n    }\n    async importPublicKey(pem) {\n        return crypto.subtle.importKey(\"spki\", this.pemToArrayBuffer(pem), {\n            name: \"RSA-OAEP\",\n            hash: \"SHA-256\"\n        }, true, [\"encrypt\"]);\n    }\n    async importPrivateKey(pem) {\n        return crypto.subtle.importKey(\"pkcs8\", this.pemToArrayBuffer(pem), {\n            name: \"RSA-OAEP\",\n            hash: \"SHA-256\"\n        }, true, [\"decrypt\"]);\n    }\n    arrayBufferToPEM(buffer, type) {\n        const base64 = this.arrayBufferToBase64(buffer);\n        const pem = `-----BEGIN ${type}-----\\n${base64.match(/.{1,64}/g)?.join('\\n')}\\n-----END ${type}-----`;\n        return pem;\n    }\n    arrayBufferToBase64(buffer) {\n        let binary = '';\n        const bytes = new Uint8Array(buffer);\n        const len = bytes.byteLength;\n        for (let i = 0; i < len; i++) {\n            binary += String.fromCharCode(bytes[i]);\n        }\n        return window.btoa(binary);\n    }\n    base64ToArrayBuffer(base64) {\n        const binaryString = window.atob(base64);\n        const len = binaryString.length;\n        const bytes = new Uint8Array(len);\n        for (let i = 0; i < len; i++) {\n            bytes[i] = binaryString.charCodeAt(i);\n        }\n        return bytes.buffer;\n    }\n    pemToArrayBuffer(pem) {\n        const base64 = pem.replace(/-----BEGIN [A-Z ]+-----|-----END [A-Z ]+-----|\\n/g, '');\n        return this.base64ToArrayBuffer(base64);\n    }\n    async gr() {\n        this.encryptionKeyPair = await this.gra();\n        const signingKeyPair = await crypto.subtle.generateKey({ name: \"RSASSA-PKCS1-v1_5\", modulusLength: 2048, publicExponent: new Uint8Array([0x01, 0x00, 0x01]), hash: \"SHA-256\" }, true, [\"sign\", \"verify\"]);\n        const ep = this.textToBase64(this.encryptionKeyPair.publicKey);\n        const spBuffer = await crypto.subtle.exportKey(\"spki\", signingKeyPair.publicKey);\n        const sp = btoa(String.fromCharCode(...new Uint8Array(spBuffer)));\n        const s = crypto.randomUUID();\n        const xdi = await this.gdi();\n        const encoder = new TextEncoder();\n        const sBuffer = encoder.encode(s + xdi);\n        const signatureBuffer = await crypto.subtle.sign({ name: \"RSASSA-PKCS1-v1_5\" }, signingKeyPair.privateKey, sBuffer);\n        const ss = btoa(String.fromCharCode(...new Uint8Array(signatureBuffer)));\n        return { ep, sp, ss, s };\n    }\n    textToBase64(text) {\n        return btoa(unescape(encodeURIComponent(text)));\n    }\n    sc(name, value, minutes) {\n        const date = new Date();\n        date.setTime(date.getTime() + (minutes * 60 * 1000));\n        const expires = \"expires=\" + date.toUTCString();\n        document.cookie = name + \"=\" + value + \";\" + expires + \";path=/\";\n    }\n    gc(name) {\n        const nameEQ = name + \"=\";\n        const ca = document.cookie.split(';');\n        for (let i = 0; i < ca.length; i++) {\n            let c = ca[i];\n            while (c.charAt(0) === ' ')\n                c = c.substring(1, c.length);\n            if (c.indexOf(nameEQ) === 0)\n                return c.substring(nameEQ.length, c.length);\n        }\n        return null;\n    }\n    //remove cookie\n    rc(name) {\n        document.cookie = name + '=; Max-Age=-99999999;';\n    }\n    //remove all cookies\n    ra() {\n        const cookies = document.cookie.split(\";\");\n        for (let i = 0; i < cookies.length; i++) {\n            const cookie = cookies[i];\n            const eqPos = cookie.indexOf(\"=\");\n            const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;\n            document.cookie = name + \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n        }\n    }\n    async spu() {\n        const { ep, sp, ss, s } = await this.gr();\n        const request = {\n            ep: ep,\n            sp: sp,\n            ss: ss,\n            s: s\n        };\n        const requestJson = JSON.stringify(request);\n        const encryptedData = await this.he(publicKey, requestJson);\n        const requestEncrypt = {\n            EncryptData: encryptedData\n        };\n        try {\n            const response = await fetchWithDeviceId(apiUrl + '/api/Crypto/dr', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestEncrypt)\n            });\n            if (!response.ok) {\n                throw new Error('Network response was not ok');\n            }\n            const responseData = await response.json();\n            if (responseData && responseData.resultObj && responseData.resultObj.encryptedData) {\n                this.sc('s', responseData.resultObj.encryptedData, 5);\n                const privateKeyBase64String = this.textToBase64(this.encryptionKeyPair.privateKey);\n                this.sc('c', privateKeyBase64String, 5);\n            }\n            else {\n                console.error('Invalid response from server:', responseData);\n            }\n        }\n        catch (error) {\n            console.error('Error in spu:', error);\n        }\n    }\n    async dsk() {\n        var c = this.gc('c');\n        var s = this.gc('s');\n        if (!c || !s) {\n            return \"\";\n        }\n        var cText = atob(c);\n        var serverKey = await this.hd(cText, s);\n        return serverKey;\n    }\n    async eda(data) {\n        var s = await this.dsk();\n        var sText = atob(s);\n        if (!s) {\n            return \"\";\n        }\n        var encryptedData = await this.he(sText, data);\n        return encryptedData;\n    }\n    async dda(encryptedData) {\n        var c = this.gc('c');\n        if (!c) {\n            return \"\";\n        }\n        var cText = atob(c);\n        var decryptedData = await this.hd(cText, encryptedData);\n        return decryptedData;\n    }\n    async csi() {\n        try {\n            const response = await fetchWithDeviceId(apiUrl + '/api/Crypto/check-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: null\n            });\n            if (!response.ok) {\n                throw new Error('Network response was not ok');\n            }\n            const responseData = await response.json();\n            console.log(responseData);\n        }\n        catch (error) {\n            console.error('Error in csi:', error);\n        }\n    }\n    async iih() {\n        if (!this.ch()) {\n            await this.spu();\n        }\n    }\n    ch() {\n        if (this.gc('s') && this.gc('c')) {\n            return true;\n        }\n        return false;\n    }\n    async wk() {\n        let retries = 10;\n        while (!this.gc('s') && retries > 0) {\n            await new Promise(resolve => setTimeout(resolve, 200));\n            retries--;\n        }\n    }\n    async gdi() {\n        const fp = await FingerprintJS.load();\n        const result = await fp.get();\n        return result.visitorId; // Device ID duy nhất\n    }\n}\n", "import { environment } from \"../environments/environment\";\nimport { fetchWithDeviceIdandApiKey } from \"../utils/deviceUtils\";\nconst apiUrl = environment.apiUrl;\nexport class FlightService {\n    async request(endpoint, request, useDeviceId = true, apiKey) {\n        try {\n            const fetchFn = useDeviceId ? fetchWithDeviceIdandApiKey : fetch;\n            const response = await fetchFn(`${apiUrl}/api/Library/${endpoint}`, {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' },\n                body: JSON.stringify(request)\n            }, apiKey);\n            if (!response.ok) {\n                throw response;\n            }\n            return await response.json();\n        }\n        catch (error) {\n            throw error;\n        }\n    }\n    SearchTrip(request, api) { return this.request('SearchTrip', request, true, api); }\n    PriceAncillary(request, api) { return this.request('PriceAncillary', request, true, api); }\n    FareRules(request, language) { return this.request('../FareRules/get-fare-rules/' + language, request, false, ''); }\n    AvailableTrip(request, api) { return this.request('AvailableTrip', request, true, api); }\n    RequestTrip(request, api) { return this.request('RequestTrip', request, true, api); }\n    RePayment(request, api) { return this.request('RePayment', request, true, api); }\n}\n", "import { environment } from \"../environments/environment\";\nimport { fetchWithDeviceIdandApiKey } from \"../utils/deviceUtils\";\nconst apiUrl = environment.apiUrl;\nexport const getAirportInfoByCode = async (airportsCode, language, apiKey) => {\n    const requestBody = {\n        airportsCode: airportsCode.join(';'),\n        language: language\n    };\n    try {\n        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/airport-info`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(requestBody)\n        }, apiKey);\n        if (!response.ok) {\n            throw response;\n        }\n        return await response.json();\n    }\n    catch (error) {\n        throw error;\n    }\n};\nexport const phones = async () => {\n    const response = await fetch(`${apiUrl}/api/World/phones`, {\n        method: 'GET'\n    });\n    return response.json();\n};\nexport const getAirportsDefault = async (language, apiKey) => {\n    const requestBody = {\n        language: language\n    };\n    try {\n        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/airports-default`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(requestBody)\n        }, apiKey);\n        if (!response.ok) {\n            throw response;\n        }\n        return await response.json();\n    }\n    catch (error) {\n        throw error;\n    }\n};\nexport const getFeatures = async (features, apiKey) => {\n    try {\n        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/feature/${features}`, {\n            method: 'GET',\n            headers: { 'Content-Type': 'application/json' },\n        }, apiKey);\n        if (!response.ok) {\n            throw response;\n        }\n        return await response.json();\n    }\n    catch (error) {\n        throw error;\n    }\n};\nexport const searchAirport = async (request) => {\n    const requestBody = JSON.stringify(request);\n    const response = await fetch(`${apiUrl}/api/World/flight/airport-search`, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: requestBody\n    });\n    return response.json();\n};\n", "export const colors = {\n    inherit: 'inherit',\n    current: 'currentColor',\n    transparent: 'transparent',\n    black: '#000',\n    white: '#fff',\n    slate: {\n        '50': '#f8fafc',\n        '100': '#f1f5f9',\n        '200': '#e2e8f0',\n        '300': '#cbd5e1',\n        '400': '#94a3b8',\n        '500': '#64748b',\n        '600': '#475569',\n        '700': '#334155',\n        '800': '#1e293b',\n        '900': '#0f172a',\n        '950': '#020617'\n    },\n    gray: {\n        '50': '#f9fafb',\n        '100': '#f3f4f6',\n        '200': '#e5e7eb',\n        '300': '#d1d5db',\n        '400': '#9ca3af',\n        '500': '#6b7280',\n        '600': '#4b5563',\n        '700': '#374151',\n        '800': '#1f2937',\n        '900': '#111827',\n        '950': '#030712'\n    },\n    zinc: {\n        '50': '#fafafa',\n        '100': '#f4f4f5',\n        '200': '#e4e4e7',\n        '300': '#d4d4d8',\n        '400': '#a1a1aa',\n        '500': '#71717a',\n        '600': '#52525b',\n        '700': '#3f3f46',\n        '800': '#27272a',\n        '900': '#18181b',\n        '950': '#09090b'\n    },\n    neutral: {\n        '50': '#fafafa',\n        '100': '#f5f5f5',\n        '200': '#e5e5e5',\n        '300': '#d4d4d4',\n        '400': '#a3a3a3',\n        '500': '#737373',\n        '600': '#525252',\n        '700': '#404040',\n        '800': '#262626',\n        '900': '#171717',\n        '950': '#0a0a0a'\n    },\n    stone: {\n        '50': '#fafaf9',\n        '100': '#f5f5f4',\n        '200': '#e7e5e4',\n        '300': '#d6d3d1',\n        '400': '#a8a29e',\n        '500': '#78716c',\n        '600': '#57534e',\n        '700': '#44403c',\n        '800': '#292524',\n        '900': '#1c1917',\n        '950': '#0c0a09',\n    },\n    red: {\n        '50': '#fef2f2',\n        '100': '#fee2e2',\n        '200': '#fecaca',\n        '300': '#fca5a5',\n        '400': '#f87171',\n        '500': '#ef4444',\n        '600': '#dc2626',\n        '700': '#b91c1c',\n        '800': '#991b1b',\n        '900': '#7f1d1d',\n        '950': '#450a0a'\n    },\n    orange: {\n        '50': '#fff7ed',\n        '100': '#ffedd5',\n        '200': '#fed7aa',\n        '300': '#fdba74',\n        '400': '#fb923c',\n        '500': '#f97316',\n        '600': '#ea580c',\n        '700': '#c2410c',\n        '800': '#9a3412',\n        '900': '#7c2d12',\n        '950': '#431407'\n    },\n    amber: {\n        '50': '#fffbeb',\n        '100': '#fef3c7',\n        '200': '#fde68a',\n        '300': '#fcd34d',\n        '400': '#fbbf24',\n        '500': '#f59e0b',\n        '600': '#d97706',\n        '700': '#b45309',\n        '800': '#92400e',\n        '900': '#78350f',\n        '950': '#451a03'\n    },\n    yellow: {\n        '50': '#fefce8',\n        '100': '#fef9c3',\n        '200': '#fef08a',\n        '300': '#fde047',\n        '400': '#facc15',\n        '500': '#eab308',\n        '600': '#ca8a04',\n        '700': '#a16207',\n        '800': '#854d0e',\n        '900': '#713f12',\n        '950': '#422006'\n    },\n    lime: {\n        '50': '#f7fee7',\n        '100': '#ecfccb',\n        '200': '#d9f99d',\n        '300': '#bef264',\n        '400': '#a3e635',\n        '500': '#84cc16',\n        '600': '#65a30d',\n        '700': '#4d7c0f',\n        '800': '#3f6212',\n        '900': '#365314',\n        '950': '#1a2e05',\n    },\n    green: {\n        '50': '#f0fdf4',\n        '100': '#dcfce7',\n        '200': '#bbf7d0',\n        '300': '#86efac',\n        '400': '#4ade80',\n        '500': '#22c55e',\n        '600': '#16a34a',\n        '700': '#15803d',\n        '800': '#166534',\n        '900': '#14532d',\n        '950': '#052e16'\n    },\n    emerald: {\n        '50': '#ecfdf5',\n        '100': '#d1fae5',\n        '200': '#a7f3d0',\n        '300': '#6ee7b7',\n        '400': '#34d399',\n        '500': '#10b981',\n        '600': '#059669',\n        '700': '#047857',\n        '800': '#065f46',\n        '900': '#064e3b',\n        '950': '#022c22'\n    },\n    teal: {\n        '50': '#f0fdfa',\n        '100': '#ccfbf1',\n        '200': '#99f6e4',\n        '300': '#5eead4',\n        '400': '#2dd4bf',\n        '500': '#14b8a6',\n        '600': '#0d9488',\n        '700': '#0f766e',\n        '800': '#115e59',\n        '900': '#134e4a',\n        '950': '#042f2e'\n    },\n    cyan: {\n        '50': '#ecfeff',\n        '100': '#cffafe',\n        '200': '#a5f3fc',\n        '300': '#67e8f9',\n        '400': '#22d3ee',\n        '500': '#06b6d4',\n        '600': '#0891b2',\n        '700': '#0e7490',\n        '800': '#155e75',\n        '900': '#164e63',\n        '950': '#083344'\n    },\n    sky: {\n        '50': '#f0f9ff',\n        '100': '#e0f2fe',\n        '200': '#bae6fd',\n        '300': '#7dd3fc',\n        '400': '#38bdf8',\n        '500': '#0ea5e9',\n        '600': '#0284c7',\n        '700': '#0369a1',\n        '800': '#075985',\n        '900': '#0c4a6e',\n        '950': '#082f49'\n    },\n    blue: {\n        '50': '#eff6ff',\n        '100': '#dbeafe',\n        '200': '#bfdbfe',\n        '300': '#93c5fd',\n        '400': '#60a5fa',\n        '500': '#3b82f6',\n        '600': '#2563eb',\n        '700': '#1d4ed8',\n        '800': '#1e40af',\n        '900': '#1e3a8a',\n        '950': '#172554'\n    },\n    indigo: {\n        '50': '#eef2ff',\n        '100': '#e0e7ff',\n        '200': '#c7d2fe',\n        '300': '#a5b4fc',\n        '400': '#818cf8',\n        '500': '#6366f1',\n        '600': '#4f46e5',\n        '700': '#4338ca',\n        '800': '#3730a3',\n        '900': '#312e81',\n        '950': '#1e1b4b'\n    },\n    violet: {\n        '50': '#f5f3ff',\n        '100': '#ede9fe',\n        '200': '#ddd6fe',\n        '300': '#c4b5fd',\n        '400': '#a78bfa',\n        '500': '#8b5cf6',\n        '600': '#7c3aed',\n        '700': '#6d28d9',\n        '800': '#5b21b6',\n        '900': '#4c1d95',\n        '950': '#2e1065'\n    },\n    purple: {\n        '50': '#faf5ff',\n        '100': '#f3e8ff',\n        '200': '#e9d5ff',\n        '300': '#d8b4fe',\n        '400': '#c084fc',\n        '500': '#a855f7',\n        '600': '#9333ea',\n        '700': '#7e22ce',\n        '800': '#6b21a8',\n        '900': '#581c87',\n        '950': '#3b0764'\n    },\n    fuchsia: {\n        '50': '#fdf4ff',\n        '100': '#fae8ff',\n        '200': '#f5d0fe',\n        '300': '#f0abfc',\n        '400': '#e879f9',\n        '500': '#d946ef',\n        '600': '#c026d3',\n        '700': '#a21caf',\n        '800': '#86198f',\n        '900': '#701a75',\n        '950': '#4a044e',\n    },\n    pink: {\n        '50': '#fdf2f8',\n        '100': '#fce7f3',\n        '200': '#fbcfe8',\n        '300': '#f9a8d4',\n        '400': '#f472b6',\n        '500': '#ec4899',\n        '600': '#db2777',\n        '700': '#be185d',\n        '800': '#9d174d',\n        '900': '#831843',\n        '950': '#500724'\n    },\n    rose: {\n        '50': '#fff1f2',\n        '100': '#ffe4e6',\n        '200': '#fecdd3',\n        '300': '#fda4af',\n        '400': '#fb7185',\n        '500': '#f43f5e',\n        '600': '#e11d48',\n        '700': '#be123c',\n        '800': '#9f1239',\n        '900': '#881337',\n        '950': '#4c0519'\n    }\n};\n", "import { colors } from \"../interface/DefaultColors\";\n/**\n * Thi<PERSON><PERSON> lập các biến CSS đại diện cho màu \"nmt\"\n * @param baseColor Tên màu trong Tailwind (vd: 'blue', 'rose') hoặc mã màu hex (vd: '#3b82f6')\n */\nexport function setnmtColors(baseColor) {\n    console.log('baseColor', baseColor);\n    // Nếu baseColor là object chứa các giá trị màu đã được tính toán sẵn\n    try {\n        const parsed = JSON.parse(baseColor);\n        if (typeof parsed === 'object') {\n            const root = document.documentElement;\n            Object.entries(parsed).forEach(([key, value]) => {\n                root.style.setProperty(`--color-nmt-${key}`, value);\n            });\n            return;\n        }\n    }\n    catch (e) {\n    }\n    // Lấy màu hex từ tên màu Tailwind hoặc chuỗi hex\n    const getHexColor = (baseColor) => {\n        // Kiểm tra xem baseColor có phải là mã hex không\n        if (baseColor.startsWith(\"#\")) {\n            return baseColor;\n        }\n        // Kiểm tra xem baseColor có phải là tên màu trong Tailwind không\n        const color = colors[baseColor];\n        if (color) {\n            // Lấy màu chính (500) từ danh sách màu\n            return color[\"500\"];\n        }\n        // Nếu không phải tên màu hợp lệ, trả về màu mặc định (màu chính của Tailwind)\n        // return colors.blue[\"500\"]; // Màu xanh dương mặc định\n        return colors.orange[\"500\"]; // Màu cam mặc định\n    };\n    // Làm sáng màu\n    const lighten = (hex, percent) => {\n        const num = parseInt(hex.replace(\"#\", \"\"), 16);\n        const amt = Math.round(2.55 * percent);\n        const r = Math.min(255, Math.max(0, (num >> 16) + amt));\n        const g = Math.min(255, Math.max(0, ((num >> 8) & 0xff) + amt));\n        const b = Math.min(255, Math.max(0, (num & 0xff) + amt));\n        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;\n    };\n    // Làm tối màu\n    const darken = (hex, percent) => {\n        const num = parseInt(hex.replace(\"#\", \"\"), 16);\n        const amt = Math.round(2.55 * percent);\n        const r = Math.min(255, Math.max(0, (num >> 16) - amt));\n        const g = Math.min(255, Math.max(0, ((num >> 8) & 0xff) - amt));\n        const b = Math.min(255, Math.max(0, (num & 0xff) - amt));\n        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;\n    };\n    // Lấy màu gốc\n    const hexColor = getHexColor(baseColor);\n    // Các sắc độ màu\n    const shades = {\n        \"50\": lighten(hexColor, 50),\n        \"100\": lighten(hexColor, 40),\n        \"200\": lighten(hexColor, 30),\n        \"300\": lighten(hexColor, 20),\n        \"400\": lighten(hexColor, 10),\n        \"500\": hexColor,\n        \"600\": darken(hexColor, 10),\n        \"700\": darken(hexColor, 20),\n        \"800\": darken(hexColor, 30),\n        \"900\": darken(hexColor, 40),\n        \"950\": darken(hexColor, 50),\n    };\n    // Lấy root element\n    const root = document.documentElement;\n    // Gán vào biến CSS\n    Object.entries(shades).forEach(([key, value]) => {\n        root.style.setProperty(`--color-nmt-${key}`, value);\n    });\n}\n", "import { __decorate, __metadata } from \"tslib\";\nimport { css, LitElement, unsafeCSS } from \"lit\";\nimport { customElement, property, state } from \"lit/decorators.js\";\nimport styles from '../../styles/styles.css';\nimport { TripAvailableTemplate } from \"./trip-available-template\";\nimport { CryptoService } from \"../../services/CryptoService\";\nimport { FlightService } from \"../../services/FlightService\";\nimport { getAirportInfoByCode } from \"../../services/WorldServices\";\nimport { formatDateTo_ddMMyyyy, getDurationByArray, convertDurationToHour, getTimeFromDateTime, getDayInWeek, formatddMMyyyy, getDuration } from \"../../utils/dateUtils\";\nimport { setnmtColors } from \"../../services/ColorService\";\nconst cryptoService = new CryptoService();\nconst flightService = new FlightService();\nlet TripAvailable = class TripAvailable extends LitElement {\n    static { this.styles = [\n        unsafeCSS(styles),\n        css `\r\n        :host {\r\n          font-family: var(--nmt-font, 'Roboto', sans-serif);\r\n        }\r\n  \r\n          `\n    ]; }\n    get language() {\n        return this._language;\n    }\n    set language(value) {\n        const oldValue = this._language;\n        // Chỉ kiểm tra URL nếu autoLanguageParam được bật\n        if (this.autoLanguageParam) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const languageParam = urlParams.get('language');\n            if (languageParam && languageParam !== this._language) {\n                // URL có language parameter - luôn ưu tiên URL\n                this._language = languageParam;\n                console.log('Language overridden from URL parameter:', this._language);\n            }\n            else {\n                // URL không có language parameter - sử dụng giá trị được set\n                this._language = value;\n                console.log('Language set from property:', this._language);\n                // Tự động thêm vào URL nếu chưa có\n                if (!this._hasCheckedURL) {\n                    this.updateURLWithLanguage();\n                    this._hasCheckedURL = true;\n                }\n            }\n        }\n        else {\n            // Không sử dụng URL parameter, chỉ set giá trị trực tiếp\n            this._language = value;\n            console.log('Language set from property (autoLanguageParam disabled):', this._language);\n        }\n        this.requestUpdate('language', oldValue);\n    }\n    get currencySymbolAv() {\n        return this.convertedVND === 1 || this.language === 'vi' ? '₫' : this.currencySymbol;\n    }\n    constructor() {\n        super();\n        this.autoFillOrderCode = false;\n        this.uri_searchBox = \"\";\n        this.showLanguageSelect = false;\n        this.autoLanguageParam = false;\n        this.ApiKey = '';\n        this.color = \"\";\n        this.mode = \"online\";\n        this.googleFontsUrl = \"\";\n        this.font = \"\";\n        this._ApiKey = '';\n        this.isLoading = false;\n        this.isNotValid = false;\n        this.orderAvailable = null;\n        this.orderDetails = null;\n        this.inforAirports = [];\n        this.bankSelected = '';\n        this.errorString = '';\n        this.formSubmitted = false;\n        this.orderCode = '';\n        this.contact = '';\n        this._NoteModel = null;\n        this._PaymentNote = null;\n        this.convertedVND = 1;\n        this.currencySymbol = '₫';\n        this.request = {\n            OrderCode: '',\n            PhoneCustomer: '',\n            EmailCustomer: ''\n        };\n        this._language = \"vi\";\n        this._hasCheckedURL = false;\n        this._cryptoService = cryptoService;\n        this._flightService = flightService;\n        this.checkLanguageFromURL();\n        this.checkUrlParams();\n    }\n    async firstUpdated(_changedProperties) {\n        super.firstUpdated(_changedProperties);\n        if (this.color !== \"\") {\n            setnmtColors(this.color);\n            this.requestUpdate();\n        }\n        console.log(this.googleFontsUrl);\n        // Handle Google Fonts\n        if (this.googleFontsUrl) {\n            const googleFontsLink = document.createElement('link');\n            googleFontsLink.rel = 'stylesheet';\n            googleFontsLink.href = this.googleFontsUrl;\n            document.head.appendChild(googleFontsLink);\n        }\n        else {\n            // Default font if no Google Fonts URL provided\n            const googleFontsLink = document.createElement('link');\n            googleFontsLink.rel = 'stylesheet';\n            googleFontsLink.href = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap';\n            document.head.appendChild(googleFontsLink);\n        }\n        console.log('font', this.font);\n        if (this.font !== \"\") {\n            const root = document.documentElement;\n            root.style.setProperty('--nmt-font', this.font);\n        }\n    }\n    connectedCallback() {\n        super.connectedCallback();\n        this._ApiKey = this.ApiKey;\n        this.removeAttribute(\"ApiKey\");\n        // Kiểm tra URL ngay khi component được kết nối, trường hợp không có attribute language\n        this.checkLanguageFromURL();\n    }\n    checkLanguageFromURL() {\n        if (!this.autoLanguageParam) {\n            console.log('autoLanguageParam disabled, skipping URL check');\n            return;\n        }\n        const urlParams = new URLSearchParams(window.location.search);\n        const languageParam = urlParams.get('language');\n        if (languageParam) {\n            this._language = languageParam;\n            console.log('Language initialized from URL parameter:', this._language);\n            this.requestUpdate('language');\n        }\n        else if (!this._hasCheckedURL) {\n            this.updateURLWithLanguage();\n            this._hasCheckedURL = true;\n        }\n    }\n    updateURLWithLanguage() {\n        const currentUrl = new URL(window.location.href);\n        const params = new URLSearchParams(currentUrl.search);\n        params.set('language', this._language);\n        const newUrl = `${currentUrl.pathname}?${params.toString()}`;\n        window.history.replaceState({}, '', newUrl);\n        console.log('URL updated with language parameter:', newUrl);\n    }\n    checkUrlParams() {\n        const urlParams = new URLSearchParams(window.location.search);\n        this.request.OrderCode = urlParams.get('OrderCode') || '';\n        this.request.PhoneCustomer = urlParams.get('Contact') || '';\n        this.request.EmailCustomer = urlParams.get('Contact') || '';\n        if (this.request.OrderCode && this.request.PhoneCustomer && this.request.EmailCustomer) {\n            this.AvailableTrip(this.request);\n        }\n        else {\n            this.orderAvailable = null;\n        }\n    }\n    updated(_changedProperties) {\n        super.updated(_changedProperties);\n    }\n    async onSubmitForm(e) {\n        e.preventDefault(); // Prevent form from submitting and reloading page\n        this.formSubmitted = true;\n        if (!this.orderCode || !this.contact) {\n            this.errorString = 'Vui lòng nhập đầy đủ thông tin.';\n            return;\n        }\n        this.errorString = '';\n        // Update URL parameters without page reload\n        const params = new URLSearchParams();\n        params.append('OrderCode', this.orderCode);\n        params.append('Contact', this.contact);\n        const currentUrl = new URL(window.location.href);\n        currentUrl.search = params.toString();\n        window.history.pushState({}, '', currentUrl.toString());\n        // Update the request and call AvailableTrip\n        this.request.OrderCode = this.orderCode;\n        this.request.PhoneCustomer = this.contact;\n        this.request.EmailCustomer = this.contact;\n        await this.AvailableTrip(this.request);\n    }\n    async RequestEncrypt(data) {\n        const encryptedData = await this._cryptoService.eda(JSON.stringify(data));\n        return {\n            EncryptData: encryptedData\n        };\n    }\n    rePayment() {\n        const params = new URLSearchParams();\n        params.append('OrderCode', this.request.OrderCode);\n        params.append('PhoneCustomer', this.request.PhoneCustomer);\n        params.append('EmailCustomer', this.request.EmailCustomer);\n        // Navigate to repayment page\n        const currentUrl = new URL(window.location.href);\n        currentUrl.pathname = '/TripRePayment';\n        currentUrl.search = params.toString();\n        window.location.href = currentUrl.toString();\n    }\n    async CallAvailableTrip(request) {\n        this.isLoading = true;\n        try {\n            const payloadsEncrypted = await this.RequestEncrypt(request);\n            const res = await this._flightService.AvailableTrip(payloadsEncrypted, this._ApiKey);\n            const resDecrypted = await this._cryptoService.dda(res.resultObj);\n            const resJson = JSON.parse(resDecrypted);\n            console.log(resJson);\n            if (resJson.IsSuccessed) {\n                const noteData = JSON.parse(resJson.ResultObj.Note);\n                this.orderDetails = noteData;\n                this.isNotValid = true;\n                this.orderAvailable = resJson.ResultObj;\n                this._PaymentNote = JSON.parse(resJson.ResultObj.PaymentNote);\n                console.log('this._PaymentNote', this._PaymentNote);\n                this._NoteModel = JSON.parse(resJson.ResultObj.NoteResult);\n                this.formatPassenger();\n                await this.getInforAirports();\n                if (this.orderAvailable?.PaymentMethod.includes('bank-transfer')) {\n                    this.bankSelected = this.orderAvailable?.PaymentMethod.split('_')[1];\n                }\n            }\n            else {\n                this.errorString = 'Không tìm thấy thông tin đơn hàng này';\n            }\n        }\n        catch (error) {\n            console.error('Error in CallAvailableTrip:', error);\n            if (error.status !== 200) {\n                this._cryptoService.ra();\n                await this._cryptoService.spu();\n                await this.CallAvailableTrip(request);\n            }\n            else {\n                this.errorString = 'Có lỗi xảy ra khi tìm kiếm đơn hàng';\n            }\n        }\n        finally {\n            this.isLoading = false;\n        }\n    }\n    formatPassenger() {\n        if (!this.orderDetails?.paxList)\n            return;\n        let indexInfant = 0;\n        this.orderDetails.paxList.forEach((pax, index) => {\n            if (pax.type === 'infant') {\n                const paxAdult = this.orderDetails.paxList.find((p) => p.type === 'adult' && p.index === indexInfant);\n                if (paxAdult) {\n                    paxAdult.withInfant = pax;\n                    this.orderDetails.paxList.splice(index, 1);\n                }\n                indexInfant++;\n            }\n            else {\n                pax.index = index;\n            }\n        });\n    }\n    async AvailableTrip(request) {\n        if (!this._cryptoService.ch()) {\n            await this._cryptoService.spu();\n        }\n        await this.CallAvailableTrip(request);\n    }\n    async getInforAirports() {\n        if (!this.orderDetails?.full?.InventoriesSelected)\n            return;\n        const airportsCode = [];\n        this.orderDetails.full.InventoriesSelected.forEach((inventory) => {\n            inventory.segment.Legs.forEach((leg) => {\n                if (!airportsCode.includes(leg.DepartureCode)) {\n                    airportsCode.push(leg.DepartureCode);\n                }\n                if (!airportsCode.includes(leg.ArrivalCode)) {\n                    airportsCode.push(leg.ArrivalCode);\n                }\n            });\n        });\n        try {\n            var res = await getAirportInfoByCode(airportsCode, this.language || 'vi', this._ApiKey);\n            if (res.isSuccessed) {\n                this.inforAirports = res.resultObj;\n                const currencyObj = typeof res.feature.currency === 'string' ? JSON.parse(res.feature.currency) : res.feature.currency;\n                this.currencySymbol = currencyObj.symbol || '₫';\n                this.convertedVND = currencyObj.convertedVND || 1;\n            }\n            console.log('mode', this.mode);\n            if (this.mode === \"online\") {\n                if (res.feature?.color) {\n                    this.color = res.feature.color;\n                    if (this.color !== \"\") {\n                        setnmtColors(this.color);\n                        this.requestUpdate();\n                    }\n                }\n            }\n        }\n        catch (error) {\n            console.error('Error getting airport info:', error);\n        }\n    }\n    // Form input handlers\n    onOrderCodeChange(e) {\n        const target = e.target;\n        this.orderCode = target.value;\n    }\n    onContactChange(e) {\n        const target = e.target;\n        this.contact = target.value;\n    }\n    // Utility methods\n    formatDateTo_ddMMyyyy(date) {\n        return formatDateTo_ddMMyyyy(new Date(date), this.language);\n    }\n    getDurationByArray(legs) {\n        return getDurationByArray(legs);\n    }\n    convertDurationToHour(duration) {\n        return convertDurationToHour(duration);\n    }\n    getTimeFromDateTime(dateTime) {\n        return getTimeFromDateTime(dateTime);\n    }\n    getDayInWeek(date) {\n        return getDayInWeek(date);\n    }\n    formatddMMyyyy(date) {\n        return formatddMMyyyy(date);\n    }\n    getDuration(leg) {\n        return getDuration(leg);\n    }\n    handleLanguageChange(newLang) {\n        console.log('newLang', newLang);\n        this.language = newLang;\n        this.getInforAirports();\n        // Tự động cập nhật URL với language mới\n        this.updateURLWithLanguage();\n        this.requestUpdate();\n    }\n    render() {\n        return TripAvailableTemplate(this.autoFillOrderCode, this.language, this.uri_searchBox, this.showLanguageSelect, this.isLoading, this.orderAvailable, this.isNotValid, this.orderDetails, this.inforAirports, this.bankSelected, this.errorString, this.formSubmitted, this.orderCode, this.contact, this.request, this._PaymentNote, this._NoteModel, this.currencySymbolAv, this.convertedVND, {\n            onSubmitForm: this.onSubmitForm.bind(this),\n            onOrderCodeChange: this.onOrderCodeChange.bind(this),\n            onContactChange: this.onContactChange.bind(this),\n            rePayment: this.rePayment.bind(this),\n            formatDateTo_ddMMyyyy: this.formatDateTo_ddMMyyyy.bind(this),\n            getDurationByArray: this.getDurationByArray.bind(this),\n            convertDurationToHour: this.convertDurationToHour.bind(this),\n            getTimeFromDateTime: this.getTimeFromDateTime.bind(this),\n            getDayInWeek: this.getDayInWeek.bind(this),\n            formatddMMyyyy: this.formatddMMyyyy.bind(this),\n            getDuration: this.getDuration.bind(this),\n            handleLanguageChange: this.handleLanguageChange.bind(this),\n        });\n    }\n};\n__decorate([\n    property({ type: Boolean }),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"autoFillOrderCode\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"uri_searchBox\", void 0);\n__decorate([\n    property({ type: Boolean }),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"showLanguageSelect\", void 0);\n__decorate([\n    property({ type: Boolean }),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"autoLanguageParam\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"ApiKey\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"color\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"mode\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"googleFontsUrl\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"font\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripAvailable.prototype, \"_ApiKey\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripAvailable.prototype, \"isLoading\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripAvailable.prototype, \"isNotValid\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"orderAvailable\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"orderDetails\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Array)\n], TripAvailable.prototype, \"inforAirports\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripAvailable.prototype, \"bankSelected\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripAvailable.prototype, \"errorString\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripAvailable.prototype, \"formSubmitted\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripAvailable.prototype, \"orderCode\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripAvailable.prototype, \"contact\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"_NoteModel\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripAvailable.prototype, \"_PaymentNote\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Number)\n], TripAvailable.prototype, \"convertedVND\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripAvailable.prototype, \"currencySymbol\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", String),\n    __metadata(\"design:paramtypes\", [String])\n], TripAvailable.prototype, \"language\", null);\nTripAvailable = __decorate([\n    customElement(\"trip-available\"),\n    __metadata(\"design:paramtypes\", [])\n], TripAvailable);\nexport { TripAvailable };\n"], "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "this", "__decorate", "decorators", "target", "key", "desc", "d", "c", "r", "getOwnPropertyDescriptor", "Reflect", "decorate", "defineProperty", "__metadata", "metadataKey", "metadataValue", "metadata", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "value", "step", "next", "e", "rejected", "throw", "result", "done", "then", "__generator", "body", "f", "y", "_", "label", "sent", "trys", "ops", "g", "create", "Iterator", "verb", "return", "Symbol", "iterator", "v", "op", "TypeError", "pop", "push", "__spread<PERSON><PERSON>y", "to", "from", "pack", "ar", "l", "Array", "slice", "concat", "globalThis", "ShadowRoot", "ShadyCSS", "nativeShadow", "Document", "CSSStyleSheet", "o", "WeakMap", "n$3", "constructor", "_$cssResult$", "Error", "cssText", "styleSheet", "let", "get", "replaceSync", "set", "toString", "cssRules", "is", "getOwnPropertyNames", "h", "getOwnPropertySymbols", "getPrototypeOf", "a", "trustedTypes", "emptyScript", "reactiveElementPolyfillSupport", "u", "toAttribute", "Boolean", "JSON", "stringify", "fromAttribute", "Number", "parse", "attribute", "type", "String", "converter", "reflect", "has<PERSON><PERSON>ed", "litPropertyMetadata", "b", "HTMLElement", "addInitializer", "_$Ei", "observedAttributes", "finalize", "_$Eh", "keys", "createProperty", "state", "elementProperties", "noAccessor", "getPropertyDescriptor", "requestUpdate", "configurable", "enumerable", "getPropertyOptions", "Map", "finalized", "properties", "_$Eu", "elementStyles", "finalizeStyles", "styles", "isArray", "Set", "flat", "reverse", "unshift", "toLowerCase", "super", "_$Ep", "isUpdatePending", "hasUpdated", "_$Em", "_$Ev", "_$ES", "enableUpdating", "_$AL", "_$E_", "for<PERSON>ach", "addController", "_$EO", "add", "renderRoot", "isConnected", "hostConnected", "removeController", "delete", "size", "createRenderRoot", "shadowRoot", "attachShadow", "shadowRootOptions", "adoptedStyleSheets", "map", "document", "createElement", "litNonce", "setAttribute", "textContent", "append<PERSON><PERSON><PERSON>", "connectedCallback", "disconnectedCallback", "hostDisconnected", "attributeChangedCallback", "_$AK", "_$EC", "removeAttribute", "_$ET", "has", "_$Ej", "scheduleUpdate", "performUpdate", "wrapped", "shouldUpdate", "willUpdate", "hostUpdate", "update", "_$EU", "_$AE", "hostUpdated", "firstUpdated", "updated", "updateComplete", "getUpdateComplete", "mode", "ReactiveElement", "reactiveElementVersions", "createPolicy", "createHTML", "Math", "random", "toFixed", "createComment", "m", "RegExp", "$", "x", "_$litType$", "strings", "values", "T", "for", "E", "A", "C", "createTreeWalker", "V", "lastIndex", "exec", "test", "startsWith", "N", "parts", "el", "currentNode", "content", "<PERSON><PERSON><PERSON><PERSON>", "replaceWith", "childNodes", "nextNode", "nodeType", "hasAttributes", "getAttributeNames", "endsWith", "getAttribute", "split", "index", "name", "ctor", "H", "I", "L", "k", "tagName", "append", "data", "indexOf", "innerHTML", "S", "_$Co", "_$Cl", "_$litDirective$", "_$AO", "_$AT", "_$AS", "M$2", "_$AV", "_$AN", "_$AD", "_$AM", "parentNode", "_$AU", "creationScope", "importNode", "R", "nextS<PERSON>ling", "z", "_$AI", "_$Cv", "_$AH", "_$AA", "_$AB", "options", "startNode", "endNode", "_$AR", "O", "insertBefore", "createTextNode", "_$AC", "M", "_$AP", "remove", "setConnected", "element", "fill", "j", "toggleAttribute", "capture", "once", "passive", "removeEventListener", "addEventListener", "handleEvent", "host", "litHtmlPolyfillSupport", "B", "litHtmlVersions", "renderBefore", "_$litPart$", "r$2", "renderOptions", "_$Do", "render", "_$litElement$", "litElementHydrateSupport", "LitElement", "litElementPolyfillSupport", "litElementVersions", "customElements", "define", "kind", "init", "formatDateTo_ddMMyyyy", "date", "language", "month", "undefined", "date1", "Date", "day", "year", "getDurationByArray", "legs", "duration", "minutes", "departure", "hours", "getTimeFromDateTime", "dateTime", "convertDurationToHour", "getDuration", "leg", "formatddMMyyyy", "date<PERSON><PERSON>j", "getDayInWeek", "_0x2173", "_0x3b98bf", "_0x528116", "_0x16ff", "_0x4e51ab", "_0x5257cd", "_0x217387", "_0x16ff68", "_0x5b19ac", "_0x5377fb", "_0x1a2555", "parseInt", "shift", "_0x2c0d07", "environment", "apiUrl", "TripAvailableTemplate", "autoFillOrderCode", "uri_searchBox", "showLanguageSelect", "isLoading", "orderAvailable", "isNotValid", "orderDetails", "inforAirports", "errorString", "formSubmitted", "orderCode", "contact", "_PaymentNote", "__<PERSON><PERSON><PERSON><PERSON>", "_currencySymbol", "_convertedVND", "handlers", "console", "html", "_templateObject", "_taggedTemplateLiteral", "_templateObject2", "_templateObject7", "_templateObject8", "_templateObject9", "_templateObject10", "_templateObject11", "_templateObject12", "_templateObject13", "formatNumber", "convertedVND", "integerPart", "_result$toFixed$split2", "_slicedToArray", "decimalPart", "formattedInteger", "_templateObject14", "_PaymentNote$banksInf", "_PaymentNote$banksInf2", "_PaymentNote$banksInf3", "_PaymentNote$banksInf4", "_PaymentNote$banksInf5", "_templateObject15", "_PaymentNote$banksInf6", "_PaymentNote$banksInf7", "_templateObject16", "_orderAvailable$Payme", "_templateObject17", "_orderAvailable$Payme2", "_orderAvailable$Payme3", "_templateObject18", "pax", "_pax$withInfant6", "_templateObject19", "_templateObject20", "_pax$withInfant", "_pax$withInfant2", "_pax$withInfant3", "_pax$withInfant4", "_pax$withInfant5", "baggage", "_templateObject21", "_templateObject22", "formatDateToString", "_typeof", "parsed", "isNaN", "dd", "mm", "yyyy", "_inforAirports$orderA", "_inforAirports$orderA2", "_templateObject23", "_orderDetails$full", "_templateObject24", "_orderDetails$full2", "itinerarySelected", "_orderDetails$full3", "_itinerarySelected$se", "_templateObject25", "_templateObject26", "$index", "_itinerarySelected$in2", "_templateObject27", "_templateObject28", "_inforAirports$leg$De", "_inforAirports$leg$De2", "_itinerarySelected$in", "_inforAirports$leg$Ar", "_templateObject29", "_orderDetails$full4", "_templateObject30", "_orderDetails$full5", "_templateObject31", "_inforAirports$leg$De3", "_itinerarySelected$in11", "_templateObject32", "_inforAirports$leg$Ar2", "_inforAirports$leg$De4", "_inforAirports$leg$Ar3", "_itinerarySelected$in3", "_itinerarySelected$in4", "_templateObject33", "_itinerarySelected$in5", "_itinerarySelected$in6", "_itinerarySelected$in7", "_itinerarySelected$in8", "_templateObject34", "_itinerarySelected$in9", "_itinerarySelected$in10", "_templateObject35", "_templateObject36", "_templateObject37", "_NoteModel$TimeCheck", "_NoteModel$TimeCheck2", "_NoteModel$IdentityD", "identityDoc", "_templateObject38", "_NoteModel$SpecialRu", "specialRule", "_templateObject39", "_templateObject40", "_templateObject3", "_templateObject4", "_handlers$onSubmitFor", "_templateObject5", "_templateObject6", "wait", "durationMs", "resolveWith", "setTimeout", "isPromise", "awaitIfAsync", "action", "callback", "returnedValue", "error", "mapWithBreaks", "items", "loopReleaseInterval", "results", "lastLoopReleaseTime", "now", "_a", "channel", "MessageChannel", "port1", "onmessage", "port2", "postMessage", "suppressUnhandledRejectionWarning", "promise", "toInt", "toFloat", "parseFloat", "replaceNaN", "replacement", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "sum", "round", "base", "abs", "counterBase", "x64Add", "m0", "n0", "o0", "o1", "o2", "o3", "x64Multiply", "m1", "m2", "m3", "n1", "n2", "n3", "x64Rotl", "bits", "x64LeftShift", "x64Xor", "F1", "F2", "x64Fmix", "shifted", "C1", "C2", "M$1", "N1", "N2", "x64hash128", "input", "seed", "getUTF8Bytes", "Uint8Array", "charCode", "charCodeAt", "TextEncoder", "encode", "remainder", "bytes", "h1", "h2", "k1", "k2", "val", "loadSources", "sources", "sourceOptions", "excludeSources", "includedSources", "filter", "sourceKey", "excludes", "haystack", "needle", "includes", "sourceGettersPromise", "loadSource", "source", "sourceLoadPromise", "resolveLoad", "loadStartTime", "bind", "loadArgs", "_i", "loadResult", "loadDuration", "isFinalResultLoaded", "resolveGet", "getStartTime", "getArgs", "finalizeSource", "componentPromises", "componentArray", "components", "sourceGetter", "all", "isTrident", "w", "window", "navigator", "isChromium", "vendor", "isWebKit", "isDesktopWebKit", "isSafariWebKit", "isFunctionNative", "func", "print", "browser", "isGecko", "_b", "documentElement", "style", "isWebKit616OrNewer", "CSS", "HTMLButtonElement", "supports", "exitFullscreen", "msExitFullscreen", "mozCancelFullScreen", "webkitExitFullscreen", "isAndroid", "isItChromium", "isItGecko", "Audio", "appVersion", "makeInnerError", "withIframe", "initialHtml", "domPollInterval", "_c", "iframe", "_d", "_resolve", "_reject", "isComplete", "onload", "onerror", "checkReadyState", "setProperty", "position", "top", "left", "visibility", "srcdoc", "src", "contentWindow", "readyState", "<PERSON><PERSON><PERSON><PERSON>", "selectorToElement", "selector", "parseSimpleCssSelector", "errorMessage", "tagMatch", "attributes", "tag", "partsRegex", "addAttribute", "match", "part", "attributeMatch", "name_1", "join", "addStyleString", "name_2", "property", "priority", "baseFonts", "fontList", "canvasToString", "canvas", "toDataURL", "screenFrameBackup", "screenFrameSizeTimeoutId", "getUnstableScreenFrame", "_this", "watchScreenFrame", "checkScreenFrame", "frameSize", "getCurrentScreenFrame", "isFrameSizeNull", "getFullscreenElement", "fullscreenElement", "msFullscreenElement", "mozFullScreenElement", "webkitFullscreenElement", "screen", "availTop", "width", "availWidth", "availLeft", "height", "availHeight", "getBlockedSelectors", "selectors", "root", "elements", "blockedSelectors", "holder", "forceShow", "show", "offsetParent", "doesMatch$5", "matchMedia", "matches", "doesMatch$4", "doesMatch$3", "doesMatch$2", "doesMatch$1", "doesMatch", "fallbackFn", "presets", "default", "apple", "font", "serif", "fontFamily", "sans", "mono", "min", "fontSize", "system", "willPrintConsoleError", "isAnyParentCrossOrigin", "currentWindow", "parentWindow", "parent", "location", "origin", "validContextParameters", "validExtensionParams", "shaderTypes", "precisionTypes", "getWebGLContext", "cache", "webgl", "context", "getContext", "getShaderPrecision", "gl", "shaderType", "precisionType", "shaderPrecision", "getShaderPrecisionFormat", "rangeMin", "rangeMax", "precision", "getConstantsFromPrototype", "obj", "__proto__", "isConstantLike", "shouldAvoidDebugRendererInfo", "isValidParameterGetter", "getParameter", "fonts", "getFonts", "spansContainer", "defaultWidth", "defaultHeight", "createSpan", "createSpanWithFonts", "initializeFontsSpans", "isFontAvailable", "baseFontsSpans", "fontsSpans", "span", "fontToDetect", "baseFont", "spans", "fontList_1", "fontSpans", "some", "baseFontIndex", "offsetWidth", "offsetHeight", "domBlockers", "getDomBlockers", "debug", "filters", "filterNames", "isApplicable", "getFilters", "fromB64", "atob", "abpIndo", "abpvn", "adBlockFinland", "ad<PERSON><PERSON><PERSON><PERSON><PERSON>", "adBlockWarningRemoval", "adGuardAnnoyances", "adGuardBase", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adGuardJapanese", "adGuardMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adGuard<PERSON><PERSON><PERSON>", "adGuardSpanishPortuguese", "adGuardTrackingProtection", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bulgarian", "easyList", "easyListChina", "easyList<PERSON><PERSON><PERSON>", "easyListCzechSlovak", "easyList<PERSON>ut<PERSON>", "easyListGermany", "easyListItaly", "easyListLithuania", "estonian", "fanboyAnnoyances", "fanboyAntiFacebook", "fanboyEnhancedTrackers", "fanboySocial", "frellwitSwedish", "greekAdBlock", "hungarian", "iDontCareAboutCookies", "icelandicAbp", "latvian", "listKr", "listeAr", "listeFr", "officialPolish", "ro", "ruAd", "thaiAds", "webAnnoyancesUltralist", "filterName", "printDebug", "activeBlockers", "sort", "fontPreferences", "getFontPreferences", "withNaturalFonts", "containerWidthPx", "iframeWindow", "linesOfText", "iframeDocument", "iframeBody", "bodyStyle", "webkitTextSizeAdjust", "textSizeAdjust", "zoom", "devicePixelRatio", "container", "sizes", "text", "whiteSpace", "_e", "_f", "_g", "_h", "getBoundingClientRect", "audio", "getAudioFingerprint", "doesBrowserPerformAntifingerprinting$1", "isSamsungInternet", "audioPrototype", "visualViewport", "Image", "isChromium122OrNewer", "URLPattern", "WebGLRenderingContext", "getUnstableAudioFingerprint", "renderPromise", "finishRendering", "fingerprintPromise", "AudioContext", "OfflineAudioContext", "webkitOfflineAudioContext", "doesBrowserSuspendAudioContext", "isWebKit606OrNewer", "oscillator", "createOscillator", "frequency", "compressor", "createDynamicsCompressor", "threshold", "knee", "ratio", "attack", "release", "connect", "destination", "start", "startRenderingAudio", "isFinalized", "renderTryCount", "startedRunningAt", "startRunningTimeout", "oncomplete", "event", "<PERSON><PERSON><PERSON><PERSON>", "tryRender", "renderingPromise", "startRendering", "hidden", "buffer", "getHash", "signal", "hash", "getChannelData", "subarray", "screenFrame", "getScreenFrame", "screenFrameGetter", "processSize", "sideSize", "getCanvasFingerprint", "getUnstableCanvasFingerprint", "skipImages", "geometry", "winding", "makeCanvasContext", "isSupported", "doesSupportWinding", "rect", "isPointInPath", "renderImages", "renderTextImage", "textBaseline", "fillStyle", "fillRect", "printedText", "fromCharCode", "fillText", "textImage1", "renderGeometryImage", "globalCompositeOperation", "color", "beginPath", "arc", "PI", "closePath", "doesBrowserPerformAntifingerprinting", "osCpu", "getOsCpu", "oscpu", "languages", "getLanguages", "userLanguage", "browserLanguage", "systemLanguage", "isChromium86OrNewer", "Intl", "colorDepth", "getColorDepth", "deviceMemory", "getDeviceMemory", "screenResolution", "getScreenResolution", "getUnstableScreenResolution", "parseDimension", "dimensions", "hardwareConcurrency", "getHardwareConcurrency", "timezone", "getTimezone", "DateTimeFormat", "resolvedOptions", "timeZone", "offset", "getTimezoneOffset", "currentYear", "getFullYear", "max", "sessionStorage", "getSessionStorage", "localStorage", "getLocalStorage", "indexedDB", "getIndexedDB", "isEdgeHTML", "openDatabase", "getOpenDatabase", "cpuClass", "getCpuClass", "platform", "getPlatform", "isIPad", "screenRatio", "Element", "webkitRequestFullscreen", "plugins", "getPlugins", "rawPlugins", "plugin", "mimeTypes", "mimeType", "suffixes", "description", "touchSupport", "getTouchSupport", "touchEvent", "maxTouchPoints", "msMaxTouchPoints", "createEvent", "touchStart", "get<PERSON>endor", "vendorFlavors", "getVendorFlavors", "flavors", "cookiesEnabled", "areCookiesEnabled", "cookie", "colorGamut", "getColorGamut", "gamut", "invertedColors", "areColorsInverted", "forcedColors", "areColorsForced", "monochrome", "getMonochromeDepth", "contrast", "getContrastPreference", "reducedMotion", "isMotionReduced", "reducedTransparency", "isTransparencyReduced", "hdr", "isHDR", "math", "getMathFingerprint", "acos", "acosh", "asin", "asinh", "atanh", "atan", "sin", "sinh", "cos", "cosh", "tan", "tanh", "exp", "expm1", "log1p", "acoshPf", "log", "sqrt", "asinhPf", "atanhPf", "sinhPf", "coshPf", "tanhPf", "expm1Pf", "log1pPf", "powPI", "pow", "pdfViewerEnabled", "isPdfViewerEnabled", "architecture", "getArchitecture", "Float32Array", "u8", "Infinity", "applePay", "getApplePayState", "ApplePaySession", "canMakePayments", "getStateFromError", "message", "privateClickMeasurement", "getPrivateClickMeasurement", "link", "sourceId", "attributionSourceId", "attributionsourceid", "audioBaseLatency", "getAudioContextBaseLatency", "baseLatency", "dateTimeLocale", "getDateTimeLocale", "locale", "webGlBasics", "getWebGlBasics", "debugExtension", "getExtension", "version", "VERSION", "VENDOR", "vendorUnmasked", "UNMASKED_VENDOR_WEBGL", "renderer", "RENDERER", "rendererUnmasked", "UNMASKED_RENDERER_WEBGL", "shadingLanguageVersion", "SHADING_LANGUAGE_VERSION", "webGlExtensions", "getWebGlExtensions", "extensions", "getSupportedExtensions", "contextAttributes", "getContextAttributes", "unsupportedExtensions", "parameters", "extensionParameters", "shaderPrecisions", "attributeName", "constants_1", "code", "constant", "extensions_1", "extension", "shaderTypes_1", "precisionTypes_1", "loadBuiltinSources", "getConfidence", "proConfidenceScore", "deriveProConfidenceScore", "openConfidenceScore", "getOpenConfidenceScore", "score", "comment", "replace", "hashComponents", "componentsToCanonicalString", "componentKey", "component", "prepareForSources", "<PERSON><PERSON><PERSON><PERSON>", "requestIdleCallbackIfAvailable", "fallbackTimeout", "deadlineTimeout", "requestIdleCallback", "timeout", "makeAgent", "getComponents", "makeLazyGetResult", "visitorIdCache", "confidence", "visitorId", "load", "monitoring", "monitor", "__fpjs_d_m", "request", "XMLHttpRequest", "open", "send", "componentsToDebugString", "_key", "errorToObject", "stack", "getDeviceId", "_getDeviceId", "_asyncToGenerator", "_regeneratorRuntime", "_callee", "_context", "FingerprintJS", "fp", "fetchWithDeviceId", "_x", "_x2", "_fetchWithDeviceId", "_0x1cf2", "_0x2d423b", "_callee2", "headers", "modifiedInit", "_context2", "_0xf34c7", "_0x1b6eb5", "deviceId", "Headers", "_objectSpread", "fetch", "fetchWithDeviceIdandApiKey", "_x3", "_fetchWithDeviceIdandApiKey", "_callee3", "<PERSON><PERSON><PERSON><PERSON>", "response", "_args3", "_context3", "_0x2cbc58", "_0x4f96", "public<PERSON>ey", "CryptoService", "_gdi", "_wk", "_iih", "_csi", "_dda", "_eda", "_dsk", "_spu", "_gr", "_importPrivateKey", "_importPublicKey", "_decrypt", "_encrypt", "_da", "_hd", "_he", "_dra", "_era", "_irpr", "_irpu", "_ea", "_ga", "_gra", "_createClass", "_classCallCheck", "keyPair", "encryptionKeyPair", "privateKey", "_0x320c9b", "_0x19f8ab", "crypto", "arrayBufferToPEM", "aes<PERSON>ey", "encoder", "iv", "encryptedData", "_0x33623b", "_0xa86ebf", "dataBuffer", "_callee4", "pem", "binaryDer", "_context4", "pemToA<PERSON>y<PERSON><PERSON>er", "irpu", "_callee5", "_context5", "_x4", "irpr", "_callee6", "exportedAESKey", "_context6", "_0x1d3bd9", "_0x5d89", "_x5", "_x6", "era", "_callee7", "_context7", "_x7", "_x8", "_callee8", "publicKeyPem", "encryptedAESKey", "combinedData", "_context8", "_0x286f58", "_0x5a77b3", "ga", "ea", "_yield$this$ea", "btoa", "_toConsumableArray", "_x9", "_x10", "_callee9", "privateKeyBem", "encryptedText", "encryptedAesKey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decryptedData", "_context9", "_0x663981", "_0x3c5435", "da", "_x11", "_x12", "_callee10", "encryptedBuffer", "decryptedBuffer", "_context10", "cipherText", "TextDecoder", "_x13", "_x14", "_callee11", "plainText", "_context11", "_0x14c72c", "_0x593472", "_x15", "_x16", "encrypt", "_callee12", "_context12", "_0x4109b9", "_0x47c408", "_x17", "_x18", "_callee13", "_context13", "_0x1454a7", "_x19", "_callee14", "_context14", "_x20", "importPrivateKey", "base64", "_base64$match", "binary", "len", "binaryString", "base64ToArrayBuffer", "_callee15", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ep", "sp", "signature<PERSON><PERSON>er", "ss", "_context15", "gra", "spB<PERSON>er", "gdi", "xdi", "s<PERSON><PERSON><PERSON>", "gr", "unescape", "encodeURIComponent", "expires", "nameEQ", "ca", "cookies", "eqPos", "_callee16", "requestEncrypt", "responseData", "privateKeyBase64String", "_context16", "_0x416ea6", "_yield$this$gr", "requestJson", "he", "sc", "_callee17", "server<PERSON>ey", "_context17", "gc", "cText", "hd", "_callee18", "sText", "_context18", "_0x3c9c0f", "_0x3f573b", "_x21", "_callee19", "_context19", "_0x1591d8", "_0x1358bb", "_x22", "_callee20", "_context20", "csi", "_callee21", "_context21", "_0x13328d", "_0x14df2b", "ch", "spu", "_callee22", "retries", "_context22", "_0x5f0a59", "_0x1b815d", "_callee23", "_context23", "_0x47d747", "_0x3af8f0", "FlightService", "_request2", "endpoint", "fetchFn", "_args", "_0x7d4176", "_0x33932c", "useDeviceId", "api", "_0x219a9a", "_0x4d8c87", "_0x9f3810", "_0x3917ca", "getAirportInfoByCode", "_ref", "airportsCode", "requestBody", "_0x542ff6", "_0x4307", "_0x4cd942", "_0x552586", "_0x26265e", "_0x2626", "_0x4307d6", "_0x1c1f4a", "_ref3", "_0x182deb", "features", "_ref5", "_0xe25953", "_0x18eb", "_0x1f4e", "_0xa5ab09", "_0x392d7c", "_0x3e7aa1", "_0x1f4ea2", "_0x18eb9d", "_0x10abf1", "_0x3ec37f", "_0xa0a40d", "_0x1e04f2", "colors", "setnmtColors", "baseColor", "_ref2", "lighten", "hex", "percent", "num", "amt", "darken", "shades", "hexColor", "_ref4", "cryptoService", "flightService", "TripAvailable", "_TripAvailable", "_getInforAirports", "_AvailableTrip", "_CallAvailableTrip", "_RequestEncrypt", "_onSubmitForm", "_firstUpdated", "_callSuper", "_inherits", "_language", "oldValue", "languageParam", "URLSearchParams", "urlParams", "currencySymbol", "_changedProperties", "googleFontsLink", "_googleFontsLink", "_superPropGet", "_0x3dd8d8", "_0x5373e4", "updateURLWithLanguage", "currentUrl", "URL", "params", "newUrl", "PhoneCustomer", "AvailableTrip", "_0x5b35ed", "_0x59aebd", "RequestEncrypt", "EmailCustomer", "payloadsEncrypted", "res", "noteData", "_this$orderAvailable2", "_0x3c1218", "_0x142119", "resDecrypted", "res<PERSON>son", "_this$orderAvailable", "ra", "_cryptoService", "_this$orderDetails", "indexInfant", "_this2", "paxAdult", "_res$feature", "_this$orderDetails2", "inventory", "currencyObj", "getInforAirports", "newLang", "bankSelected", "unsafeCSS", "css", "customElement"], "mappings": "ijXA+BO,IAAIA,SAAW,WAQlB,OAPAA,SAAWC,OAAOC,QAAU,SAAkBC,GAC1C,IAAK,IAAIC,EAAGC,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAE5C,IAAK,IAAII,KADTL,EAAIG,UAAUF,GACOJ,OAAOS,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,IAE9E,OAAON,CACV,GACeU,MAAMC,KAAMP,UAChC,EAcO,SAASQ,WAAWC,EAAYC,EAAQC,EAAKC,GAChD,IAA2HC,EAAvHC,EAAId,UAAUC,OAAQc,EAAID,EAAI,EAAIJ,EAAkB,OAATE,EAAgBA,EAAOlB,OAAOsB,yBAAyBN,EAAQC,GAAOC,EACrH,GAAuB,iBAAZK,SAAoD,mBAArBA,QAAQC,SAAyBH,EAAIE,QAAQC,SAAST,EAAYC,EAAQC,EAAKC,QACpH,IAAK,IAAId,EAAIW,EAAWR,OAAS,EAAQ,GAALH,EAAQA,KAASe,EAAIJ,EAAWX,MAAIiB,GAAKD,EAAI,EAAID,EAAEE,GAAS,EAAJD,EAAQD,EAAEH,EAAQC,EAAKI,GAAKF,EAAEH,EAAQC,KAASI,GAChJ,OAAW,EAAJD,GAASC,GAAKrB,OAAOyB,eAAeT,EAAQC,EAAKI,GAAIA,CAChE,CAmDO,SAASK,WAAWC,EAAaC,GACpC,GAAuB,iBAAZL,SAAoD,mBAArBA,QAAQM,SAAyB,OAAON,QAAQM,SAASF,EAAaC,EACpH,CAEO,SAASE,UAAUC,EAASC,EAAYC,EAAGC,GAE9C,OAAO,IAAKD,EAAAA,GAAUE,UAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUC,GAAS,IAAMC,EAAKN,EAAUO,KAAKF,IAAW,MAAOG,GAAKL,EAAOK,GAAO,CAC3F,SAASC,EAASJ,GAAS,IAAMC,EAAKN,EAAiBU,MAAEL,IAAW,MAAOG,GAAKL,EAAOK,GAAO,CAC9F,SAASF,EAAKK,GAJlB,IAAeN,EAIaM,EAAOC,KAAOV,EAAQS,EAAON,SAJ1CA,EAIyDM,EAAON,iBAJ/BN,EAAIM,EAAQ,IAAIN,GAAE,SAAUG,GAAWA,EAAQG,EAAO,KAIhBQ,KAAKT,EAAWK,EAAY,CAC9GH,GAAMN,EAAYA,EAAUtB,MAAMmB,EAASC,GAAc,KAAKS,OACtE,GACA,CAEO,SAASO,YAAYjB,EAASkB,GACjC,IAAsGC,EAAGC,EAAGjD,EAAxGkD,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPpD,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAG,EAAIqD,KAAM,GAAIC,IAAK,IAAeC,EAAIzD,OAAO0D,QAA4B,mBAAbC,SAA0BA,SAAW3D,QAAQS,WACtL,OAAOgD,EAAEhB,KAAOmB,EAAK,GAAIH,EAASb,MAAIgB,EAAK,GAAIH,EAAUI,OAAID,EAAK,GAAsB,mBAAXE,SAA0BL,EAAEK,OAAOC,UAAY,WAAa,OAAOlD,IAAO,GAAG4C,EAC1J,SAASG,EAAKvD,GAAK,OAAO,SAAU2D,GAAYxB,IAClCyB,EADuC,CAAC5D,EAAG2D,GAErD,GAAId,EAAG,MAAM,IAAIgB,UAAU,mCAC3B,KAA8Bd,EAAvBK,GAAaQ,EAAPR,EAAI,GAAiB,EAAKL,GAAG,IACtC,GAAIF,EAAI,EAAGC,IAAMjD,EAAY,EAAR+D,EAAG,GAASd,EAAUU,OAAII,EAAG,GAAKd,EAASP,SAAO1C,EAAIiD,EAAUU,SAAM3D,EAAES,KAAKwC,GAAI,GAAKA,EAAEV,SAAWvC,EAAIA,EAAES,KAAKwC,EAAGc,EAAG,KAAKnB,KAAM,OAAO5C,EAE3J,OADIiD,EAAI,GAAMc,EAAH/D,EAAQ,CAAS,EAAR+D,EAAG,GAAQ/D,EAAEqC,OACzB0B,GAAG,IACP,KAAK,EAAG,KAAK,EAAG/D,EAAI+D,EAAI,MACxB,KAAK,EAAc,OAAXb,EAAEC,QAAgB,CAAEd,MAAO0B,EAAG,GAAInB,MAAM,GAChD,KAAK,EAAGM,EAAEC,QAASF,EAAIc,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKb,EAAEI,IAAIW,MAAOf,EAAEG,KAAKY,MAAO,SACxC,QACI,KAAkBjE,EAAe,GAA3BA,EAAIkD,EAAEG,MAAYhD,QAAcL,EAAEA,EAAEK,OAAS,MAAkB,IAAV0D,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEb,EAAI,EAAG,QAAW,CAC5G,GAAc,IAAVa,EAAG,MAAc/D,GAAM+D,EAAG,GAAK/D,EAAE,IAAM+D,EAAG,GAAK/D,EAAE,IAAQkD,EAAEC,MAAQY,EAAG,QAC1E,GAAc,IAAVA,EAAG,IAAYb,EAAEC,MAAQnD,EAAE,GAAMkD,EAAEC,MAAQnD,EAAE,GAAIA,EAAI+D,MAAzD,CACA,KAAI/D,GAAKkD,EAAEC,MAAQnD,EAAE,IAArB,CACIA,EAAE,IAAIkD,EAAEI,IAAIW,MAChBf,EAAEG,KAAKY,MAAO,QAFqD,CAAxCf,EAAEC,MAAQnD,EAAE,GAAIkD,EAAEI,IAAIY,KAAKH,EADe,EAK7EA,EAAKhB,EAAKtC,KAAKoB,EAASqB,EAC8B,CAAxD,MAAOV,GAAKuB,EAAK,CAAC,EAAGvB,GAAIS,EAAI,CAAE,CAAW,QAAED,EAAIhD,EAAI,CAAI,CAC1D,GAAY,EAAR+D,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE1B,MAAO0B,EAAG,GAAKA,EAAG,QAAK,EAAQnB,MAAM,EArBf,CAAG,CAuBtE,CA+DO,SAASuB,cAAcC,EAAIC,EAAMC,GACpC,GAAIA,GAA6B,IAArBlE,UAAUC,OAAc,IAAK,IAA4BkE,EAAxBrE,EAAI,EAAGsE,EAAIH,EAAKhE,OAAYH,EAAIsE,EAAGtE,KACxEqE,GAAQrE,KAAKmE,KACRE,EAAAA,GAASE,MAAMlE,UAAUmE,MAAMjE,KAAK4D,EAAM,EAAGnE,IAC/CA,GAAKmE,EAAKnE,IAGrB,OAAOkE,EAAGO,OAAOJ,GAAME,MAAMlE,UAAUmE,MAAMjE,KAAK4D,GACtD,CCxNA,IAAMrE,IAAE4E,WAAWpC,IAAExC,IAAE6E,kBAAa,IAAS7E,IAAE8E,UAAU9E,IAAE8E,SAASC,eAAe,uBAAuBC,SAASzE,WAAW,YAAY0E,cAAc1E,UAAUN,IAAE2D,SAASsB,IAAE,IAAIC,QAAQC,IAAQC,MAAAA,WAAAA,CAAYrF,EAAEwC,EAAE0C,GAAG,GAAGvE,KAAK2E,cAAa,EAAGJ,IAAIjF,IAAE,MAAMsF,MAAM,qEAAqE5E,KAAK6E,QAAQxF,EAAEW,KAAKX,EAAEwC,CAAC,CAACiD,cAAAA,GAAiBC,IAAI1F,EAAEW,KAAKuE,EAAE,IAAuC1C,EAAjCvC,EAAEU,KAAKX,EAAwJ,OAAnJwC,UAAG,IAASxC,QAAoD,KAAZA,GAA/BwC,OAAE,IAASvC,GAAG,IAAIA,EAAEI,QAAa6E,IAAES,IAAI1F,GAAaD,MAAKW,KAAKuE,EAAElF,EAAE,IAAIiF,eAAeW,YAAYjF,KAAK6E,SAAShD,IAAG0C,IAAEW,IAAI5F,EAAED,GAAWA,CAAC,CAAC8F,QAAAA,GAAW,OAAOnF,KAAK6E,OAAO,GAAQrE,IAAEnB,GAAG,IAAIG,IAAE,iBAAiBH,EAAEA,EAAEA,EAAE,QAAG,EAAOC,KAAmlBiB,IAAEsB,IAAExC,GAAGA,EAAEA,IAAGA,GAAAA,aAAaiF,cAAc,CAAKS,IAAIlD,EAAE,GAAG,IAAI,IAAMvC,KAA2CD,EAApC+F,SAASvD,GAAGvC,EAAEuF,QAAQ,OAAOrE,IAAEqB,EAAM,CAAExC,OAAAA,ICAlzCgG,GAAG9F,IAAEqB,eAAeiB,IAAEpB,yBAAyBD,IAAE8E,oBAAoBC,IAAEC,sBAAsBjB,IAAEkB,eAAejG,KAAGL,OAAOuG,IAAEzB,WAAW1D,IAAEmF,IAAEC,aAAa9B,IAAEtD,IAAEA,IAAEqF,YAAY,GAAGjG,IAAE+F,IAAEG,+BAA+BvF,IAAE,CAACjB,EAAEC,IAAID,EAAEyG,IAAE,CAACC,WAAAA,CAAY1G,EAAEC,GAAG,OAAOA,GAAG,KAAK0G,QAAQ3G,EAAEA,EAAEwE,IAAE,KAAK,MAAM,KAAK1E,OAAO,KAAK2E,MAAMzE,EAAE,MAAMA,EAAEA,EAAE4G,KAAKC,UAAU7G,GAAG,OAAOA,CAAC,EAAE8G,aAAAA,CAAc9G,EAAEC,GAAGyF,IAAIxF,EAAEF,EAAE,OAAOC,GAAG,KAAK0G,QAAQzG,EAAE,OAAOF,EAAE,MAAM,KAAK+G,OAAO7G,EAAE,OAAOF,EAAE,KAAK+G,OAAO/G,GAAG,MAAM,KAAKF,OAAO,KAAK2E,MAAM,IAAIvE,EAAE0G,KAAKI,MAAMhH,EAAkB,CAAf,MAAMA,GAAGE,EAAE,IAAI,EAAE,OAAOA,CAAC,GAAG8C,IAAE,CAAChD,EAAEC,KAAKC,IAAEF,EAAEC,GAAGgD,IAAE,CAACgE,WAAU,EAAGC,KAAKC,OAAOC,UAAUX,IAAEY,SAAQ,EAAGC,WAAWtE,KAAGY,OAAOjC,WAAWiC,OAAO,YAAYyC,IAAEkB,sBAAsB,IAAIpC,QAAAA,MAAcqC,UAAUC,YAAYC,qBAAsB1H,CAAAA,GAAGW,KAAKgH,QAAQhH,KAAK6D,IAAI,IAAIN,KAAKlE,EAAE,CAAC4H,6BAAAA,GAAgC,OAAOjH,KAAKkH,WAAWlH,KAAKmH,MAAM,IAAInH,KAAKmH,KAAKC,OAAO,CAACC,qBAAAA,CAAsBhI,EAAEC,EAAEgD,KAAG,IAAyG9B,EAAtGlB,EAAEgI,QAAQhI,EAAEgH,WAAU,GAAItG,KAAKgH,OAAOhH,KAAKuH,kBAAkBrC,IAAI7F,EAAEC,GAAIA,EAAEkI,aAAkBjI,EAAE0D,cAA6C,KAApCzC,EAAER,KAAKyH,sBAAsBpI,EAAEE,EAAED,KAAeuC,IAAE7B,KAAKJ,UAAUP,EAAEmB,GAAG,CAACiH,6BAA6BpI,EAAEC,EAAEC,GAAG,IAAMyF,IAAInD,EAAEqD,IAAIK,GAAG/E,IAAER,KAAKJ,UAAUP,IAAI,CAAC2F,GAAAA,GAAM,OAAOhF,KAAKV,EAAE,EAAE4F,GAAAA,CAAI7F,GAAGW,KAAKV,GAAGD,CAAC,GAAG,MAAM,CAAC2F,GAAAA,GAAM,OAAOnD,GAAG/B,KAAKE,KAAK,EAAEkF,GAAAA,CAAI5F,GAAG,IAAMkB,EAAEqB,GAAG/B,KAAKE,MAAMuF,EAAEzF,KAAKE,KAAKV,GAAGU,KAAK0H,cAAcrI,EAAEmB,EAAEjB,EAAE,EAAEoI,cAAa,EAAGC,YAAW,EAAG,CAACC,yBAA0BxI,CAAAA,GAAG,OAAOW,KAAKuH,kBAAkBvC,IAAI3F,IAAIiD,GAAC,CAAC0E,WAAAA,GAAc,IAA4D3H,EAAzDW,KAAKH,eAAeS,IAAE,yBAAmCjB,EAAEG,IAAEQ,OAAQkH,gBAAW,IAAS7H,EAAEwE,IAAI7D,KAAK6D,EAAE,IAAIxE,EAAEwE,IAAI7D,KAAKuH,kBAAkB,IAAIO,IAAIzI,EAAEkI,mBAAkB,CAACL,eAAkBA,GAAA,IAAGlH,KAAKH,eAAeS,IAAE,cAAzB,CAA8C,GAAGN,KAAK+H,WAAU,EAAG/H,KAAKgH,OAAOhH,KAAKH,eAAeS,IAAE,eAAe,CAAC,IAAMjB,EAAEW,KAAKgI,WAAW1I,EAAE,IAAIiG,IAAElG,MAAMkF,IAAElF,IAAI,IAAI,IAAME,KAAKD,EAAEU,KAAKqH,eAAe9H,EAAEF,EAAEE,GAAG,CAAC,IAAMF,EAAEW,KAAKiD,OAAOjC,UAAU,GAAG,OAAO3B,EAAE,CAAC,IAAMC,EAAEsH,oBAAoB5B,IAAI3F,GAAG,QAAG,IAASC,EAAE,IAAI,IAAMD,EAAEE,KAAKD,EAAEU,KAAKuH,kBAAkBrC,IAAI7F,EAAEE,EAAE,CAACS,KAAKmH,KAAK,IAAIW,IAAI,IAAI,IAAMzI,EAAEC,KAAKU,KAAKuH,kBAAkB,CAAC,IAAMhI,EAAES,KAAKiI,KAAK5I,EAAEC,QAAG,IAASC,GAAGS,KAAKmH,KAAKjC,IAAI3F,EAAEF,EAAE,CAACW,KAAKkI,cAAclI,KAAKmI,eAAenI,KAAKoI,OAA5c,CAAmd,CAACD,qBAAsB7I,CAAAA,GAAG,IAAMC,EAAE,GAAG,GAAGuE,MAAMuE,QAAQ/I,GAAG,CAAC,IAAMuC,EAAE,IAAIyG,IAAIhJ,EAAEiJ,KAAK,KAAKC,WAAW,IAAI,IAAMlJ,KAAKuC,EAAEtC,EAAEkJ,QAAQpJ,IAAEC,GAAG,WAAM,IAASA,GAAGC,EAAEgE,KAAKlE,IAAEC,IAAI,OAAOC,CAAC,CAAC0I,WAAAA,CAAY5I,EAAEC,GAAuB,OAAM,KAApBC,EAAED,EAAEgH,gBAAuB,EAAO,iBAAiB/G,EAAEA,EAAE,iBAAiBF,EAAEA,EAAEqJ,mBAAc,CAAM,CAAChE,WAAAA,GAAciE,QAAQ3I,KAAK4I,UAAK,EAAO5I,KAAK6I,iBAAgB,EAAG7I,KAAK8I,YAAW,EAAG9I,KAAK+I,KAAK,KAAK/I,KAAKgJ,MAAM,CAACA,IAAAA,GAAOhJ,KAAKiJ,KAAK,IAAI3H,SAASjC,GAAGW,KAAKkJ,eAAe7J,IAAIW,KAAKmJ,KAAK,IAAIrB,IAAI9H,KAAKoJ,OAAOpJ,KAAK0H,gBAAgB1H,KAAK0E,YAAYb,GAAGwF,SAAShK,GAAGA,EAAEW,OAAO,CAACsJ,aAAAA,CAAcjK,IAAIW,KAAKuJ,OAAO,IAAIjB,KAAKkB,IAAInK,QAAG,IAASW,KAAKyJ,YAAYzJ,KAAK0J,aAAarK,EAAEsK,iBAAiB,CAACC,gBAAAA,CAAiBvK,GAAGW,KAAKuJ,MAAMM,OAAOxK,EAAE,CAAC+J,IAAAA,GAAO,IAA+D7J,EAAzDF,EAAE,IAAIyI,IAAyC,IAAUvI,KAA7CS,KAAK0E,YAAY6C,kBAAmCH,OAAOpH,KAAKH,eAAeN,KAAKF,EAAE6F,IAAI3F,EAAES,KAAKT,WAAWS,KAAKT,IAAW,EAAPF,EAAEyK,OAAS9J,KAAK4I,KAAKvJ,EAAE,CAAC0K,gBAAAA,GAAmB,IAAM1K,EAAEW,KAAKgK,YAAYhK,KAAKiK,aAAajK,KAAK0E,YAAYwF,mBAAmB,MDAhiE,EAAC5K,EAAEiF,KAAK,GAAG1C,IAAEvC,EAAE6K,mBAAmB5F,EAAE6F,KAAK/K,GAAGA,aAAaiF,cAAcjF,EAAEA,EAAEyF,kBAAkB,IAAI,IAAMjD,KAAK0C,EAAE,CAAC,IAAMA,EAAE8F,SAASC,cAAc,SAAS9K,EAAEH,IAAEkL,cAAS,IAAS/K,GAAG+E,EAAEiG,aAAa,QAAQhL,GAAG+E,EAAEkG,YAAY5I,EAAEgD,QAAQvF,EAAEoL,YAAYnG,EAAE,GCAqzDjF,CAAED,EAAEW,KAAK0E,YAAYwD,eAAe7I,CAAC,CAACsL,iBAAAA,GAAoB3K,KAAKyJ,aAAazJ,KAAK+J,mBAAmB/J,KAAKkJ,gBAAe,GAAIlJ,KAAKuJ,MAAMF,SAAShK,GAAGA,EAAEsK,mBAAmB,CAACT,cAAAA,CAAe7J,GAAAA,CAAIuL,oBAAAA,GAAuB5K,KAAKuJ,MAAMF,SAAShK,GAAGA,EAAEwL,sBAAsB,CAACC,wBAAAA,CAAyBzL,EAAEC,EAAEC,GAAGS,KAAK+K,KAAK1L,EAAEE,EAAE,CAACyL,IAAAA,CAAK3L,EAAEC,GAAG,IAAMC,EAAES,KAAK0E,YAAY6C,kBAAkBvC,IAAI3F,GAAGwC,EAAE7B,KAAK0E,YAAYuD,KAAK5I,EAAEE,QAAM,IAASsC,IAAG,IAAKtC,EAAEmH,UAAelG,QAAG,IAASjB,EAAEkH,WAAWV,YAAYxG,EAAEkH,UAAUX,KAAGC,YAAYzG,EAAEC,EAAEgH,MAAMvG,KAAK+I,KAAK1J,EAAE,MAAMmB,EAAER,KAAKiL,gBAAgBpJ,GAAG7B,KAAKwK,aAAa3I,EAAErB,GAAGR,KAAK+I,KAAK,KAAK,CAACgC,IAAAA,CAAK1L,EAAEC,GAAG,IAAMC,EAAES,KAAK0E,YAAY7C,EAAEtC,EAAE4H,KAAKnC,IAAI3F,GAAG,QAAG,IAASwC,GAAG7B,KAAK+I,OAAOlH,EAAE,CAAC,IAAMxC,EAAEE,EAAEsI,mBAAmBhG,GAAGrB,EAAE,mBAAmBnB,EAAEoH,UAAU,CAACN,cAAc9G,EAAEoH,gBAAW,IAASpH,EAAEoH,WAAWN,cAAc9G,EAAEoH,UAAUX,IAAE9F,KAAK+I,KAAKlH,EAAE7B,KAAK6B,GAAGrB,EAAE2F,cAAc7G,EAAED,EAAEkH,MAAMvG,KAAK+I,KAAK,IAAI,CAAC,CAACrB,aAAAA,CAAcrI,EAAEC,EAAEC,GAAG,QAAG,IAASF,EAAE,CAAC,MAAGE,IAAIS,KAAK0E,YAAYmD,mBAAmBxI,IAAOsH,YAAYtE,KAAGrC,KAAKX,GAAGC,GAAG,OAAOU,KAAKoB,EAAE/B,EAAEC,EAAEC,EAAE,EAAC,IAAKS,KAAK6I,kBAAkB7I,KAAKiJ,KAAKjJ,KAAKkL,OAAO,CAAC9J,CAAAA,CAAE/B,EAAEC,EAAEC,GAAGS,KAAKmJ,KAAKgC,IAAI9L,IAAIW,KAAKmJ,KAAKjE,IAAI7F,EAAEC,IAAG,IAAKC,EAAEmH,SAAS1G,KAAK+I,OAAO1J,IAAIW,KAAKoL,OAAO,IAAI9C,KAAKkB,IAAInK,EAAE,CAAC6L,aAAalL,KAAK6I,iBAAgB,EAAG,UAAU7I,KAAKiJ,IAA+B,CAA1B,MAAM5J,GAAGiC,QAAQE,OAAOnC,EAAE,CAAC,IAAMA,EAAEW,KAAKqL,iBAAiB,OAAO,MAAMhM,SAASA,GAAGW,KAAK6I,eAAe,CAACwC,cAAAA,GAAiB,OAAOrL,KAAKsL,eAAe,CAACA,aAAAA,GAAgB,GAAItL,KAAK6I,gBAAT,CAAgC,IAAI7I,KAAK8I,WAAW,CAAC,GAAG9I,KAAKyJ,aAAazJ,KAAK+J,mBAAmB/J,KAAK4I,KAAK,CAAC,IAAI,IAAMvJ,EAAEC,KAAKU,KAAK4I,KAAK5I,KAAKX,GAAGC,EAAEU,KAAK4I,UAAK,CAAM,CAAC,IAAMvJ,EAAEW,KAAK0E,YAAY6C,kBAAkB,GAAU,EAAPlI,EAAEyK,KAAO,IAAI,IAAMxK,EAAEC,KAAKF,GAAI,IAAGE,EAAEgM,SAASvL,KAAKmJ,KAAKgC,IAAI7L,SAAI,IAASU,KAAKV,IAAIU,KAAKoB,EAAE9B,EAAEU,KAAKV,GAAGC,EAAE,CAACwF,IAAI1F,GAAE,EAASC,EAAEU,KAAKmJ,KAAK,KAAI9J,EAAEW,KAAKwL,aAAalM,KAAMU,KAAKyL,WAAWnM,GAAGU,KAAKuJ,MAAMF,SAAShK,GAAGA,EAAEqM,iBAAiB1L,KAAK2L,OAAOrM,IAAIU,KAAK4L,MAAwC,CAAjC,MAAMtM,GAAG,MAAMD,GAAE,EAAGW,KAAK4L,OAAOtM,CAAC,CAACD,GAAGW,KAAK6L,KAAKvM,EAAtd,CAAwd,CAACmM,UAAAA,CAAWpM,GAAIwM,CAAAA,IAAAA,CAAKxM,GAAGW,KAAKuJ,MAAMF,SAAShK,GAAGA,EAAEyM,kBAAkB9L,KAAK8I,aAAa9I,KAAK8I,YAAW,EAAG9I,KAAK+L,aAAa1M,IAAIW,KAAKgM,QAAQ3M,EAAE,CAACuM,IAAAA,GAAO5L,KAAKmJ,KAAK,IAAIrB,IAAI9H,KAAK6I,iBAAgB,CAAE,CAACoD,qBAAqB,OAAOjM,KAAKkM,mBAAmB,CAACA,iBAAAA,GAAoB,OAAOlM,KAAKiJ,IAAI,CAACuC,YAAAA,CAAanM,GAAG,OAAQ,CAAA,CAACsM,MAAAA,CAAOtM,GAAGW,KAAKoL,OAAOpL,KAAKoL,KAAK/B,SAAShK,GAAGW,KAAKgL,KAAK3L,EAAEW,KAAKX,MAAMW,KAAK4L,MAAM,CAACI,OAAAA,CAAQ3M,GAAI0M,CAAAA,YAAAA,CAAa1M,GAAI,EAACwH,EAAEqB,cAAc,GAAGrB,EAAEqD,kBAAkB,CAACiC,KAAK,QAAQtF,EAAEvG,IAAE,sBAAsB,IAAIwH,IAAIjB,EAAEvG,IAAE,cAAc,IAAIwH,IAAInI,MAAI,CAACyM,gBAAgBvF,KAAKnB,IAAE2G,0BAA0B,IAAI9I,KAAK,SCA56K,IAAClE,IAAE4E,WAAW1E,IAAEF,IAAEsG,aAAarG,EAAEC,IAAEA,IAAE+M,aAAa,WAAW,CAACC,WAAWlN,GAAGA,SAAI,EAAOwC,EAAE,QAAQ0D,EAAAA,OAASiH,KAAKC,SAASC,QAAQ,GAAG3I,MAAM,MAAMQ,IAAE,IAAIgB,EAAE/F,IAAM+E,IAAAA,OAAK/D,IAAE6J,SAASxG,EAAE,IAAIrD,IAAEmM,cAAc,IAAIpM,EAAElB,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,mBAAmBA,EAAEqG,EAAE5B,MAAMuE,QAA2D/H,EAAE,cAAc+B,EAAE,sDAAsDc,EAAE,OAAOZ,EAAE,KAAKqK,EAAEC,OAAAA,KAAYvM,sBAAsBA,MAAMA,uCAAuC,KAAKX,EAAE,KAAKiD,EAAE,KAAKkK,EAAE,qCAAwFC,EAAjD1N,IAAG,CAACE,KAAKD,KAAK,CAAC0N,WAAW3N,EAAE4N,QAAQ1N,EAAE2N,OAAO5N,IAAMgD,CAAE,GAAiB6K,EAAElK,OAAOmK,IAAI,gBAAgBC,EAAEpK,OAAOmK,IAAI,eAAeE,EAAE,IAAI9I,QAAQ+I,EAAE/M,IAAEgN,iBAAiBhN,IAAE,KAAK,SAASY,EAAE/B,EAAEE,GAAG,GAAImG,EAAErG,IAAKA,EAAEQ,eAAe,OAAqD,YAAO,IAASP,EAAEA,EAAEiN,WAAWhN,GAAGA,EAAhF,MAAMqF,MAAM,iCAAqE,CAAC,IAAM6I,EAAE,CAACpO,EAAEE,KAAK,IAA4BiB,EAAtBlB,EAAED,EAAEK,OAAO,EAAE6E,EAAE,GAASV,EAAE,IAAItE,EAAE,QAAQ,IAAIA,EAAE,SAAS,GAAGgB,EAAE8B,EAAE,IAAI0C,IAAIxF,EAAE,EAAEA,EAAED,EAAEC,IAAI,CAAC,IAAiBmG,EAAEI,EAAbxG,EAAED,EAAEE,GAAWe,KAAKgC,EAAE,EAAE,KAAKA,EAAEhD,EAAEI,SAASa,EAAEmN,UAAUpL,EAAc,QAAZwD,EAAEvF,EAAEoN,KAAKrO,MAAcgD,EAAE/B,EAAEmN,UAAUnN,IAAI8B,EAAE,QAAQyD,EAAE,GAAGvF,EAAE4C,OAAE,IAAS2C,EAAE,GAAGvF,EAAEgC,OAAE,IAASuD,EAAE,IAAIgH,EAAEc,KAAK9H,EAAE,MAAMtF,EAAEqM,OAAO,KAAK/G,EAAE,GAAG,MAAMvF,EAAEqM,QAAG,IAAS9G,EAAE,KAAKvF,EAAEqM,GAAGrM,IAAIqM,EAAE,MAAM9G,EAAE,IAAIvF,EAAEC,GAAG6B,EAAE/B,GAAE,QAAI,IAASwF,EAAE,GAAGxF,MAAMA,EAAEC,EAAEmN,UAAU5H,EAAE,GAAGpG,OAAOgG,EAAEI,EAAE,GAAGvF,OAAE,IAASuF,EAAE,GAAG8G,EAAE,MAAM9G,EAAE,GAAGlD,EAAEjD,GAAGY,IAAIqC,GAAGrC,IAAIZ,EAAEY,EAAEqM,EAAErM,IAAI4C,GAAG5C,IAAIgC,EAAEhC,EAAE8B,GAAG9B,EAAEqM,EAAEpM,OAAE,GAAQ,IAAMuM,EAAExM,IAAIqM,GAAGvN,EAAEE,EAAE,GAAGsO,WAAW,MAAM,IAAI,GAAGhK,GAAGtD,IAAI8B,EAAE/C,EAAEE,IAAK,GAAHc,GAAMiE,EAAEhB,KAAKmC,GAAGpG,EAAEyE,MAAM,EAAEzD,GAAGuB,EAAEvC,EAAEyE,MAAMzD,GAAGiF,EAAEwH,GAAGzN,EAAEiG,QAAQjF,EAAEf,EAAEwN,EAAE,CAAC,MAAM,CAAC3L,EAAE/B,EAAEwE,GAAGxE,EAAEC,IAAI,QAAQ,IAAIC,EAAE,SAAS,IAAIA,EAAE,UAAU,KAAKgF,EAAE,EAAA,MAAQuJ,EAAEpJ,WAAAA,EAAauI,QAAQ5N,EAAE2N,WAAW1N,GAAGE,GAAGuF,IAAIvE,EAAER,KAAK+N,MAAM,GAAGhJ,IAAIxE,EAAE,EAAEmF,EAAE,EAAE,IAAMI,EAAEzG,EAAEK,OAAO,EAAEY,EAAEN,KAAK+N,OAAO1L,EAAEc,GAAGsK,EAAEpO,EAAEC,GAAG,GAAGU,KAAKgO,GAAGF,EAAExD,cAAcjI,EAAE7C,GAAG+N,EAAEU,YAAYjO,KAAKgO,GAAGE,QAAQ,IAAI5O,GAAG,IAAIA,EAAE,CAAC,IAAMD,EAAEW,KAAKgO,GAAGE,QAAQC,WAAW9O,EAAE+O,eAAe/O,EAAEgP,WAAW,CAAC,KAAK,QAAQ7N,EAAE+M,EAAEe,aAAahO,EAAEZ,OAAOoG,GAAG,CAAC,GAAG,IAAItF,EAAE+N,SAAS,CAAC,GAAG/N,EAAEgO,gBAAgB,IAAI,IAAMnP,KAAKmB,EAAEiO,oBAAoB,GAAGpP,EAAEqP,SAAS7M,GAAG,CAAC,IAAMtC,EAAE4D,EAAEuC,KAAKpG,EAAEkB,EAAEmO,aAAatP,GAAGuP,MAAMrJ,GAAG1D,EAAE,eAAe8L,KAAKpO,GAAGe,EAAEiD,KAAK,CAACgD,KAAK,EAAEsI,MAAMtO,EAAEuO,KAAKjN,EAAE,GAAGoL,QAAQ3N,EAAEyP,KAAK,MAAMlN,EAAE,GAAGmN,EAAE,MAAMnN,EAAE,GAAGoN,EAAE,MAAMpN,EAAE,GAAGqN,EAAEC,IAAI3O,EAAEyK,gBAAgB5L,EAAE,MAAMA,EAAEwO,WAAWtI,KAAKjF,EAAEiD,KAAK,CAACgD,KAAK,EAAEsI,MAAMtO,IAAIC,EAAEyK,gBAAgB5L,IAAI,GAAGyN,EAAEc,KAAKpN,EAAE4O,SAAS,CAAC,IAAM/P,EAAEmB,EAAEiK,YAAYmE,MAAMrJ,GAAGjG,EAAED,EAAEK,OAAO,EAAE,GAAK,EAAFJ,EAAI,CAACkB,EAAEiK,YAAYlL,IAAEA,IAAEqG,YAAY,GAAG,IAAIb,IAAIxF,EAAE,EAAEA,EAAED,EAAEC,IAAIiB,EAAE6O,OAAOhQ,EAAEE,GAAGsE,KAAK0J,EAAEe,WAAWhO,EAAEiD,KAAK,CAACgD,KAAK,EAAEsI,QAAQtO,IAAIC,EAAE6O,OAAOhQ,EAAEC,GAAGuE,IAAI,CAAC,CAAC,MAAM,GAAG,IAAIrD,EAAE+N,SAAS,GAAG/N,EAAE8O,OAAO/K,IAAEjE,EAAEiD,KAAK,CAACgD,KAAK,EAAEsI,MAAMtO,QAAQ,CAACwE,IAAI1F,GAAE,EAAG,MAAK,KAAMA,EAAEmB,EAAE8O,KAAKC,QAAQhK,EAAElG,EAAE,KAAKiB,EAAEiD,KAAK,CAACgD,KAAK,EAAEsI,MAAMtO,IAAIlB,GAAGkG,EAAE7F,OAAO,CAAC,CAACa,GAAG,CAAC,CAAC+J,qBAAqBjL,EAAEE,GAAG,IAAMD,EAAEkB,IAAE8J,cAAc,YAAY,OAAOhL,EAAEkQ,UAAUnQ,EAAEC,CAAC,EAAE,SAASmQ,EAAEpQ,EAAEE,EAAED,EAAED,EAAEwC,GAAG,GAAGtC,IAAI4N,EAAP,CAAkBpI,IAAIQ,OAAE,IAAS1D,EAAEvC,EAAEoQ,OAAO7N,GAAGvC,EAAEqQ,KAAK,IAAMpL,EAAEhE,EAAEhB,QAAG,EAAOA,EAAEqQ,gBAAuBrK,GAAGb,cAAcH,IAAIgB,GAAGsK,QAAO,QAAI,IAAStL,EAAEgB,OAAE,GAAQA,EAAE,IAAIhB,EAAElF,IAAKyQ,KAAKzQ,EAAEC,EAAEuC,QAAI,IAASA,GAAGvC,EAAEoQ,OAAO,IAAI7N,GAAG0D,EAAEjG,EAAEqQ,KAAKpK,QAAG,IAASA,IAAIhG,EAAEkQ,EAAEpQ,EAAEkG,EAAEwK,KAAK1Q,EAAEE,EAAE2N,QAAQ3H,EAAE1D,GAApP,CAA2E,OAA6KtC,CAAC,CAAAwF,IAAAiL,IAAStL,MAAAA,WAAAA,CAAYrF,EAAEE,GAAGS,KAAKiQ,KAAK,GAAGjQ,KAAKkQ,UAAK,EAAOlQ,KAAKmQ,KAAK9Q,EAAEW,KAAKoQ,KAAK7Q,CAAC,CAAC8Q,iBAAiB,OAAOrQ,KAAKoQ,KAAKC,UAAU,CAACC,WAAW,OAAOtQ,KAAKoQ,KAAKE,IAAI,CAACxK,CAAAA,CAAEzG,GAAG,IAAM2O,IAAIE,QAAQ3O,GAAGwO,MAAMzO,GAAGU,KAAKmQ,KAAKtO,GAAGxC,GAAGkR,eAAe/P,KAAGgQ,WAAWjR,GAAE,GAAIgO,EAAEU,YAAYpM,EAAEkD,IAAIQ,EAAEgI,EAAEe,WAAW/J,EAAE,EAAE/E,EAAE,EAAEqE,EAAEvE,EAAE,GAAG,UAAK,IAASuE,GAAG,CAAC,GAAGU,IAAIV,EAAEgL,MAAM,CAAC9J,IAAIxF,EAAE,IAAIsE,EAAE0C,KAAKhH,EAAE,IAAIkR,EAAElL,EAAEA,EAAEmL,YAAY1Q,KAAKX,GAAG,IAAIwE,EAAE0C,KAAKhH,EAAE,IAAIsE,EAAEkL,KAAKxJ,EAAE1B,EAAEiL,KAAKjL,EAAEoJ,QAAQjN,KAAKX,GAAG,IAAIwE,EAAE0C,OAAOhH,EAAE,IAAIoR,EAAEpL,EAAEvF,KAAKX,IAAIW,KAAKiQ,KAAK1M,KAAKhE,GAAGsE,EAAEvE,IAAIE,EAAE,CAAC+E,IAAIV,GAAGgL,QAAQtJ,EAAEgI,EAAEe,WAAW/J,IAAI,CAAC,OAAOgJ,EAAEU,YAAYzN,IAAEqB,CAAC,CAAClC,CAAAA,CAAEN,GAAG0F,IAAIxF,EAAE,EAAE,IAAI,IAAMD,KAAKU,KAAKiQ,cAAc3Q,SAAI,IAASA,EAAE2N,SAAS3N,EAAEsR,KAAKvR,EAAEC,EAAEC,GAAGA,GAAGD,EAAE2N,QAAQvN,OAAO,GAAGJ,EAAEsR,KAAKvR,EAAEE,KAAKA,GAAG,GAAC,MAAOkR,EAAEH,QAAAA,GAAW,OAAOtQ,KAAKoQ,MAAME,MAAMtQ,KAAK6Q,IAAI,CAACnM,WAAAA,CAAYrF,EAAEE,EAAED,EAAEuC,GAAG7B,KAAKuG,KAAK,EAAEvG,KAAK8Q,KAAKzD,EAAErN,KAAKkQ,UAAK,EAAOlQ,KAAK+Q,KAAK1R,EAAEW,KAAKgR,KAAKzR,EAAES,KAAKoQ,KAAK9Q,EAAEU,KAAKiR,QAAQpP,EAAE7B,KAAK6Q,KAAKhP,GAAG6H,cAAa,CAAE,CAAC2G,cAAAA,GAAiBtL,IAAI1F,EAAEW,KAAK+Q,KAAKV,WAAW,IAAM9Q,EAAES,KAAKoQ,KAAK,YAAO,IAAS7Q,GAAG,KAAKF,GAAGkP,SAAahP,EAAE8Q,WAAYhR,CAAC,CAAC6R,aAAAA,GAAgB,OAAOlR,KAAK+Q,IAAI,CAACI,WAAcA,GAAA,OAAOnR,KAAKgR,IAAI,CAACJ,IAAAA,CAAKvR,EAAEE,EAAES,MAAMX,EAAEoQ,EAAEzP,KAAKX,EAAEE,GAAGgB,EAAElB,GAAGA,IAAIgO,GAAG,MAAMhO,GAAG,KAAKA,GAAGW,KAAK8Q,OAAOzD,GAAGrN,KAAKoR,OAAOpR,KAAK8Q,KAAKzD,GAAGhO,IAAIW,KAAK8Q,MAAMzR,IAAI8N,GAAGnN,KAAKuC,EAAElD,QAAG,IAASA,EAAE2N,WAAWhN,KAAK8M,EAAEzN,QAAG,IAASA,EAAEkP,SAASvO,KAAKmN,EAAE9N,GAA1zHA,IAAGqG,EAAErG,IAAI,mBAAmBA,IAAI4D,OAAOC,UAAsxH4C,CAAEzG,GAAGW,KAAKmP,EAAE9P,GAAGW,KAAKuC,EAAElD,EAAE,CAACgS,CAAAA,CAAEhS,GAAG,OAAOW,KAAK+Q,KAAKV,WAAWiB,aAAajS,EAAEW,KAAKgR,KAAK,CAAC7D,CAAAA,CAAE9N,GAAGW,KAAK8Q,OAAOzR,IAAIW,KAAKoR,OAAOpR,KAAK8Q,KAAK9Q,KAAKqR,EAAEhS,GAAG,CAACkD,CAAAA,CAAElD,GAAGW,KAAK8Q,OAAOzD,GAAG9M,EAAEP,KAAK8Q,MAAM9Q,KAAK+Q,KAAKL,YAAYpB,KAAKjQ,EAAEW,KAAKmN,EAAE3M,IAAE+Q,eAAelS,IAAIW,KAAK8Q,KAAKzR,CAAC,CAACyN,CAAAA,CAAEzN,GAAG,IAAM6N,OAAO3N,EAAEyN,WAAW1N,GAAGD,EAAEwC,EAAE,iBAAiBvC,EAAEU,KAAKwR,KAAKnS,SAAI,IAASC,EAAE0O,KAAK1O,EAAE0O,GAAGF,EAAExD,cAAclJ,EAAE9B,EAAEiG,EAAEjG,EAAEiG,EAAE,IAAIvF,KAAKiR,UAAU3R,GAAG,GAAGU,KAAK8Q,MAAMX,OAAOtO,EAAE7B,KAAK8Q,KAAKnR,EAAEJ,OAAO,CAAC,IAAMF,EAAE,IAAIoS,IAAE5P,EAAE7B,MAAMV,EAAED,EAAEyG,EAAE9F,KAAKiR,SAAS5R,EAAEM,EAAEJ,GAAGS,KAAKmN,EAAE7N,GAAGU,KAAK8Q,KAAKzR,CAAC,CAAC,CAACmS,IAAAA,CAAKnS,GAAG0F,IAAIxF,EAAE+N,EAAEtI,IAAI3F,EAAE4N,SAAS,YAAO,IAAS1N,GAAG+N,EAAEpI,IAAI7F,EAAE4N,QAAQ1N,EAAE,IAAIuO,EAAEzO,IAAIE,CAAC,CAAC4P,CAAAA,CAAE9P,GAAGqG,EAAE1F,KAAK8Q,QAAQ9Q,KAAK8Q,KAAK,GAAG9Q,KAAKoR,QAAQ,IAAsC7L,EAAhChG,EAAES,KAAK8Q,KAAK/L,IAAIzF,EAAEuC,EAAE,EAAE,IAAU0D,KAAKlG,EAAEwC,IAAItC,EAAEG,OAAOH,EAAEgE,KAAKjE,EAAE,IAAImR,EAAEzQ,KAAKqR,EAAExN,KAAK7D,KAAKqR,EAAExN,KAAK7D,KAAKA,KAAKiR,UAAU3R,EAAEC,EAAEsC,GAAGvC,EAAEsR,KAAKrL,GAAG1D,IAAIA,EAAEtC,EAAEG,SAASM,KAAKoR,KAAK9R,GAAGA,EAAE0R,KAAKN,YAAY7O,GAAGtC,EAAEG,OAAOmC,EAAE,CAACuP,IAAAA,CAAK/R,EAAEW,KAAK+Q,KAAKL,YAAYnR,GAAG,IAAIS,KAAK0R,QAAO,GAAG,EAAGnS,GAAGF,GAAGA,IAAIW,KAAKgR,MAAM,CAAC,IAAMzR,EAAEF,EAAEqR,YAAYrR,EAAEsS,SAAStS,EAAEE,CAAC,CAAC,CAACqS,YAAAA,CAAavS,QAAG,IAASW,KAAKoQ,OAAOpQ,KAAK6Q,KAAKxR,EAAEW,KAAK0R,OAAOrS,GAAG,QAAQ8P,EAAEC,WAAAA,GAAc,OAAOpP,KAAK6R,QAAQzC,OAAO,CAACkB,QAAAA,GAAW,OAAOtQ,KAAKoQ,KAAKE,IAAI,CAAC5L,WAAAA,CAAYrF,EAAEE,EAAED,EAAEuC,EAAE0D,GAAGvF,KAAKuG,KAAK,EAAEvG,KAAK8Q,KAAKzD,EAAErN,KAAKkQ,UAAK,EAAOlQ,KAAK6R,QAAQxS,EAAEW,KAAK8O,KAAKvP,EAAES,KAAKoQ,KAAKvO,EAAE7B,KAAKiR,QAAQ1L,EAAW,EAATjG,EAAEI,QAAU,KAAKJ,EAAE,IAAI,KAAKA,EAAE,IAAIU,KAAK8Q,KAAKhN,MAAMxE,EAAEI,OAAO,GAAGoS,KAAK,IAAItL,QAAQxG,KAAKiN,QAAQ3N,GAAGU,KAAK8Q,KAAKzD,CAAC,CAACuD,IAAAA,CAAKvR,EAAEE,EAAES,KAAKV,EAAEuC,GAAG,IAAM0D,EAAEvF,KAAKiN,QAAQlI,IAAIR,GAAE,EAAG,QAAG,IAASgB,EAAElG,EAAEoQ,EAAEzP,KAAKX,EAAEE,EAAE,IAAGgF,GAAGhE,EAAElB,IAAIA,IAAIW,KAAK8Q,MAAMzR,IAAI8N,KAAMnN,KAAK8Q,KAAKzR,OAAO,CAAC,IAAcG,EAAEgB,EAAVqB,EAAExC,EAAU,IAAIA,EAAEkG,EAAE,GAAG/F,EAAE,EAAEA,EAAE+F,EAAE7F,OAAO,EAAEF,KAAIgB,EAAEiP,EAAEzP,KAAK6B,EAAEvC,EAAEE,GAAGD,EAAEC,MAAO2N,IAAI3M,EAAER,KAAK8Q,KAAKtR,IAAI+E,KAAKhE,EAAEC,IAAIA,IAAIR,KAAK8Q,KAAKtR,GAAGgB,IAAI6M,EAAEhO,EAAEgO,EAAEhO,IAAIgO,IAAIhO,IAAImB,GAAG,IAAI+E,EAAE/F,EAAE,IAAIQ,KAAK8Q,KAAKtR,GAAGgB,CAAC,CAAC+D,IAAI1C,GAAG7B,KAAK+R,EAAE1S,EAAE,CAAC0S,CAAAA,CAAE1S,GAAGA,IAAIgO,EAAErN,KAAK6R,QAAQ5G,gBAAgBjL,KAAK8O,MAAM9O,KAAK6R,QAAQrH,aAAaxK,KAAK8O,KAAKzP,GAAG,GAAG,EAAC,MAAO2P,UAAUG,EAAEzK,WAAAA,GAAciE,SAASlJ,WAAWO,KAAKuG,KAAK,CAAC,CAACwL,CAAAA,CAAE1S,GAAGW,KAAK6R,QAAQ7R,KAAK8O,MAAMzP,IAAIgO,OAAE,EAAOhO,CAAC,EAAQ4P,MAAAA,UAAUE,EAAEzK,WAAAA,GAAciE,SAASlJ,WAAWO,KAAKuG,KAAK,CAAC,CAACwL,CAAAA,CAAE1S,GAAGW,KAAK6R,QAAQG,gBAAgBhS,KAAK8O,OAAOzP,GAAGA,IAAIgO,EAAE,EAAC,MAAO6B,UAAUC,EAAEzK,WAAAA,CAAYrF,EAAEE,EAAED,EAAEuC,EAAE0D,GAAGoD,MAAMtJ,EAAEE,EAAED,EAAEuC,EAAE0D,GAAGvF,KAAKuG,KAAK,CAAC,CAACqK,IAAAA,CAAKvR,EAAEE,EAAES,MAAM,IAAqD6B,EAA8E0D,GAA/HlG,EAAEoQ,EAAEzP,KAAKX,EAAEE,EAAE,IAAI8N,KAAKF,IAAe7N,EAAEU,KAAK8Q,KAAKjP,EAAExC,IAAIgO,GAAG/N,IAAI+N,GAAGhO,EAAE4S,UAAU3S,EAAE2S,SAAS5S,EAAE6S,OAAO5S,EAAE4S,MAAM7S,EAAE8S,UAAU7S,EAAE6S,QAAQ5M,EAAElG,IAAIgO,IAAI/N,IAAI+N,GAAGxL,GAAGA,GAAG7B,KAAK6R,QAAQO,oBAAoBpS,KAAK8O,KAAK9O,KAAKV,GAAGiG,GAAGvF,KAAK6R,QAAQQ,iBAAiBrS,KAAK8O,KAAK9O,KAAKX,GAAGW,KAAK8Q,KAAKzR,EAAC,CAACiT,WAAAA,CAAYjT,GAAG,mBAAmBW,KAAK8Q,KAAK9Q,KAAK8Q,KAAKhR,KAAKE,KAAKiR,SAASsB,MAAMvS,KAAK6R,QAAQxS,GAAGW,KAAK8Q,KAAKwB,YAAYjT,EAAE,EAAQsR,MAAAA,EAAEjM,WAAAA,CAAYrF,EAAEE,EAAED,GAAGU,KAAK6R,QAAQxS,EAAEW,KAAKuG,KAAK,EAAEvG,KAAKkQ,UAAK,EAAOlQ,KAAKoQ,KAAK7Q,EAAES,KAAKiR,QAAQ3R,CAAC,CAACgR,WAAW,OAAOtQ,KAAKoQ,KAAKE,IAAI,CAACM,IAAAA,CAAKvR,GAAGoQ,EAAEzP,KAAKX,EAAE,EAAO,IAA6D0S,EAAE1S,IAAEmT,uBAA6EC,GAAtDV,IAAIjE,EAAE2C,IAAIpR,IAAEqT,kBAAkB,IAAInP,KAAK,SAAiB,CAAClE,EAAEE,EAAED,KAAK,IAAMuC,EAAEvC,GAAGqT,cAAcpT,EAAEwF,IAAIQ,EAAE1D,EAAE+Q,WAAW,QAAG,IAASrN,EAAE,CAAC,IAAMlG,EAAEC,GAAGqT,cAAc,KAAK9Q,EAAE+Q,WAAWrN,EAAE,IAAIkL,EAAElR,EAAE+R,aAAazN,IAAIxE,GAAGA,OAAE,EAAOC,GAAG,CAAA,EAAG,CAAC,OAAOiG,EAAEqL,KAAKvR,GAAGkG,ICAz6NsN,IAAAA,cAAgBxT,EAAEqF,WAAAA,GAAciE,SAASlJ,WAAWO,KAAK8S,cAAc,CAACP,KAAKvS,MAAMA,KAAK+S,UAAK,CAAM,CAAChJ,gBAAAA,GAAmB,IAAM1K,EAAEsJ,MAAMoB,mBAAmB,OAAO/J,KAAK8S,cAAcH,eAAetT,EAAE8O,WAAW9O,CAAC,CAACsM,MAAAA,CAAOtM,GAAG,IAAMC,EAAEU,KAAKgT,SAAShT,KAAK8I,aAAa9I,KAAK8S,cAAcpJ,YAAY1J,KAAK0J,aAAaf,MAAMgD,OAAOtM,GAAGW,KAAK+S,KAAKlR,EAAEvC,EAAEU,KAAKyJ,WAAWzJ,KAAK8S,cAAc,CAACnI,iBAAAA,GAAoBhC,MAAMgC,oBAAoB3K,KAAK+S,MAAMnB,cAAa,EAAG,CAAChH,oBAAAA,GAAuBjC,MAAMiC,uBAAuB5K,KAAK+S,MAAMnB,cAAa,EAAG,CAACoB,MAAAA,GAAS,OAAO1T,CAAC,GAAmGC,GAAjGiB,IAAEyS,eAAc,EAAGzS,IAAauH,WAAE,EAAG9D,WAAWiP,2BAA2B,CAACC,WAAW3S,MAAYyD,WAAWmP,2BCA7oB/T,GDAuqBE,IAAI,CAAC4T,WAAW3S,OAA0DyD,WAAWoP,qBAAqB,IAAI9P,KAAK,SCAxxBlE,GAAG,CAACwC,EAAE0C,cAAcA,EAAEA,EAAEwC,gBAAgB,KAAKuM,eAAeC,OAAOlU,EAAEwC,EAAC,IAAKyR,eAAeC,OAAOlU,EAAEwC,EAAC,GCAnG0C,EAAE,CAAC+B,WAAU,EAAGC,KAAKC,OAAOC,UAAUpH,IAAEqH,SAAQ,EAAGC,WAAW9E,KAAGrB,IAAE,CAACnB,EAAEkF,EAAE1C,EAAErB,KAAK,IAAMgT,KAAKhU,EAAEwB,SAASzB,GAAGiB,EAAEuE,IAAIzF,EAAE2E,WAAW2C,oBAAoB5B,IAAIzF,GAAG,QAAG,IAASD,GAAG2E,WAAW2C,oBAAoB1B,IAAI3F,EAAED,EAAE,IAAIwI,KAAKxI,EAAE4F,IAAI1E,EAAEsO,KAAKzP,GAAG,aAAaG,EAAE,CAAC,IAAW+E,EAAG/D,EAAFsO,KAAI,MAAM,CAAC5J,GAAAA,CAAI1E,GAAG,IAAMhB,EAAEqC,EAAEmD,IAAIlF,KAAKE,MAAM6B,EAAEqD,IAAIpF,KAAKE,KAAKQ,GAAGR,KAAK0H,cAAcnD,EAAE/E,EAAEH,EAAE,EAAEoU,IAAAA,CAAK5R,GAAG,YAAO,IAASA,GAAG7B,KAAKoB,EAAEmD,OAAE,EAAOlF,GAAGwC,CAAC,EAAE,CAAC,GAAG,WAAWrC,EAAgG,MAAMoF,MAAM,mCAAmCpF,GAA7I,CAAC,IAAW+E,EAAG/D,EAAFsO,KAAI,OAAO,SAAStO,GAAG,IAAMhB,EAAEQ,KAAKuE,GAAG1C,EAAE/B,KAAKE,KAAKQ,GAAGR,KAAK0H,cAAcnD,EAAE/E,EAAEH,EAAE,CAAC,GAAoD,SAASG,EAAEH,GAAG,MAAM,CAACwC,EAAE0C,KAAI,MAAA,iBAAiBA,EAAE/D,IAAEnB,EAAEwC,EAAE0C,IAAKlF,EAAkJA,EAAnImB,EAAqIqB,EAAjIhC,eAAe0E,GAAU1C,EAAE6C,YAAY2C,eAAe9C,EAAE/D,EAAE,IAAInB,EAAEkM,SAAQ,GAAIlM,GAAGmB,EAAErB,OAAOsB,yBAAyBoB,EAAE0C,QAAG,GAA3I,IAAElF,EAAemB,EAAyI,CCApwB,SAASA,EAAEA,GAAG,OAAOnB,EAAE,IAAImB,EAAE8G,OAAM,EAAGhB,WAAU,GAAI,CCMhD,SAASoN,sBAAsBC,EAAMC,OAelCC,mBAdDF,QAAiBG,IAATH,GAGTI,EAAQ,IAAIC,KAAKL,UACjBC,EAEOG,iBAAkC,OAAA,aAAA,oBAOvCE,EAAMF,IAAAA,UAAAA,UAAAA,aACNF,EAAQE,kBAA8B,gBACtCG,EAAOH,YACb/P,KAAAA,MAAUiQ,SAAVjQ,MAAiB6P,QAAjB7P,OAA2BkQ,KAfhB,KAiBR,SAASC,mBAAmBC,GAC3BA,IAKJC,EAEIC,EAPAF,EAAAA,QAAAA,OAAQ,MAARA,QAGAG,EAAY,IAAIP,KAAKI,OAAAA,OAEzBC,EADc,IAAIL,KAAKI,EAAKA,EAAAA,UAALA,uBACQG,YAC3BC,EAAQhI,OAAAA,MAAW6H,QACnBC,EAAU9H,aAAY6H,YACnBG,+BAA0CF,IAAAA,UAAAA,aAErD,4FAwCO,SAASG,oBAAoBC,iBAE1BF,GADAb,EAAO,IAAIK,KAAKU,IACRf,qBAAAA,gBACRW,EAAUX,IAAAA,QAAAA,aAAAA,aAChB,MAAA,KAAA,MAAUa,eAASF,GAEhB,SAASK,sBAAsBN,GAC5BG,IAAAA,EAAAA,QAAAA,EAAQhI,aAAW6H,gBAAX7H,gBACR8H,GAAWD,wCACjBrQ,OAAUwQ,eAASF,GAEhB,SAASM,YAAYC,OAMpBL,mBALO,MAAPK,MAEAN,EAAY,IAAIP,KAAKa,WAErBR,EADU,IAAIL,KAAKa,qBACYN,EAAAA,UAC/BC,EAAQhI,OAAAA,MAAW6H,QACnBC,EAAU9H,aAAY6H,YACnBG,YAAAA,oBAA0CF,2BAErD,CAAO,SAASQ,eAAenB,wBACf,MAARA,MAEAoB,EAAU,IAAIf,KAAKL,IAChBoB,sBAAAA,kBAAwDA,EAAAA,gBAAD,UAAA,iBAA8DA,WAEhI,CAAO,SAASC,aAAarB,uBACZ,sDACG,IAAIK,KAAKL,KACboB,g3BC/GhB,SAAAE,UAAA,IAAAC,EAAA,CAAA,+BAAA,gBAAA,gBAAA,gBAAA,gBAAA,umBAAA,gBAAA,gBAAA,iBAAA,OAAAD,QAAA,WAAA,OAAAC,CAAA,IAAA,sTAAA,IAAAC,UAAAC,QAAA,SAAAA,QAAAC,EAAAC,GAAA,IAAAC,EAAAN,UAAA,OAAAG,QAAA,SAAAI,EAAAC,GAAA,OAAAF,EAAAC,GAAA,IAAA,GAAAH,EAAAC,EAAA,CAAA,MAAA,IAAA,IAAAI,EAAAN,QAAAO,EAAAV,YAAA,IAAA,GAAA,SAAAW,SAAAF,EAAA,MAAAE,SAAAF,EAAA,MAAA,GAAAE,SAAAF,EAAA,MAAA,GAAAE,SAAAF,EAAA,MAAA,EAAAE,SAAAF,EAAA,MAAA,GAAAE,SAAAF,EAAA,MAAA,EAAAE,SAAAF,EAAA,MAAA,EAAA,MAAAC,EAAApS,KAAAoS,EAAAE,QAAA,CAAA,MAAAC,GAAAH,EAAApS,KAAAoS,EAAAE,QAAA,GAAA,GAAO,IAAME,YAAc,gCAAA,yj+CCG3B,IAAMC,SAASD,4BACFE,sBAAwB,SAACC,EAAmBtC,EAAUuC,EAAeC,mBAAoBC,IAAS5W,wBAAAqU,IAAArU,cAAAA,aAAU6W,IAAc7W,UAAAA,aAAAqU,IAAArU,aAAAA,aAAG,KAAM8W,IAAU9W,UAAAA,aAAAqU,IAAArU,cAAAA,aAAU+W,IAAY/W,UAAAA,aAAAqU,IAAArU,aAAAA,aAAG,KAAMgX,IAAahX,YAAAA,YAAAqU,IAAArU,aAAAA,aAAG,GAAuBiX,KAAWjX,YAAAA,YAAAqU,IAAArU,cAAAA,iBAAOkX,KAAalX,wBAAAqU,IAAArU,eAAAA,cAAUmX,KAASnX,UAAAA,aAAAqU,IAAArU,cAAAA,iBAAOoX,KAAOpX,UAAAA,aAAAqU,IAAArU,cAAAA,iBAAuBqX,aAAYrX,UAAAA,OAAAA,mBAAAqU,GAAEiD,KAAWtX,YAAAA,MAAAA,mBAAAqU,EAAEkD,KAAevX,kBAAAA,mBAAAqU,EAAEmD,KAAaxX,kBAAAA,mBAAAqU,EAAEoD,KAAQzX,wBAAAqU,IAAArU,cAAAA,cAAG,CACzX0X,EAAAA,OAEOC,EAAIC,kBAAAA,mBAAAC,uBAAA,GAAA,YACTjB,EAAYe,EAAIG,iBAAAA,kBAAAD,uBAAA,oKAIEtB,kBAC2BpC,EAAAA,IALjCwD,MAQTd,EAuEDc,EAAII,iBAAAA,kBAAAF,uBAKcjB,GAAAA,4GAAAA,EAAYe,EAAIK,iBAAAA,kBAAAH,uBAGJ1D,GAAAA,+IAAAA,EAAAA,IAHAwD,cAKwCxD,MALxCwD,MAOVb,EAAaa,EAAIM,iBAAAA,kBAAAJ,uBAAA,8eAAA,OAAA,mlCAAA,kPAAA,sTAAA,OAAA,OAAA,OAAA,8XAAA,OAAA,4BAAA,OAAA,6LAAA,OAAA,ooBAAA,OAAA,0WAAA,OAAA,OAAA,OAAA,gBAMX1D,WAGIwC,EAAqBgB,EAAIO,kBAAAA,mBAAAL,uBAAA,+DAAA,OAGd1D,GACC,SAAC/R,GAAMqV,IAAAA,EAAAA,EAAAA,OAAAA,IAAAA,MAA8BrV,UAAAA,sBAazB+R,WAGkD0C,MAAAA,SAAAA,IAAAA,aAKlD1C,sBAGyD0C,MAAAA,OAAAA,EAAAA,iBAKzD1C,IAAAA,mBAII0C,MAAAA,OAAAA,EAAAA,WAA+Bc,EAAIQ,kBAAAA,mBAAAN,6DACJ1D,2BACZsD,IAAAA,OAAuB,sBAElCtD,EAAAA,eAEN0C,MAAAA,OAAc,EAAdA,EAAAA,QAA+Bc,EAAIS,kBAAAA,mBAAAP,oHACJ1D,oBADAwD,OAEJ,KAA3Bd,MAAAA,OAAc,EAAdA,IAAAA,OAAgCc,EAAIU,kBAAAA,mBAAAR,uBAAA,UAAA,gBACP1D,wBAOnCA,EAAAA,IA9DrBwD,KFuElC,SAASW,aAAarW,EAAOsW,EAAcpE,GAC1ClS,IAAAA,EAAAA,QAAAA,OAAAA,MAAAA,MAEEM,SAAS4R,EAAoBlS,EAAQA,EAAQsW,SAC/CpE,OAAqBoE,EACdxL,OAAAA,MAAWxK,KAAXwK,UAAAA,MAAsC,8BAGtCyL,GAAwDC,EAAAC,eAA5BnW,EAAAA,WAAAA,kBAAfoW,EAAWF,KACzBG,EAAmBJ,UAAoB,6BAC7C,KAAA,MAAUI,SAAV,MAA8BD,KEhB0EL,CAAavB,MAAAA,OAAAA,EAAAA,EAAAA,WAA0BS,EAAerD,GAAaoD,WAKrHpD,WAGC0C,MAAAA,GAAAA,IAAAA,sBAA0Dc,EAAIkB,kBAAAA,mBAAAhB,uBAanE1D,yXAAAA,4BAAAA,+GAAAA,0iBAAAA,qBAAAA,6BAb+DwD,YAe7BxD,uGAIPA,EAAAA,IAnBoCwD,cAwB5BxD,MAxB4BwD,KA0BnDN,MAAAA,GAAAA,OAAYyB,EAAZzB,EAAAA,qBAAAyB,EAAAA,uBAGuB3E,EAAAA,SACUkD,MAAAA,GAAAA,OAAY0B,EAAZ1B,mBAAA0B,EAAAA,IAAAA,eAEV5E,EAAAA,SACUkD,MAAAA,GAAAA,OAAY2B,EAAZ3B,IAAAA,eAAA2B,EAAAA,mBAEV7E,WACUkD,MAAAA,GAAAA,OAAY4B,EAAZ5B,IAAAA,eAA0B,EAA1B4B,iBAEV9E,qCACSkD,MAAAA,OAAAA,EAAAA,UAAiCZ,EAAoBI,MAAAA,OAAAA,EAAAA,aAEzFQ,MAAAA,GAAAA,OAAY6B,EAAZ7B,eAAA6B,UAAyCvB,EAAIwB,kBAAAA,mBAAAtB,uBAAA,GAAA,mCAG/BR,MAAAA,GAAAA,OAAY+B,EAAZ/B,qBAAA+B,IAAAA,MAAgD/B,MAAAA,GAAAA,OAAYgC,EAAZhC,IAAAA,eAAAgC,EAAAA,qBAOtBlF,8CACnBkD,MAAAA,SAAAA,WAIvCR,MAAAA,GAAAA,EAAAA,gBAAAA,cAAiDc,EAAI2B,kBAAAA,mBAAAzB,uBAa3C1D,snHAAAA,kBAEkCA,gEAI3BA,EAAAA,IAnBgCwD,cAmCJxD,oBACUA,EAAAA,SAE7BkD,MAAAA,OAAY,EAAZA,IAAAA,aAgBmBlD,sBAEfkD,MAAAA,OAAAA,EAAAA,UACAA,MAAAA,OAAY,EAAZA,mBAkBelD,WAEvBkD,MAAAA,OAAAA,EAAAA,IAAAA,OAOtBR,MAAAA,GAAAA,OAAc0C,EAAd1C,YAAA0C,IAAAA,cAAsD5B,EAAI6B,kBAAAA,mBAAA3B,uBAahD1D,GAAAA,8BAAAA,EAAAA,IAb4CwD,YAeVxD,iBAA6D0C,MAAAA,GAAAA,OAAc4C,EAAd5C,IAAAA,YAA6B,EAA7B4C,wBAAsD,aAAuC5C,MAAAA,GAAAA,OAAc6C,EAAd7C,IAAAA,YAAA6C,EAAAA,IAAAA,oBAEtM7C,MAAAA,SAAAA,eAAA,KAAkDc,EAAIgC,kBAAAA,mBAAA9B,uBAY5C1D,UAAAA,OAAAA,gBAAAA,oBAEkCA,sBAY9CA,wBAhQLwD,YAmQmDxD,qBACkB0C,MAAAA,OAAc,EAAdA,EAAAA,oBAGlB1C,yBACkB0C,MAAAA,OAAc,EAAdA,UAIYA,MAAAA,OAAc,EAAdA,iBAQxF1C,mCAOgBA,EAAAA,gBAGAA,IAAAA,4BAGAA,kBAKZ4C,MAAAA,OAAY,EAAZA,mBAA0B,SAAC6C,OAAGC,aAAKlC,EAAImC,kBAAAA,mBAAAjC,uBAMvB+B,GAAAA,iQAAAA,OAAAA,OAAAA,MAAAA,SAAAA,IAAAA,MAEJA,MAAAA,GAAAA,UAAkBjC,EAAIoC,kBAAAA,mBAAAlC,uBAGZ1D,2SAAAA,EAAAA,SAA2CyF,MAAAA,GAAAA,OAAGI,EAAHJ,gBAAAI,EAAAA,EAAAA,UAA+BJ,MAAAA,GAAAA,OAAGK,EAAHL,EAAAA,iBAAe,EAAfK,IAAAA,oBAAsCL,MAAAA,GAAAA,OAAGM,EAAHN,EAAAA,iBAAe,EAAfM,IAAAA,oBAAwCN,MAAAA,GAAAA,OAAGO,EAAHP,IAAAA,cAAAO,IAAAA,yBAC9JP,MAAAA,GAAAA,OAAGQ,EAAHR,EAAAA,mBAAAQ,oBAAqCjG,YAAwCyF,MAAAA,GAAAA,OAAGC,EAAHD,gBAAe,EAAfC,IAAAA,uBAAqC1F,yBAAwCA,WAJ5IwD,SASlBiC,EAAAA,WAAAA,OAAiB,SAACS,GAAY1C,IAAAA,EAAAA,EAAAA,OAAAA,EAAI2C,kBAAAA,mBAAAzC,uBAAA,UAAA,OAClCwC,MAAAA,GAAAA,IAAAA,MAAmB1C,EAAI4C,kBAAAA,mBAAA1C,uBAAA,8GAAA,qEAC+BwC,MAAAA,SAAAA,IAAAA,MAAmBA,MAAAA,SAAAA,IAAAA,cFnXpH,SAASG,mBAAmBtG,EAAMC,OAGrBM,gBAFXP,EACD,OAAO,QAGPA,aAAgBK,KAChBC,EAAMN,YACNE,EAAQF,IAAAA,UACRO,EAAOP,oBAGFuG,QAAOvG,OAAPuG,eAAsCvG,YAAmBA,YAAkBA,GAChFM,EAAMN,EAAAA,OACNE,EAAQF,IAAAA,SACRO,EAAOP,wCAGKA,EASL,OAAA,QARDwG,EAAS,IAAInG,KAAKL,GACpByG,MAAMD,IAAAA,SACN,OAAO,KACXlG,EAAMkG,IAAAA,QACNtG,EAAQsG,EAAAA,aACRjG,EAAOiG,IAAAA,OAKX,CAAME,EAAKpG,2BAAX,IACMqG,EAAKzG,IAAAA,QAAAA,gBACL0G,EAAOrG,0BAENN,aAAuByG,eAAMC,YAAUtW,MAAQsW,eAAMD,gBAAlBE,GEyVIN,CAAmBZ,MAAAA,OAAAA,EAAAA,UAAezF,UAGlCyF,iBAAsBzF,gBAAqCyF,IAAAA,UAAAA,YAAuBzF,uBAAsCA,IAAAA,0BAiBpIA,MAnVGwD,cAuV8BxD,WAA0D4G,OAAAA,EAEnF/D,EAAcH,MAAAA,OAAc,EAAdA,IAAAA,aAAdkE,EAAAA,UACClE,MAAAA,OAAAA,EAAAA,iBAI2C1C,0BAAkD6G,OAAAA,EAG9FhE,EAAcH,MAAAA,SAAAA,iBAAwB,EAAtCmE,UACCnE,MAAAA,SAAAA,mBAIsD1C,MAtW5DwD,KAwWKd,MAAAA,OAAc,EAAdA,IAAAA,MAGRA,MAAAA,GAAAA,UAA6Bc,EAAIsD,kBAAAA,mBAAApD,uBAAA,GAAA,OAAA,uBAE8B1D,MAFlCwD,KAIrBd,MAAAA,OAAAA,EAAAA,EAAAA,sBAOuD1C,IAAAA,0BAC3D+G,OAAAA,EAAAnE,IAAAA,YAAiB,EAAjBmE,YAAAA,OAAoDvD,EAAIwD,kBAAAA,mBAAAtD,uBAAA,GAAA,cAAAuD,OAAAA,EAExDrE,gBAAiB,EAAjBqE,IAAAA,QAAAA,OAA2C,SAACC,EAAmBjM,GAAKkM,IAAAC,EAAAD,EAAAA,SAAK3D,EAAI6D,kBAAAA,mBAAA3D,iIAQzDyD,OAAAA,EAAAvE,IAAAA,cAAAuE,mBAAoD3D,EAAI8D,kBAAAA,mBAAA5D,gCAAIzI,gBAAkB+E,EAAAA,IAAlB/E,YAA8D+E,yBAK1HF,sBAAqBsH,OAAAA,EAACF,IAAAA,QAAAA,eAAAE,EAAAA,UAAkDpH,UAExEA,gCACAO,mBAAmB2G,mBAI3BA,IAAAA,QAAAA,eAAmC,SAACjG,EAAKsG,OAAMC,aAAKhE,EAAIiE,kBAAAA,mBAAA/D,uBACxDzI,GAAAA,ivBAAAA,0GAAAA,8YAAAA,6VAAAA,EAAYuI,EAAIkE,kBAAAA,mBAAAhE,uBAAA,GAAA,mCAQI1D,4BAAqD2H,OAAAA,EACrD9E,EAAc5B,iBAAd0G,EAAAA,UACA5G,sBAAsBmG,EAAAA,UAAAA,MAA+BK,gBAyBjD1G,oBAAoBI,MAAAA,OAAAA,EAAAA,WAKjBA,MAAAA,OAAAA,EAAAA,UAAkB2G,OAAAA,EACzB/E,EAAc5B,MAAAA,SAAAA,EAAAA,qBAAmB,EAAjC2G,EAAAA,KAWcxF,SAA8BnB,MAAAA,SAAAA,EAAAA,kBAUpCA,MAAAA,OAAAA,EAAAA,WAA6BA,MAAAA,OAAG,EAAHA,EAAAA,oBAAyBA,MAAAA,OAAAA,EAAAA,EAAAA,eAAoB4G,OAAAA,EAAAX,IAAAA,QAAAW,OAAmCA,EAAnCA,UAAkDN,SAAO,EAAzDM,EAAAA,YAAmEL,OAAAA,EAAIN,IAAAA,QAAJM,OAAuCA,EAAnCA,UAAkDD,SAAlDC,EAAAA,IAAAA,OASnJ3G,oBAAoBI,MAAAA,OAAAA,EAAAA,WAInBA,MAAAA,SAAAA,UAAgB6G,OAAAA,EACvBjF,EAAc5B,MAAAA,OAAG,EAAHA,iBAAiB,EAA/B6G,EAAAA,SAjGhCb,KA8GEzD,EAAIuE,kBAAAA,mBAAArE,uBAAA,UAelBsE,OAAAA,EAAApF,kBAAAoF,IAAAA,QAAAA,OAAoDxE,EAAIyE,kBAAAA,mBAAAvE,uBAAA,iBAAAwE,OAAAA,EAEpEtF,IAAAA,YAAAsF,EAAAA,mBAA2C,SAAChB,EAAmBjM,kBAAUuI,EAAI2E,kBAAAA,mBAAAzE,uBAC7EwD,+BAAAA,YAAAA,QAAAA,OAAmC,SAACjG,EAAKsG,GAAMa,IAAAC,EAAAD,EAAAA,SAAK5E,EAAI8E,kBAAAA,mBAAA5E,uBAAA,6pDAAA,OAAA,4BAAA,OAAA,wpBAAA,OAAA,iRAAA,OAAA,OAAA,oRAAA,wTAAA0E,OAAAA,EAcLvF,EAAc5B,iBAAkB,EAAhCmH,UAA0CG,OAAAA,EAAM1F,EAAc5B,EAAAA,qBAAdsH,UAMzDnG,SAA8BnB,iBAKcjB,mBAERiB,iBAIQjB,2BAE9CiB,IAAAA,MAAwBA,UA8BnBG,aAAaH,WAAyBC,eAAeD,IAAAA,OAQtDJ,oBAAoBI,IAAAA,OAIpBC,eAAeD,EAAAA,eAGfA,kBAAyBuH,OAAAA,EAAG3F,EAAc5B,IAAAA,aAAduH,EAAAA,IAAAA,gBAG5BxI,MAjFQwD,KAiFuCvC,EAAAA,uBAK/CA,UAaAD,YAAYC,GAKZJ,oBAAoBI,EAAAA,aAIpBC,eAAeD,YAGfwH,OAAAA,EAAA5F,EAAc5B,EAAAA,mBAAgB,EAA9BwH,aAAA,KAAmDxH,EAAAA,qBAGnDjB,WAA+CiB,IAAAA,kBAYzCjB,gCACN0I,OAAAA,EAAAxB,YAAAwB,OAAmCA,EAAnCA,IAAAA,MAAkDnB,SAAO,EAAzDmB,iBAA8EC,OAAAA,EAAAzB,EAAAA,oBAAAyB,OAAmCA,EAAnCA,EAAAA,aAAkDpB,SAAO,EAAzDoB,EAAAA,eAAiFnF,EAAIoF,kBAAAA,mBAAAlF,uBAAA,qFAAAmF,OAAAA,EAC3J3B,IAAAA,QAD2J2B,OACxHA,EAAnCA,UAAkDtB,SAAO,EAAzDsB,IAAAA,UACJC,OAAAA,EACI5B,YADJ4B,OACuCA,EAAnCA,UAAkDvB,SAAO,EAAzDuB,iBAGF9I,sBArIEwD,QAsIRuF,OAAAA,EAAA7B,YAAA6B,OAAmCA,EAAnCA,UAAkDxB,WAAlDwB,iBAA4EC,OAAAA,EAAA9B,EAAAA,oBAAA8B,OAAmCA,EAAnCA,IAAAA,MAAkDzB,SAAO,EAAzDyB,EAAAA,WAA6ExF,EAAIyF,kBAAAA,mBAAAvF,uBAAA,GAAA,OAAA,OAAAwF,OAAAA,EACrJhC,EAAAA,oBADqJgC,OAClHA,EAAnCA,UAAkD3B,SAAO,EAAzD2B,IAAAA,eAERC,OAAAA,EAAAjC,EAAAA,oBAAAiC,OAAmCA,EAAnCA,UAAkD5B,SAAlD4B,EAAAA,EAAAA,WAA6E3F,EAAI4F,kBAAAA,mBAAA1F,uBAC3E1D,GAAAA,OAAAA,cAAAA,0BACJwD,EAAI6F,kBAAAA,mBAAA3F,uBAAA,GAAA,cAAA2E,OAAAA,EAAYnB,EAAAA,oBAAZmB,OAA+CA,EAAnCA,UAAkDd,SAAlDc,EAAAA,EAAAA,oBAgB5D7E,EAAI8F,kBAAAA,mBAAA5F,qCAKkF1D,uCAEpEA,EAAAA,IA3pBGwD,KA4pBoCL,MAAAA,GAAAA,OAAWoG,EAAXpG,gBAAwB,EAAxBoG,IAAAA,eACtCvJ,EAAAA,SAC2CmD,MAAAA,GAAAA,OAAWqG,EAAXrG,EAAAA,oBAAAqG,IAAAA,eAC3CxJ,kBACDA,8BAhqBGwD,YAoqBiExD,4BAExEmD,MAAAA,GAAAA,OAAWsG,EAAXtG,EAAAA,wBAAAsG,EAAAA,WAAoC,SAACC,kBAAgBlG,EAAImG,kBAAAA,mBAAAjG,uBAAA,GAAA,cAAQgG,uBAIO1J,EAAAA,SAE5EmD,MAAAA,GAAAA,OAAWyG,EAAXzG,EAAAA,mBAAyB,EAAzByG,WAA+B,SAACC,GAAgBrG,IAAAA,EAAAA,EAAAA,OAAAA,EAAIsG,kBAAAA,mBAAApG,uBAAA,UAAA,OAAQmG,IAAAA,MAA5DD,WAGuD5J,IAAAA,0BAKzDwD,EAAIuG,kBAAAA,mBAAArG,uBAAA,2FAAA,gBAGM1D,oBACwCA,aA3wBpDwD,EAAIwG,iBAAAA,kBAAAtG,uBAAA,UAAA,mZAAA,OAAA,yMAAA,m1CAAA,gBAQuE1D,oBAC1BA,EAAAA,SAC7C8C,EAAcU,EAAIyG,iBAAAA,kBAAAvG,uBAAA,gLAEVZ,OAIiB,SAAC7U,GAACic,IAAAA,EAAAA,EAAAA,EAAK,OAAAA,OAAAA,EAAA5G,gBAAqB,EAArB4G,UAAA5G,EAAwBrV,KAAMA,cAI3C+U,EACAM,WAA+B,WAAA,WAEzBtD,MAvB7BwD,KAyBYT,IAAkBC,EAAYQ,EAAI2G,iBAAAA,kBAAAzG,uBAAA,ozBAMrB1D,eAMFiD,EACAK,IAAAA,OAA6B,WAAA,WAEvBtD,EAAAA,IAxC7BwD,KA0CYT,IAAkBE,EAAUO,EAAI4G,iBAAAA,kBAAA1G,yIAMnB1D,EAAAA,IANewD,kBAqB5BxD,EAAAA,UA3E/B,ECYP,SAASqK,KAAKC,EAAYC,GACtB,OAAO,IAAI7c,SAAQ,SAAUC,GAAW,OAAO6c,WAAW7c,EAAS2c,EAAYC,KACnF,CA4BA,SAASE,UAAU3c,GACf,QAASA,GAA+B,mBAAfA,EAAMQ,IACnC,CAcA,SAASoc,aAAaC,EAAQC,GAC1B,IACI,IAAIC,EAAgBF,IAChBF,UAAUI,GACVA,EAAcvc,MAAK,SAAUF,GAAU,OAAOwc,GAAS,EAAMxc,EAAQ,IAAI,SAAU0c,GAAS,OAAOF,GAAS,EAAOE,MAGnHF,GAAS,EAAMC,EAK3B,CAFI,MAAOC,GACHF,GAAS,EAAOE,EACxB,CACA,CAMA,SAASC,cAAcC,EAAOJ,EAAUK,GAEpC,YAD4B,IAAxBA,IAAkCA,EAAsB,IACrD5d,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAI8e,EAASC,EAAqBxf,EAAGyf,EACrC,OAAO7c,YAAYnC,MAAM,SAAUif,GAC/B,OAAQA,EAAGzc,OACP,KAAK,EACDsc,EAAUhb,MAAM8a,EAAMlf,QACtBqf,EAAsB/K,KAAKgL,MAC3Bzf,EAAI,EACJ0f,EAAGzc,MAAQ,EACf,KAAK,EACD,OAAMjD,EAAIqf,EAAMlf,QAChBof,EAAQvf,GAAKif,EAASI,EAAMrf,GAAIA,GAChCyf,EAAMhL,KAAKgL,MACED,EAAsBF,GAA7BG,GACND,EAAsBC,EACf,CAAC,EAvEjB,IAAI1d,SAAQ,SAAUC,GACzB,IAAI2d,EAAU,IAAIC,eAClBD,EAAQE,MAAMC,UAAY,WAAc,OAAO9d,GAAY,EAC3D2d,EAAQI,MAAMC,YAAY,KAClC,MAiEoF,CAAC,EAAa,IAH9C,CAAC,EAAa,GAMlD,KAAK,EACDN,EAAGxc,OACHwc,EAAGzc,MAAQ,EACf,KAAK,EAED,QADEjD,EACK,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,EAAcuf,GAE9C,GACA,GACA,CAQA,SAASU,kCAAkCC,GAEvC,OADAA,EAAQvd,UAAK4R,GAAW,eACjB2L,CACX,CAyBA,SAASC,MAAMhe,GACX,OAAOkU,SAASlU,EACpB,CAIA,SAASie,QAAQje,GACb,OAAOke,WAAWle,EACtB,CACA,SAASme,WAAWne,EAAOoe,GACvB,MAAwB,iBAAVpe,GAAsB0Y,MAAM1Y,GAASoe,EAAcpe,CACrE,CACA,SAASqe,YAAY7S,GACjB,OAAOA,EAAO8S,QAAO,SAAUC,EAAKve,GAAS,OAAOue,GAAOve,EAAQ,EAAI,EAAG,GAAI,EAClF,CACA,SAASwe,MAAMxe,EAAOye,GAElB,YADa,IAATA,IAAmBA,EAAO,GACR,GAAlB3T,KAAK4T,IAAID,GACF3T,KAAK0T,MAAMxe,EAAQye,GAAQA,GAK9BE,EAAc,EAAIF,EACf3T,KAAK0T,MAAMxe,EAAQ2e,GAAeA,EAEjD,CAyEA,SAASC,OAAO1T,EAAGpN,GACf,IAAI+gB,EAAK3T,EAAE,KAAO,GACd4T,EAAKhhB,EAAE,KAAO,GACdihB,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAEjCD,IADAC,IAHwE,MAAPhU,EAAE,KACK,MAAPpN,EAAE,OAGtD,GACbohB,GAAM,MAENF,IADAC,IAN+C/T,EAAE,KAAO,KACTpN,EAAE,KAAO,OAM3C,GACbmhB,GAAM,MAMN/T,EAAE,KAJF6T,IADAC,IATkC,MAAP9T,EAAE,KACK,MAAPpN,EAAE,OAShB,KAEP+gB,EAAKC,GACL,QACQ,IAHdE,GAAM,OAIN9T,EAAE,GAAM+T,GAAM,GAAMC,CACxB,CAKA,SAASC,YAAYjU,EAAGpN,GACpB,IAAI+gB,EAAK3T,EAAE,KAAO,GAAIkU,EAAY,MAAPlU,EAAE,GAAamU,EAAKnU,EAAE,KAAO,GAAIoU,EAAY,MAAPpU,EAAE,GAC/D4T,EAAKhhB,EAAE,KAAO,GAAIyhB,EAAY,MAAPzhB,EAAE,GAAa0hB,EAAK1hB,EAAE,KAAO,GACpDihB,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAEjCD,IADAC,GAAMI,GAFsDG,EAAY,MAAP3hB,EAAE,OAGtD,GACbohB,GAAM,MAKNF,IAJAC,GAAMI,EAAKI,KACE,MAEbR,GADM,MAANA,GACMK,EAAKE,KACE,IACbP,GAAM,MAYN/T,EAAE,KAVF6T,IADAC,GAAMI,EAAKK,KACE,MAEbT,GADM,MAANA,GACMK,EAAKG,KACE,MAEbR,GADM,MAANA,GACMM,EAAKC,KACE,KAEPV,EAAKY,EAAKL,EAAKI,EAAKH,EAAKE,EAAKD,EAAKR,GACnC,QACQ,IAHdE,GAAM,OAIN9T,EAAE,GAAM+T,GAAM,GAAMC,CACxB,CAKA,SAASQ,QAAQxU,EAAGyU,GAChB,IAAId,EAAK3T,EAAE,GAEE,KADbyU,GAAQ,KAEJzU,EAAE,GAAKA,EAAE,GACTA,EAAE,GAAK2T,GAEFc,EAAO,IACZzU,EAAE,GAAM2T,GAAMc,EAASzU,EAAE,KAAQ,GAAKyU,EACtCzU,EAAE,GAAMA,EAAE,IAAMyU,EAASd,IAAQ,GAAKc,IAItCzU,EAAE,GAAMA,EAAE,KADVyU,GAAQ,IACiBd,IAAQ,GAAKc,EACtCzU,EAAE,GAAM2T,GAAMc,EAASzU,EAAE,KAAQ,GAAKyU,EAE9C,CAKA,SAASC,aAAa1U,EAAGyU,GAER,IADbA,GAAQ,MAICA,EAAO,IACZzU,EAAE,GAAKA,EAAE,KAAQ,GAAKyU,EACtBzU,EAAE,GAAKA,EAAE,IAAMyU,IAGfzU,EAAE,GAAKA,EAAE,IAAOyU,EAAO,GACvBzU,EAAE,GAAK,GAEf,CAKA,SAAS2U,OAAO3U,EAAGpN,GACfoN,EAAE,IAAMpN,EAAE,GACVoN,EAAE,IAAMpN,EAAE,EACd,CACA,IAAIgiB,GAAK,CAAC,WAAY,YAClBC,GAAK,CAAC,WAAY,WAMtB,SAASC,QAAQnc,GACb,IAAIoc,EAAU,CAAC,EAAGpc,EAAE,KAAO,GAC3Bgc,OAAOhc,EAAGoc,GACVd,YAAYtb,EAAGic,IACfG,EAAQ,GAAKpc,EAAE,KAAO,EACtBgc,OAAOhc,EAAGoc,GACVd,YAAYtb,EAAGkc,IACfE,EAAQ,GAAKpc,EAAE,KAAO,EACtBgc,OAAOhc,EAAGoc,EACd,CACA,IAAIC,GAAK,CAAC,WAAY,WAClBC,GAAK,CAAC,WAAY,WAClBC,IAAM,CAAC,EAAG,GACVC,GAAK,CAAC,EAAG,YACTC,GAAK,CAAC,EAAG,WAQb,SAASC,WAAWC,EAAOC,GAWvB,IAVA,IAAI/hB,EArJR,SAASgiB,aAAaF,GAIlB,IADA,IAAIlgB,EAAS,IAAIqgB,WAAWH,EAAMxiB,QACzBH,EAAI,EAAGA,EAAI2iB,EAAMxiB,OAAQH,IAAK,CAEnC,IAAI+iB,EAAWJ,EAAMK,WAAWhjB,GAEhC,GAAe,IAAX+iB,EACA,OAAO,IAAIE,aAAcC,OAAOP,GAEpClgB,EAAOzC,GAAK+iB,CACpB,CACI,OAAOtgB,CACX,CAuIcogB,CAAaF,GAGnBQ,GADAhjB,EAAS,CAAC,EAAGU,EAAIV,SACE,GAAK,GACxBijB,EAAQjjB,EAAO,GAAKgjB,EACpBE,EAAK,CAAC,EAJVT,EAAOA,GAAQ,GAKXU,EAAK,CAAC,EAAGV,GACTW,EAAK,CAAC,EAAG,GACTC,EAAK,CAAC,EAAG,GAERxjB,EAAI,EAAGA,EAAIojB,EAAOpjB,GAAQ,GAC3BujB,EAAG,GAAK1iB,EAAIb,EAAI,GAAMa,EAAIb,EAAI,IAAM,EAAMa,EAAIb,EAAI,IAAM,GAAOa,EAAIb,EAAI,IAAM,GAC7EujB,EAAG,GAAK1iB,EAAIb,GAAMa,EAAIb,EAAI,IAAM,EAAMa,EAAIb,EAAI,IAAM,GAAOa,EAAIb,EAAI,IAAM,GACzEwjB,EAAG,GAAK3iB,EAAIb,EAAI,IAAOa,EAAIb,EAAI,KAAO,EAAMa,EAAIb,EAAI,KAAO,GAAOa,EAAIb,EAAI,KAAO,GACjFwjB,EAAG,GAAK3iB,EAAIb,EAAI,GAAMa,EAAIb,EAAI,IAAM,EAAMa,EAAIb,EAAI,KAAO,GAAOa,EAAIb,EAAI,KAAO,GAC/EshB,YAAYiC,EAAIlB,IAChBR,QAAQ0B,EAAI,IACZjC,YAAYiC,EAAIjB,IAChBN,OAAOqB,EAAIE,GACX1B,QAAQwB,EAAI,IACZtC,OAAOsC,EAAIC,GACXhC,YAAY+B,EAAId,KAChBxB,OAAOsC,EAAIb,IACXlB,YAAYkC,EAAIlB,IAChBT,QAAQ2B,EAAI,IACZlC,YAAYkC,EAAInB,IAChBL,OAAOsB,EAAIE,GACX3B,QAAQyB,EAAI,IACZvC,OAAOuC,EAAID,GACX/B,YAAYgC,EAAIf,KAChBxB,OAAOuC,EAAIb,IAEfc,EAAG,GAAK,EAERC,EADAD,EAAG,GAAK,GACA,EAER,IAAIE,EAAM,CADVD,EAAG,GAAK,EACM,GACd,OAAQL,GACJ,KAAK,GACDM,EAAI,GAAK5iB,EAAIb,EAAI,IACjB+hB,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAK5iB,EAAIb,EAAI,IACjB+hB,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAK5iB,EAAIb,EAAI,IACjB+hB,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAK5iB,EAAIb,EAAI,IACjB+hB,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAK5iB,EAAIb,EAAI,IACjB+hB,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAK5iB,EAAIb,EAAI,GACjB+hB,aAAa0B,EAAK,GAClBzB,OAAOwB,EAAIC,GAEf,KAAK,EACDA,EAAI,GAAK5iB,EAAIb,EAAI,GACjBgiB,OAAOwB,EAAIC,GACXnC,YAAYkC,EAAIlB,IAChBT,QAAQ2B,EAAI,IACZlC,YAAYkC,EAAInB,IAChBL,OAAOsB,EAAIE,GAEf,KAAK,EACDC,EAAI,GAAK5iB,EAAIb,EAAI,GACjB+hB,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAK5iB,EAAIb,EAAI,GACjB+hB,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAK5iB,EAAIb,EAAI,GACjB+hB,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAK5iB,EAAIb,EAAI,GACjB+hB,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAK5iB,EAAIb,EAAI,GACjB+hB,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAK5iB,EAAIb,EAAI,GACjB+hB,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAK5iB,EAAIb,EAAI,GACjB+hB,aAAa0B,EAAK,GAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAK5iB,EAAIb,GACbgiB,OAAOuB,EAAIE,GACXnC,YAAYiC,EAAIlB,IAChBR,QAAQ0B,EAAI,IACZjC,YAAYiC,EAAIjB,IAChBN,OAAOqB,EAAIE,GAWnB,OARAvB,OAAOqB,EAAIljB,GACX6hB,OAAOsB,EAAInjB,GACX4gB,OAAOsC,EAAIC,GACXvC,OAAOuC,EAAID,GACXlB,QAAQkB,GACRlB,QAAQmB,GACRvC,OAAOsC,EAAIC,GACXvC,OAAOuC,EAAID,IACF,YAAcA,EAAG,KAAO,GAAGzd,SAAS,KAAKpB,WAC7C,YAAc6e,EAAG,KAAO,GAAGzd,SAAS,KAAKpB,WACzC,YAAc8e,EAAG,KAAO,GAAG1d,SAAS,KAAKpB,WACzC,YAAc8e,EAAG,KAAO,GAAG1d,SAAS,KAAKpB,SAClD,CA6EA,SAASkf,YAAYC,EAASC,EAAeC,EAAgBvE,GACzD,IAAIwE,EAAkBlkB,OAAOiI,KAAK8b,GAASI,QAAO,SAAUC,GAAa,OAzb7E,SAASC,SAASC,EAAUC,GACxB,OAZJ,SAASC,SAASF,EAAUC,GACxB,IAAK,IAAInkB,EAAI,EAAGsE,EAAI4f,EAAS/jB,OAAQH,EAAIsE,IAAKtE,EAC1C,GAAIkkB,EAASlkB,KAAOmkB,EAChB,OAAO,EAGf,OAAO,CACX,CAKYC,CAASF,EAAUC,EAC/B,CAuboFF,CAASJ,EAAgBG,EAAW,IAGhHK,EAAuBpE,kCAAkCb,cAAc0E,GAAiB,SAAUE,GAAa,OA1DvH,SAASM,WAAWC,EAAQX,GACxB,IAAIY,EAAoBvE,kCAAkC,IAAIle,SAAQ,SAAU0iB,GAC5E,IAAIC,EAAgBjQ,KAAKgL,MAGzBV,aAAawF,EAAOI,KAAK,KAAMf,IAAgB,WAE3C,IADA,IAAIgB,EAAW,GACNC,EAAK,EAAGA,EAAK3kB,UAAUC,OAAQ0kB,IACpCD,EAASC,GAAM3kB,UAAU2kB,GAE7B,IAKIC,EALAC,EAAetQ,KAAKgL,MAAQiF,EAEhC,OAAKE,EAAS,GArB1B,SAASI,oBAAoBF,GACzB,MAA6B,mBAAfA,CAClB,CAwBgBE,CAFAF,EAAaF,EAAS,IAGfH,GAAY,WAAc,OAAUtiB,MAAO2iB,EAAYhQ,SAAUiQ,WAG5EN,GAAY,WACR,OAAO,IAAI1iB,SAAQ,SAAUkjB,GACzB,IAAIC,EAAezQ,KAAKgL,MACxBV,aAAa+F,GAAY,WAErB,IADA,IAAIK,EAAU,GACLN,EAAK,EAAGA,EAAK3kB,UAAUC,OAAQ0kB,IACpCM,EAAQN,GAAM3kB,UAAU2kB,GAE5B,IAAI/P,EAAWiQ,EAAetQ,KAAKgL,MAAQyF,EAE3C,IAAKC,EAAQ,GACT,OAAOF,EAAW,CAAE9F,MAAOgG,EAAQ,GAAIrQ,SAAUA,IAGrDmQ,EAAW,CAAE9iB,MAAOgjB,EAAQ,GAAIrQ,SAAUA,GAClE,GACA,GACA,IAzBuB2P,GAAY,WAAc,MAAQ,CAAEtF,MAAOyF,EAAS,GAAI9P,SAAUiQ,KA0BzF,GACA,KACI,OAAO,WACH,OAAOP,EAAkB7hB,MAAK,SAAUyiB,GAAkB,OAAOA,MACpE,CACL,CAc8Hd,CAAWX,EAAQK,GAAYJ,EAAiB,GAAEtE,IAC5K,OAAO,WACH,OAAO5d,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAmB4kB,EAAmBC,EAAgBC,EAAYjW,EAClE,OAAO1M,YAAYnC,MAAM,SAAUif,GAC/B,OAAQA,EAAGzc,OACP,KAAK,EAAG,MAAO,CAAC,EAAaohB,GAC7B,KAAK,EAED,MAAO,CAAC,EAAajF,cADLM,EAAGxc,QAC+B,SAAUsiB,GAAgB,OAAOvF,kCAAkCuF,IAAkB,GAAElG,IAC7I,KAAK,EAED,OADA+F,EAAoB3F,EAAGxc,OAChB,CAAC,EAAanB,QAAQ0jB,IAAIJ,IAGrC,KAAK,EAGD,IAFAC,EAAiB5F,EAAGxc,OACpBqiB,EAAa,CAAA,EACRjW,EAAQ,EAAGA,EAAQwU,EAAgB3jB,SAAUmP,EAC9CiW,EAAWzB,EAAgBxU,IAAUgW,EAAehW,GAExD,MAAO,CAAC,EAAciW,GAE9C,GACA,GACK,CACL,CAuCA,SAASG,YACL,IAAIC,EAAIC,OACJ3lB,EAAI4lB,UAER,OAMM,GANErF,YAAY,CAChB,gBAAiBmF,EACjB,mBAAoBA,EACpB,gBAAiBA,EACjB,qBAAsB1lB,EACtB,qBAAsBA,GAE9B,CAoBA,SAAS6lB,aAEL,IAAIH,EAAIC,OACJ3lB,EAAI4lB,UACR,OAQM,GARErF,YAAY,CAChB,4BAA6BvgB,EAC7B,2BAA4BA,EACW,KAAtCA,EAAE8lB,QAAU,IAAI/V,QAAQ,UACzB,oCAAqC2V,EACrC,mBAAoBA,EACpB,sBAAuBA,EACvB,wBAAyBA,GAEjC,CAQA,SAASK,WAEL,IAAIL,EAAIC,OAER,OAOM,GAPEpF,YAAY,CAChB,kBAAmBmF,EACnB,sBAAuBA,EACvB,YAAaA,EACiB,IAL1BE,UAKFE,OAAO/V,QAAQ,SACjB,aAAc2V,EACd,oBAAqBA,GAE7B,CAQA,SAASM,kBAEL,IAAIN,EAAIC,OACJre,EAAcoe,EAAEpe,YAAazC,EAAW6gB,EAAE7gB,SAC9C,OAOM,GAPE0b,YAAY,CAChB,WAAYmF,IACV,iBAAkBA,KAClB,eAAgBA,KAChB,gBAAiBA,GACnBpe,KAAiB,mBAAoBA,EAAYlH,WACjDyE,GAAY,uBAAwBA,EAASzE,WAErD,CAOA,SAAS6lB,iBAKL,IAAIP,EAAIC,OACR,OAzOJ,SAASO,iBAAiBC,GACtB,MAAO,yCAAyC/X,KAAKpH,OAAOmf,GAChE,CAyOID,CAAiBR,EAAEU,QAEO,8BAAtBpf,OAAO0e,EAAEW,QACjB,CAOA,SAASC,UACL,IAAQC,EACJb,EAAIC,OAER,OAOM,GAPEpF,YAAY,CAChB,YAAaqF,UACb,kBAAoB,OAACW,EAAK,OAAC9G,EAAK5U,SAAS2b,sBAA6C,EAAS/G,EAAGgH,OAAmCF,EAAK,IAC1I,0BAA2Bb,EAC3B,oBAAqBA,EACrB,uBAAwBA,EACxB,6BAA8BA,GAEtC,CAqDA,SAASgB,qBACL,IAAIhB,EAAIC,OACJ3lB,EAAI4lB,UACJe,EAAMjB,EAAEiB,IAAKC,EAAoBlB,EAAEkB,kBACvC,OAMM,GANErG,YAAY,GACd,sBAAuBvgB,GACzB4mB,GAAqB,YAAaA,EAAkBxmB,UACpD,wBAAyBslB,EACzBiB,EAAIE,SAAS,mCACbF,EAAIE,SAAS,+BAErB,CAmCA,SAASC,iBACL,IAAIhmB,EAAI+J,SAER,OAAQ/J,EAAEgmB,gBAAkBhmB,EAAEimB,kBAAoBjmB,EAAEkmB,qBAAuBlmB,EAAEmmB,sBAAsB3mB,KAAKQ,EAC5G,CAOA,SAASomB,YACL,IAAIC,EAAetB,aACfuB,EAAYd,UACZZ,EAAIC,OACJ3lB,EAAI4lB,UACJ7kB,EAAI,aAGR,OAAIomB,EAQM,GAPE5G,YAAY,GACd,iBAAkBmF,GAIpB1lB,EAAEe,IAAM,iBAAkBf,EAAEe,KAC1B,WAAY,IAAIsmB,WAGjBD,GACkG,GAAhG7G,YAAY,CAAC,wBAAyBmF,EAAG,gBAAiBA,EAAG,WAAWtX,KAAKpO,EAAEsnB,aAO9F,CAgLA,SAASC,eAAejY,GACpB,IAAI4P,EAAQ,IAAI9Z,MAAMkK,GAEtB,OADA4P,EAAM5P,KAAOA,EACN4P,CACX,CAYA,SAASsI,WAAWzI,EAAQ0I,EAAaC,GACrC,IAAYC,EAEZ,YADwB,IAApBD,IAA8BA,EAAkB,IAC7CjmB,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAIM,EAAG8mB,EACP,OAAOjlB,YAAYnC,MAAM,SAAUqnB,GAC/B,OAAQA,EAAG7kB,OACP,KAAK,EACDlC,EAAI+J,SACJgd,EAAG7kB,MAAQ,EACf,KAAK,EACD,OAAMlC,EAAE8B,KAAa,CAAC,EAAa,GAC5B,CAAC,EAAa6b,KAAKiJ,IAC9B,KAAK,EAED,OADAG,EAAG5kB,OACI,CAAC,EAAa,GACzB,KAAK,EACD2kB,EAAS9mB,EAAEgK,cAAc,UACzB+c,EAAG7kB,MAAQ,EACf,KAAK,EAED,OADA6kB,EAAG3kB,KAAKa,KAAK,CAAC,EAAK,CAAA,GAAI,KAChB,CAAC,EAAa,IAAIjC,SAAQ,SAAUgmB,EAAUC,GAC7C,IAAIC,GAAa,EACbjmB,EAAU,WACVimB,GAAa,EACbF,GACH,EAOGrB,GAFJmB,EAAOK,OAASlmB,EAChB6lB,EAAOM,QALM,SAAUhJ,GACnB8I,GAAa,EACbD,EAAQ7I,EACX,EAGW0I,EAAOnB,OAgBf0B,GAfJ1B,EAAM2B,YAAY,UAAW,QAAS,aACtC3B,EAAM4B,SAAW,WACjB5B,EAAM6B,IAAM,IACZ7B,EAAM8B,KAAO,IACb9B,EAAM+B,WAAa,SACff,GAAe,WAAYG,EAC3BA,EAAOa,OAAShB,EAGhBG,EAAOc,IAAM,cAEjB5nB,EAAE8B,KAAKsI,YAAY0c,GAIG,WAClB,IAAQrB,EAIJyB,IAK6I,cAA5I,OAACzB,EAAK,OAAC9G,EAAKmI,EAAOe,oBAA2C,EAASlJ,EAAG5U,eAAsC,EAAS0b,EAAGqC,YAC7H7mB,IAGA6c,WAAWuJ,EAAiB,IAEnC,GACDA,GAC5B,KACgB,KAAK,EACDN,EAAG5kB,OACH4kB,EAAG7kB,MAAQ,EACf,KAAK,EACD,OAAO,OAACujB,EAAK,OAAC9G,EAAKmI,EAAOe,oBAA2C,EAASlJ,EAAG5U,WAA+C0b,EAAG3jB,KAAc,CAAC,EAAa,GACxJ,CAAC,EAAa6b,KAAKiJ,IAC9B,KAAK,EAED,OADAG,EAAG5kB,OACI,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,EAAa8b,EAAO6I,EAAQA,EAAOe,gBACnD,KAAK,EAAG,MAAO,CAAC,EAAcd,EAAG5kB,QACjC,KAAK,GAED,OADA,OAAC0kB,EAAKC,EAAO/W,aAAiD8W,EAAGkB,YAAYjB,GACtE,CAAC,GACZ,KAAK,GAAI,MAAO,CAAC,GAEjC,GACA,GACA,CAKA,SAASkB,kBAAkBC,GACnBtJ,EAn/BR,SAASuJ,uBAAuBD,GAW5B,IAVA,IAAQxC,EACJ0C,EAAe,sBAAsBzkB,OAAOukB,EAAU,KACtDG,EAAW,sBAAsB/a,KAAK4a,GAEtCI,GADAC,EAAMF,EAAS,SAAM5U,EACR,CAAA,GACb+U,EAAa,0BACbC,EAAe,SAAUha,EAAMpN,GAC/BinB,EAAW7Z,GAAQ6Z,EAAW7Z,IAAS,GACvC6Z,EAAW7Z,GAAMvL,KAAK7B,EACzB,IACQ,CACL,IAAIqnB,EAAQF,EAAWlb,KAAK+a,EAAS,IACrC,IAAKK,EACD,MAEJ,IAAIC,EAAOD,EAAM,GACjB,OAAQC,EAAK,IACT,IAAK,IACDF,EAAa,QAASE,EAAKjlB,MAAM,IACjC,MACJ,IAAK,IACD+kB,EAAa,KAAME,EAAKjlB,MAAM,IAC9B,MACJ,IAAK,IACD,IAAIklB,EAAiB,yDAAyDtb,KAAKqb,GACnF,IAAIC,EAIA,MAAM,IAAIrkB,MAAM6jB,GAHhBK,EAAaG,EAAe,GAAI,OAAClD,EAAK,OAAC9G,EAAKgK,EAAe,IAAgChK,EAAKgK,EAAe,IAAgClD,EAAK,IAKxJ,MAEJ,QACI,MAAM,IAAInhB,MAAM6jB,GAEhC,CACI,MAAO,CAACG,EAAKD,EACjB,CA48BaH,CAAuBD,GAEhC,IAFA,IAA2CK,EAAM3J,EAAG,GAAI0J,EAAa1J,EAAG,GACpEpN,EAAUxH,SAASC,cAAcse,MAAAA,EAAiCA,EAAM,OACnExE,EAAK,EAAG2B,EAAK5mB,OAAOiI,KAAKuhB,GAAavE,EAAK2B,EAAGrmB,OAAQ0kB,IAAM,CACjE,IAAI8E,EAASnD,EAAG3B,GACZ1iB,EAAQinB,EAAWO,GAAQC,KAAK,KAGrB,UAAXD,EACAE,eAAevX,EAAQoU,MAAOvkB,GAG9BmQ,EAAQrH,aAAa0e,EAAQxnB,EAEzC,CACI,OAAOmQ,CACX,CAIA,SAASuX,eAAenD,EAAOnC,GAG3B,IAAK,IAAIM,EAAK,EAAGnF,EAAK6E,EAAOlV,MAAM,KAAMwV,EAAKnF,EAAGvf,OAAQ0kB,IAAM,CAC3D,IAGQiF,EAAmB3nB,EAHvB4nB,EAAWrK,EAAGmF,IACd2E,EAAQ,8CAA8Cpb,KAAK2b,MAEvDD,EAASN,EAAM,GAAIrnB,EAAQqnB,EAAM,GAAIQ,EAAWR,EAAM,GAC1D9C,EAAM2B,YAAYyB,EAAQ3nB,EAAO6nB,GAAY,IAEzD,CACA,CA6BA,IAKIC,UAAY,CAAC,YAAa,aAAc,SACxCC,SAAW,CAEX,kBACA,WACA,YACA,qBACA,mBACA,mBACA,mBACA,SACA,2BACA,UACA,UACA,iBACA,YACA,YACA,kBACA,eACA,eACA,SACA,YACA,OACA,mBACA,iBACA,gBACA,aACA,gBACA,aACA,gBACA,cACA,QACA,YACA,aACA,yBACA,eACA,WACA,aACA,UACA,YACA,mBACA,aACA,mBACA,WACA,WACA,YACA,iBACA,SACA,SACA,cACA,iBACA,aACA,uBACA,SACA,YA0NJ,SAASC,eAAeC,GACpB,OAAOA,EAAOC,WAClB,CAyGA,IAGIC,kBACAC,yBA8BJ,SAASC,yBACL,IAAIC,EAAQhqB,KAEZ,OAzBJ,SAASiqB,mBACL,IAGIC,OAH6BpW,IAA7BgW,2BAGAI,EAAmB,WACnB,IAAIC,EAAYC,wBAEZN,yBADAO,gBAAgBF,GACW/L,WAAW8L,EAnBnB,WAsBnBL,kBAAoBM,EAG3B,IAEL,CASIF,GACO,WAAc,OAAOhpB,UAAU+oB,OAAO,OAAQ,GAAQ,WACzD,IAAIG,EACJ,OAAOhoB,YAAYnC,MAAM,SAAUif,GAC/B,OAAQA,EAAGzc,OACP,KAAK,EAED,OAAK6nB,gBADLF,EAAYC,yBAERP,kBACO,CAAC,EAAcrmB,cAAc,GAAIqmB,mBAAmB,IA7yBnF,SAASS,uBACL,IAAIhqB,EAAI+J,SACR,OAAO/J,EAAEiqB,mBAAqBjqB,EAAEkqB,qBAAuBlqB,EAAEmqB,sBAAwBnqB,EAAEoqB,yBAA2B,IAClH,CA4yByBJ,GAIE,CAAC,EAAahE,kBAJe,CAAC,EAAa,GAJV,CAAC,EAAa,GAS1D,KAAK,EAIDrH,EAAGxc,OACH0nB,EAAYC,wBACZnL,EAAGzc,MAAQ,EACf,KAAK,EAID,OAHK6nB,gBAAgBF,KACjBN,kBAAoBM,GAEjB,CAAC,EAAcA,GAE1C,GACK,GAAI,CACT,CA8BA,SAASC,wBACL,IAAI9qB,EAAIqrB,OAMR,MAAO,CACH9K,WAAWF,QAAQrgB,EAAEsrB,UAAW,MAChC/K,WAAWF,QAAQrgB,EAAEurB,OAASlL,QAAQrgB,EAAEwrB,YAAcjL,WAAWF,QAAQrgB,EAAEyrB,WAAY,GAAI,MAC3FlL,WAAWF,QAAQrgB,EAAE0rB,QAAUrL,QAAQrgB,EAAE2rB,aAAepL,WAAWF,QAAQrgB,EAAEsrB,UAAW,GAAI,MAC5F/K,WAAWF,QAAQrgB,EAAEyrB,WAAY,MAEzC,CACA,SAASV,gBAAgBF,GACrB,IAAK,IAAI5qB,EAAI,EAAGA,EAAI,IAAKA,EACrB,GAAI4qB,EAAU5qB,GACV,OAAO,EAGf,OAAO,CACX,CAweA,SAAS2rB,oBAAoBC,GACzB,IAAIlM,EACJ,OAAOhe,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAIM,EAAG8qB,EAAMC,EAAUC,EAAqBzZ,EAAS0Z,EAAQhsB,EAC7D,OAAO4C,YAAYnC,MAAM,SAAU+lB,GAC/B,OAAQA,EAAGvjB,OACP,KAAK,EASD,IARAlC,EAAI+J,SACJ+gB,EAAO9qB,EAAEgK,cAAc,OACvB+gB,EAAW,IAAIvnB,MAAMqnB,EAAUzrB,QAC/B4rB,EAAmB,CAEnBE,EAAAA,UAAUJ,GAGL7rB,EAAI,EAAGA,EAAI4rB,EAAUzrB,SAAUH,EAER,YADxBsS,EAAUyW,kBAAkB6C,EAAU5rB,KAC1B6P,SACRyC,EAAQ4Z,OAIZD,UAFAD,EAASjrB,EAAEgK,cAAc,QAGzBihB,EAAO7gB,YAAYmH,GACnBuZ,EAAK1gB,YAAY6gB,GACjBF,EAAS9rB,GAAKsS,EAElBkU,EAAGvjB,MAAQ,EACf,KAAK,EACD,OAAMlC,EAAE8B,KAAa,CAAC,EAAa,GAC5B,CAAC,EAAa6b,KAAK,KAC9B,KAAK,EAED,OADA8H,EAAGtjB,OACI,CAAC,EAAa,GACzB,KAAK,EACDnC,EAAE8B,KAAKsI,YAAY0gB,GACnB,IAEI,IAAK7rB,EAAI,EAAGA,EAAI4rB,EAAUzrB,SAAUH,EAC3B8rB,EAAS9rB,GAAGmsB,eACbJ,EAAiBH,EAAU5rB,KAAM,EAOjE,CAH4B,QAEJ,OAAC0f,EAAKmM,EAAK/a,aAAiD4O,EAAGoJ,YAAY+C,EACnG,CACoB,MAAO,CAAC,EAAcE,GAE1C,GACA,GACA,CACA,SAASE,UAAU3Z,GACfA,EAAQoU,MAAM2B,YAAY,aAAc,SAAU,aAClD/V,EAAQoU,MAAM2B,YAAY,UAAW,QAAS,YAClD,CA0CA,SAAS+D,YAAYjqB,GACjB,OAAOkqB,WAAW,qBAAqB5nB,OAAOtC,EAAO,MAAMmqB,OAC/D,CAcA,SAASC,YAAYpqB,GACjB,OAAOkqB,WAAW,mBAAmB5nB,OAAOtC,EAAO,MAAMmqB,OAC7D,CA8CA,SAASE,YAAYrqB,GACjB,OAAOkqB,WAAW,sBAAsB5nB,OAAOtC,EAAO,MAAMmqB,OAChE,CAcA,SAASG,YAAYtqB,GACjB,OAAOkqB,WAAW,4BAA4B5nB,OAAOtC,EAAO,MAAMmqB,OACtE,CAcA,SAASI,YAAYvqB,GACjB,OAAOkqB,WAAW,kCAAkC5nB,OAAOtC,EAAO,MAAMmqB,OAC5E,CAcA,SAASK,UAAUxqB,GACf,OAAOkqB,WAAW,mBAAmB5nB,OAAOtC,EAAO,MAAMmqB,OAC7D,CAEA,IAAIpa,EAAIjF,KACJ2f,WAAa,WAAc,OAAO,CAAI,EAiE1C,IAIIC,QAAU,CAKVC,QAAS,GAETC,MAAO,CAAC,CAAEC,KAAM,uBAEhBC,MAAO,CAAC,CAAEC,WAAY,UAEtBC,KAAM,CAAC,CAAED,WAAY,eAErBE,KAAM,CAAC,CAAEF,WAAY,cAKrBG,IAAK,CAAC,CAAEC,SAAU,QAElBC,OAAQ,CAAC,CAAEL,WAAY,eAoK3B,IAAIM,sBA38CJ,SAASC,yBAEL,IADA,IAAIC,EAAgB9H,SACX,CACL,IAAI+H,EAAeD,EAAcE,OACjC,IAAKD,GAAgBA,IAAiBD,EAClC,OAAO,EAEX,IACI,GAAIC,EAAaE,SAASC,SAAWJ,EAAcG,SAASC,OACxD,OAAO,CASvB,CANQ,MAAO3O,GAEH,GAAIA,aAAiB9Z,OAAwB,kBAAf8Z,EAAM5P,KAChC,OAAO,EAEX,MAAM4P,CAClB,CACQuO,EAAgBC,CACxB,CACA,EAm9CA,IAGII,uBAAyB,IAAIhlB,IAAI,CACjC,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAC7G,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAChH,MAAO,MAAO,KAAM,MAAO,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAC9G,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAC/G,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,OAItDilB,qBAAuB,IAAIjlB,IAAI,CAC/B,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,QAEAklB,YAAc,CAAC,kBAAmB,iBAClCC,eAAiB,CAAC,YAAa,eAAgB,aAAc,UAAW,aAAc,YA2G1F,SAASC,gBAAgBC,GACrB,GAAIA,EAAMC,MACN,OAAOD,EAAMC,MAAMC,QAEvB,IACIA,EADAlE,EAAStf,SAASC,cAAc,UAEpCqf,EAAOtX,iBAAiB,2BAA2B,WAAc,OAAQwb,OAAU/Z,KACnF,IAAK,IAAIsQ,EAAK,EAAGnF,EAAK,CAAC,QAAS,sBAAuBmF,EAAKnF,EAAGvf,OAAQ0kB,IAAM,CACzE,IAAI7d,EAAO0Y,EAAGmF,GACd,IACIyJ,EAAUlE,EAAOmE,WAAWvnB,EAIxC,CAFQ,MAAOwf,GAAAA,CAGP,GAAI8H,EACA,KAEZ,CAEI,OADAF,EAAMC,MAAQ,CAAEC,QAASA,GAClBA,CACX,CAMA,SAASE,mBAAmBC,EAAIC,EAAYC,GAExC,OADIC,EAAkBH,EAAGI,yBAAyBJ,EAAGC,GAAaD,EAAGE,KAC5C,CAACC,EAAgBE,SAAUF,EAAgBG,SAAUH,EAAgBI,WAAa,EAC/G,CACA,SAASC,0BAA0BC,GAG/B,OADWtvB,OAAOiI,KAAKqnB,EAAIC,WACfpL,OAAOqL,eACvB,CACA,SAASA,eAAevuB,GACpB,MAAsB,iBAARA,IAAqBA,EAAI2oB,MAAM,cACjD,CAKA,SAAS6F,+BACL,OAAO9I,SACX,CAWA,SAAS+I,uBAAuBb,GAC5B,MAAkC,mBAApBA,EAAGc,YACrB,CA8CA,IAAI5L,QAAU,CAMV6L,MAnoDJ,SAASC,WACL,IAAIhF,EAAQhqB,KAIZ,OAAOgnB,YAAW,SAAUzkB,EAAG0c,GAC3B,IAAI5U,EAAW4U,EAAG5U,SAClB,OAAOpJ,UAAU+oB,OAAO,OAAQ,GAAQ,WACpC,IAAIuB,EAAQ0D,EAAgBC,EAAcC,EAAeC,EAAYC,EAA+CC,EAAsBC,EAAiBC,EAAgBC,EAAY5gB,EACvL,OAAO1M,YAAYnC,MAAM,SAAU+lB,GA+C/B,KA9CAwF,EAASlhB,EAASjI,MACX6jB,MAAM4G,SAvEd,QAwECoC,EAAiB5kB,EAASC,cAAc,QACzB2b,MAAM2B,YAAY,aAAc,SAAU,aACzDsH,EAAe,GACfC,EAAgB,CAAA,EAChBC,EAAa,SAAU3C,GACnB,IAAIiD,EAAOrlB,EAASC,cAAc,QAC9B2b,EAAQyJ,EAAKzJ,MAOjB,OANAA,EAAM4B,SAAW,WACjB5B,EAAM6B,IAAM,IACZ7B,EAAM8B,KAAO,IACb9B,EAAMwG,WAAaA,EACnBiD,EAAKjlB,YArFR,gBAsFGwkB,EAAevkB,YAAYglB,GACpBA,CACV,EACDL,EAAsB,SAAUM,EAAcC,GAC1C,OAAOR,EAAW,IAAIprB,OAAO2rB,EAAc,MAAM3rB,OAAO4rB,GAC3D,EAIDN,EAAuB,WAMnB,IAJA,IAAIO,EAAQ,CAAA,EAIHzL,EAAK,EAAG0L,EAAarG,SAAUrF,EAAK0L,EAAWpwB,OAAQ0kB,IAHxCmI,CAAAA,IACpBsD,EAAMtD,GAAQ/C,UAAUpf,KAAI,SAAUwlB,GAAY,OAAOP,EAAoB9C,EAAMqD,EAAU,GAGlFE,EAJSvD,CAITuD,EAAW1L,IAG1B,OAAOyL,CACV,EACDN,EAAkB,SAAUQ,GACxB,OAAOvG,UAAUwG,MAAK,SAAUJ,EAAUK,GACtC,OAAOF,EAAUE,GAAeC,cAAgBhB,EAAaU,IACzDG,EAAUE,GAAeE,eAAiBhB,EAAcS,EACpF,GACiB,EACDJ,EApBWhG,UAAUpf,IAAIglB,GAqBzBK,EAAaH,IAEb/D,EAAO7gB,YAAYukB,GAEdpgB,EAAQ,EAAGA,EAAQ2a,UAAU9pB,OAAQmP,IACtCqgB,EAAa1F,UAAU3a,IAAU2gB,EAAe3gB,GAAOqhB,YACvDf,EAAc3F,UAAU3a,IAAU2gB,EAAe3gB,GAAOshB,aAG5D,MAAO,CAAC,EAAc1G,SAASnG,QAAO,SAAUiJ,GAAQ,OAAOgD,EAAgBE,EAAWlD,GAAS,IACnH,GACA,GACA,GACA,EAmkDI6D,YAvwBJ,SAASC,eAAepR,GACpB,IAAkCqR,QAAlB,IAAPrR,EAAgB,CAAA,EAAKA,GAAeqR,MAC7C,OAAOrvB,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAIuwB,EAASC,EAA2BlF,EACpCnE,EACJ,OAAOhlB,YAAYnC,MAAM,SAAUqnB,GAC/B,OAAQA,EAAG7kB,OACP,KAAK,EACD,OAuBpB,SAASiuB,eAEL,OAAOlL,YAAcmB,WACzB,CA1ByB+J,IAGLF,EA7RpB,SAASG,aACL,IAAIC,EAAUC,KACd,MAAO,CACHC,QAAS,CACL,kBACA,mBACA,wBACA,wBACAF,EAAQ,yBAEZG,MAAO,CAAC,YAAa,iBAAkBH,EAAQ,oBAAqB,2BAA4B,UAChGI,eAAgB,CACZ,cACAJ,EAAQ,oBACR,aACAA,EAAQ,wCACRA,EAAQ,yDAEZK,eAAgB,CACZ,oBACA,QACA,uBACA,YACAL,EAAQ,qDAEZM,sBAAuB,CACnB,oBACA,kBACA,qBACAN,EAAQ,4BACRA,EAAQ,qBAEZO,kBAAmB,CACf,aACA,oBACA,yBACA,UACA,iDAEJC,YAAa,CACT,sBACAR,EAAQ,oBACRA,EAAQ,wBACRA,EAAQ,4BACRA,EAAQ,qBAEZS,eAAgB,CACZT,EAAQ,oBACRA,EAAQ,oCACR,eACAA,EAAQ,oCACRA,EAAQ,qCAEZU,cAAe,CACX,WACAV,EAAQ,gCACR,mBACA,aACAA,EAAQ,iBAEZW,cAAe,CAAC,uCAChBC,gBAAiB,CACb,eACAZ,EAAQ,wDACRA,EAAQ,gCACRA,EAAQ,gBACRA,EAAQ,6BAEZa,cAAe,CACXb,EAAQ,oBACRA,EAAQ,gBACR,0BACA,gBACAA,EAAQ,yBAEZc,eAAgB,CACZd,EAAQ,oDACRA,EAAQ,gBACR,yBACAA,EAAQ,oCACR,qBAEJe,cAAe,CACXf,EAAQ,gEACRA,EAAQ,oDACR,cACA,eACA,iBAEJgB,yBAA0B,CAAC,oBAAqB,eAAgB,iBAAkB,cAAe,cACjGC,0BAA2B,CACvB,eACAjB,EAAQ,oDACRA,EAAQ,gEACRA,EAAQ,oDACR,kBAEJkB,eAAgB,CACZ,aACAlB,EAAQ,gBACRA,EAAQ,wDACRA,EAAQ,wDACRA,EAAQ,yDAEZmB,UAAW,CAACnB,EAAQ,gCAAiC,iBAAkB,kBAAmB,sBAC1FoB,SAAU,CACN,cACApB,EAAQ,gCACRA,EAAQ,4BACR,mBACAA,EAAQ,iCAEZqB,cAAe,CACXrB,EAAQ,wDACRA,EAAQ,wBACR,YACA,kBACA,cAEJsB,eAAgB,CACZ,gBACA,oBACA,uBACAtB,EAAQ,4BACR,6BAEJuB,oBAAqB,CACjB,oBACAvB,EAAQ,wBACRA,EAAQ,4BACR,SACAA,EAAQ,iCAEZwB,cAAe,CACXxB,EAAQ,oBACRA,EAAQ,oCACR,YACAA,EAAQ,oDACR,sBAEJyB,gBAAiB,CACb,oBACAzB,EAAQ,4BACRA,EAAQ,oBACRA,EAAQ,gCACRA,EAAQ,6CAEZ0B,cAAe,CACX1B,EAAQ,4BACR,4BACAA,EAAQ,4DACRA,EAAQ,oDACRA,EAAQ,iEAEZ2B,kBAAmB,CACf3B,EAAQ,4BACRA,EAAQ,4BACRA,EAAQ,4CACRA,EAAQ,gDACRA,EAAQ,iDAEZ4B,SAAU,CAAC5B,EAAQ,qDACnB6B,iBAAkB,CAAC,iBAAkB,mBAAoB,mBAAoB,qBAAsB,aACnGC,mBAAoB,CAAC,oCACrBC,uBAAwB,CACpB,kBACA,oDACA,mBACA,kEACA,mBAEJC,aAAc,CAAC,YAAa,kBAAmB,iBAAkB,kBAAmB,2BACpFC,gBAAiB,CACbjC,EAAQ,4DACRA,EAAQ,gDACR,6BACAA,EAAQ,oBACR,gBAEJkC,aAAc,CACVlC,EAAQ,gDACRA,EAAQ,4DACRA,EAAQ,oEACR,gBACA,kBAEJmC,UAAW,CACP,cACA,6BACAnC,EAAQ,gBACRA,EAAQ,gCACR,mBAEJoC,sBAAuB,CACnB,gDACA,iCACA,yBACA,yBACA,oBAEJC,aAAc,CAACrC,EAAQ,qEACvBsC,QAAS,CACLtC,EAAQ,4KAERA,EAAQ,6KAGZuC,OAAQ,CACJvC,EAAQ,gDACRA,EAAQ,4BACRA,EAAQ,gDACRA,EAAQ,wBACR,4BAEJwC,QAAS,CACLxC,EAAQ,oBACR,2BACAA,EAAQ,oCACRA,EAAQ,gCACRA,EAAQ,yDAEZyC,QAAS,CACLzC,EAAQ,oDACRA,EAAQ,oCACRA,EAAQ,gDACR,yBACA,mCAEJ0C,eAAgB,CACZ,8BACA1C,EAAQ,gDACRA,EAAQ,wEACRA,EAAQ,gEACRA,EAAQ,yBAEZ2C,GAAI,CACA3C,EAAQ,4DACRA,EAAQ,oEACRA,EAAQ,4EACRA,EAAQ,oDACR,oBAEJ4C,KAAM,CACF5C,EAAQ,oCACRA,EAAQ,oCACRA,EAAQ,wCACR,WACA,qBAEJ6C,QAAS,CACL,2BACA7C,EAAQ,oDACRA,EAAQ,gBACR,QACA,eAEJ8C,uBAAwB,CACpB,sBACA,gBACA9C,EAAQ,4BACR,qBACA,2BAGZ,CAqB8BD,GACVF,EAAcrxB,OAAOiI,KAAKmpB,GAEnB,CAAC,EAAarF,qBADL/D,EAAK,IAAInjB,OAAOjE,MAAMonB,EAAIqJ,EAAYpmB,KAAI,SAAUspB,GAAc,OAAOnD,EAAQmD,EAAY,QAJlG,CAAC,OAAc5f,GAM9B,KAAK,EAWD,OAVAwX,EAAmBjE,EAAG5kB,OAClB6tB,GA2ExB,SAASqD,WAAWpD,EAASjF,GAEzB,IADA,IACSlH,EAAK,EAAGnF,EAAK9f,OAAOiI,KAAKmpB,GAAUnM,EAAKnF,EAAGvf,OAAQ0kB,IAAM,CAC9D,IAAIsP,EAAazU,EAAGmF,GACT,KAAKpgB,OAAO0vB,EAAY,KACnC,IAAK,IAAI3N,EAAK,EAAGoB,EAAKoJ,EAAQmD,GAAa3N,EAAKoB,EAAGznB,OAAQqmB,IAAM,CAC7D,IAAIwC,EAAWpB,EAAGpB,GACP,OAAO/hB,OAAOsnB,EAAiB/C,GAAY,KAAO,KAAM,KAAKvkB,OAAOukB,EAC3F,CACA,CAIA,CAvFwBoL,CAAWpD,EAASjF,IAExBsI,EAAiBpD,EAAYlN,QAAO,SAAUoQ,GAG1C,OADmB3T,aADfoL,EAAYoF,EAAQmD,IACiBtpB,KAAI,SAAUme,GAAY,OAAO+C,EAAiB/C,EAAY,KAC9D,GAAnB4C,EAAUzrB,MACxD,KACmCm0B,OACR,CAAC,EAAcD,GAE1C,GACA,GACA,EA0uBIE,gBA1aJ,SAASC,qBACL,OAkCJ,SAASC,iBAAiBzV,EAAQ0V,GA8C9B,YA7CyB,IAArBA,IAA+BA,EAAmB,KA6C/CjN,YAAW,SAAUzkB,EAAG2xB,GAC3B,IAaIC,EAbAC,EAAiBF,EAAa7pB,SAC9BgqB,EAAaD,EAAehyB,KAehC,OAHI+xB,IAXAG,EAAYD,EAAWpO,OACjB4E,MAAQ,GAAG7mB,OAAOiwB,EAAkB,MAC9CK,EAAUC,qBAAuBD,EAAUE,eAAiB,OAExDnP,aACAgP,EAAWpO,MAAMwO,KAAO,GAAGzwB,OAAO,EAAIkwB,EAAaQ,kBAE9CnP,aACL8O,EAAWpO,MAAMwO,KAAO,SAGVL,EAAe9pB,cAAc,SACnCG,YAAcjH,cAAc,GAAIM,MAAOmwB,EAAmB,GAAO,IAAI,GAAM7pB,KAAI,WAAc,MAAO,UAAW+e,KAAK,KAChIkL,EAAW3pB,YAAYypB,GAChB5V,EAAO6V,EAAgBC,EACjC,GAAE,kGACP,CAnGWL,EAAiB,SAAU3pB,EAAUsqB,GAKxC,IAJA,IAAItJ,EAAW,GACXuJ,EAAQ,CAAA,EAGHxQ,EAAK,EAAGnF,EAAK9f,OAAOiI,KAAKglB,SAAUhI,EAAKnF,EAAGvf,OAAQ0kB,IAAM,CAC9D,IACoEiD,EADhEjnB,EAAM6e,EAAGmF,GACsB6B,OAAe,KAA3BkB,GAAnBpB,EAAKqG,QAAQhsB,IAAc,IAA4B,CAAA,EAAK+mB,EAAgB0N,OAAc,KAA1BxN,EAAKtB,EAAG,IA3CtE,oBA2C+GsB,EACjHxV,EAAUxH,EAASC,cAAc,QACrCuH,EAAQpH,YAAcoqB,EACtBhjB,EAAQoU,MAAM6O,WAAa,SAC3B,IAAK,IAAIC,EAAK,EAAGC,EAAK71B,OAAOiI,KAAK6e,GAAQ8O,EAAKC,EAAGt1B,OAAQq1B,IAAM,CAC5D,IAAI7L,EAAS8L,EAAGD,GACZrzB,EAAQukB,EAAMiD,QACJpV,IAAVpS,IACAmQ,EAAQoU,MAAMiD,GAAUxnB,EAE5C,CACY2pB,EAASjrB,GAAOyR,EAChB8iB,EAAUtlB,OAAOhF,EAASC,cAAc,MAAOuH,EAC3D,CAEQ,IAAK,IAAIojB,EAAK,EAAGC,EAAK/1B,OAAOiI,KAAKglB,SAAU6I,EAAKC,EAAGx1B,OAAQu1B,IAExDL,EAAMx0B,EADI80B,EAAGD,IACA5J,EAASjrB,GAAK+0B,wBAAwBtK,MAEvD,OAAO+J,CACf,GACA,EA8YIQ,MA7/DJ,SAASC,sBACL,OA8DJ,SAASC,yCACL,OAEC/P,YAAcW,sBAAwBT,kBAElCJ,cA1FT,SAASkQ,oBAEL,IAAI/1B,EAAI4lB,UACJF,EAAIC,OACJqQ,EAAiB3O,MAAMjnB,UAE3B,OAMM,GANEmgB,YAAY,CAChB,cAAeyV,EACf,mBAAoBA,EACpB,kBAAmBh2B,GAJnBi2B,EAAiBvQ,EAAEuQ,iBAKD,aAAcA,EAChC,uBAAwBC,MAAM91B,WAEtC,CA6EyB21B,IAvNzB,SAASI,uBAEL,IAAIzQ,EAAIC,OACJyQ,EAAa1Q,EAAE0Q,WACnB,OAKM,GALE7V,YAAY,CAChB,UAAWzX,IAAI1I,UACf,aAAcslB,EACd0Q,GAAc,oBAAqBA,EAAWh2B,UAC9C,SAAUi2B,sBAAsBj2B,WAExC,CA6MgD+1B,EAChD,CApEQL,IACS,EAUjB,SAASQ,8BACL,IA2BI7W,EAAmC8W,EAAuBC,EAE1DC,EA7BA/Q,EAAIC,OACJ+Q,EAAehR,EAAEiR,qBAAuBjR,EAAEkR,0BAC9C,OAAKF,EAyCT,SAASG,iCAEL,OAAO9Q,aAAeC,oBA5L1B,SAAS8Q,qBAEL,IAAIpR,EAAIC,OACR,OAKM,GALEpF,YAAY,CAChB,gBAAiBmF,EACjB,8BAA+BA,EAC/B,uBAAwBA,EACxB,uBAAwBA,GAEhC,CAmLgDoR,EAChD,CArCQD,IACS,IAKTE,GADA1I,EAAU,IAAIqI,EAAa,EADb,IAC6B,QACtBM,oBACdjwB,KAAO,WAClBgwB,EAAWE,UAAU/0B,MAAQ,KACzBg1B,EAAa7I,EAAQ8I,4BACdC,UAAUl1B,OAAW,GAChCg1B,EAAWG,KAAKn1B,MAAQ,GACxBg1B,EAAWI,MAAMp1B,MAAQ,GACzBg1B,EAAWK,OAAOr1B,MAAQ,EAC1Bg1B,EAAWM,QAAQt1B,MAAQ,IAC3B60B,EAAWU,QAAQP,GACnBA,EAAWO,QAAQpJ,EAAQqJ,aAC3BX,EAAWY,MAAM,GACsBpB,GAAnC9W,EAkCR,SAASmY,oBAAoBvJ,GACzB,IAII3mB,EAAW,WA0Df,EAAA,MAAO,CAzDa,IAAI5F,SAAQ,SAAUC,EAASC,GAC/C,IAAI61B,GAAc,EACdC,EAAiB,EACjBC,EAAmB,EAEnBC,GADJ3J,EAAQ4J,WAAa,SAAUC,GAAS,OAAOn2B,EAAQm2B,EAAMC,eAAkB,EACrD,WACtBvZ,YAAW,WAAc,OAAO5c,EAAOulB,eAAe,WAA2C,GAAEva,KAAKogB,IATtF,IAS+G2K,EAR7G,IAQwJvjB,KAAKgL,OACpL,GACG4Y,EAAY,WACZ,IACI,IAAIC,EAAmBhK,EAAQiK,iBAM/B,OAJIzZ,UAAUwZ,IAEVrY,kCAAkCqY,GAE9BhK,EAAQvmB,OACZ,IAAK,UACDiwB,EAAmBvjB,KAAKgL,MACpBqY,GACAG,IAEJ,MAIJ,IAAK,YAKIntB,SAAS0tB,QACVT,IAEAD,GAvCA,GAuCeC,EACf91B,EAAOulB,eAAe,cAGtB3I,WAAWwZ,EA1CZ,KAiD3B,CAFY,MAAOlZ,GACHld,EAAOkd,EACvB,CACS,EACDkZ,IACA1wB,EAAW,WACFmwB,IACDA,GAAc,EACS,EAAnBE,GACAC,IAGX,CACT,IAC2BtwB,EAC3B,CAlGakwB,CAAoBvJ,IAA6B,GAAImI,EAAkB/W,EAAG,GAE/EgX,EAAqBzW,kCAAkCuW,EAAc7zB,MAAK,SAAU81B,GAAU,OAiGtG,SAASC,QAAQC,GAEb,IADA,IAAIC,EAAO,EACF54B,EAAI,EAAGA,EAAI24B,EAAOx4B,SAAUH,EACjC44B,GAAQ3rB,KAAK4T,IAAI8X,EAAO34B,IAE5B,OAAO44B,CACX,CAvG6GF,CAAQD,EAAOI,eAAe,GAAGC,SAjBtH,MAiB+I,IAAI,SAAU3Z,GAC7K,GAAmB,YAAfA,EAAM5P,MAAkE,cAAf4P,EAAM5P,KAC/D,OAAS,EAEb,MAAM4P,CACd,KACW,WAEH,OADAsX,IACOC,CACV,IAnCY,CAoCjB,CAhDWH,EACX,EAy/DIwC,YAzvCJ,SAASC,iBACL,IAIIC,EAJAxO,EAAQhqB,KACZ,OAAIulB,YAAcW,sBAAwBT,iBAC/B,WAAc,OAAOnkB,QAAQC,aAAQuS,EAAa,GAEzD0kB,EAAoBzO,yBACjB,WAAc,OAAO9oB,UAAU+oB,OAAO,OAAQ,GAAQ,WACzD,IAAIG,EAAWsO,EACf,OAAOt2B,YAAYnC,MAAM,SAAUif,GAC/B,OAAQA,EAAGzc,OACP,KAAK,EAAG,MAAO,CAAC,EAAag2B,KAC7B,KAAK,EAKD,OAJArO,EAAYlL,EAAGxc,OAIR,CAAC,EAAc,EAHtBg2B,EAAc,SAAUC,GAAY,OAAqB,OAAbA,EAAoB,KAAOxY,MAAMwY,EAxFzE,GAwFyG,GAG1EvO,EAAU,IAAKsO,EAAYtO,EAAU,IAAKsO,EAAYtO,EAAU,IAAKsO,EAAYtO,EAAU,MAElJ,GACK,GAAI,EACT,EAsuCIR,OAliDJ,SAASgP,uBACL,OAQJ,SAASC,6BAA6BC,GAClC,IAEIC,EACAjE,EAFAkE,GAAU,EAGVhT,EAeR,SAASiT,oBACL,IAAIrP,EAAStf,SAASC,cAAc,UAGpC,OAFAqf,EAAOkB,MAAQ,EACflB,EAAOqB,OAAS,EACT,CAACrB,EAAQA,EAAOmE,WAAW,MACtC,CApBakL,GAAqBrP,EAAS5D,EAAG,GAAI8H,EAAU9H,EAAG,GAa3D,OAQJ,SAASkT,YAAYtP,EAAQkE,GACzB,SAAUA,IAAWlE,EAAOC,UAChC,CAtBSqP,CAAYtP,EAAQkE,IAIrBkL,EAmBR,SAASG,mBAAmBrL,GAKxB,OAFAA,EAAQsL,KAAK,EAAG,EAAG,GAAI,IACvBtL,EAAQsL,KAAK,EAAG,EAAG,EAAG,IACdtL,EAAQuL,cAAc,EAAG,EAAG,UACxC,CAzBkBF,CAAmBrL,GACzBgL,EACAC,EAAWjE,EAAO,WAGkBiE,GAApC7Z,EAqBZ,SAASoa,aAAa1P,EAAQkE,IAiB9B,SAASyL,gBAAgB3P,EAAQkE,GAE7BlE,EAAOkB,MAAQ,IACflB,EAAOqB,OAAS,GAChB6C,EAAQ0L,aAAe,aACvB1L,EAAQ2L,UAAY,OACpB3L,EAAQ4L,SAAS,IAAK,EAAG,GAAI,IAC7B5L,EAAQ2L,UAAY,OAGpB3L,EAAQtB,KAAO,yBAOXmN,EAAc,qBAAqB11B,OAAOwC,OAAOmzB,aAAa,MAAO,QACzE9L,EAAQ+L,SAASF,EAAa,EAAG,IACjC7L,EAAQ2L,UAAY,yBACpB3L,EAAQtB,KAAO,aACfsB,EAAQ+L,SAASF,EAAa,EAAG,GACrC,CAtCIJ,CAAgB3P,EAAQkE,GACxB,IAAIgM,EAAanQ,eAAeC,GAIhC,OAAIkQ,IAHanQ,eAAeC,GAIrB,CAAC,WAAuC,aAiCvD,SAASmQ,oBAAoBnQ,EAAQkE,GAEjClE,EAAOkB,MAAQ,IACflB,EAAOqB,OAAS,IAIhB6C,EAAQkM,yBAA2B,WACnC,IAAK,IAAI3V,EAAK,EAAGnF,EAAK,CAClB,CAAC,OAAQ,GAAI,IACb,CAAC,OAAQ,GAAI,IACb,CAAC,OAAQ,GAAI,KACdmF,EAAKnF,EAAGvf,OAAQ0kB,IAAM,CACrB,IAAiB4V,GAAbjU,EAAK9G,EAAGmF,IAAgB,GAAIrX,EAAIgZ,EAAG,GAAIzjB,EAAIyjB,EAAG,GAClD8H,EAAQ2L,UAAYQ,EACpBnM,EAAQoM,YACRpM,EAAQqM,IAAIntB,EAAGzK,EAAG,GAAI,EAAa,EAAVkK,KAAK2tB,IAAQ,GACtCtM,EAAQuM,YACRvM,EAAQ/b,MAChB,CAII+b,EAAQ2L,UAAY,OACpB3L,EAAQqM,IAAI,GAAI,GAAI,GAAI,EAAa,EAAV1tB,KAAK2tB,IAAQ,GACxCtM,EAAQqM,IAAI,GAAI,GAAI,GAAI,EAAa,EAAV1tB,KAAK2tB,IAAQ,GACxCtM,EAAQ/b,KAAK,UACjB,CAtDIgoB,CAAoBnQ,EAAQkE,GAErB,CADanE,eAAeC,GACZkQ,GAC3B,CArCiBR,CAAa1P,EAAQkE,IAAwB,GAAIgH,EAAO5V,EAAG,KARpE6Z,EAAWjE,EAAO,cAWf,CAAEkE,QAASA,EAASD,SAAUA,EAAUjE,KAAMA,EACzD,CA3BW+D,CAsHX,SAASyB,uCAEL,OAAO9U,YAAcW,sBAAwBT,gBACjD,CAzHwC4U,GACxC,EAiiDIC,MAv4CJ,SAASC,WACL,OAAOnV,UAAUoV,KACrB,EAs4CIC,UAp4CJ,SAASC,eACL,IAAIl7B,EAAI4lB,UACJpjB,EAAS,GACT4R,EAAWpU,EAAEoU,UAAYpU,EAAEm7B,cAAgBn7B,EAAEo7B,iBAAmBp7B,EAAEq7B,eAiBtE,YAhBiB/mB,IAAbF,GACA5R,EAAOuB,KAAK,CAACqQ,IAEb9P,MAAMuE,QAAQ7I,EAAEi7B,WAGVpV,cAxyBd,SAASyV,sBAEL,IAAI5V,EAAIC,OACR,OAKM,GALEpF,YAAY,GACd,uBAAwBmF,GAC1B,yBAA0BA,EAC1B,GAAKA,EAAE6V,MAAS,gBAChB,GAAK7V,EAAExkB,SAAY,oBAE3B,CA+xB8Bo6B,IAClB94B,EAAOuB,KAAK/D,EAAEi7B,WAGU,iBAAhBj7B,EAAEi7B,YACVA,EAAYj7B,EAAEi7B,YAEdz4B,EAAOuB,KAAKk3B,EAAU7rB,MAAM,MAG7B5M,CACX,EAg3CIg5B,WA92CJ,SAASC,gBACL,OAAO9V,OAAOwF,OAAOqQ,UACzB,EA62CIE,aA32CJ,SAASC,kBAEL,OAAOtb,WAAWF,QAAQyF,UAAU8V,mBAAepnB,EACvD,EAy2CIsnB,iBAl2CJ,SAASC,sBACL,KAAI9V,YAAcW,sBAAwBT,kBAG1C,OAQJ,SAAS6V,8BAKgB,SAAjBC,EAA2B75B,GAAS,OAAOme,WAAWH,MAAMhe,GAAQ,KAAQ,CAJhF,IAAIpC,EAAIqrB,OAKJ6Q,EAAa,CAACD,EAAej8B,EAAEurB,OAAQ0Q,EAAej8B,EAAE0rB,SAE5D,OADAwQ,EAAW3H,OAAOrrB,UACXgzB,CACX,CAjBWF,EACX,EA81CIG,oBAptCJ,SAASC,yBAEL,OAAO7b,WAAWH,MAAM0F,UAAUqW,0BAAsB3nB,EAC5D,EAktCI6nB,SAhtCJ,SAASC,cACL,IACIC,EAAiB,OAAC5c,EAAKkG,OAAO4V,WAAkC,EAAS9b,EAAG4c,eAChF,OAAIA,IACIF,GAAW,IAAIE,GAAiBC,kBAAkBC,UAE3CJ,GAKXK,GAGR,SAASC,oBACL,IAAIC,OAAkBloB,MAAOmoB,cAK7B,OAAO3vB,KAAK4vB,IAEZzc,QAAQ,IAAI3L,KAAKkoB,EAAa,EAAG,GAAGD,qBAAsBtc,QAAQ,IAAI3L,KAAKkoB,EAAa,EAAG,GAAGD,qBAClG,CAZkBA,GACP,MAAMj4B,OAAiB,GAAVg4B,EAAc,IAAM,IAAIh4B,OAAOg4B,GACvD,EAosCIK,eAxrCJ,SAASC,oBACL,IACI,QAASnX,OAAOkX,cAKxB,CAHI,MAAO3d,GAEH,OAAO,CACf,CACA,EAirCI6d,aA9qCJ,SAASC,kBACL,IACI,QAASrX,OAAOoX,YAKxB,CAHI,MAAO16B,GAEH,OAAO,CACf,CACA,EAuqCI46B,UArqCJ,SAASC,eAGL,IAAIzX,cApnCR,SAAS0X,aAEL,IAAIzX,EAAIC,OACJ3lB,EAAI4lB,UACR,OAA6G,GAArGrF,YAAY,CAAC,wBAAyBmF,EAAG,aAAcA,EAAG,gBAAiB1lB,EAAG,eAAgBA,MACjGylB,WACT,CA8mCuB0X,GAGnB,IACI,QAASxX,OAAOsX,SAKxB,CAHI,MAAO56B,GAEH,OAAO,CACf,CACA,EAypCI+6B,aAvpCJ,SAASC,kBACL,QAAS1X,OAAOyX,YACpB,EAspCIE,SAppCJ,SAASC,cACL,OAAO3X,UAAU0X,QACrB,EAmpCIE,SAjpCJ,SAASC,cAEL,IAAID,EAAW5X,UAAU4X,SAKzB,MAAiB,aAAbA,GACIzX,aAAeC,kBAp+B3B,SAAS0X,SAOL,IAIIC,EAJJ,MAA2B,SAAvB/X,UAAU4X,WAIVG,GADA79B,EAAIqrB,QACYE,MAAQvrB,EAAE0rB,OASxB,GAREjL,YAAY,CAEhB,gBAAiBoF,SAEfiY,QAAQx9B,UAAUy9B,wBAGN,IAAdF,GAAsBA,EAAc,OAE5C,CAg9BmBD,GAAW,OAAS,SAG5BF,CACX,EAqoCIM,QAnlDJ,SAASC,aACL,IAAIC,EAAapY,UAAUkY,QAC3B,GAAKE,EAAL,CAKA,IAFA,IAAIF,EAAU,GAEL/9B,EAAI,EAAGA,EAAIi+B,EAAW99B,SAAUH,EAAG,CACxC,IAAIk+B,EAASD,EAAWj+B,GACxB,GAAKk+B,EAAL,CAIA,IADA,IAAIC,EAAY,GACP3rB,EAAI,EAAGA,EAAI0rB,EAAO/9B,SAAUqS,EAAG,CACpC,IAAI4rB,EAAWF,EAAO1rB,GACtB2rB,EAAUn6B,KAAK,CACXgD,KAAMo3B,EAASp3B,KACfq3B,SAAUD,EAASC,UAEnC,CACQN,EAAQ/5B,KAAK,CACTuL,KAAM2uB,EAAO3uB,KACb+uB,YAAaJ,EAAOI,YACpBH,UAAWA,GAZvB,CAcA,CACI,OAAOJ,CAtBX,CAuBA,EAyjDIQ,aA96CJ,SAASC,kBACL,IAEIC,EAFAx+B,EAAI4lB,UACJ6Y,EAAiB,OAEInqB,IAArBtU,EAAEy+B,eACFA,EAAiBve,MAAMlgB,EAAEy+B,qBAEGnqB,IAAvBtU,EAAE0+B,mBACPD,EAAiBz+B,EAAE0+B,kBAEvB,IACI7zB,SAAS8zB,YAAY,cACrBH,GAAa,CAIrB,CAFI,MAAO/e,GACH+e,GAAa,CACrB,CAEI,MAAO,CACHC,eAAgBA,EAChBD,WAAYA,EACZI,WAJa,iBAAkBjZ,OAMvC,EAw5CIG,OAroCJ,SAAS+Y,YACL,OAAOjZ,UAAUE,QAAU,EAC/B,EAooCIgZ,cA9nCJ,SAASC,mBAEL,IADA,IAAIC,EAAU,GACLpa,EAAK,EAAGnF,EAAK,CAElB,SAEA,SAEA,UACA,WAEA,SAEA,OACA,SAEA,cAEA,qCACA,SAEA,OAEA,YAEA,QACA,cAEA,gBAGDmF,EAAKnF,EAAGvf,OAAQ0kB,IAAM,CACrB,IAAIhkB,EAAM6e,EAAGmF,GACT1iB,EAAQyjB,OAAO/kB,GACfsB,GAA0B,iBAAVA,GAChB88B,EAAQj7B,KAAKnD,EAEzB,CACI,OAAOo+B,EAAQ3K,MACnB,EAwlCI4K,eA/kCJ,SAASC,oBACL,IAAIp+B,EAAI+J,SAQR,IAEI/J,EAAEq+B,OAAS,iCACX,IAAI38B,GAA8C,IAArC1B,EAAEq+B,OAAOpvB,QAAQ,eAG9B,OADAjP,EAAEq+B,OAAS,uEACJ38B,CAIf,CAFI,MAAOH,GACH,OAAO,CACf,CACA,EA4jCI+8B,WAhrBJ,SAASC,gBAEL,IAAK,IAAIza,EAAK,EAAGnF,EAAK,CAAC,UAAW,KAAM,QAASmF,EAAKnF,EAAGvf,OAAQ0kB,IAAM,CACnE,IAAI0a,EAAQ7f,EAAGmF,GACf,GAAIwH,WAAW,iBAAiB5nB,OAAO86B,EAAO,MAAMjT,QAChD,OAAOiT,CAEnB,CAEA,EAwqBIC,eAnqBJ,SAASC,oBACL,QAAIrT,YAAY,cAGZA,YAAY,cAAhB,CAIJ,EA4pBIsT,aAppBJ,SAASC,kBACL,QAAIpT,YAAY,YAGZA,YAAY,cAAhB,CAIJ,EA6oBIqT,WAhoBJ,SAASC,qBACL,GAAKxT,WAAW,uBAAuBC,QAAvC,CAMA,IAAK,IAAItsB,EAAI,EAAGA,GAfE,MAesBA,EACpC,GAAIqsB,WAAW,oBAAoB5nB,OAAOzE,EAAG,MAAMssB,QAC/C,OAAOtsB,EAGf,MAAM,IAAIqF,MAAM,iBARpB,CASA,EAonBIy6B,SA9mBJ,SAASC,wBACL,OAAIvT,YAAY,iBACL,EAIPA,YAAY,SAAWA,YAAY,QAC5B,EAEPA,YAAY,QAAUA,YAAY,SACzB,EAETA,YAAY,UACL,QADX,CAIJ,EA+lBIwT,cAvlBJ,SAASC,kBACL,QAAIxT,YAAY,YAGZA,YAAY,uBAAhB,CAIJ,EAglBIyT,oBAxkBJ,SAASC,wBACL,QAAIzT,YAAY,YAGZA,YAAY,uBAAhB,CAIJ,EAikBI0T,IAzjBJ,SAASC,QACL,QAAI1T,UAAU,UAGVA,UAAU,kBAAd,CAIJ,EAkjBI2T,KAviBJ,SAASC,qBAEL,IAAIC,EAAOtuB,EAAEsuB,MAAQ5T,WACjB6T,EAAQvuB,EAAEuuB,OAAS7T,WACnB8T,EAAOxuB,EAAEwuB,MAAQ9T,WACjB+T,EAAQzuB,EAAEyuB,OAAS/T,WACnBgU,EAAQ1uB,EAAE0uB,OAAShU,WACnBiU,EAAO3uB,EAAE2uB,MAAQjU,WACjBkU,EAAM5uB,EAAE4uB,KAAOlU,WACfmU,EAAO7uB,EAAE6uB,MAAQnU,WACjBoU,EAAM9uB,EAAE8uB,KAAOpU,WACfqU,EAAO/uB,EAAE+uB,MAAQrU,WACjBsU,EAAMhvB,EAAEgvB,KAAOtU,WACfuU,EAAOjvB,EAAEivB,MAAQvU,WACjBwU,EAAMlvB,EAAEkvB,KAAOxU,WACfyU,EAAQnvB,EAAEmvB,OAASzU,WACnB0U,EAAQpvB,EAAEovB,OAAS1U,WAYvB,MAAO,CACH4T,KAAMA,EAAK,oBACXC,MAAOA,EAAM,OACbc,SAZoBp/B,EAYH,MAZmB+P,EAAEsvB,IAAIr/B,EAAQ+P,EAAEuvB,KAAKt/B,EAAQA,EAAQ,KAazEu+B,KAAMA,EAAK,oBACXC,MAAOA,EAAM,GACbe,SAdoBv/B,EAcH,EAdmB+P,EAAEsvB,IAAIr/B,EAAQ+P,EAAEuvB,KAAKt/B,EAAQA,EAAQ,KAezEy+B,MAAOA,EAAM,IACbe,SAfoBx/B,EAeH,GAfmB+P,EAAEsvB,KAAK,EAAIr/B,IAAU,EAAIA,IAAU,GAgBvE0+B,KAAMA,EAAK,IACXC,IAAKA,UACLC,KAAMA,EAAK,GACXa,QAlBmBz/B,EAkBJ,EAlBoB+P,EAAEkvB,IAAIj/B,GAAS,EAAI+P,EAAEkvB,IAAIj/B,GAAS,GAmBrE6+B,IAAKA,EAAI,iBACTC,KAAMA,EAAK,GACXY,QApBmB1/B,EAoBJ,GApBqB+P,EAAEkvB,IAAIj/B,GAAS,EAAI+P,EAAEkvB,IAAIj/B,IAAU,GAqBvE++B,IAAKA,UACLC,KAAMA,EAAK,GACXW,QArBmB3/B,EAqBJ,GArBqB+P,EAAEkvB,IAAI,EAAIj/B,GAAS,IAAM+P,EAAEkvB,IAAI,EAAIj/B,GAAS,IAsBhFi/B,IAAKA,EAAI,GACTC,MAAOA,EAAM,GACbU,QAzBoC7vB,EAAEkvB,IAyBrB,GAzBkC,EA0BnDE,MAAOA,EAAM,IACbU,QAzBoC9vB,EAAEsvB,IAAI,IA0B1CS,MAlCkC/vB,EAAEgwB,IAAIhwB,EAAE0oB,SAoClD,EAkfIuH,iBAnWJ,SAASC,qBACL,OAAOvc,UAAUsc,gBACrB,EAkWIE,aAxVJ,SAASC,kBACL,IAAIx/B,EAAI,IAAIy/B,aAAa,GACrBC,EAAK,IAAI1f,WAAWhgB,EAAE21B,QAG1B,OAFA31B,EAAE,GAAK2/B,IACP3/B,EAAE,GAAKA,EAAE,GAAKA,EAAE,GACT0/B,EAAG,EACd,EAmVIE,SA7UJ,SAASC,mBACL,IAAIC,EAAkBhd,OAAOgd,gBAC7B,GAAmH,mBAAvGA,MAAAA,OAAyD,EAASA,EAAgBC,iBAC1F,OAAS,EAEb,GAAIrV,wBACA,OAAS,EAEb,IACI,OAAOoV,EAAgBC,kBAAoB,EAAgC,CAInF,CAFI,MAAO1jB,GACH,OAgBR,SAAS2jB,kBAAkB3jB,GAEvB,GAAIA,aAAiB9Z,OAAwB,uBAAf8Z,EAAM5P,MAAiC,0BAA0BlB,KAAK8Q,EAAM4jB,SACtG,OAAS,EAEb,MAAM5jB,CACV,CAtBe2jB,CAAkB3jB,EACjC,CACA,EAgUI6jB,wBA9RJ,SAASC,6BACL,IACIC,EAAOp4B,SAASC,cAAc,KAC9Bo4B,EAAW,OAACzjB,EAAKwjB,EAAKE,qBAAiD1jB,EAAKwjB,EAAKG,oBACrF,YAAoB9uB,IAAb4uB,OAAyB5uB,EAAYtN,OAAOk8B,EACvD,EA0RIG,iBAvFJ,SAASC,6BACL,IAAI7jB,EAIJ,OADwByH,aAAenB,WAIlCJ,OAAO+Q,cAGL,OAACjX,GAAK,IAAIiX,cAAe6M,aAAyC9jB,GAF5D,GAHA,CAMjB,EA4EI+jB,eApEJ,SAASC,oBACL,IAOIC,EAPJ,OAAK/d,OAAO4V,MAGRc,EAAiB1W,OAAO4V,KAAKc,iBAI7BqH,EAASrH,IAAiBC,kBAAkBoH,SACtB,KAAXA,EAGRA,GAFM,GAJA,GAJA,CAWjB,EA0DIC,YA5PJ,SAASC,eAAenkB,GACpB,IAAoB+V,EAShBqO,EANJ,OADIrV,EAAKN,gBADGzO,EAAG0O,QAKVkB,uBAAuBb,IAGxBqV,EAAiBzU,+BAAiC,KAAOZ,EAAGsV,aAfpC,6BAgBrB,CACHC,SAAU,OAACxd,EAAKiI,EAAGc,aAAad,EAAGwV,eAAsC,EAASzd,EAAG5gB,aAAe,GACpGmgB,QAAS,OAAC6B,EAAK6G,EAAGc,aAAad,EAAGyV,cAAqC,EAAStc,EAAGhiB,aAAe,GAClGu+B,eAAgBL,EAAiB,OAAChc,EAAK2G,EAAGc,aAAauU,EAAeM,6BAAoD,EAAStc,EAAGliB,WAAa,GACnJy+B,UAAW,OAAC7O,EAAK/G,EAAGc,aAAad,EAAG6V,gBAAuC,EAAS9O,EAAG5vB,aAAe,GACtG2+B,iBAAkBT,EAAiB,OAACrO,EAAKhH,EAAGc,aAAauU,EAAeU,+BAAsD,EAAS/O,EAAG7vB,WAAa,GACvJ6+B,wBAAyB,OAAC/O,EAAKjH,EAAGc,aAAad,EAAGiW,gCAAuD,EAAShP,EAAG9vB,aAAe,MA9ClG,GAFf,CAkD3B,EA0OI++B,gBAtOJ,SAASC,mBAAmBllB,GACxB,IACI+O,EAAKN,gBADGzO,EAAG0O,OAEf,IAAKK,EACD,OA1DmB,EA4DvB,IAAKa,uBAAuBb,GACxB,OA3DkC,EA6DlCoW,EAAapW,EAAGqW,yBAApB,IACIC,EAAoBtW,EAAGuW,uBACvBC,EAAwB,GAExB7b,EAAa,GACb8b,EAAa,GACbC,EAAsB,GACtBC,EAAmB,GAEvB,GAAIL,EACA,IAAK,IAAIlgB,EAAK,EAAG2B,EAAK5mB,OAAOiI,KAAKk9B,GAAoBlgB,EAAK2B,EAAGrmB,OAAQ0kB,IAAM,CACxE,IAAIwgB,EAAgB7e,EAAG3B,GACvBuE,EAAWplB,KAAK,GAAGS,OAAO4gC,EAAe,KAAK5gC,OAAOsgC,EAAkBM,IACnF,CAII,IADA,IACSzd,EAAK,EAAG0d,EADDrW,0BAA0BR,GACA7G,EAAK0d,EAAYnlC,OAAQynB,IAAM,CACrE,IACI2d,EAAO9W,EAAG+W,EADCF,EAAY1d,IAE3Bsd,EAAWlhC,KAAK,GAAGS,OAAO+gC,EAAU,KAAK/gC,OAAO8gC,GAAM9gC,OAAOspB,uBAAuBniB,IAAI25B,GAAQ,IAAI9gC,OAAOgqB,EAAGc,aAAagW,IAAS,IAC5I,CAEI,GAAIV,EACA,IAAK,IAAI/c,EAAK,EAAG2d,EAAeZ,EAAY/c,EAAK2d,EAAatlC,OAAQ2nB,IAAM,CACxE,IAAI6B,EAAS8b,EAAa3d,GAC1B,KA/DoB,8BA+Df6B,GAAwC0F,gCA9D1B,uBA+Dd1F,IA6FN7D,cAAgBE,aA9Ff,CAIA,IAAI0f,EAAYjX,EAAGsV,aAAapa,GAChC,GAAK+b,EAIL,IAAK,IAAIlQ,EAAK,EAAGC,EAAKxG,0BAA0ByW,GAAYlQ,EAAKC,EAAGt1B,OAAQq1B,IAAM,CAC9E,IAAIgQ,EACAD,EAAOG,EAAUF,EADN/P,EAAGD,IAElB2P,EAAoBnhC,KAAK,GAAGS,OAAO+gC,EAAU,KAAK/gC,OAAO8gC,GAAM9gC,OAAOupB,qBAAqBpiB,IAAI25B,GAAQ,IAAI9gC,OAAOgqB,EAAGc,aAAagW,IAAS,IAC3J,MAPgBN,EAAsBjhC,KAAK2lB,EAH3C,CAWA,CAGI,IAAK,IAAI+L,EAAK,EAAGiQ,EAAgB1X,YAAayH,EAAKiQ,EAAcxlC,OAAQu1B,IAErE,IADA,IAAIhH,EAAaiX,EAAcjQ,GACtBC,EAAK,EAAGiQ,EAAmB1X,eAAgByH,EAAKiQ,EAAiBzlC,OAAQw1B,IAAM,CACpF,IAAIhH,EAAgBiX,EAAiBjQ,GACjC/G,EAAkBJ,mBAAmBC,EAAIC,EAAYC,GACzDyW,EAAiBphC,KAAK,GAAGS,OAAOiqB,EAAY,KAAKjqB,OAAOkqB,EAAe,KAAKlqB,OAAOmqB,EAAgBhF,KAAK,MACpH,CAKI,OAFAub,EAAoB7Q,OACpB4Q,EAAW5Q,OACJ,CACHyQ,kBAAmB3b,EACnB8b,WAAYA,EACZE,iBAAkBA,EAClBP,WAAYA,EACZM,oBAAqBA,EACrBF,sBAAuBA,EAE/B,GAqKA,SAASY,mBAAmBn0B,GACxB,OAAOgS,YAAYC,QAASjS,EAAS,GACzC,CAGA,SAASo0B,cAAcvgB,GACnB,IACIwgB,EAkCR,SAASC,yBAAyBC,GAC9B,OAAOtlB,MAAM,IAAO,IAAOslB,EAAqB,KACpD,CApC6BD,CADrBC,EAIR,SAASC,uBAAuB3gB,GAI5B,OAAI4B,YACO,GAGPnB,YACOC,mBAAuBU,sBAAwBT,iBAA0B,GAAN,IAE1EuX,EAAW,UAAWlY,EAAWkY,SAAWlY,EAAWkY,SAASt7B,MAAQ,GAExE,OAAOkM,KAAKovB,GAKL,GAGP,OAAOpvB,KAAKovB,GAKL,GAGJ,GACX,CAlC8ByI,CAAuB3gB,IAEjD,MAAO,CAAE4gB,MAAOF,EAAqBG,QAJnB,4CAI4CC,QAAQ,MAAO,GAAG5hC,OAAOshC,IAC3F,CAsDA,SAASO,eAAe/gB,GACpB,OAAO7C,WAnBX,SAAS6jB,4BAA4BhhB,GAEjC,IADA,IAAI9iB,EAAS,GACJoiB,EAAK,EAAGnF,EAAK9f,OAAOiI,KAAK0d,GAAY+O,OAAQzP,EAAKnF,EAAGvf,OAAQ0kB,IAAM,CACxE,IAAI2hB,EAAe9mB,EAAGmF,GAElB1iB,EAAQ,UADRskC,EAAYlhB,EAAWihB,IACQ,QAAU9/B,KAAKC,UAAU8/B,EAAUtkC,OACtEM,GAAU,GAAGgC,OAAOhC,EAAS,IAAM,IAAIgC,OAAO+hC,EAAaH,QAAQ,YAAa,QAAS,KAAK5hC,OAAOtC,EAC7G,CACI,OAAOM,CACX,CAUsB8jC,CAA4BhhB,GAClD,CA+BA,SAASmhB,kBAAkBC,GAGvB,OAnhGJ,SAASC,+BAA+BC,EAAiBC,QAC7B,IAApBA,IAA8BA,EAAkBrE,KACpD,IAAIsE,EAAsBnhB,OAAOmhB,oBACjC,OAAIA,EAIO,IAAIhlC,SAAQ,SAAUC,GAAW,OAAO+kC,EAAoBxmC,KAAKqlB,QAAQ,WAAc,OAAO5jB,MAAc,CAAEglC,QAASF,OAGvHpoB,KAAKzR,KAAKogB,IAAIwZ,EAAiBC,GAE9C,CAugGWF,CAFyBD,OAAV,IAAlBA,EAA4C,GAEVA,EAA+B,EAAhBA,EACzD,CAQA,SAASM,UAAUC,EAAenW,GACXtc,KAAKgL,MACxB,MAAO,CACHha,IAAK,SAAUiM,GACX,OAAOhQ,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAe8kB,EAAY9iB,EAC3B,OAAOG,YAAYnC,MAAM,SAAUif,GAC/B,OAAQA,EAAGzc,OACP,KAAK,EAED,OADYwR,KAAKgL,MACV,CAAC,EAAaynB,KACzB,KAAK,EAQD,OAPA3hB,EAAa7F,EAAGxc,OAChBT,EAnD5B,SAAS0kC,kBAAkB5hB,GACvB,IAAI6hB,EAEAC,EAAavB,cAAcvgB,GAE/B,MAAO,CACH+hB,aAIIA,GAAA,OAFIF,OADmB7yB,IAAnB6yB,EACiBd,eAAe7lC,KAAK8kB,YAElC6hB,CACV,EACDE,aAAAA,CAAcA,GACVF,EAAiBE,CACpB,EACDD,WAAYA,EACZ9hB,WAAYA,EACZye,QA1hGM,QA4hGd,CAgCqCmD,CAAkB5hB,GACvBwL,GAAUrf,MAAAA,GAAkDA,EAAQqf,MAKjE,CAAC,EAActuB,GAElD,GACA,GACS,EAET,CA+CA,IAAI6M,MAAQ,CAAEi4B,KAxBd,SAASA,KAAK71B,GACV,IAAIgO,EAEJ,YADgB,IAAZhO,IAAsBA,EAAU,CAAA,GAC7BhQ,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAIkmC,EAAe5V,EACnB,OAAOnuB,YAAYnC,MAAM,SAAU+lB,GAC/B,OAAQA,EAAGvjB,OACP,KAAK,EAKD,OAJI,OAACyc,EAAKhO,EAAQ81B,cAAwC9nB,GA3B9E,SAAS+nB,UAEL,KAAI7hB,OAAO8hB,YAA+B,MAAjBz6B,KAAKC,UAG9B,IACI,IAAIy6B,EAAU,IAAIC,eAClBD,EAAQE,KAAK,MAAO,0CAA0CpjC,OAnlGxD,QAmlGwE,oBAAoB,GAClGkjC,EAAQG,MAMhB,CAJI,MAAO3oB,GAIX,CACA,CAawBsoB,GAEJd,EAAgBj1B,EAAQi1B,cAAe5V,EAAQrf,EAAQqf,MAChD,CAAC,EAAa2V,kBAAkBC,IAC3C,KAAK,EAGD,OAFAngB,EAAGtjB,OAEI,CAAC,EAAc+jC,UADNpB,mBAAmB,CAAEzX,MAAO,CAAA,EAAI2C,MAAOA,IACRA,IAEnE,GACA,GACA,EAI0BuV,eAAgBA,eAAgByB,wBA7H1D,SAASA,wBAAwBxiB,GAC7B,OAAO7e,KAAKC,UAAU4e,GAAY,SAAUyiB,EAAM7lC,GAC9C,OAAIA,aAAiBkD,MA9gF7B,SAAS4iC,cAAc9oB,GACnB,IAAIO,EACJ,OAAO/f,SAAS,CAAE4P,KAAM4P,EAAM5P,KAAMwzB,QAAS5jB,EAAM4jB,QAASmF,MAAO,OAACxoB,EAAKP,EAAM+oB,YAAmC,EAASxoB,EAAGrQ,MAAM,OAAS8P,EACjJ,CA4gFmB8oB,CAAc9lC,GAElBA,CACV,GAAE,EACP,8FC9gGA,SAAsBgmC,cAAWC,OAAAA,aAAAA,MAAA3nC,KAAAP,WAIhC,SAAAkoC,eAAAA,IAAAA,EAAAA,QAAAA,OAAAA,aAAAC,kBAAAC,+BAJM,SAAAC,QAAA9lC,aAAA6lC,+BAAA,SAAAE,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,OAAA,OAAAA,YACcC,gBAAoB,OAA7B,OAAFC,EAAEF,UAAAA,YACaE,YAAQ,OAAjB,OAANjmC,EAAM+lC,UAAAA,iBACL/lC,EAAAA,WAAgB,OAAA,YAAA,OAAA+lC,eAAAD,QAC1BH,MAAA3nC,KAAAP,WACD,SAAsByoC,kBAAiBC,EAAAC,UAAAC,mBAAAA,MAAAroC,KAAAP,UAqBtC,CAAA,SAAA6oC,UAAA,IAAAC,EAAA,CAAA,QAAA,aAAA,YAAA,OAAA,UAAA,OAAA,iBAAA,SAAA,gBAAA,UAAA,QAAA,WAAA,SAAA,MAAA,UAAA,eAAA,OAAA,MAAA,OAAA,gBAAA,YAAA,gBAAA,SAAA,OAAA,QAAA,aAAA,MAAA,OAAA,QAAA,OAAAD,QAAA,WAAA,OAAAC,CAAA,IAAA,CAAA,SAAAF,0CAAAA,mBAAAT,kBAAAC,wBAAAA,OArBM,SAAAW,EAAiCtmB,EAAOzO,OAAIg1B,EAAAC,aAAAb,+BAAA,SAAAc,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,IAAAA,OAAA,OAAA,OAAAA,EAAAA,OACxBjB,cAAa,OAiBR,OAjBtBoB,EAAQH,WAGVl1B,MAAAA,OAAI,EAAJA,qBAAyBs1B,SACzBN,EAAU,IAAIM,QACdt1B,mBAAqB,SAAC/R,EAAOtB,GACzBqoC,EAAAA,EAAAA,MAAYroC,EAAKsB,OAIrB+mC,EAAU,IAAIM,SAAQt1B,MAAAA,OAAI,EAAJA,YAAiB,CAA7B,GAGdg1B,IAAAA,oBAA2BK,GACrBJ,EAAYM,eAAAA,kBACXv1B,GAAI,GAAA,SACPg1B,uBACwBE,IAAAA,aAErBM,MAAM/mB,EAAOwmB,IAAa,OAAA,YAAA,OAAAC,EAAAA,UAAAH,gBACpCxoC,KAAAP,WACqBypC,SAAAA,2BAA0BC,UAAAC,0CAAAppC,KAAAP,WAoB/C,SAAA2pC,8BAAAA,IAAAA,EAAAA,QAAAA,OAAAA,4BAAAxB,kBAAAC,+BApBM,SAAAwB,EAA0CnnB,GAAK,IAAAzO,EAAA61B,EAAAZ,EAAAa,EAAAC,EAAA/pC,iBAAAooC,sBAAAA,MAAA,SAAA4B,GAAA,IAAA,IAAAC,EAAAC,UAAA,OAAAF,UAAAA,IAAAA,OAAA,OAAmB,OAAjBh2B,IAAI+1B,gBAAA11B,IAAA01B,KAAAA,KAAG,GAAIF,IAAME,UAAAA,UAAA11B,EAAA21B,YAC9C/B,cAAa,OASR,OATtBoB,EAAQW,EAAAA,MAERhB,EAAU,IAAIM,QAAQt1B,cAC5Bg1B,oBAA2BK,GAC3BL,IAAAA,kBAAyBa,GAEnBZ,EAAYM,eAAAA,gBACXv1B,EAAAA,GAAI,CAAA,EAAA,SACPg1B,gBADO,OAEiBgB,IAAAA,QAAAA,EAAAA,QAGDR,MAAM/mB,EAAOwmB,GAAa,QAAnC,OAARa,EAAQE,UAAAA,iBACPF,GAAQ,QAGsB,MAHtBE,IAAAA,SAAAA,EAAAA,GAAAA,aAGsBA,EAAAA,GAAA,QAAA,OAAA,KAAA,OAAAA,IAAAA,WAAAJ,EAAA,KAAA,CAAA,sBAG5CrpC,KAAAP,qhEC7CD,IAAMuW,SAASD,4BACT6zB,UAAYhZ,KAAK7a,sBAAAA,OACV8zB,cAAa,UA6RdC,IADPC,IA7BQC,EATAC,EATAC,EAVAC,EAAAA,EAtCAC,IADRC,IAjDAC,IAtCoBC,EAPRC,EAAAA,IAPAC,EAdLC,IADPC,EA5BOC,EAHCC,IAJAC,EAJCC,IAJAC,IAPFC,EAHAC,EAAAA,EAdCC,EADRC,EAAAA,qBAAAC,cAHD,SAAAxB,IAAcyB,qBAAAzB,GACV7pC,KAAAurC,QAAe,KACfvrC,KAAAwrC,kBAAyB,OAC5B,CAAA,OAAA,YAAAJ,EAAAA,EAAAA,EAAAxD,kBAAAC,+BACD,SAAAC,QAAA8B,EAAA6B,MAAA5D,OAAAA,wBAAAA,OAAA,SAAAE,GAAA,IAAA,IAAA2D,EAAAC,IAAA,OAAA5D,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACyB6D,OAAAA,OAAAA,YAA0B,+CAG3B,IAAIvpB,WAAW,yBAE1B,uBAAuB,OALpB,OAAZriB,aAAY+nC,UAAAA,YAMY6D,SAAAA,qBAAgC5rC,KAAAurC,iBAAuB,OAAhE,OAAT3B,EAAS7B,UAAAA,YACU6D,SAAAA,QAAAA,aAAiC5rC,OAAA,eAAwB,OAAlE,OAAVyrC,EAAU1D,UAAAA,IAAAA,eACT,WACQ/nC,OAAA,MAAsB4pC,qBACrB5pC,KAAA6rC,iBAAsBJ,mBACrC,QAAA,YAAA,OAAA1D,eAAAD,EAAA9nC,UACJ,WAbQorC,OAAAA,EAAAA,QAAAprC,KAAAP,cAAA,iBAAA0rC,EAAAvD,kBAAAC,sBAAAA,MAcT,SAAAW,yBAAAX,+BAAA,SAAAc,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,OAAA,OAAAA,YACiBiD,OAAAA,SAAAA,MAA0B,QAAA,oBAAwC,oBAAuB,OAAA,OAAAjD,YAAAA,KAAAA,WAAA,OAAA,YAAA,OAAAA,eAAAH,OACzG,kBAFO2C,EAAAA,MAAAnrC,KAAAP,cAAA,iBAAAyrC,EAAAA,EAAAA,EAAAtD,kBAAAC,+BAGR,SAAAwB,EAASyC,EAAQx8B,GAAIy8B,IAAAC,EAAAC,EAAAF,EAAAA,SAAAlE,+BAAA,SAAA4B,GAAA,IAAA,IAAAyC,EAAAC,IAAA,OAAA1C,UAAAA,WAAA,OAGoC,OAD/C2C,GADAL,EAAU,IAAIvpB,aACDupB,OAAez8B,GAC5B08B,EAAKJ,OAAAA,gBAAuB,IAAIvpB,gBAAeonB,EAAAA,OACzBmC,OAAAA,eAAsB,QAAA,QAAmBI,GAAMF,EAAQM,GAAW,OAA3E,OAAbH,EAAaxC,UAAAA,iBACZ,eAAEwC,KAAeD,IAAI,OAAA,OAAA,KAAA,OAAAvC,IAAAA,WAAAJ,EARxBxB,KASP,SANOM,EAAAC,GAAA8C,OAAAA,EAAAA,QAAAlrC,KAAAP,cAAA,OAAA,gBAAAwrC,EAAArD,kBAAAC,+BAOR,SAAAwE,EAAWC,OAAGC,aAAA1E,+BAAA,SAAA2E,GAAA,cAAA,OAAAA,EAAAA,KAAAA,WAAA,OACkC,OAAtCD,EAAYvsC,KAAAysC,iBAAsBH,GAAIE,YAC/BZ,iBAAAA,aAAgCW,EAAW,oBAAA,SAA6C,aAAY,OAAA,OAAAC,EAAAA,SAAAA,KAAAA,WAAA,OAAA,YAAA,OAAAA,EAAAA,UAAAH,EAAArsC,KAT7G6nC,KAUP,SAHSsB,UAAA8B,EAAAA,MAAAjrC,KAAAP,UAAJitC,IAAI,uBAAA1B,EAAApD,kBAAAC,wBAAAA,OAIV,SAAA8E,EAAWL,OAAGC,aAAA1E,+BAAA,SAAA+E,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,OACkC,OAAtCL,EAAYvsC,OAAA,MAAsBssC,GAAIM,YAC/BhB,iBAAAA,cAAiCW,EAAW,oBAAA,SAA6C,UAAY,OAAA,OAAAK,IAAAA,eAAAA,EAAAA,MAAA,OAAA,UAAA,OAAAA,eAAAD,EAAA3sC,KAN5G6nC,KAOT,SAHSgF,UAAA7B,UAAAhrC,KAAAP,UAAJqtC,IAAI,mBAAA/B,EAAAnD,kBAAAC,sBAAAA,MAIV,SAAAkF,EAAUnD,EAAWkC,GAAM,IAAAkB,SAAAnF,sBAAAA,MAAA,SAAAoF,GAAA,IAAA,IAAAC,EAAAC,UAAA,OAAAF,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACMrB,8BAA+BE,GAAO,OAA/C,OAAdkB,EAAcC,EAAAA,KAAAA,YACPrB,OAAAA,SAAAA,MAAsB,QAAA,MAAsBhC,EAAWoD,GAAe,OAAA,OAAAC,YAAAA,KAAAA,WAAA,OAAA,UAAA,OAAAA,eAAAF,OACtF,SAHQK,EAAAC,GAAAtC,OAAAA,EAAAA,QAAAA,MAAA/qC,KAAAP,UAAH6tC,IAAG,uBAAAxC,EAAAlD,kBAAAC,wBAAAA,OAIT,SAAA0F,EAAU9B,EAAYQ,kBAAapE,wBAAAA,OAAA,SAAA2F,GAAA,cAAA,OAAAA,EAAAA,KAAAA,EAAAA,MAAA,OAAA,OAAAA,YAClB5B,eAAAA,QAAsB,kBAAsBH,EAAYQ,GAAc,OAAA,OAAAuB,EAAAA,SAAAA,KAAAA,WAAA,OAAA,YAAA,OAAAA,EAAAA,UAAAD,OACtF,SAFQE,EAAAC,UAAA5C,IAAAA,MAAA9qC,KAAAP,cAAA,iBAAAorC,EAAAjD,kBAAAC,sBAAAA,MAGT,SAAA8F,EAASC,EAAct+B,GAAIw8B,IAAAA,EAAAG,EAAAD,EAAA6B,EAAAC,EAAAhC,EAAAA,eAAAjE,+BAAA,SAAAkG,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,IAAAA,MAAAA,WAAA,OAAA,OAAAA,YACF/tC,KAAAkuC,KAAS,OAAlB,OAANpC,EAAMiC,UAAAA,EAAAA,OACwB/tC,KAAAmuC,GAAQrC,EAAQx8B,GAAK,OAAhC,OAAgC8+B,EAAAL,UAAjD9B,EAAamC,UAAEpC,EAAEoC,EAAAA,GAAAL,IAAAA,SACD/tC,OAAA,MAAU4tC,GAAa,QAAhC,OAAThE,EAASmE,IAAAA,MAAAA,aACe/tC,OAAA,MAAS4pC,EAAWkC,GAAO,QAImC,OAJtF+B,EAAeE,WACfD,EAAe,IAAIzrB,WAAWwrB,IAAAA,MAA6B7B,UAAgBC,oBAChE,IAAI5pB,WAAWwrB,MAChCC,IAAAA,MAAiB9B,EAAI6B,IAAAA,OACrBC,EAAAA,IAAiB,IAAIzrB,WAAW4pB,GAAgB4B,UAA6B7B,EAAAA,YAAe+B,EAAAA,SAAAA,KACrFM,KAAK7nC,uBAAAA,OAAM8nC,mBAAiBR,MAAc,QAAA,YAAA,OAAAC,eAAAJ,EAAA3tC,KAZ5C6nC,KAaR,SAVO0G,EAAAC,UAAA3D,gBAAA7qC,KAAAP,cAAA,iBAAAmrC,EAAAhD,kBAAAC,sBAAAA,MAWR,SAAA4G,EAASC,EAAeC,OAAaC,EAAA3C,EAAA4C,EAAAC,YAAAjH,OAAAA,wBAAAA,OAAA,SAAAkH,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,EAAAA,MAAA,OAIqC,OAJrCA,IAAAA,QAEvBjB,EAAezrB,mBAAgBuO,KAAK+d,IAAgB,SAAApuC,UAAKA,gBACzDquC,EAAkBd,iBAClB7B,EAAgB6B,cAAwBA,IAAAA,OAAoBiB,IAAAA,QACzC/uC,KAAA8sC,KAAU4B,GAAc,OAAjC,OAAVjD,EAAUsD,IAAAA,MAAAA,IAAAA,QACW/uC,aAASyrC,EAAYmD,GAAgB,OAA9C,OAAZC,EAAYE,UAAAA,aACU/uC,KAAAkvC,GAAQL,EAAc5C,GAAc,QAA7C,OAAb6C,EAAaC,IAAAA,MAAAA,iBACZD,GAAa,QAG6B,MAH7BC,IAAAA,SAAAA,EAAAA,GAAAA,EAAAA,SAId,IAAInqC,cAA0B,QAAA,UAAA,OAAAmqC,IAAAA,WAAAN,EAAAzuC,KAAA,CAAA,aAE3C,SAdOmvC,EAAAC,GAAAxE,OAAAA,EAAAA,cAAA5qC,KAAAP,cAAA,iBAeR,SAAIu4B,kBACOqW,KAAK7nC,OAAAA,eAAAA,MAAAA,OAAM8nC,mBAAiB,IAAIjsB,WAAW2V,QACrD,qBAAA2S,EAAA/C,kBAAAC,+BACD,SAAAwH,EAASR,EAAc5C,GAAa,IAAAH,EAAAE,EAAAsD,EAAAC,SAAA1H,sBAAAA,MAAA,SAAA2H,GAAA,oBAAA,OAAAA,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QAAAA,IAAAA,QAEP5D,OAAAA,iBAAAA,KAA+BiD,EAAc,iBAA4B,GAAA,OAAY,OAI3C,OAJzD/C,EAAM0D,EAAAA,KACNxD,EAAKC,gBACLrjB,EAAMqjB,EAAAA,aACNwD,EAAaxD,IAAAA,UACbqD,EAAkB,IAAIjtB,WAAU,GAAAre,OAAAsqC,mBAAKmB,GAAUnB,mBAAK1lB,KAAK4mB,aACjC5D,eAAAA,QAAsB,mBAAuBI,GAAMF,EAAQwD,GAAgB,QAApF,OAAfC,EAAeC,UAAAA,IAAAA,oBACVE,qBAAqBH,IAAgB,QAAA,MAAAC,EAAAA,QAAAA,EAAAA,GAAAA,aAG1C,IAAI5qC,cAAkC,QAAA,UAAA,OAAA4qC,eAAAH,EAAA,KAAA,CAAA,aAEnD,SAbOM,EAAAC,UAAAjF,UAAA3qC,KAAAP,UAAFyvC,IAAE,sBAAAxE,EAAA9C,kBAAAC,sBAAAA,MAcR,SAAAgI,EAAcjG,EAAWkG,OAAS7D,YAAApE,OAAAA,+BAAA,SAAAkI,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,EAAAA,KAAAA,IAAAA,OAAA,OAAA,OAAAA,YACZ/vC,aAAqB4pC,GAAU,OAAxC,OAAHxpC,EAAG2vC,EAAAA,KAAAA,EAAAA,OACmBnE,uBAAsB,kBAE/CxrC,OAASoiB,qBAAqBstB,IAAW,OAFzB,OAAb7D,EAAa8D,EAAAA,KAAAA,iBAGZ/vC,aAAyBisC,IAAc,OAAA,YAAA,OAAA8D,eAAAF,EAAA7vC,KAnB1C6nC,KAoBP,SANYqI,EAAAC,GAAAzF,OAAAA,EAAAA,QAAAA,MAAA1qC,KAAAP,UAAP2wC,IAAO,uBAAA3F,EAAA7C,kBAAAC,wBAAAA,OAOb,SAAAwI,EAAc5E,EAAYkD,OAAaG,aAAAjH,+BAAA,SAAAyI,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,WAAA,OAAA,OAAAA,IAAAA,QACjBtwC,aAAsByrC,GAAW,OAA1C,OAAHrrC,EAAGkwC,IAAAA,MAAAA,IAAAA,QACmB1E,eAAAA,QAAsB,cAE/CxrC,EAAKJ,aAAyB2uC,IAAe,OAF7B,OAAbG,EAAawB,UAAAA,sBAGRZ,qBAAqBZ,IAAc,OAAA,UAAA,OAAAwB,IAAAA,WAAAD,EAAArwC,KAZrC6nC,KAaZ,SANY4I,EAAAC,GAAAjG,OAAAA,EAAAA,QAAAzqC,KAAAP,cAAA,OAAA,YAAA+qC,EAAAA,EAAAA,EAAA5C,kBAAAC,+BAOb,SAAA8I,EAAsBrE,UAAGzE,sBAAAA,MAAA,SAAA+I,GAAA,IAAA,IAAAC,EAAA1D,UAAA,OAAAyD,IAAAA,MAAAA,WAAA,OAAA,OAAAA,iBACdhF,SAAAA,QAAAA,aAAgC5rC,aAAsBssC,GAAM,6BAG1D,cAAY,OAAA,OAAA,KAAA,OAAAsE,IAAAA,WAAAD,EAAA3wC,UACxB,SALoB8wC,GAAAtG,OAAAA,EAAAA,QAAAxqC,KAAAP,cAAA,OAAA,gBAAA8qC,EAAA3C,kBAAAC,+BAMrB,SAAAkJ,EAAuBzE,UAAGzE,sBAAAA,MAAA,SAAAmJ,GAAA,oBAAA,OAAAA,UAAAA,WAAA,OAAA,OAAAA,YAAAA,KACfpF,OAAAA,uBAAiC5rC,KAAAysC,iBAAsBH,GAAM,oBAAA,SAG3D,GAAA,QAAY,OAAA,YAAA,OAAA0E,eAAAD,EAAA/wC,KAVJ6nC,KAWpB,SALqBoJ,UAAA1G,IAAAA,MAAAvqC,KAAAP,UAAhByxC,IAAgB,8BAMtB,SAAiBlZ,EAAQzxB,WACf4qC,EAASnxC,OAAA,MAAyBg4B,+BACdzxB,IAAjB,cAAqB6qC,OAAAA,EAAUD,EAAAA,MAAa,kBAAbC,EAAAA,IAAAA,6BAA/B,MAAiF7qC,UARxE,GAUrB,iCACD,SAAoByxB,GAIhB,YAHIqZ,KACE1uB,EAAQ,IAAIN,WAAW2V,GACvBsZ,EAAM3uB,UACHpjB,IAAOA,EAAI+xC,EAAK/xC,IACrB8xC,GAAU7qC,OAAAA,aAAoBmc,EAAMpjB,WAEjC4lB,eAAYksB,EARtB,GASA,kBACD,SAAoBF,GAIhB,IAHMI,IAAAA,EAAAA,EAAAA,EAAepsB,OAAAA,KAAYgsB,GAC3BG,EAAMC,IAAAA,MACN5uB,EAAQ,IAAIN,WAAWivB,GACpB/xC,IAAOA,EAAI+xC,EAAK/xC,IACrBojB,EAAMpjB,GAAKgyC,EAAAA,WAAwBhyC,UAEhCojB,YACV,kBACD,SAAiB2pB,GAEN,OADD6E,EAAS7E,UAAY,wDACpBtsC,KAAAwxC,oBAAyBL,KACnC,qBAAA7G,EAAA1C,kBAAAC,wBAAAA,OACD,SAAA4J,IAAAC,IAAAA,EAAAC,EAAAC,EAAAtyC,EAAAuyC,EAAAC,EAAAJ,EAAAA,SAAA7J,+BAAA,SAAAkK,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,OAAA,OAAAA,EAAAA,OACmC/xC,KAAAgyC,MAAU,OAAnB,OAAtBhyC,KAAAwrC,kBAAsBuG,IAAAA,MAAAA,IAAAA,QACOnG,uBAA0B,+CAAkE,IAAIvpB,WAAW,4BAA8C,GAAA,gBAAmB,OAC3I,OADxDqvB,EAAcK,EAAAA,KACdJ,EAAK3xC,OAAA,MAAkBA,eAAA,OAAiC+xC,EAAAA,OACvCnG,SAAAA,qBAAgC8F,IAAAA,OAAyB,OAEnD,OAFvBO,EAAQF,UACRH,EAAKvD,KAAK7nC,uBAAAA,OAAM8nC,mBAAiB,IAAIjsB,WAAW4vB,MAChD3yC,EAAIssC,iBAAmBmG,EAAAA,QACX/xC,KAAAkyC,MAAU,QAEW,OAFjCC,EAAGJ,UAEHK,GADArG,EAAU,IAAIvpB,qBACWljB,EAAI6yC,GAAIJ,IAAAA,SACTnG,OAAAA,eAAmB,cAA+B8F,UAA2BU,GAAQ,QAC3C,OADlEP,EAAeE,UACfD,EAAKzD,KAAK7nC,uBAAAA,OAAM8nC,mBAAiB,IAAIjsB,WAAWwvB,MAAkBE,IAAAA,aACjE,IAAEJ,KAAIC,KAAIE,IAAIxyC,IAAG,QAAA,YAAA,OAAAyyC,eAAAN,EAAAzxC,KAb3B6nC,KAcA,kBAbOyC,IAAAA,MAAAtqC,KAAAP,UAAF4yC,IAAE,kBAcR,SAAaxd,GACFwZ,OAAAA,KAAKiE,SAASC,mBAAmB1d,IAfpC,GAgBP,gBACD,SAAG/lB,EAAMpN,EAAO4S,WACNX,EAAO,IAAIK,KACjBL,IAAAA,MAAaA,eAAkBW,OACzBk+B,SAAuB7+B,IAAAA,QAC7BtJ,SAAAA,OAAkByE,MAAapN,MAAc8wC,WAChD,gBACD,SAAG1jC,GAGC,IAFM2jC,IAAAA,EAAAA,EAAAA,EAAS3jC,MACT4jC,EAAKroC,SAAAA,OAAAA,WACF9K,IAAOA,EAAImzC,UAAWnzC,KAE3B,IADA,IAAIgB,EAAImyC,EAAGnzC,SACJgB,EAAAA,WACHA,EAAIA,YAAeA,kBACnBA,IAAAA,MAAUkyC,GACV,OAAOlyC,UAAYkyC,EAAAA,OAAelyC,EAAAA,QAEnC,OAAA,IAXV,GAaD,gBACA,SAAGuO,WACCzE,iBAAkByE,WAEtB,gBACA,WAEI,IADM6jC,IAAAA,EAAAA,EAAAA,EAAUtoC,WAAAA,QAAAA,WACP9K,IAAOA,EAAIozC,UAAgBpzC,KAChC,IAAMo/B,EAASgU,EAAQpzC,GAEjBuP,GAAO8jC,GADPA,EAAQjU,gBACYA,IAAAA,QAAiBiU,GAASjU,EACpDt0B,WAAAA,MAAkByE,+CAEzB,OAAA,gBAAAu7B,EAAAzC,kBAAAC,+BACD,SAAAgL,IAAA,IAAA5G,EAAA6G,EAAAvJ,EAAAwJ,EAAAC,SAAAnL,sBAAAA,MAAA,SAAAoL,GAAA,IAAA,IAAAC,EAAA/F,UAAA,OAAA8F,UAAAA,WAAA,OAAA,OAAAA,EAAAA,OACoCjzC,KAAAqyC,KAAS,OAOE,OAPFc,EAAAF,UACnC/L,EAAU,IADRyK,EAAEwB,EAAAA,MAAIA,EAAAA,MAAIA,EAAAA,KAAGA,EAAAA,GAOfC,EAAcntC,aAAeihC,GAAQ+L,EAAAA,QACfjzC,KAAAqzC,GAAQzJ,UAAWwJ,GAAY,QAG1D,OAHKnH,EAAagH,UACbH,EAAiB,aACN7G,GAChBgH,aAAAA,EAAAA,QAE0B/K,kBAAkBlyB,gBAA2B,uBAEvD,6BAGH/P,OAAAA,MAAe6sC,KACvB,YANIvJ,EAAQ0J,WAOT1J,IAAW0J,aAAA,MAAA,MACN,IAAIruC,qCAAoC,QAAA,OAAAquC,EAAAA,QAEvB1J,IAAAA,QAAe,SAApCwJ,EAAYE,IAAAA,QACEF,WAA0BA,EAAAA,YAAAA,QAC1C/yC,KAAAszC,OAAaP,UAAAA,iBACPC,EAAyBhzC,OAAA,MAAkBA,OAAA,QAAA,OACjDA,KAAAszC,OAAaN,MAIhBC,aAAA,MAAA,QAAAA,IAAAA,SAAAA,EAAAA,GAAAA,cAGqC,QAAA,YAAA,OAAAA,eAAAJ,EAAA7yC,KAAA,CAAA,cAE7C,WArCQqqC,OAAAA,EAAAA,MAAArqC,KAAAP,cAAA,kBAAA2qC,EAAAxC,kBAAAC,sBAAAA,MAsCT,SAAA0L,IAAA,IAAAhzC,EAAAjB,EAAAk0C,SAAA3L,sBAAAA,MAAA,SAAA4L,GAAA,oBAAA,OAAAA,IAAAA,MAAAA,EAAAA,MAAA,UACQlzC,EAAIP,KAAA0zC,QACJp0C,EAAIU,KAAA0zC,QACHnzC,GAAMjB,EAACm0C,CAAAA,IAAAA,QAAA,MAAA,OAAAA,qBACC,OAEM,OAAfE,EAAQ/iB,KAAKrwB,GAAEkzC,YACGzzC,KAAA4zC,GAAQD,EAAOr0C,GAAE,OAA1B,OAATk0C,EAASC,UAAAA,iBACND,GAAS,OAAA,UAAA,OAAAC,EAAAA,UAAAF,EAAAvzC,UACnB,WATQoqC,OAAAA,EAAAA,cAAApqC,KAAAP,cAAA,OAAA,YAAA0qC,EAAAA,EAAAA,EAAAvC,kBAAAC,+BAUT,SAAAgM,EAAUvkC,GAAIhQ,IAAAw0C,EAAA7H,EAAA3sC,EAAAA,EAAAuoC,OAAAA,+BAAA,SAAAkM,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,EAAAA,MAAA,OAAA,OAAAA,YACI/zC,eAAU,UAApBV,EAACy0C,EAAAA,KACDD,EAAQljB,KAAKtxB,GACZA,EAACy0C,CAAAA,IAAAA,QAAA,MAAA,OAAAA,qBACO,OAAA,OAAAA,IAAAA,QAEa/zC,KAAAqzC,GAAQS,EAAOxkC,GAAK,OAA7B,OAAb28B,EAAa8H,EAAAA,KAAAA,IAAAA,eACV9H,GAAa,QAAA,UAAA,OAAA8H,IAAAA,WAAAF,EAAA7zC,KAjBf6nC,KAkBR,SARQqM,UAAA/J,UAAAnqC,KAAAP,cAAA,mBAAAyqC,EAAAtC,kBAAAC,sBAAAA,MAST,SAAAsM,EAAUlI,GAAa1rC,IAAAA,EAAAuuC,EAAAvuC,EAAAA,QAAAsnC,OAAAA,wBAAAA,OAAA,SAAAuM,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,EAAAA,MAAA,UACf7zC,EAAIP,KAAA0zC,SACFU,YAAA,MAAA,OAAAA,qBACO,OAEM,OAAfT,EAAQ/iB,KAAKrwB,GAAE6zC,IAAAA,QACOp0C,KAAA4zC,GAAQD,EAAO1H,GAAc,OAAtC,OAAb6C,EAAasF,UAAAA,IAAAA,aACVtF,GAAa,OAAA,UAAA,OAAAsF,EAAAA,UAAAD,EAAAn0C,KAhBf6nC,KAiBR,SARQ0M,GAAArK,OAAAA,EAAAA,cAAAlqC,KAAAP,cAAA,mBAAAwqC,EAAArC,kBAAAC,sBAAAA,MAST,SAAA2M,IAAA,IAAAjL,SAAA1B,sBAAAA,MAAA,SAAA4M,GAAA,oBAAA,OAAAA,UAAAA,WAAA,OAAA,OAAAA,IAAAA,QAAAA,IAAAA,QAE+BvM,kBAAkBlyB,qCAAsC,UAAA,aAElE,kBAAA,WAGH,OACR,WANIuzB,EAAQkL,IAAAA,OAOTlL,GAAWkL,CAAAA,IAAAA,QAAA,MAAA,MACN,IAAI7vC,QAAJ,MAAwC,OAAA,OAAA6vC,IAAAA,QAEvBlL,EAAAA,OAAe,OAAxBkL,UACQA,IAAAA,SAAA,MAAA,QAAAA,IAAAA,SAAAA,EAAAA,GAAAA,EAAAA,SAGY,QAAA,OAAA,KAAA,OAAAA,EAAAA,UAAAD,EAAA,KAAA,CAAA,QAzBrC3M,KA2BR,kBAlBQoC,UAAAA,MAAAjqC,KAAAP,UAAHi1C,IAAG,uBAAA1K,EAAApC,kBAAAC,wBAAAA,OAmBT,SAAA8M,IAAA9M,IAAAA,EAAAA,EAAAA,OAAAA,wBAAAA,OAAA,SAAA+M,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,IAAAA,OAAA,UACS50C,KAAA+0C,KAASH,CAAAA,IAAAA,QAAA,MAAA,OAAAA,EAAAA,OACJ50C,KAAAg1C,MAAU,OAAA,UAAA,OAAAJ,eAAAD,EAAA30C,UAEvB,WAJQgqC,OAAAA,EAAAA,MAAAhqC,KAAAP,cAAA,gBAKT,oBACQO,KAAA0zC,UAAgB1zC,KAAA0zC,WAIvB,qBAAA3J,EAAAnC,kBAAAC,+BACD,SAAAoN,QAAAC,aAAArN,+BAAA,SAAAsN,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,WAAA,OACQD,KAAY,WACRl1C,KAAA0zC,WAAgBwB,EAAW,OAAAC,YACzB,IAAI7zC,SAAQ,SAAAC,UAAW6c,WAAW7c,MAAlC,IADyB4zC,IAAAA,QAAA,MACuB,OACtDD,IAAUC,YAAA,MAAA,OAAA,UAAA,OAAAA,eAAAF,EAAAj1C,KALjB6nC,KAOA,kBANOkC,IAAAA,MAAA/pC,KAAAP,cAAA,kBAAAqqC,EAAAlC,kBAAAC,sBAAAA,MAOR,SAAAyN,QAAAtzC,YAAA6lC,OAAAA,wBAAAA,OAAA,SAAA0N,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,EAAAA,KAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACqBvN,gBAAoB,OAA7B,OAAFC,EAAEsN,EAAAA,KAAAA,YACatN,IAAAA,QAAQ,OAAjB,OAANjmC,EAAMuzC,IAAAA,MAAAA,EAAAA,SAAAA,KACLvzC,WAAgB,OAAA,YAAA,OAAAuzC,eAAAD,OAC1B,WAJQxL,OAAAA,EAAAA,QAAAA,MAAA9pC,KAAAP,eApSb,EAA0B,u3BCH1B,IAAMuW,SAASD,sBAAAA,MACF2/B,cAAAA,UAAaC,EAAAA,cAAAtK,OAAAA,cAAA,SAAAqK,IAAApK,qBAAAoK,EAAArK,GAAA,CAAA,mBAAAsK,EAAAA,EAAAA,EAAA/N,kBAAAC,wBAAAA,OACtB,SAAAC,EAAc8N,EAAU1O,OAAOoC,EAAAuM,EAAAtM,MAAAuM,EAAAr2C,iBAAAooC,wBAAAA,OAAA,SAAAE,GAAA,IAAA,IAAAgO,EAAAC,IAAA,OAAAjO,UAAAA,WAAA,OAEyC,OAFvCkO,MAAWH,gBAAAhiC,IAAAgiC,OAAAA,KAASxM,IAAMwM,IAAAA,MAAAA,UAAAhiC,EAAAi0B,EAAAA,OAE7C8N,EAAUI,EAAc/M,2BAA6BD,MAAKlB,YACzC8N,aAAW7/B,yBAAsB4/B,GAAY,UAAA,aAEvD,yCACH3vC,aAAeihC,IACtBoC,GAAO,WAJJC,EAAQxB,EAAAA,MAKTwB,IAAWxB,YAAA,MAAA,MACNwB,EAAQ,OAAA,OAAAxB,EAAAA,QAELwB,IAAAA,QAAe,QAAA,OAAAxB,EAAAA,gBAAAA,IAAAA,OAAA,QAAA,MAAAA,aAAAA,EAAAA,GAAAA,IAAAA,SAAAA,EAAAA,GAAA,QAAA,OAAA,KAAA,OAAAA,eAAAD,EAAA,KAAA,CAAA,aAKnC,SAhBYK,EAAAC,UAAAuN,IAAAA,MAAA31C,KAAAP,UAAPynC,IAAO,kBAiBb,SAAWA,EAASgP,kBAAcl2C,KAAAknC,eAA2BA,KAAegP,KAAO,4BACnF,SAAehP,EAASgP,GAAc,IAAAC,EAAAC,EAAA,OAAAp2C,oBAA+BknC,KAAegP,KAAO,kBAC3F,SAAUhP,EAAStzB,GAAmB,OAAA5T,KAAAo2C,EAAA,qCAA8CxiC,EAAUszB,WAAsB,kBACpH,SAAcA,EAASgP,GAAc,OAAAl2C,KAAAo2C,EAAA,sBAA8BlP,KAAegP,KAAO,kBACzF,SAAYhP,EAASgP,GAAc,IAAAG,EAAAD,EAAA,OAAAp2C,OAAA,QAAA,KAA4BknC,KAAegP,KAAO,kBACrF,SAAUhP,EAASgP,GAAc,IAAAI,EAAAF,EAAA,OAAAp2C,OAAA,QAAA,KAA0BknC,KAAegP,EADW,IAtBzF,EAAaR,y0BCDb,IAAM1/B,OAASD,4BACFwgC,qBAAAA,sBAAoBC,EAAA5O,kBAAAC,wBAAAA,OAAG,SAAAC,EAAO2O,EAAc7iC,EAAU01B,GAAM,IAAAoN,EAAAnN,SAAA1B,sBAAAA,MAAA,SAAAE,GAAA,IAAA,IAAA4O,EAAAC,UAAA,OAAA7O,EAAAA,KAAAA,WAAA,OAIpE,OAHK2O,EAAc,cACFD,IAAAA,oBACJ7iC,GACbm0B,IAAAA,QAAAA,YAE0BmB,gCAA0B,MAAIlzB,SAAJ,MAAuC,uBAE3E,kBAAA,WACH/P,aAAeywC,IACtBpN,GAAO,WAJJC,EAAQxB,WAKTwB,IAAWxB,IAAAA,QAAA,MAAA,MACNwB,EAAQ,OAAA,OAAAxB,EAAAA,OAELwB,EAAAA,OAAe,OAAA,OAAAxB,iBAAAA,EAAAA,MAAA,QAAA,MAAAA,IAAAA,SAAAA,EAAAA,GAAAA,EAAAA,SAAAA,EAAAA,GAAA,QAAA,YAAA,OAAAA,IAAAA,WAAAD,EAAA,KAAA,CAAA,aAKnC,OAAA,SAnBgCK,EAAAC,EAAAe,GAAAqN,OAAAA,EAAAA,MAAAx2C,KAAAP,aAApB82C,YA0BbK,QAAAC,EAAAC,GAAA,IAAAC,EAAAC,UAAA,OAAAJ,QAAA,SAAAK,EAAAC,GAAA,OAAAH,EAAAE,GAAA,IAAA,GAAAJ,EAAAC,EAAA,uBANmBlP,kBAAAC,wBAAAA,OAAG,SAAAW,QAAAe,aAAA1B,wBAAAA,OAAA,SAAAc,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,OAAA,OAAAA,EAAAA,OACKM,WAAK,MAAIjzB,4BAA2B,UAAA,OAEzD,OAFY,OAARuzB,EAAQZ,UAAAA,iBAGPY,EAAAA,QAAe,OAAA,YAAA,OAAAZ,IAAAA,WAAAH,MAJnB,KAAA,MAMwB2O,IAAAA,EAAAA,UAAAvP,kBAAAC,+BAAG,SAAAwB,EAAOz1B,EAAU01B,GAAM,IAAAoN,EAAAnN,SAAA1B,sBAAAA,MAAA,SAAA4B,GAAA,IAAA,IAAA2N,EAAAR,UAAA,OAAAnN,IAAAA,MAAAA,IAAAA,OAAA,OAGpD,OAFKiN,EAAc,UACN9iC,GACb61B,IAAAA,QAAAA,EAAAA,OAE0BP,sCAA8BlzB,eAAuC,uBAE/E,kBAAA,WACH/P,aAAeywC,IACtBpN,GAAO,WAJJC,EAAQE,IAAAA,OAKTF,GAAWE,CAAAA,YAAA,MAAA,MACNF,EAAQ,OAAA,OAAAE,YAELF,IAAAA,QAAe,OAAA,OAAAE,iBAAAA,EAAAA,MAAA,QAAA,MAAAA,aAAAA,EAAAA,GAAAA,IAAAA,SAAAA,EAAAA,GAAA,QAAA,YAAA,OAAAA,EAAAA,UAAAJ,EAAA,KAAA,CAAA,QAbLxB,IAAxB,EANA,GAMA,sBAmBiBD,kBAAAC,wBAAAA,OAAG,SAAAwE,EAAOgL,EAAU/N,GAAM,IAAAC,SAAA1B,sBAAAA,MAAA,SAAA2E,GAAA,oBAAA,OAAAA,EAAAA,KAAAA,IAAAA,OAAA,OAAA,OAAAA,EAAAA,OAAAA,IAAAA,QAEnBtD,sCAA8BlzB,uBAA8BqhC,GAAY,sBAElF,yBACV/N,GAAO,WAHJC,EAAQiD,WAITjD,IAAWiD,IAAAA,QAAA,MAAA,MACNjD,EAAQ,OAAA,OAAAiD,YAELjD,IAAAA,QAAe,OAAA,OAAAiD,EAAAA,cAAAA,WAAA,QAAA,MAAAA,EAAAA,QAAAA,EAAAA,GAAAA,aAAAA,EAAAA,GAAA,QAAA,UAAA,OAAAA,IAAAA,WAAAH,EAAA,KAAA,CAAA,QATZxE,IAAA,EAnBjB,SAkCmByP,IAAAA,EAAAA,UAAA1P,kBAAAC,+BAAG,SAAA8E,EAAOzF,GAAOwP,IAAAnN,EAAAmN,EAAAA,SAAA7O,wBAAAA,OAAA,SAAA+E,GAAA,cAAA,OAAAA,IAAAA,MAAAA,IAAAA,OAAA,OACI,OAArC8J,EAAczwC,OAAAA,MAAeihC,GAAQ0F,YACpB3D,SAAKjlC,OAAIgS,2CAA0C,UAAA,aAE7D,6BAGH0gC,IACR,OANY,OAARnN,EAAQqD,IAAAA,MAAAA,IAAAA,QAAAA,KAOPrD,IAAAA,SAAe,OAAA,YAAA,OAAAqD,IAAAA,WAAAD,EATA9E,SC/D1B,IAAA0P,UAAAC,QAAA,SAAAC,UAAA,IAAAC,EAAA,CAAA,UAAA,UAAA,eAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,aAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,YAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,gBAAA,UAAA,UAAA,UAAA,UAAA,UAAA,eAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,gBAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,OAAA,UAAA,UAAA,gBAAA,UAAA,UAAA,UAAA,UAAA,UAAA,eAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,WAAA,OAAAD,QAAA,WAAA,OAAAC,CAAA,IAAA,CAAA,SAAAF,QAAAG,EAAAC,GAAA,IAAAC,EAAAJ,UAAA,OAAAD,QAAA,SAAAM,EAAAC,GAAA,OAAAF,EAAAC,GAAA,IAAA,GAAAH,EAAAC,EAAA,OAAA,IAAA,IAAAI,EAAAR,QAAAS,EAAAR,YAAA,IAAA,GAAA,SAAA7hC,SAAAoiC,EAAA,OAAApiC,SAAAoiC,EAAA,MAAA,GAAApiC,SAAAoiC,EAAA,MAAA,GAAApiC,SAAAoiC,EAAA,MAAA,IAAApiC,SAAAoiC,EAAA,MAAA,IAAApiC,SAAAoiC,EAAA,MAAA,EAAApiC,SAAAoiC,EAAA,MAAA,EAAApiC,SAAAoiC,EAAA,MAAA,EAAA,MAAAC,EAAA10C,KAAA00C,EAAApiC,QAAA,CAAA,MAAAqiC,GAAAD,EAAA10C,KAAA00C,EAAApiC,QAAA,CAAA,KAAO,IAAMsiC,OAAS,mBAAA,mFAAA,WAMX,cAAA,mBAAA,mBAAA,mBAAA,6GAAA,mBAAA,WAaD,cAAA,6GAAA,mBAAA,mBAAA,iDAaA,+CAAA,mBAAA,mBAAA,mBAAA,mBAAA,mBAAA,mBAAA,mBAAA,iCAaG,oDAAA,iCAAA,yHAaF,4BAAA,wGAAA,mBAAA,mBAAA,wBAaF,sKAAA,mBAAA,mBAAA,aAaG,qFAAA,mBAAA,mBAAA,mBAAA,gEAaD,0CAAA,mBAAA,wGAAA,mBAAA,aAaC,cAAA,6GAAA,mBAAA,mBAAA,4CAaF,+CAAA,iCAAA,mBAAA,sGAaC,cAAA,mBAAA,mBAAA,qFAAA,mBAAA,mBAAA,mBAAA,cAaE,cAAA,mBAAA,mBAAA,kHAAA,iCAAA,WAaH,qFAAA,iCAAA,mBAAA,+DAaA,iCAAA,iCAAA,mBAAA,wGAAA,UAaD,cAAA,iHAAA,mBAAA,mBAAA,8BAaC,kEAAA,mBAAA,mBAAA,6FAaE,4BAAA,mBAAA,2JAaA,kJAAA,yCAaA,sMAaC,iCAAA,mBAAA,mBAAA,2HAAA,WAaH,cAAA,iHAAA,mBAAA,4CAaA,0CAAA,mBAAA,iCAAA,8kBClRH,SAASC,aAAaC,GACzBlhC,IAAAA,EAAAA,QAEA,IACI,IAEUiU,EAFJjR,EAASlU,KAAAA,MAAWoyC,MACtBn+B,QAAOC,OACP,KAIA,OAJMiR,EAAO/gB,sBACblL,eAAegb,YAAgB,SAAAq8B,WAAEp2C,GAAgBk4C,EAAAngC,eAAAq+B,SAAX90C,EAAK42C,KACvCltB,YAAAA,uBAAsB,MAAgBhrB,GAAOsB,EADjDvC,GASR,CAHA,MAAO0C,IAmBS,SAAV02C,EAAWC,EAAKC,WAGZj4C,GAFAk4C,EAAM9iC,SAAS4iC,IAAAA,kBACfG,EAAMnsC,KAAAA,WAAkBisC,GACpBjsC,OAAAA,UAAcA,gBAAaksC,OAAaC,KAC5C/1C,EAAI4J,OAAAA,UAAcA,KAAAA,OAAcksC,UAAoBC,IACpD9xC,EAAI2F,iBAAcA,oBAAaksC,GAAcC,gCAC1Bn4C,QAAYoC,MAAUiE,iBAApC,SAtBf,CAyBe,SAAT+xC,EAAUJ,EAAKC,GACXC,IAAAA,EAAAA,EAEAl4C,GAFAk4C,EAAM9iC,SAAS4iC,EAAAA,oBACfG,EAAMnsC,kBAAkBisC,GACpBjsC,OAAAA,UAAcA,OAAAA,SAAaksC,OAAaC,KAC5C/1C,EAAI4J,iBAAcA,OAAAA,SAAcksC,UAAoBC,IACpD9xC,EAAI2F,iBAAcA,oBAAaksC,GAAcC,gBACnD,gBAAyBn4C,QAAYoC,MAAUiE,GAApC1B,aAAApB,UAKT80C,EAAS,IACLN,EAHJO,GAlCeT,EAkCQA,SAhCrBA,WACOA,GAGLre,EAAQme,OAAOE,IAGVre,OAIJme,SAAAA,sBAyBAI,EAAQO,UACRP,EAAQO,UACRP,EAAQO,UACRP,EAAQO,UACRA,MACAF,EAAOE,UACPF,EAAOE,UACPF,EAAOE,UACPF,EAAOE,UACPF,EAAOE,OA/ClB,IAkDM1tB,EAAO/gB,WAAAA,MAEblL,SAAAA,MAAe05C,YAAgB,SAAA1B,WAAE/2C,GAAgB24C,EAAA5gC,eAAAg/B,SAAXz1C,EAAKq3C,KACvC3tB,oBAAsB,KAAApnB,OAAgB5D,GAAOsB,wxBChE/Cs3C,IAAAA,cAAgB,IAAInP,cACpBoP,cAAgB,IAAIvD,cACtBwD,gBAAaC,eAAAA,UA8PMC,EADlBC,EA3DAC,EAAAA,EArCiBC,IADjBC,EAhHAC,YACD,SAAAP,QAAclvB,mBAAAshB,qBAAA4N,IACVlvB,EAAA0vB,WAAA15C,KAAAk5C,eAEAlvB,IAAAA,SACAA,aACAA,aACAA,IAAAA,SACAA,aACAA,IAAAA,QACAA,KAAAA,EAAAA,kBACAA,EAAAA,QACAA,IAAAA,SACAA,EAAAA,aACAA,EAAAA,cACAA,UAAsB,KACtBA,UAAoB,KACpBA,UAAqB,GACrBA,aACAA,EAAAA,eACAA,aACAA,EAAAA,aACAA,aACAA,IAAAA,MAAkB,KAClBA,UAAoB,KACpBA,EAAAA,eACAA,IAAAA,UACAA,UAAe,iDAKfA,IAAAA,WACAA,aACAA,EAAAA,eAAsBgvB,cACtBhvB,IAAAA,MAAsBivB,cACtBjvB,IAAAA,QACAA,IAAAA,QAAsBA,CACzB2vB,CAAAA,oTAAAA,CAAAT,EAlF2C/lC,KAkF3Ck4B,aAAA6N,EAAA,CAAA,oBAxED,kBACWl5C,KAAA45C,eAEX,SAAal4C,GACHm4C,IAIIC,EAJJD,EAAAA,QAAAA,EAAW75C,OAAA,MAEbA,OAAA,OAEM85C,EADY,IAAIC,gBAAgB50B,OAAAA,oBAChB60B,QAAAA,QACDF,IAAkB95C,OAAA,MAEnCA,OAAA,MAAiB85C,GAKjB95C,OAAA,MAAiB0B,EAGZ1B,eACDA,eACAA,kBAMRA,OAAA,MAAiB0B,EAGrB1B,OAAA,QAAA,KAA+B65C,EA0ClC,GAzCA,OAAA,SACD,8BACW75C,qBAA2BA,OAAA,UAA+BA,KAAAi6C,iBACpE,mBAAAR,EAAA7R,kBAAAC,sBAAAA,MAuCD,SAAAC,EAAmBoS,GAAkBC,IAAAC,EAAAD,EAAAA,QAAAtS,OAAAA,+BAAA,SAAAE,GAAA,cAAA,OAAAA,IAAAA,MAAAA,IAAAA,OAAA,OACjCsS,cAAAnB,iBAAAl5C,KAAAq6C,CAAA,CAAmBH,SACfl6C,OAAA,QACAo4C,aAAap4C,OAAA,OACbA,gBAIAA,eACMm6C,EAAkB9vC,WAAAA,QAAAA,OACxB8vC,iBACAA,IAAAA,MAAuBn6C,aACvBqK,WAAAA,QAAAA,MAA0B8vC,MAIpBA,EAAkB9vC,WAAAA,QAAAA,OACxB8vC,WACAA,IAAAA,0FACA9vC,iBAAAA,YAA0B8vC,SAG1Bn6C,cACaqK,WAAAA,MACb+gB,qBAAqCprB,OAAA,OACxC,OAAA,YAAA,OAAA+nC,EAAAA,UAAAD,EAAA9nC,KAhEJ6nC,KAiEA,SA1BiBM,UAAAsR,UAAAA,MAAAz5C,KAAAP,UAAZsM,IAAY,+BA2BlB,WACIsuC,IAAAA,EAAAA,EAAAA,cAAAnB,SAAAl5C,KAAAq6C,CAAA,IACAr6C,OAAA,MAAeA,aACfA,qBAEAA,iBACH,kBACD,WACQ,IAKE85C,EALFQ,EAAAC,EAACv6C,OAAA,SAKC85C,EADY,IAAIC,gBAAgB50B,eAAAA,kBAChB60B,QAElBh6C,aAAiB85C,EAEjB95C,KAAA0H,uBAEM1H,OAAA,QACNA,KAAAw6C,wBACAx6C,OAAA,UAfP,GAiBA,OAAA,WACD,mBACUy6C,EAAa,IAAIC,IAAIv1B,iBAAAA,OACrBw1B,EAAS,IAAIZ,gBAAgBU,EAAAA,QACnCE,EAAAA,WAAuB36C,OAAA,OACjB46C,OAAM,MAAMH,EAAAA,sBAAuBE,aACzCx1B,eAAAA,aAA4B,CAAA,KAAQy1B,KAEvC,kBACD,WACUZ,IAAAA,EAAAA,EAAAA,EAAY,IAAID,gBAAgB50B,OAAAA,WAAAA,OACtCnlB,KAAAknC,gBAAyB8S,EAAAA,gBACzBh6C,aAAA66C,cAA6Bb,yBAC7Bh6C,qBAA6Bg6C,IAAAA,qBACzBh6C,OAAA,QAAA,OAA0BA,sBAA8BA,qBACxDA,aAAmBA,OAAA,OAGnBA,OAAA,MAAsB,OAE7B,kBACD,SAAQk6C,GACJG,cAAAnB,EAAAmB,OAAAr6C,KAAAq6C,CAAA,CAAcH,GAFjB,GAGA,OAAA,gBAAAV,EAAA5R,kBAAAC,wBAAAA,OACD,SAAAW,EAAmB3mC,GAAC,IAAA84C,EAAAF,SAAA5S,sBAAAA,MAAA,SAAAc,GAAA,oBAAA,OAAAA,IAAAA,MAAAA,IAAAA,OAAA,UAChB9mC,YACA7B,gBACKA,cAAmBA,OAAA,OAAY2oC,IAAAA,QAAA,MACqB,OAArD3oC,KAAA0W,mBAAqDiyB,IAAAA,QAAAA,MAAA,OAcf,OAX1C3oC,iBAEM26C,EAAS,IAAIZ,iBACnBY,SAAAA,KAA2B36C,cAC3B26C,IAAAA,QAAAA,KAAyB36C,KAAA6W,UACnB4jC,EAAa,IAAIC,IAAIv1B,SAAAA,MAAAA,SAC3Bs1B,MAAoBE,IAAAA,QACpBx1B,OAAAA,QAAAA,UAAyB,MAAQs1B,EAAAA,YAEjCz6C,OAAA,QAAA,MAAyBA,aACzBA,eAAA,MAA6BA,KAAA6W,QAC7B7W,qBAA6BA,KAAA6W,QAAa8xB,EAAAA,QACpC3oC,KAAA86C,cAAmB96C,KAAAknC,SAAa,QAAA,YAAA,OAAAyB,IAAAA,WAAAH,EAAAxoC,KApBzC6nC,KAqBA,SApBiBO,UAAAoR,UAAAx5C,KAAAP,cAAA,OAAA,YAAA85C,EAAA3R,kBAAAC,sBAAAA,MAqBlB,SAAAwB,EAAqB/5B,GAAI28B,IAAAA,EAAAA,EAAAA,QAAApE,OAAAA,+BAAA,SAAA4B,GAAA,IAAA,IAAAsR,EAAAC,IAAA,OAAAvR,UAAAA,EAAAA,MAAA,OAAA,OAAAA,IAAAA,QACOzpC,OAAA,QAAA,MAAwBiG,OAAAA,MAAeqJ,IAAM,OAAtD,OAAb28B,EAAaxC,UAAAA,EAAAA,gBACZ,aACUwC,IAChB,OAAA,OAAA,KAAA,OAAAxC,eAAAJ,EAAArpC,KAzBa6nC,KA0BjB,SALmBsB,UAAAoQ,UAAAA,MAAAv5C,KAAAP,UAAdw7C,IAAc,kBAMpB,WACUN,IAAAA,EAAAA,EAAAA,EAAS,IAAIZ,gBAKbU,GAJNE,iBAA2B36C,OAAA,QAAA,OAC3B26C,EAAAA,SAAAA,KAA+B36C,eAAA,OAC/B26C,IAAAA,QAAAA,KAA+B36C,aAAAk7C,eAEZ,IAAIR,IAAIv1B,eAAAA,OAC3Bs1B,EAAAA,WACAA,KAAAA,UAAoBE,YACpBx1B,OAAAA,SAAAA,KAAuBs1B,IAAAA,OAfP,GAgBnB,mBAAAnB,EAAAA,EAAAA,EAAA1R,kBAAAC,+BACD,SAAAwE,EAAwBnF,GAAOiU,IAAAC,EAAAC,EAAAC,EAAAH,EAAAA,EAAAtT,OAAAA,+BAAA,SAAA2E,GAAA,IAAA,IAAA+O,EAAAC,IAAA,OAAAhP,UAAAA,WAAA,OACL,OAAtBxsC,KAAAqW,aAAsBm2B,IAAAA,QAAAA,YAEcxsC,aAAoBknC,GAAQ,OAArC,OAAjBiU,EAAiB3O,UAAAA,EAAAA,OACLxsC,OAAA,MAAA86C,cAAkCK,EAAmBn7C,cAAa,OAA3E,OAAHo7C,EAAG5O,EAAAA,KAAAA,EAAAA,QACkBxsC,OAAA,QAAA,MAAwBo7C,IAAAA,OAAc,WAA3DK,EAAYjP,WACZkP,EAAUz1C,KAAAA,MAAWw1C,MAEvBC,MAQuB,OAPjBL,EAAWp1C,aAAWy1C,EAAAA,YAAAA,OAC5B17C,KAAAwW,aAAoB6kC,EACpBr7C,KAAAuW,cACAvW,OAAA,MAAsB07C,IAAAA,MACtB17C,KAAA8W,aAAoB7Q,KAAAA,MAAWy1C,EAAAA,mBAE/B17C,aAAkBiG,aAAWy1C,mBAC7B17C,eAAuBwsC,IAAAA,SACjBxsC,OAAA,QATawsC,EAAAA,QAAA,MASU,QAC7BmP,OAAAA,EAAI37C,OAAA,QAAA27C,yCACiBL,OAAAA,EAAGt7C,qBAAAs7C,IAAAA,MAAAA,eACvB9O,IAAAA,SAAA,MAAA,QAGDxsC,oBAA2D,QAAAwsC,IAAAA,SAAA,MAAA,WAAAA,IAAAA,SAAAA,EAAAA,GAAAA,mBAK3DA,EAAAA,KAAAA,MACyB,OAAzBxsC,OAAA,MAAA47C,KAAyBpP,aACnBxsC,KAAA67C,yBAFcrP,aAAA,MAEW,QAAA,OAAAA,aACzBxsC,aAAuBknC,GAAQ,QAAAsF,IAAAA,SAAA,MAAA,QAGrCxsC,oBAAyD,QAItC,OAJsCwsC,IAAAA,SAI7DxsC,OAAA,SAAuBwsC,IAAAA,UAAA,QAAA,YAAA,OAAAA,EAAAA,UAAAH,EAAArsC,KAAA,CAAA,mBAE9B,SAxCsB6sC,GAAAyM,OAAAA,EAAAA,QAAAt5C,KAAAP,cAAA,6BAyCvB,eAAkBq8C,EAGVC,MAHUC,EAAAh8C,KACV87C,OAAAA,EAAC97C,OAAA,QAAA87C,EAAAA,UAEDC,IACJ/7C,8BAAkC,SAACqZ,EAAKxK,GAChCwK,IACM4iC,EADN5iC,EAAAA,EAAAA,qBACM4iC,EAAWD,IAAAA,QAAAA,QAAAA,OAA+B,SAACr8C,kBAAMA,EAAAA,SAAAA,MAAsBA,IAAAA,QAAYo8C,QAErFE,EAAAA,WAAsB5iC,EACtB2iC,UAAAA,QAAAA,OAAiCntC,MAErCktC,KAGA1iC,UAAYxK,CAVpB,IA7CmB,GA0DtB,4BAAAwqC,EAAAzR,kBAAAC,sBAAAA,MACD,SAAA8E,EAAoBzF,GAAOW,IAAAA,EAAAA,QAAAA,OAAAA,+BAAA,SAAA+E,GAAA,cAAA,OAAAA,EAAAA,KAAAA,EAAAA,MAAA,UAClB5sC,KAAA67C,eAAA9G,KAAwBnI,CAAAA,YAAA,MAAA,OAAAA,IAAAA,QACnB5sC,OAAA,QAAA,QAAyB,OAAA,OAAA4sC,IAAAA,QAE7B5sC,OAAA,MAAuBknC,GAAQ,OAAA,UAAA,OAAA0F,eAAAD,EAAA3sC,UACxC,SALkBotC,GAAAiM,OAAAA,EAAAA,cAAAr5C,KAAAP,cAAA,OAAA,YAAA25C,EAAAxR,kBAAAC,sBAAAA,MAMnB,SAAAkF,IAAA,IAAA0J,EAAA2E,EAAAc,SAAArU,sBAAAA,MAAA,SAAAoF,GAAA,oBAAA,OAAAA,IAAAA,MAAAA,IAAAA,OAAA,UAAAkP,OAAAA,EACSn8C,eADTm8C,OAC0BA,EAAjBA,YAAAA,EAAAA,qBAA4ClP,EAAAA,OAAA,MAAA,OAAAA,oBAAA,OAY9C,OAVGwJ,EAAe,GACrBz2C,KAAAwW,eAAA,QAAA,QAAA,OAAmD,SAAC4lC,WAChDA,IAAAA,MAAAA,KAAAA,SAA+B,SAACvnC,WACvB4hC,IAAAA,MAAsB5hC,EAAAA,gBACvB4hC,IAAAA,MAAkB5hC,IAAAA,OAEjB4hC,EAAAA,SAAsB5hC,EAAAA,cACvB4hC,IAAAA,MAAkB5hC,IAAAA,aAG3Bo4B,IAAAA,QAAAA,YAEiBsJ,qBAAqBE,EAAcz2C,KAAA4T,eAAuB5T,cAAa,QAAnFo7C,EAAGnO,IAAAA,OACHmO,cACAp7C,aAAqBo7C,UACfiB,SAAqBjB,sBAAP,KAA2Cn1C,KAAAA,MAAWm1C,IAAAA,QAAAA,OAAwBA,IAAAA,MAAAA,SAClGp7C,aAAsBq8C,eACtBr8C,aAAoBq8C,cAGpBr8C,KAAAmM,eACA+vC,OAAAA,EAAId,YAAAc,EAAAA,QACAl8C,OAAA,MAAao7C,IAAAA,MAAAA,WACTp7C,gBACAo4C,aAAap4C,cACbA,OAAA,SAGXitC,aAAA,MAAA,QAAAA,aAAAA,EAAAA,GAAAA,IAAAA,SAGmD,QAAA,YAAA,OAAAA,IAAAA,WAAAF,EAAA/sC,KAAA,CAAA,aAE3D,WApCqBo5C,OAAAA,EAAAA,cAAAp5C,KAAAP,UAAhB68C,IAqCN,kBACA,SAAkBz6C,GACR1B,IAAAA,EAAAA,EAAAA,EAAS0B,UACf7B,aAAiBG,IAAAA,KAHrB,GAIC,OAAA,WACD,SAAgB0B,WACN1B,EAAS0B,IAAAA,MACf7B,KAAA6W,QAAe1W,IAAAA,KAHlB,GAKD,OAAA,WACA,SAAsBwT,kBACXD,sBAAsB,IAAIM,KAAKL,GAAO3T,aAFjD,GAGC,kBACD,SAAmBoU,GACRD,OAAAA,mBAAmBC,KAC7B,kBACD,SAAsBC,GACXM,OAAAA,sBAAsBN,KAChC,kBACD,SAAoBK,GACTD,OAAAA,oBAAoBC,KAC9B,OAAA,WACD,SAAaf,UACFqB,aAAarB,EAFvB,GAGA,4BACD,SAAeA,UACJmB,eAAenB,EAFzB,GAGA,yBACD,SAAYkB,UACDD,YAAYC,EAFtB,GAGA,kCACD,SAAqB0nC,GACjBplC,IAAAA,EAAAA,EACAnX,KAAA4T,SAAgB2oC,EAChBv8C,OAAA,QAEAA,KAAAw6C,wBACAx6C,iBACH,kBACD,WACWiW,IAAAA,EAAAA,EAAAA,OAAAA,sBAAsBjW,OAAA,MAAwBA,KAAA4T,SAAe5T,OAAA,MAAoBA,KAAAoW,mBAAyBpW,aAAgBA,aAAqBA,aAAiBA,aAAmBA,aAAoBA,KAAAw8C,aAAmBx8C,aAAkBA,aAAoBA,aAAgBA,aAAcA,aAAcA,aAAmBA,aAAiBA,aAAuBA,aAAmB,cAC/WA,qBAAuBA,wBAClBA,qBAA4BA,sBAC9BA,qBAA0BA,gBAChCA,qBAAoBA,4BACRA,qBAAgCA,yBACnCA,qBAA6BA,4BAC1BA,OAAA,MAAAkkB,KAAgClkB,0BAClCA,OAAA,QAAA,MAA8BA,mBACrCA,OAAA,QAAA,MAAuBA,qBACrBA,OAAA,QAAA,MAAyBA,kBAC5BA,OAAA,QAAA,MAAsBA,2BACbA,OAAA,MAAAkkB,KAA+BlkB,OAd5D,IA/U2CmT,EAA/BgmC,cACJA,MAAc,CACnBsD,0lrEjBTwnB,EAACp9C,KAAKwC,KAAW0C,EAAE,IAAIlF,EAAEK,OAAOL,EAAE,GAAGwC,EAAEme,SAASne,EAAEvC,EAAEiF,IAAI1C,EAAAA,MAAO,IAAG,IAA+OvC,EAAxOqF,aAAa,OAA2NrF,EAAlNuF,QAAQ,GAAG,iBAAuMvF,EAApL,OAAoLA,EAA3K,MAAMsF,MAAM,mEAA+JtF,EAA1F,uFAA2F,EAA1PuC,GAA4PxC,EAAEkF,EAAE,IAAIlF,EAAE,IAAW,IAAIG,IAAE+E,EAAElF,EAAEC,MiBU38Bo9C,CAAGrlC,gBAAAA,iBAAAC,uBAAA,WAAA,SAMN6hC,yqFAwVLl5C,WAAW,CACPqpB,EAAS,MAAQtjB,UACjBnF,0BAA0B1B,SAC3B+5C,uDAA8C,GACjDj5C,WAAW,CACPqpB,EAAS,MAAQ9iB,SACjB3F,0BAA0B1B,SAC3B+5C,mDAA0C,GAC7Cj5C,WAAW,CACPqpB,EAAS,MAAQtjB,UACjBnF,yBAA0B1B,SAC3B+5C,cAAAA,8BAA+C,GAClDj5C,WAAW,CACPqpB,EAAS,MAAQtjB,UACjBnF,qBAAAA,KAA0B1B,SAC3B+5C,wBAAAA,0BAA8C,GACjDj5C,WAAW,CACPqpB,EAAS,MAAQ9iB,SACjB3F,0BAA0B1B,SAC3B+5C,wCAHHj5C,UAGsC,GACtCA,WAAW,CACPqpB,EAAS,MAAQ9iB,SACjB3F,yBAA0B1B,SAC3B+5C,wCAHHj5C,UAGqC,GACrCA,WAAW,CACPqpB,EAAS,MAAQ9iB,SACjB3F,qBAAAA,KAA0B1B,SAC3B+5C,wBAAAA,kBAAiC,GACpCj5C,WAAW,CACPqpB,EAAS,MAAQ9iB,SACjB3F,qBAAAA,KAA0B1B,SAC3B+5C,wBAAAA,0BAA2C,GAC9Cj5C,WAAW,CACPqpB,EAAS,MAAQ9iB,SACjB3F,qBAAAA,KAA0B1B,SAC3B+5C,wBAAAA,0BAAiC,GACpCj5C,WAAW,CACPqH,IACAzG,qBAAAA,KAA0B2F,SAC3B0yC,wBAAAA,0BAAoC,GACvCj5C,WAAW,CACPqH,IACAzG,0BAA0BmF,UAC3BkzC,cAAAA,oBAHHj5C,UAGyC,GACzCA,WAAW,CACPqH,IACAzG,qBAAAA,KAA0BmF,UAC3BkzC,wCAHHj5C,UAG0C,GAC1CA,WAAW,CACPqH,IACAzG,qBAAAA,KAA0B1B,SAC3B+5C,cAAAA,gCAA2C,GAC9Cj5C,WAAW,CACPqH,IACAzG,0BAA0B1B,SAC3B+5C,cAAAA,oBAHHj5C,UAG4C,GAC5CA,WAAW,CACPqH,IACAzG,yBAA0BiD,QAC3Bo1C,wCAHHj5C,UAG6C,GAC7CA,WAAW,CACPqH,IACAzG,qBAAAA,KAA0B2F,SAC3B0yC,wBAAAA,0BAAyC,GAC5Cj5C,WAAW,CACPqH,IACAzG,0BAA0B2F,SAC3B0yC,wBAAAA,0BAAwC,GAC3Cj5C,WAAW,CACPqH,IACAzG,0BAA0BmF,UAC3BkzC,kDAA0C,GAC7Cj5C,WAAW,CACPqH,IACAzG,0BAA0B2F,SAC3B0yC,+CAAsC,GACzCj5C,WAAW,CACPqH,IACAzG,0BAA0B2F,SAC3B0yC,wCAHHj5C,UAGuC,GACvCA,WAAW,CACPqH,IACAzG,qBAAAA,KAA0B1B,SAC3B+5C,wBAAAA,0BAAuC,GAC1Cj5C,WAAW,CACPqH,IACAzG,yBAA0B1B,SAC3B+5C,wBAAAA,0BAAyC,GAC5Cj5C,WAAW,CACPqH,IACAzG,0BAA0BuF,SAC3B8yC,wCAHHj5C,UAG4C,GAC5CA,WAAW,CACPqH,IACAzG,qBAAAA,KAA0B2F,SAC3B0yC,wBAAAA,4BAA2C,GAC9Cj5C,WAAW,CACPqpB,EAAS,MAAQ9iB,SACjB3F,qBAAAA,KAA0B2F,QAC1B3F,0BAAgC,CAAC2F,UAClC0yC,wCAJHj5C,KAIwC,MACxCi5C,cAAgBj5C,WAAW,CACvB08C,YAAAA,MACA97C,0BAAgC,KACjCq4C,eAHaj5C,wUAAAA", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 11]}