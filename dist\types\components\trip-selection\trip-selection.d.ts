import { LitElement, PropertyValues } from "lit";
import { CryptoService } from "../../services/CryptoService";
import { FlightService } from "../../services/FlightService";
export declare class TripSelection extends LitElement {
    private _cryptoService;
    private _flightService;
    static styles: import("lit").CSSResult[];
    mode: string;
    color: string;
    font: string;
    googleFontsUrl: string;
    ApiKey: string;
    redirect_uri: string;
    autoLanguageParam: boolean;
    private _language;
    private _hasCheckedURL;
    get language(): string;
    set language(value: string);
    uri_searchBox: string;
    showFromText: boolean;
    showDetailTicket: boolean;
    showDetailFareRule: boolean;
    showMoreClassic: boolean;
    autoShowPriceDetails: string;
    showLanguageSelect: boolean;
    hideMultiSegmentFlights: boolean;
    get isAutoShowPriceDetails(): boolean;
    get currencySymbolAv(): string;
    private _ApiKey;
    private searchTripResponseMain;
    private _searchTripResponse;
    private _searchTripRequest;
    private _isLoading;
    private _isGlobal;
    private _isMobileDevice;
    private _isShowLoading;
    private _isError;
    private _isShowPriceDetail;
    private InventoriesSelected;
    private inforAirports;
    private _airItinerarySelected;
    private pricePaxInfor;
    private _sumPrice;
    private _progress;
    private calendarWeek;
    private _isShowDetail;
    private _isShowPriceDetails;
    private canScrollLeft;
    private canScrollRight;
    private currentSortOption;
    private dateStart;
    private showOffcanvas;
    private showMultiSegmentFlights;
    private isCountDown;
    private countdown;
    private isopenModalResult;
    private departureItem;
    private arrivalItem;
    private allAirlineResult;
    private segmentsSelected;
    private fareRulesRquest;
    private fareRules;
    private maxCountLeg;
    private displayMode;
    private convertedVND;
    private currencySymbol;
    private isCurrencyLoaded;
    private _retryCount;
    private readonly _maxRetry;
    rangeSlider: [number, number];
    rangeSliderLegs: [number, number];
    constructor(_cryptoService: CryptoService, _flightService: FlightService);
    connectedCallback(): void;
    private checkLanguageFromURL;
    private updateURLWithLanguage;
    private loadInitialAirportInfo;
    private updateDepartureArrivalItems;
    protected firstUpdated(_changedProperties: PropertyValues): Promise<void>;
    protected updated(_changedProperties: PropertyValues): void;
    checkDevice(): void;
    CheckisGlobal(airport: string | undefined): boolean;
    RequestEncrypt(): Promise<any>;
    getTotalPriceLowestWithMode(segment: any): string;
    getTotalPriceWithMode(): string;
    getInventoryPriceWithMode(inventory: any): string;
    calculateMaxCountLeg(): void;
    SearchTrip(): Promise<void>;
    CallSearchTrip(): Promise<void>;
    checkExpiryDate(expiryDate: string): void;
    openModal(title?: string, content?: string, isCountDown?: boolean): void;
    reSearchTrip(): void;
    combineGroupsStart(): Promise<void>;
    getFareRules(request: any): Promise<void>;
    getFareRuleByFareType(fareType: string): any;
    getAllFareType(): any[];
    getInforAirports(airportsCode: any, skipLoadingFlag?: boolean): Promise<void>;
    getAllAirports(): string[];
    openModalResult(): void;
    checkResponseAdvance(resultObj: any): any;
    clearData(): void;
    createCalendarWeek(date: Date): Date[];
    loadDataRequest(): void;
    showPriceDetail(): void;
    setShowOffcanvas(show: boolean): void;
    getPricePax(): void;
    checkNextStep(): boolean;
    nextStep(): Promise<void>;
    handleRangeSliderChange: (event: CustomEvent) => Promise<void>;
    handleRangeSliderLeg: (event: CustomEvent) => Promise<void>;
    filterLegsInRange(): Promise<void>;
    filterTimeDepartureDateInRange(): Promise<void>;
    combineGroupsEnd(): Promise<any>;
    filterSearchTripResponseByCombine(combine: boolean): Promise<any>;
    getAllAirlines(): any[];
    selectSegment(bookGDS: string, airCodeRef: string, combine: boolean, segmentKeyRef: any, sumPrice: any): void;
    selectInventory(bookGDS: string, airCodeRef: string, combine: boolean, segmentKeyRef: any, sumPrice: any): void;
    getSumPrice(): number;
    checkScroll(): void;
    isShowDetailSegment(segmentMain: any): void;
    reSelectFlight(): Promise<void>;
    showPriceDetails(event: Event): void;
    scrollInventory(arror: string): void;
    checkBookingButton(): boolean;
    nextPage(): Promise<void>;
    getMinExpDate(): Date;
    getGroupCodeRefSelected(): Promise<any>;
    sortWithPrice(): void;
    sortWithAirline(): void;
    sortWithDepartureDate(): void;
    filterByAirlines(): Promise<void>;
    filterAirlines(event: Event, airline: string): void;
    checkedAllAirlinesStatus(): boolean;
    isMultiSegmentFlight(segment: any): boolean;
    toggleMultiSegmentFlights(): void;
    sortWithPriceOnly(): void;
    sortWithAirlineOnly(): void;
    sortWithDepartureDateOnly(): void;
    applyMultiSegmentFilter(): void;
    getSegmentDataFromElement(element: Element): any | null;
    private forceUpdateDOM;
    handleWeekChange(event: CustomEvent): void;
    getFileNameFromUrl(): string;
    handleLanguageChange(newLang: string): void;
    render(): import("lit-html").TemplateResult<1>;
    static get observedAttributes(): string[];
    attributeChangedCallback(name: string, oldVal: any, newVal: any): void;
}
