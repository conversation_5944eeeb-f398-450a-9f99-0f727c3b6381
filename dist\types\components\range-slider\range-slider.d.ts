import { LitElement, PropertyValues } from "lit";
export declare class RangeSlider extends LitElement {
    static styles: import("lit").CSSResult[];
    min: number;
    max: number;
    step: number;
    values: [number, number];
    allowSameValue: boolean;
    private dragging;
    private hoveredHandle;
    private lastMouseX;
    stepDivisions: number[];
    constructor();
    updated(changedProperties: PropertyValues): void;
    calculateStepDivisions(): void;
    private handleMouseDown;
    handleMouseMove(e: MouseEvent): void;
    handleMouseUp(): void;
    private handleMouseEnter;
    private handleMouseLeave;
    private handleTouchStart;
    handleTouchMove(e: TouchEvent): void;
    handleTouchEnd(): void;
    getLeftPercent(value: number): string;
    render(): import("lit-html").TemplateResult<1>;
}
