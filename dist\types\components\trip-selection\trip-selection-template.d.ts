import "../progress-bar/progress-bar";
import "../skeleton-segment/skeleton-segment";
import "../trip-step/trip-step";
import "../calenders-trip/calenders-trip";
import "../range-slider/range-slider";
import "../skeleton-segment/skeleton-segment";
import "../flight-search/flight-search";
import "../modal/modal";
export declare const TripSelectionTemplate: (uri_searchBox: string, mode: string, language: string, color: string, font: string, googleFontsUrl: string, _ApiKey: string, _currencySymbol: string, _convertedVND: number, _isLoading: boolean, _isShowLoading: boolean, _isError: boolean, _isMobileDevice: boolean, _isGlobal: boolean, _searchTripResponse: any[], departureItem: any, arrivalItem: any, _isShowPriceDetail: boolean, _isShowPriceDetails: boolean, InventoriesSelected: any[], _airItinerarySelected: number, inforAirports: any[], pricePaxInfor: any[], searchTripRequest: TripSelectionModelRq | null, _sumPrice: number, _progress: number, dateStart: Date, segmentsSelected: {
    segmentKeyRef: any;
    sumPrice: any;
}[] | undefined, canScrollLeft: boolean, canScrollRight: boolean, allAirlineResult: any[] | undefined, rangeSlider: [number, number] | undefined, rangeSliderLegs: [number, number] | undefined, maxCountLeg: number, showOffcanvas: boolean, isopenModalResult: boolean, showFromText: boolean, showDetailTicket: boolean, showDetailFareRule: boolean, showMoreClassic: boolean, search_redirect: string, getTotalPriceWithMode: () => string, getTotalPriceLowestWithMode: (segment: any) => string, getInventoryPriceWithMode: (inventory: any) => string, showPriceDetail: () => void, checkNextStep: () => boolean, nextStep: () => void, selectSegment: (bookGDS: string, airCodeRef: string, combine: boolean, segmentKeyRef: any, sumPrice: any) => void, selectInventory: (bookGDS: string, airCodeRef: string, combine: boolean, segmentKeyRef: any, sumPrice: any) => void, isShowDetailSegment: (segment: any) => void, showPriceDetails: (event: Event) => void, scrollInventory: (arrow: string) => void, checkBookingButton: () => boolean, nextPage: () => void, sortWithPrice: () => void, sortWithAirline: () => void, sortWithDepartureDate: () => void, currentSortOption: "price" | "airline" | "departure", filterAirlines: (event: Event, airline: string) => void, checkedAllAirlinesStatus: () => boolean, handleWeekChange: (event: CustomEvent) => void, handleRangeSliderChange: (event: CustomEvent) => void, handleRangeSliderLeg: (event: CustomEvent) => void, reSelectFlight: () => void, setShowOffcanvas: (show: boolean) => void, isCurrencyLoaded: boolean, // thêm đối số này
handleLanguageChange: (value: string) => void, showLanguageSelect: boolean, hideMultiSegmentFlights: boolean, showMultiSegmentFlights: boolean, isMultiSegmentFlight: (segment: any) => boolean, toggleMultiSegmentFlights: () => void) => import("lit-html").TemplateResult<1>;
