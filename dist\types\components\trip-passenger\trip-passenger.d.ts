import { LitElement, PropertyValues } from "lit";
import { CryptoService } from "../../services/CryptoService";
import { FlightService } from "../../services/FlightService";
export declare class TripPassenger extends LitElement {
    private _cryptoService;
    private _flightService;
    static styles: import("lit").CSSResult[];
    mode: string;
    googleFontsUrl: string;
    font: string;
    ApiKey: string;
    color: string;
    redirect_uri: string;
    uri_searchBox: string;
    autoRandomBirthday: string;
    showLanguageSelect: boolean;
    autoLanguageParam: boolean;
    private _language;
    private _hasCheckedURL;
    get language(): string;
    set language(value: string);
    get isAutoRandomBirthday(): boolean;
    get currencySymbolAv(): string;
    private _ApiKey;
    private dataCartSticket;
    private inforAirports;
    private PriceAncillaries;
    private _pricePaxInfor;
    private _passengers;
    private _phoneCodes;
    private _servicePrice;
    private _sumPrice;
    private _isLoading;
    private _isShowDetailsTrip;
    private _isGlobal;
    private _isSubmit;
    isMobile: boolean;
    isInitDatePicker: boolean;
    isInitDatePickerPS: boolean;
    private displayMode;
    private convertedVND;
    private currencySymbol;
    groupBySessionID: any[];
    private flatpickrInstances;
    private flatpickrInstancesPS;
    private inforContact;
    constructor(_cryptoService: CryptoService, _flightService: FlightService);
    connectedCallback(): void;
    private checkLanguageFromURL;
    private updateURLWithLanguage;
    protected firstUpdated(_changedProperties: PropertyValues): Promise<void>;
    protected updated(_changedProperties: PropertyValues): void;
    updatePhoneCode(event: any): void;
    getPhoneCodes(): Promise<void>;
    getInforAirports(): Promise<void>;
    PriceAncillary(): Promise<void>;
    RequestEncrypt(dataCodeRef: any[]): Promise<any>;
    CallPriceAncillary(): Promise<void>;
    reSearchTrip(): void;
    checkDevice(): void;
    initCheckGlobal(): void;
    CheckisGlobal(airport: string | undefined): boolean;
    getBaggageOfType(airline: string, type: string): any;
    getSumPrice(): number;
    getPricePax(): void;
    initPassengers(): void;
    /**
     * Formats a Date object to string in dd/MM/yyyy format
     * @param date The date to format
     * @returns Formatted date string
     */
    formatDateToString(date: Date): string;
    getRangeDatePicker(paxType: string): Date[];
    initDatePickerPassportExprid(): void;
    openDatePickerPS(index: number): void;
    initDatePicker(): void;
    openDatePicker(index: number): void;
    validateBirthday(birthday: any | undefined, type: string): boolean;
    updateBirthday(event: any, passenger: Passenger, index: number): void;
    inputDateInDatePicker(event: any): any;
    inputDateInDatePicker_MMddyyyy(event: any): any;
    getMaxDayOfMonth(month: number, year: number): number;
    updatepassportDate(event: any, passenger: Passenger, index: number): void;
    isValidDate(day: number, month: number, year: number): boolean;
    isLeapYear(year: number): boolean;
    /**
     * Generates a random date between two dates
     * @param startDate The start date of the range
     * @param endDate The end date of the range
     * @returns A random date between startDate and endDate
     */
    getRandomDate(startDate: Date, endDate: Date): Date;
    getInitialBaggages(): Baggage[];
    showDetailsTrip(): void;
    togglePassportVisibility(passenger: any): void;
    updateFullname(event: any, passenger: Passenger): void;
    updateGender(event: Event, passenger: any): void;
    updateCountry(event: any, passenger: Passenger): void;
    changeBaggage(event: any, passenger: Passenger, baggage: any): void;
    getSumServicePrice(): number;
    updateLemailMain(event: any): void;
    validateForm(): boolean;
    getFirstAndLastName(fullname: string): {
        firstName: string;
        lastName: string | undefined;
    };
    GetSegment(): any;
    RequestBookTrip(): any;
    CallGoToPayment2(): Promise<void>;
    goToPayment(): Promise<void>;
    updatePassport: (event: Event, passenger: any) => void;
    updatePhoneMain(event: any): void;
    updateEmailMain(event: any): void;
    handleLanguageChange(newLang: string): void;
    render(): import("lit-html").TemplateResult<1>;
}
