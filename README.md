# Flight Search UI Library

![npm](https://img.shields.io/npm/v/flight-search-ui) ![license](https://img.shields.io/npm/l/flight-search-ui)

Flight Search UI is a flight ticket search interface library built with **LitElement** and **TypeScript**, easily integrable into frameworks like Angular, React, Vue, or any JavaScript project.

## ✨ Features

- **Web Components Support**: Easily embeddable into any web application.
- **Performance Optimized**: Uses Rollup to minimize bundle size.
- **Flexible Customization**: Easily extend and tweak the UI.
- **SSR Friendly**: Can be pre-rendered with frameworks like Next.js.

---

## 📦 Installation

Install the library via npm:

```sh
npm install flight-search-ui
```

Or with yarn:

```sh
yarn add flight-search-ui
```

## 🔧 Usage

Import the library and use it in HTML:

```html
<script
  type="module"
  src="https://vudang2210.github.io/flight-search-lib/dist/flight-search/index.js"
></script>

<flight-search></flight-search>
```

Or in a React project:

```tsx
import "flight-search-ui/dist/flight-search/index.js";

const App = () => <flight-search></flight-search>;
```

Or in an Angular project:

```ts
constructor() {
  import('flight-search-ui').then(() => {
    console.log('LitElement components loaded');
  });
}
```

```html
<flight-search
  redirect_uri="TripSelection.html"
  color="red"
  vertical
></flight-search>
```

## 📂 Folder Structure

```
flight-search-ui/
├── dist/
│   ├── index.js
│   ├── style.css
│   ├── types/
│   │   ├──....
│   ├── flight-search/
│   │   ├── index.js
│   ├── trip-passenger/
│   │   ├── index.js
│   ├── trip-payment/
│   │   ├── index.js
│   ├── trip-repayment/
│   │   ├── index.js
│   ├── trip-result/
│   │   ├── index.js
│   ├── trip-selection/
│   │   ├── index.js
│   ├── ....
├── LICENSE
├── package.json
├── README.md
```

## ❗ Notes

- Make sure the browser supports Web Components.
- If you face import errors, check that you're using Node.js version >=16.
- When using with Angular, enable `CUSTOM_ELEMENTS_SCHEMA`.

---

## 🎨 Styling with CSS

### Load the default CSS

You can import the default styles globally:

```html
<link
  rel="stylesheet"
  href="https://vudang2210.github.io/flight-search-lib/dist/style.css"
/>
```

Or import it in your JavaScript/TypeScript file:

```ts
import "flight-search-ui/dist/style.css";
```

## Example

### X-Api-Key for development

```
domain: localhost
ApiKey=""
```

![alt text](https://vudang2210.github.io/flight-search-lib/image-9.png)

### Flight Search Box

```
<flight-search ApiKey="" redirect_uri="TripSelection.html"></flight-search>
```

<p align="center">
  <img src="https://vudang2210.github.io/flight-search-lib/image.png" alt="alt text" />
</p>

### Trip Selection

```
<trip-selection ApiKey="" redirect_uri="TripPassengers.html"></trip-selection>
```

<p align="center">
  <img src="https://vudang2210.github.io/flight-search-lib/image-1.png" alt="alt text" />
</p>

### Trip Passenger

```
<trip-passenger ApiKey="" redirect_uri="TripPayment.html"></trip-passenger>
```

<p align="center">
  <img src="https://vudang2210.github.io/flight-search-lib/image-2.png" alt="alt text" />
</p>

### Trip Payment

```
<trip-payment ApiKey="" termsUrl="terms-and-policies"></trip-payment>
```

<p align="center">
  <img src="https://vudang2210.github.io/flight-search-lib/image-3.png" alt="alt text" />
</p>

### Trip Result

```
<trip-result urlRePayment="TripRePayment.html" ApiKey=""></trip-result>
```

<p align="center">
  <img src="https://vudang2210.github.io/flight-search-lib/image-6.png" alt="alt text" />
</p>
<p align="center">
  <img src="https://vudang2210.github.io/flight-search-lib/image-7.png" alt="alt text" />
</p>

### Trip Repayment

```
<trip-repayment ApiKey="" termsUrl="terms-and-policies"></trip-repayment>
```

<p align="center">
  <img src="https://vudang2210.github.io/flight-search-lib/image-8.png" alt="alt text" />
</p>
