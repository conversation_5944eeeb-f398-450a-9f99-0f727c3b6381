/**
 * Chuyển đổi đối tượng Date thành chuỗi định dạng yyyy-MM-dd
 * @param date - Đối tượng Date cần chuyển đổi
 * @returns Chuỗi định dạng yyyy-MM-dd
 */
export declare const formatDate: (date: Date) => string;
export declare function formatDateTo_ddMMyyyy(date: Date, language: string): string | null;
export declare function getDurationByArray(legs: any[]): string;
export declare function formatDateToString(date: any, language: string): string | null;
export declare function validatePhone(phone: string): boolean;
export declare function validateEmail(email: string): boolean;
export declare function getTimeFromDateTime(dateTime: string): string;
export declare function convertDurationToHour(duration: number): string;
export declare function getDuration(leg: any): string;
export declare function formatddMMyyyy(date: string): string;
export declare function getDayInWeek(date: string): string;
export declare function getFlights(legs: any[]): string;
export declare function getDirect(legs: any[], language: string): string;
export declare function getDurarionLeg(leg: any): string;
export declare function getPassengerDescription(searchTripRequest: TripSelectionModelRq | null, paxType: string, language: string): string;
export declare function getPassengerDescriptionV2(paxType: string, ADT: number | undefined, CHD: number | undefined, INF: number | undefined, language: string): string;
export declare function getTypePassenger(passenger: Passenger, passengers: Passenger[], language: string): string;
export declare function formatNumber(value: number, convertedVND: number, language: string): string;
export declare function range(start: number, end: number): number[];
export declare function getDayOfWeek(date: Date, language: string): string;
export declare function formatDate_ddMM(date: Date, language: string): string;
export declare function formatDateS(dateString: string, language: string): string;
export declare function formatDateTypeDate(date: Date, language: string): string;
export declare function getFareType(bookingInfos: any[]): string;
export declare function debounce(func: (...args: any[]) => void, wait: number): (...args: any[]) => void;
