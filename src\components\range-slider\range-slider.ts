import { LitElement, html, css, PropertyValues, unsafeCSS } from "lit";
import { customElement, property, state } from "lit/decorators.js";
import styles from '../../styles/styles.css';

@customElement("range-slider")
export class RangeSlider extends LitElement {
    static styles = [
        unsafeCSS(styles)
    ];

    @property({ type: Number }) min = 0;
    @property({ type: Number }) max = 24;
    @property({ type: Number }) step = 1;
    @property({ type: Array }) values: [number, number] = [0, 24];
    @property({ type: Boolean }) allowSameValue = false;

    @state() private dragging: "min" | "max" | null = null;
    @state() private hoveredHandle: "min" | "max" | null = null;
    @state() private lastMouseX: number = 0;
    stepDivisions: number[] = [];


    constructor() {
        super();

        this.addEventListener("mouseup", this.handleMouseUp);
        this.addEventListener("mousemove", this.handleMouseMove);
        this.addEventListener("touchend", this.handleTouchEnd);
        this.addEventListener("touchmove", this.handleTouchMove);
        this.calculateStepDivisions();
    }


    updated(changedProperties: PropertyValues) {
        if (changedProperties.has('min') || changedProperties.has('max') || changedProperties.has('step') || changedProperties.has('values')) {
            this.calculateStepDivisions();
        }
    }


    calculateStepDivisions() {
        this.stepDivisions = [];
        for (let i = this.min; i <= this.max; i += this.step) {
            this.stepDivisions.push(i);
        }
    }

    private handleMouseDown(handle: "min" | "max", e: MouseEvent) {
        this.dragging = handle;
        this.hoveredHandle = handle;
        this.lastMouseX = e.clientX;
    }

    handleMouseMove(e: MouseEvent) {
        if (!this.dragging) return;
        const sliderRef = this.renderRoot.querySelector('.slider');
        if (!sliderRef) return;

        const rect = sliderRef.getBoundingClientRect();
        const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
        const value = Math.round((percent * (this.max - this.min) + this.min) / this.step) * this.step;

        // Auto determine which handle to drag when values are equal
        if (this.values[0] === this.values[1] && this.allowSameValue) {
            if (e.clientX < this.lastMouseX) {
                this.dragging = 'min';
            } else if (e.clientX > this.lastMouseX) {
                this.dragging = 'max';
            }
        }

        if (this.dragging === 'min') {
            if (this.allowSameValue) {
                if (value <= this.values[1]) {
                    this.values = [value, this.values[1]];
                }
            } else {
                if (value < this.values[1]) {
                    this.values = [value, this.values[1]];
                }
            }
        } else {
            if (this.allowSameValue) {
                if (value >= this.values[0]) {
                    this.values = [this.values[0], value];
                }
            } else {
                if (value > this.values[0]) {
                    this.values = [this.values[0], value];
                }
            }
        }

        this.lastMouseX = e.clientX;
        this.dispatchEvent(new CustomEvent('values-changed', { detail: this.values }));
        this.requestUpdate();
    }

    handleMouseUp() {
        this.dragging = null;
    }

    private handleMouseEnter(handle: "min" | "max") {
        this.hoveredHandle = handle;
    }

    private handleMouseLeave() {
        if (!this.dragging) {
            this.hoveredHandle = null;
        }
    }

    private handleTouchStart(handle: "min" | "max", e: TouchEvent) {
        e.preventDefault();
        this.dragging = handle;
        this.hoveredHandle = handle;
        this.lastMouseX = e.touches[0].clientX;
    }

    handleTouchMove(e: TouchEvent) {
        if (!this.dragging) return;
        e.preventDefault();
        const sliderRef = this.renderRoot.querySelector('.slider');
        if (!sliderRef) return;

        const rect = sliderRef.getBoundingClientRect();
        const percent = Math.max(0, Math.min(1, (e.touches[0].clientX - rect.left) / rect.width));
        const value = Math.round((percent * (this.max - this.min) + this.min) / this.step) * this.step;

        // Auto determine which handle to drag when values are equal
        if (this.values[0] === this.values[1] && this.allowSameValue) {
            if (e.touches[0].clientX < this.lastMouseX) {
                this.dragging = 'min';
            } else if (e.touches[0].clientX > this.lastMouseX) {
                this.dragging = 'max';
            }
        }

        if (this.dragging === 'min') {
            if (this.allowSameValue) {
                if (value <= this.values[1]) {
                    this.values = [value, this.values[1]];
                }
            } else {
                if (value < this.values[1]) {
                    this.values = [value, this.values[1]];
                }
            }
        } else {
            if (this.allowSameValue) {
                if (value >= this.values[0]) {
                    this.values = [this.values[0], value];
                }
            } else {
                if (value > this.values[0]) {
                    this.values = [this.values[0], value];
                }
            }
        }

        this.lastMouseX = e.touches[0].clientX;
        this.dispatchEvent(new CustomEvent('values-changed', { detail: this.values }));
        this.requestUpdate();
    }

    handleTouchEnd() {
        this.dragging = null;
    }

    getLeftPercent(value: number): string {
        return `${((value - this.min) / (this.max - this.min)) * 100}%`;
    }

    render() {
        return html`
<div class="relative w-[calc(100%-1.5rem)] mx-auto h-[5rem] select-none">
    <div  class="slider absolute top-1/2 left-0 right-0 h-1 bg-gray-300 rounded-full transform -translate-y-1/2">
        <div class="absolute h-full bg-nmt-600 rounded-full" style="left: ${this.getLeftPercent(this.values[0])}; right: ${100 - + this.getLeftPercent(this.values[1]).slice(0, -1)}%">
        </div>
        ${this.stepDivisions.map((step: number, i: number) => html`
         <div
            class="absolute w-0.5 h-3 even:h-6  even:bg-gray-500 bg-gray-400 transform -translate-x-1/2 translate-y-4 step-division rounded-sm"
            style="left: ${this.getLeftPercent(step)}">
        </div>
        `)}
       
        ${['min', 'max'].map((handle, i) => html`
        <div 
            class="absolute top-1/2 w-5 h-5 bg-white border-2 border-nmt-600 rounded-full shadow cursor-pointer transform -translate-x-1/2 -translate-y-1/2
            ${this.values[0] === this.values[1] ? 'z-10' : ''}
            ${this.hoveredHandle === handle ? 'ring-2 ring-nmt-400' : ''}"
            style="left: ${this.getLeftPercent(this.values[i])}"
            @mousedown=${(e: MouseEvent) => this.handleMouseDown(handle as "min" | "max", e)}
            @touchstart=${(e: TouchEvent) => this.handleTouchStart(handle as "min" | "max", e)}
            @mouseenter=${() => this.handleMouseEnter(handle as "min" | "max")}
            @mouseleave=${this.handleMouseLeave}>
            <span
                class="absolute -top-5 transform -translate-y-1/2 border border-gray-200 shadow bg-white px-1 rounded-sm">
                ${handle === 'min' ? this.values[0] : this.values[1]}
            </span>
            ${this.values[0] === this.values[1] && this.hoveredHandle === handle ? html`
                <span class="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 whitespace-nowrap">
                    ${handle === 'min' ? 'Min handle' : 'Max handle'}
                </span>
            ` : ''}
        </div>
        `)}
        
    </div>
    <div class="absolute top-full w-full left-0 right-0 -mt-1 flex justify-between text-base text-gray-500">
        <span>${this.min}</span>
        <span>${this.max}</span>
    </div>
</div>
        `;
    }
}
