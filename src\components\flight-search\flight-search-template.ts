import { html } from "lit";
import "../airports-menu/airports-menu";

export const flightSearchTemplate = (
    language: string,
    isChild: boolean,
    _isShowBox: boolean,
    _isShowBottom: boolean,
    isMobile: boolean,
    isRT: boolean,
    adult: number,
    child: number,
    infant: number,
    passengerString: string,
    AirportsDefault: Array<any>,
    departureAirport: string,
    arrivalAirport: string,
    selectedDates: Date[],
    isSubmitForm: boolean,
    vertical: boolean,
    isSetColorTitle: boolean,
    searchFlights: () => any,
    handleAirportClick: (event: CustomEvent) => void,
    toggleDropdown: (event: Event) => void,
    toggleShowBox: () => void,
    changeTypeTrip: (isRT: boolean) => void,
    changeQuantity: (event: Event, type: string, isIncrease: boolean) => void,
    swapAirport: (event: Event) => void,
    openDatePicker: () => void,
    handleLanguageChange: (value: string) => void,
    showLanguageSelect: boolean

) => {
    return html`
<div class="w-full ${vertical ? 'max-w-md' : 'max-w-7xl '} mx-auto max-md:flex flex-col max-md:justify-center">
    <div 
        class=" ${!isChild && !vertical ? 'pt-2 pb-3 px-4' : ''}  ${vertical ? 'w-full  border-0  rounded-[40px] shadow-lg backdrop-blur-sm bg-[#fcfdff] relative z-10' : ' backdrop-blur-lg rounded-lg  border-2 border-white'}  transition-all duration-700  ease-in-out ${_isShowBox ? 'h-auto ' : 'h-0 !overflow-hidden'}">
        <div class="${isChild && vertical ? 'border border-gray-100 shadow-md rounded-[40px] text-gray-600' : ''}">
            <div class="flex    ${vertical ? 'flex-col' : 'pb-2.5 flex-row justify-between items-center'} ">
                <div  class="${vertical ? 'bg-gradient-to-r text-2xl from-nmt-400 via-nmt-300 to-nmt-400 rounded-t-[40px]' : 'text-xl px-2'} ${isChild && !vertical ? 'text-gray-600' : 'text-white'}">
                    <div class="  ${vertical ? 'px-6 pt-4 pb-2' : ''}  flex flex-col justify-center text-left">
                    ${vertical ? html`
                        <div class="flex items-center  gap-3">
                            <div class="bg-white/20 backdrop-blur-sm p-2 rounded-xl">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class=" text-white h-6 w-6 rotate-45">
                                    <path
                                        d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                    </path>
                                </svg>
                            </div>
                            <h2 class="font-medium tracking-wide">${language === 'vi' ? 'Vé máy bay' : 'Flight tickets'}</h2>
                        </div>
                        <span class="text-sm text-white/90  ml-12 font-light">${language === 'vi' ? 'Tìm chuyến bay phù hợp với bạn' : 'Find a flight that suits you'}</span>
                        ` : html`
                        <div class="flex items-center  gap-3">
                            <div class="${isChild && !vertical ? 'bg-black/10' : 'bg-white/20'} backdrop-blur-sm p-2 rounded-xl">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="${isSetColorTitle ? 'text-nmt-500' : ''}  h-6 w-6 rotate-45">
                                    <path
                                        d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                    </path>
                                </svg>
                            </div>
                            <div class="flex flex-col ${isSetColorTitle ? 'text-nmt-500' : ''}"> 
                                <h2 class="font-medium tracking-wide">${language === 'vi' ? 'Vé máy bay' : 'Flight tickets'}</h2>
                                <span class="text-sm  font-light">${language === 'vi' ? 'Tìm chuyến bay phù hợp với bạn' : 'Find a flight that suits you'}</span>
                            </div>
                        </div>
                        `}
                        
                    </div>
                    ${vertical ? html`<div class=" h-10 bg-white rounded-t-[40px]"></div>` : ''}
                </div>
                <div class="${vertical ? '' : 'flex  gap-6  backdrop-blur-sm rounded-full me-2'} ${isChild && !vertical ? 'bg-black/10' : 'bg-white/20'}">
                    <!-- radio button onetrip and roundtrip -->
                    <div class="flex  ${vertical ? 'justify-start px-5 pt-5 gap-6 text-gray-600 -mt-6' : 'justify-center gap-6  py-2 px-4 '}" >
                        <div class="flex items-center">
                            <input type="radio" id="onetrip" name="trip" 
                                ?checked="${!isRT}" @change="${() => changeTypeTrip(false)}"
                                class="hover:cursor-pointer w-4 h-4 accent-nmt-600 bg-gray-100 border-nmt-300 ">
                            <label for="onetrip" class="ms-2 text-sm  font-normal cursor-pointer line-clamp-1  ${isChild || (!child && vertical) ? 'text-gray-600' : 'text-white'}">
                                ${language === 'vi' ? 'Một chiều' : 'One-way'}
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="roundtrip" name="trip"
                                ?checked="${isRT}" @change="${() => changeTypeTrip(true)}"
                                class="hover:cursor-pointer w-4 h-4 accent-nmt-600 bg-gray-100 border-nmt-300 ">
                            <label for="roundtrip" class="ms-2 text-sm font-normal  cursor-pointer line-clamp-1 ${isChild || (!child && vertical) ? 'text-gray-600' : 'text-white'}">
                                ${language === 'vi' ? 'Khứ hồi' : 'Round-trip'} 
                            </label>
                        </div>

                        <div class="flex justify-end items-center space-x-4">
                            ${showLanguageSelect ? html`
                            <select id="language" 
                                class="bg-transparent text-sm rounded  py-1 border-none focus:outline-none ${isChild || (!child && vertical) ? 'text-gray-600' : 'text-white'}"
                                .value=${language}
                                @change=${(e: Event) => handleLanguageChange((e.target as HTMLSelectElement).value)}
                            >
                                <option style="background-color: #f0f0f0; color: black;" value="en">English</option>
                                <option style="background-color: #f0f0f0; color: black;" value="vi">Tiếng Việt</option>
                            </select>
                            ` : ''}
                        </div>
                    </div>

                </div>
            </div>
            <form>
                <div class="flex  ${vertical ? 'vertical flex-col px-5 pb-7 pt-3' : 'flex-row px-2 pb-2 '}">
                    <div class="flex flex-col w-full"> 
                        <div class="grid   ${vertical ? 'grid-cols-1 gap-3' : 'grid-cols-4'}" >
                            <div class="col-span-2 grid   relative ${vertical ? 'gap-3' : 'gap-0 grid-cols-2'}">
                                <button type="button"  @click="${swapAirport}"
                                    class="group z-50 hover:bg-nmt-400 hover:shadow-xl hover:rounded-full transition ease-in-out duration-500 hover:ring-2 hover:ring-nmt-300 hover:shadow-white absolute top-1/2 ${vertical ? 'right-0' : 'left-1/2 -translate-x-1/2'}  transform  -translate-y-1/2 rounded-tl-lg rounded-br-sm rounded-tr-2xl rounded-bl-2xl px-2 py-1 bg-white border border-white shadow-[0px_0px_15px_#cccccc96,0px_0px_15px_#44444400_inset]">
                                    <svg class="w-6 h-6 text-nmt-500  group-hover:text-white  group-hover:animate-spin"
                                        aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        fill="none" viewBox="0 0 24 24">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M17.651 7.65a7.131 7.131 0 0 0-12.68 3.15M18.001 4v4h-4m-7.652 8.35a7.13 7.13 0 0 0 12.68-3.15M6 20v-4h4" />
                                    </svg>
                                </button>
                                <!-- start điểm đi -->
                                <div class="w-full dropdown relative" @click=${(event: Event) => toggleDropdown(event)}>
                                    <div class="relative h-full dropdown-toggle" >
                                        <div
                                            class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                            <svg class="w-4 h-4 text-nmt-500 " aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                                viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round"
                                                    stroke-linejoin="round" stroke-width="2"
                                                    d="M12 13a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" />
                                                <path stroke="currentColor" stroke-linecap="round"
                                                    stroke-linejoin="round" stroke-width="2"
                                                    d="M17.8 13.938h-.011a7 7 0 1 0-11.464.144h-.016l.14.171c.1.127.2.251.3.371L12 21l5.13-6.248c.194-.209.374-.429.54-.659l.13-.155Z" />
                                            </svg>
                                        </div>
                                        <input type="text" .value="${departureAirport}" readonly
                                            class=" cursor-pointer font-normal  border border-nmt-100  ${vertical ? '  rounded-2xl py-4 pe-4 bg-white/70 shadow-sm hover:shadow-md transition-all duration-300' : 'min-h-16 rounded-s-[20px] ring-2 ring-gray-100  h-full bg-gray-50 '} ps-10  focus:outline-nmt-200 focus:border  text-gray-600 text-sm  focus:ring-nmt-500 focus:border-nmt-500 block w-full  ${isSubmitForm && departureAirport.trim() === '' ? 'bg-red-50 border-red-500  text-red-900 placeholder-red-700 focus:ring-red-500  focus:border-red-500' : ''} "
                                            placeholder="${language === 'vi' ? 'Điểm đi' : 'Departure'}">
                                    </div>
                                    <div 
                                        class="dropdown-menu menu-departure z-50 absolute  left-0 inset-0 auto ml-0 mt-0  h-fit hidden  bg-white divide-y divide-gray-100 rounded-lg shadow ${vertical ? 'w-full  top-[53px]' : 'w-96 top-[64px]'}">
                                        
                                        <div class="w-full relative">
                                        ${AirportsDefault.length > 0 ? html`
                                         <airports-menu tripType="departure"  .AirportsDefault=${AirportsDefault ?? []} @airport-click="${handleAirportClick}"></airports-menu>
                                        ` : html``}
                                           
                                        </div>
                                    </div>
                                </div>
                                <!-- end điểm đi -->

                                
                                <!-- start điểm đến -->
                                 <div class="w-full dropdown relative" @click=${(event: Event) => toggleDropdown(event)}>
                                    <div class="relative h-full dropdown-toggle" >
                                        <div class="absolute inset-y-0 start-0 flex items-center ${vertical ? 'ps-3.5' : 'ps-5'}  pointer-events-none">
                                            <svg class="w-4 h-4 text-nmt-500 " aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd"
                                                        d="M11.906 1.994a8.002 8.002 0 0 1 8.09 8.421 7.996 7.996 0 0 1-1.297 3.957.996.996 0 0 1-.133.204l-.108.129c-.178.243-.37.477-.573.699l-5.112 6.224a1 1 0 0 1-1.545 0L5.982 15.26l-.002-.002a18.146 18.146 0 0 1-.309-.38l-.133-.163a.999.999 0 0 1-.13-.202 7.995 7.995 0 0 1 6.498-12.518ZM15 9.997a3 3 0 1 1-5.999 0 3 3 0 0 1 5.999 0Z"
                                                        clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <input type="text" .value="${arrivalAirport}" readonly
                                                class="cursor-pointer h-full font-normal border border-nmt-100 ${vertical ? ' rounded-2xl py-4 pe-4 bg-white/70 shadow-sm hover:shadow-md transition-all duration-300' : 'min-h-16 ring-2 ring-gray-100 bg-gray-50'} ps-10  focus:outline-nmt-200    text-gray-600 text-sm  focus:ring-nmt-500 focus:border-nmt-500 block w-full   ${isSubmitForm && arrivalAirport.trim() === '' ? 'bg-red-50 border-red-500  text-red-900 placeholder-red-700 focus:ring-red-500  focus:border-red-500' : ''}"
                                                placeholder="${language === 'vi' ? 'Điểm đến' : 'Arrival'}">
                                    </div>
                                    <div class="dropdown-menu menu-arrival z-50 absolute inset-0 auto  mt-0  left-0 ${vertical ? ' w-full top-[53px]' : ' w-96  top-[64px]'}  h-fit hidden  bg-white divide-y divide-gray-100 rounded-lg shadow">
                                        <div class="w-full relative">
                                                ${AirportsDefault.length > 0 ? html`
                                                <airports-menu tripType="arrival"  .AirportsDefault=${AirportsDefault} @airport-click="${handleAirportClick}"></airports-menu>
                                            ` : html``}
                                        </div>
                                    </div>
                                </div>
                                <!-- end điểm đến -->
                            </div>

                            <div class="col-span-2  grid ${vertical ? 'grid-cols-1 gap-3' : 'grid-cols-2'}">
                                <!-- start ngày đi/ ngày về -->
                                <div class="w-full" @click=${openDatePicker}>
                                    <div class="relative h-full">
                                        <div
                                            class="absolute z-10 inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                            <svg class="w-4 h-4 text-nmt-500 " aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                                viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round"
                                                    stroke-linejoin="round" stroke-width="2"
                                                    d="M4 10h16m-8-3V4M7 7V4m10 3V4M5 20h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Zm3-7h.01v.01H8V13Zm4 0h.01v.01H12V13Zm4 0h.01v.01H16V13Zm-8 4h.01v.01H8V17Zm4 0h.01v.01H12V17Zm4 0h.01v.01H16V17Z" />
                                            </svg>
                                        </div>

                                        <div 
                                            class="cursor-pointer relative h-full text-sm font-normal  focus:outline-nmt-200   focus:ring-nmt-500 focus:border-nmt-500 flex flex-col items-start justify-center w-full border border-nmt-100 ${!vertical ? 'py-2.5 ring-2 ring-gray-100 bg-gray-50 ' : ' rounded-2xl  py-[5.5px] bg-white/70 shadow-sm hover:shadow-md transition-all duration-300'} pe-2.5 ps-10 ${isSubmitForm && (selectedDates.length !== 2 && isRT || selectedDates.length !== 1 && !isRT) ? 'bg-red-50 border-red-500 text-red-900  focus:ring-red-500  focus:border-red-500' : ''}">
                                            <span
                                                class="absolute  ${!vertical ? 'top-2' : 'top-1'}  text-gray-400  text-nowrap">
                                                ${isRT ? (language === 'vi' ? 'Ngày đi / Ngày về' : 'Departure / Return') : (language === 'vi' ? 'Ngày đi' : 'Departure')}
                                            </span>
                                           
                                            <input  id="datePicker" type="text" 
                                                class="pt-5 cursor-pointer bg-transparent border-none text-gray-600  block w-full  focus:outline-none focus:border-none focus:shadow-none focus:ring-0 ${isSubmitForm && (selectedDates.length !== 2 && isRT || selectedDates.length !== 1 && !isRT) ? ' text-red-900 placeholder-red-700 focus:ring-red-500  ' : ''}"
                                                placeholder="${isRT ? 'dd/MM/yyyy - dd/MM/yyyy' : 'dd/MM/yyyy'}" 
                                                 />
                                        </div>

                                    </div>
                                </div>
                                <!-- end ngày đi/ ngày về -->


                                <div class="w-full dropdown relative" >
                                    <div class="relative h-full" @click=${(event: Event) => toggleDropdown(event)}>
                                        <div
                                            class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none ">
                                            <svg class="w-4 h-4 text-nmt-500 " aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                fill="currentColor" viewBox="0 0 24 24">
                                                <path fill-rule="evenodd"
                                                    d="M12 6a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7Zm-1.5 8a4 4 0 0 0-4 4 2 2 0 0 0 2 2h7a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-3Zm6.82-3.096a5.51 5.51 0 0 0-2.797-6.293 3.5 3.5 0 1 1 2.796 6.292ZM19.5 18h.5a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-1.1a5.503 5.503 0 0 1-.471.762A5.998 5.998 0 0 1 19.5 18ZM4 7.5a3.5 3.5 0 0 1 5.477-2.889 5.5 5.5 0 0 0-2.796 6.293A3.501 3.501 0 0 1 4 7.5ZM7.1 12H6a4 4 0 0 0-4 4 2 2 0 0 0 2 2h.5a5.998 5.998 0 0 1 3.071-5.238A5.505 5.505 0 0 1 7.1 12Z"
                                                    clip-rule="evenodd" />
                                            </svg>

                                        </div>
                                        <div
                                            class="h-full text-gray-400 font-normal text-sm  focus:outline-nmt-200   focus:ring-nmt-500 focus:border-nmt-500 flex flex-col items-start justify-center w-full border border-nmt-100 ${!vertical ? 'py-2.5 bg-gray-50 ring-2 ring-gray-100' : ' rounded-2xl  py-[5.5px] bg-white/70 shadow-sm hover:shadow-md transition-all duration-300'} pe-2.5 ps-10   ">
                                            <span class=" cursor-pointer line-clamp-1">
                                                ${language === 'vi' ? 'Hành khách' : 'Passengers'}
                                            </span>
                                            <input type="text" readonly value="${passengerString}"
                                                class="p-0 cursor-pointer bg-transparent border-none text-gray-600  block w-full focus:outline-none focus:border-none focus:shadow-none focus:ring-0 "
                                                >
                                        </div>
                                    </div>
                                    <div 
                                        class="z-50 dropdown-menu  absolute inset-0 auto ml-0 mt-0 translate-x-[0] ${vertical ? 'translate-y-[46.4px] w-full' : 'translate-y-[69.4px] w-72 '}   translate-z-[0px] bg-white divide-y divide-gray-100 rounded-lg shadow  ">
                                        <ul
                                            class="py-2  text-sm text-gray-700  space-y-3 bg-white rounded-lg">
                                            <li class="flex justify-between border-b border-gray-400 pb-3 px-4">
                                                <div class="flex flex-col items-start justify-start">
                                                    <strong class="whitespace-nowrap block  ">
                                                       ${language === 'vi' ? 'Người lớn' : 'Adult'}
                                                    </strong>
                                                    <span class="whitespace-nowrap text-sm text-gray-400">
                                                        ${language === 'vi' ? 'Từ 12 tuổi' : 'From 12 years old'}
                                                    </span>
                                                </div>
                                                <div>

                                                    <div class="max-w-xs mx-auto">
                                                        <div class="relative flex items-center max-w-[8rem]">
                                                            <button type="button"
                                                            @click="${(event: Event) => changeQuantity(event, 'adult', false)}"
                                                                
                                                                class="bg-gray-100  hover:bg-gray-200 border border-gray-300 rounded-s-lg p-3 h-11 focus:ring-gray-100  focus:ring-2 focus:outline-none">
                                                                <svg class="w-3 h-3 text-gray-900 "
                                                                    aria-hidden="true"
                                                                    xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                    viewBox="0 0 18 2">
                                                                    <path stroke="currentColor" stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="2"
                                                                        d="M1 1h16" />
                                                                </svg>
                                                            </button>
                                                            <input type="text" readonly
                                                                class="bg-gray-50 border-x-0 border-gray-300 h-11 text-center text-gray-900 text-sm focus:ring-nmt-500 focus:border-nmt-500 block w-full py-2.5 "
                                                                required value="${adult}" min="1" />
                                                            <button type="button"
                                                                @click="${(event: Event) => changeQuantity(event, 'adult', true)}"
                                                                class="bg-gray-100  hover:bg-gray-200 border border-gray-300 rounded-e-lg p-3 h-11 focus:ring-gray-100  focus:ring-2 focus:outline-none">
                                                                <svg class="w-3 h-3 text-gray-900 "
                                                                    aria-hidden="true"
                                                                    xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                    viewBox="0 0 18 18">
                                                                    <path stroke="currentColor" stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="2"
                                                                        d="M9 1v16M1 9h16" />
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </div>

                                                </div>
                                            </li>

                                            <li class="flex justify-between border-b border-gray-400 pb-3 px-4">
                                                <div class="flex flex-col items-start justify-start">
                                                    <strong class="whitespace-nowrap block  ">
                                                        ${language === 'vi' ? 'Trẻ em' : 'Child'}
                                                    </strong>
                                                    <span class="text-sm text-gray-400  line-clamp-1  ">
                                                        ${language === 'vi' ? 'Từ 2 - ' : 'From 2 - '}${'<'}${language === 'vi' ? '12 tuổi' : '12 years old'}
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="max-w-xs mx-auto">
                                                        <div class="relative flex items-center max-w-[8rem]">
                                                            <button type="button"
                                                            @click="${(event: Event) => changeQuantity(event, 'child', false)}"
                                                                class="bg-gray-100  hover:bg-gray-200 border border-gray-300 rounded-s-lg p-3 h-11 focus:ring-gray-100  focus:ring-2 focus:outline-none">
                                                                <svg class="w-3 h-3 text-gray-900 "
                                                                    aria-hidden="true"
                                                                    xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                    viewBox="0 0 18 2">
                                                                    <path stroke="currentColor" stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="2"
                                                                        d="M1 1h16" />
                                                                </svg>
                                                            </button>
                                                            <input type="text" readonly value="${child}"
                                                                class="bg-gray-50 border-x-0  border-gray-300 h-11 w-[52px] text-center text-gray-900 text-sm focus:ring-nmt-500 focus:border-nmt-500 block  py-2.5 "
                                                                required  min="0" />
                                                            <button type="button"
                                                            @click="${(event: Event) => changeQuantity(event, 'child', true)}"
                                                                class="bg-gray-100  hover:bg-gray-200 border border-gray-300 rounded-e-lg p-3 h-11  focus:ring-gray-100  focus:ring-2 focus:outline-none">
                                                                <svg class="w-3 h-3 text-gray-900 "
                                                                    aria-hidden="true"
                                                                    xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                    viewBox="0 0 18 18">
                                                                    <path stroke="currentColor" stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="2"
                                                                        d="M9 1v16M1 9h16" />
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </div>

                                                </div>
                                            </li>

                                            <li class="flex justify-between px-4">
                                                <div class="flex flex-col items-start justify-start">
                                                    <strong class="whitespace-nowrap block ">
                                                        ${language === 'vi' ? 'Em bé' : 'Infant'}
                                                    </strong>
                                                    <span class="whitespace-nowrap text-sm text-gray-400">
                                                       ${language === 'vi' ? 'Dưới 2 tuổi' : 'Under 2 years old'}
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="max-w-xs mx-auto">
                                                        <div class="relative flex items-center max-w-[8rem]">
                                                            <button type="button"
                                                            @click="${(event: Event) => changeQuantity(event, 'infant', false)}"
                                                                class="bg-gray-100  hover:bg-gray-200 border border-gray-300 rounded-s-lg p-3 h-11 focus:ring-gray-100  focus:ring-2 focus:outline-none">
                                                                <svg class="w-3 h-3 text-gray-900"
                                                                    aria-hidden="true"
                                                                    xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                    viewBox="0 0 18 2">
                                                                    <path stroke="currentColor" stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="2"
                                                                        d="M1 1h16" />
                                                                </svg>
                                                            </button>
                                                            <input type="text" readonly value="${infant}"
                                                                class="bg-gray-50 border-x-0 border-gray-300 h-11 text-center text-gray-900 text-sm focus:ring-nmt-500 focus:border-nmt-500 block w-full py-2.5 "
                                                                required  min="0" />
                                                            <button type="button"
                                                            @click="${(event: Event) => changeQuantity(event, 'infant', true)}"
                                                                class="bg-gray-100  hover:bg-gray-200 border border-gray-300 rounded-e-lg p-3 h-11 focus:ring-gray-100  focus:ring-2 focus:outline-none">
                                                                <svg class="w-3 h-3 text-gray-900 "
                                                                    aria-hidden="true"
                                                                    xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                    viewBox="0 0 18 18">
                                                                    <path stroke="currentColor" stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="2"
                                                                        d="M9 1v16M1 9h16" />
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </div>

                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>
                    <div class="${vertical ? 'flex justify-center items-center mt-5' : ''}">
                        <button type="button" @click=${searchFlights}
                            class=" px-6 text-sm italic  font-medium  from-nmt-300  to-nmt-300 via-nmt-400 hover:via-nmt-500 hover:from-nmt-300  hover:to-nmt-300 transition-all duration-300 ${vertical ? 'bg-gradient-to-r  inline-flex items-center justify-center gap-2 whitespace-nowrap  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2  h-10 px-4 w-full py-6 text-base font-medium rounded-2xl  shadow-md hover:shadow-lg' : 'bg-gradient-to-tr hover:opacity-90  rounded-e-[20px] ring-2 ring-gray-100 h-full   hover:bg-nmt-600'}  text-white   focus:ring-4 focus:outline-none focus:ring-nmt-300  flex items-center justify-center">
                            
                            ${vertical ? html`<span >${language === 'vi' ? 'Tìm chuyến bay' : 'Search flights'}</span>` : html`
                            <ng-template >
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-search w-5 h-5 mr-2">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="m21 21-4.3-4.3"></path>
                                </svg>
                            </ng-template>`}
                        </button>
                    </div>

                </div>
            </form>
            
        </div>
    </div>
    ${_isShowBottom ? html`
    <div class="flex items-center justify-between w-full bg-white py-2 md:hidden">
        <div class="line-clamp-1 text-gray-800  font-semibold ps-2">
        ${isMobile && departureAirport && arrivalAirport ? html`${departureAirport} - ${arrivalAirport}` : ''}
        </div>
        <button @click=${toggleShowBox}
            class="h-auto w-auto border text-nowrap border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br  from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300  rounded-lg text-sm px-6 py-3 text-center">
            ${_isShowBox ? (language === 'vi' ? 'Hủy thay đổi' : 'Cancel changes') : (language === 'vi' ? 'Thay đổi tìm giá' : 'Change search')}
        </button>
    </div>
    `: ''}
</div>
`;
}