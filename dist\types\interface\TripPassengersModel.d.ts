interface Baggage {
    airline: string;
    type: string;
    SsrCode: string;
    Price: number;
    CurrencyCode: string;
    DepartureCode: string;
    ArrivalCode: string;
    WeightBag: number;
    DepartureDate: string;
}
interface Passenger {
    type: string;
    gender: string;
    fullname: string;
    birthday: any;
    birthdaytString: string;
    country: string;
    passport: string;
    passportDate: any;
    passportDateString: string;
    baggages: Baggage[];
    isShowNS: boolean;
    isShowPassport: boolean;
}
interface InforContact {
    phoneMain: string;
    AreaCodePhoneMain: string;
    emailMain: string;
    LemailMain: string;
    phoneOther: string;
    AreaCodePhoneOther: string;
    emailOther: string;
    LemailOther: string;
    note: string;
}
