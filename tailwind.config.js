/** @type {import('tailwindcss').Config} */

module.exports = {
  darkMode: 'class',
  content: ["./src/**/*.{html,js,ts,jsx,tsx}"],
  important: true,
  theme: {
    extend: {
      margin: {
        '60px': '60px',
        '70px': '70px',
        '17rem': '17rem',
        '3.125rem': '3.125rem',
      },
      translate: {
        '4.25': '17rem',
      },
      height: {
        'fill-available': '-webkit-fill-available',
      },
    },
  },
  theme: {
    extend: {
      // colors: {
      //   nmt: {"50":"#eff6ff","100":"#dbeafe","200":"#bfdbfe","300":"#93c5fd","400":"#60a5fa","500":"#3b82f6","600":"#2563eb","700":"#1d4ed8","800":"#1e40af","900":"#1e3a8a","950":"#172554"}
      // }
      colors: {
        nmt: {
          "50": "var(--color-nmt-50, #b3b7c1)",    // Default light orange
          "100": "var(--color-nmt-100, #b3b7c1)",   // Default light orange
          "200": "var(--color-nmt-200, #b3b7c1)",   // Default pale orange
          "300": "var(--color-nmt-300, #b3b7c1)",   // Default soft orange
          "400": "var(--color-nmt-400, #b3b7c1)",   // Default medium orange
          "500": "var(--color-nmt-500, #b3b7c1)",   // Default orange
          "600": "var(--color-nmt-600, #b3b7c1)",   // Default slightly dark orange
          "700": "var(--color-nmt-700, #b3b7c1)",   // Default dark orange
          "800": "var(--color-nmt-800, #b3b7c1)",   // Default deep dark orange
          "900": "var(--color-nmt-900, #b3b7c1)",   // Default very deep orange
          "950": "var(--color-nmt-950, #b3b7c1)"    // Default almost black with orange tint
        }
      }
    },
    fontFamily: {
      'body': [
        'Inter',
        'ui-sans-serif',
        'system-ui',
        '-apple-system',
        'system-ui',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        'Noto Sans',
        'sans-serif',
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji'
      ],
      'sans': [
        'Inter',
        'ui-sans-serif',
        'system-ui',
        '-apple-system',
        'system-ui',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        'Noto Sans',
        'sans-serif',
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji'
      ]
    },

  },
  plugins: [
    function ({ addBase, addComponents }) {
      addBase({
        ':host': {
          display: 'block',
          contain: 'content',
        },
        ':host *': {
          boxSizing: 'border-box',
        },
        '::slotted(*)': {
          all: 'initial',
        }
      });
      // Add Tailwind classes to Shadow DOM
      addComponents({
        ':host': {
          '--tw-ring-offset-shadow': '0 0 #0000',
          '--tw-ring-shadow': '0 0 #0000',
          '--tw-shadow': '0 0 #0000',
          '--tw-shadow-colored': '0 0 #0000',
        }
      });
    }
  ]
}

