import { css, LitElement, PropertyValues, unsafeCSS } from "lit";
import { CryptoService } from "../../services/CryptoService";
import { FlightService } from "../../services/FlightService";
import { TripResultTemplate } from "./trip-result-template";
import { customElement, property, state } from "lit/decorators.js";
import { getAirportInfoByCode } from "../../services/WorldServices";
import { setnmtColors } from "../../services/ColorService";
import styles from '../../styles/styles.css';

const cryptoService = new CryptoService();
const flightService = new FlightService();

@customElement("trip-result")
export class TripResult extends LitElement {
    static styles = [
        unsafeCSS(styles),
        css`
        :host {
          font-family: var(--nmt-font, 'Roboto', sans-serif);
        }`
    ];
    @property({ type: <PERSON>olean }) autoFillOrderCode = false;
    @property({ type: String }) mode = "online";
    @property({ type: String }) googleFontsUrl = "";
    @property({ type: String }) font = "";
    @property({ type: String }) urlRePayment: string = 'TripRePayment';
    @property({ type: String }) ApiKey = '';
    @property({ type: String }) color = "";
    @property({ type: String }) uri_searchBox = "";
    @property({ type: Boolean }) showLanguageSelect = false;
    @property({ type: Boolean }) autoLanguageParam = false; // Bật/tắt tính năng tự động thêm language param

    private _language = "vi";
    private _hasCheckedURL = false; // Flag để tránh kiểm tra URL nhiều lần

    @property({ type: String })
    get language(): string {
        return this._language;
    }

    set language(value: string) {
        const oldValue = this._language;

        // Chỉ kiểm tra URL nếu autoLanguageParam được bật
        if (this.autoLanguageParam) {
            const urlParams = new URLSearchParams(window.location.search);
            const languageParam = urlParams.get('language');

            if (languageParam && languageParam !== this._language) {
                // URL có language parameter - luôn ưu tiên URL
                this._language = languageParam;
                console.log('Language overridden from URL parameter:', this._language);
            } else {
                // URL không có language parameter - sử dụng giá trị được set
                this._language = value;
                console.log('Language set from property:', this._language);
                // Tự động thêm vào URL nếu chưa có
                if (!this._hasCheckedURL) {
                    this.updateURLWithLanguage();
                    this._hasCheckedURL = true;
                }
            }
        } else {
            // Không sử dụng URL parameter, chỉ set giá trị trực tiếp
            this._language = value;
            console.log('Language set from property (autoLanguageParam disabled):', this._language);
        }

        this.requestUpdate('language', oldValue);
    }

    get currencySymbolAv(): string{
        return this.convertedVND === 1 || this.language === 'vi'  ? '₫' : this.currencySymbol;
    }

    @state() private _ApiKey: string = '';
    @state() private _isLoading: boolean = false;
    @state() private _isNotValid: boolean = false;
    @state() private _orderAvailable: any = null;
    @state() private _orderDetails: any = null;
    @state() private _inforAirports: any[] = [];
    @state() private _PaymentNote: any = null;
    @state() private _NoteModel: any = null;
    @state() private displayMode: 'total' | 'perPassenger' = 'total';
    @state() private convertedVND: number = 1;
    @state() private currencySymbol: string = '₫';

    @state() private request: any = {
        OrderCode: '',
        PhoneCustomer: '',
        EmailCustomer: ''
    };

    constructor(
        private _cryptoService: CryptoService,
        private _flightService: FlightService
    ) {
        super();

        this._cryptoService = cryptoService;
        this._flightService = flightService;
    }
    connectedCallback(): void {
        super.connectedCallback();
        this._ApiKey = this.ApiKey;
        this.removeAttribute("ApiKey");

        // Kiểm tra URL ngay khi component được kết nối, trường hợp không có attribute language
        this.checkLanguageFromURL();
    }

    private checkLanguageFromURL(): void {
        // Chỉ kiểm tra URL nếu autoLanguageParam được bật
        if (!this.autoLanguageParam) {
            console.log('autoLanguageParam disabled, skipping URL check');
            return;
        }

        const urlParams = new URLSearchParams(window.location.search);
        const languageParam = urlParams.get('language');

        if (languageParam) {
            // URL có language parameter - set giá trị từ URL
            this._language = languageParam;
            console.log('Language initialized from URL parameter:', this._language);
            this.requestUpdate('language');
        } else if (!this._hasCheckedURL) {
            // URL không có language parameter - tự động thêm vào URL với giá trị mặc định
            this.updateURLWithLanguage();
            this._hasCheckedURL = true;
        }
    }
    private updateURLWithLanguage(): void {
        const currentUrl = new URL(window.location.href);
        const params = new URLSearchParams(currentUrl.search);

        // Thêm hoặc cập nhật parameter language
        params.set('language', this._language);

        // Cập nhật URL mà không reload trang
        const newUrl = `${currentUrl.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newUrl);
        console.log('URL updated with language parameter:', newUrl);
    }

    protected async firstUpdated(_changedProperties: PropertyValues): Promise<void> {
        super.firstUpdated(_changedProperties);

        await this.getRequest();

        if (this.color !== "") {
            setnmtColors(this.color);
            this.requestUpdate();
        }

        console.log(this.googleFontsUrl);
        // Handle Google Fonts
        if (this.googleFontsUrl) {
            const googleFontsLink = document.createElement('link');
            googleFontsLink.rel = 'stylesheet';
            googleFontsLink.href = this.googleFontsUrl;
            document.head.appendChild(googleFontsLink);
        } else {
            // Default font if no Google Fonts URL provided
            const googleFontsLink = document.createElement('link');
            googleFontsLink.rel = 'stylesheet';
            googleFontsLink.href = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap';
            document.head.appendChild(googleFontsLink);
        }

        console.log('font', this.font);
        if (this.font !== "") {
            const root = document.documentElement;
            root.style.setProperty('--nmt-font', this.font);
        }
    }

    protected updated(_changedProperties: PropertyValues): void {
        super.updated(_changedProperties);
    }

    async getRequest() {
        var params = new URLSearchParams(window.location.search);
        this.request = {
            OrderCode: params.get('OrderCode') || '',
            PhoneCustomer: params.get('PhoneCustomer') || '',
            EmailCustomer: params.get('EmailCustomer') || ''
        };
        if (this.request.OrderCode && this.request.PhoneCustomer && this.request.EmailCustomer) {
            await this.AvailableTrip(this.request);
        }
    }
    async AvailableTrip(request: any) {
        if (!this._cryptoService.ch()) {
            await this._cryptoService.spu();
        }
        this.CallAvailableTrip(request);
    }
    async RequestEncrypt(data: any): Promise<any> {
        const encryptedData = await this._cryptoService.eda(JSON.stringify(data));
        return {
            EncryptData: encryptedData
        };
    }
    formatPassenger() {
        var indexInfant = 0;
        this._orderDetails?.paxList.forEach((pax: any, index: number) => {
            if (pax.type == 'infant') {
                //get pax adult index same index infant
                var paxAdult = this._orderDetails.paxList.find((pax: any) => pax.type == 'adult' && pax.index == indexInfant);
                if (paxAdult) {
                    paxAdult.withInfant = pax;
                    //remove pax infant
                    this._orderDetails.paxList.splice(index, 1);
                }
                indexInfant++;
            } else {
                pax.index = index;
            }
        });
    }
    async CallAvailableTrip(request: any) {
        this._isLoading = true;

        var payloadsEncrypted = await this.RequestEncrypt(request);

        try {
            const res = await this._flightService.AvailableTrip(payloadsEncrypted, this._ApiKey);
            const resDecrypted = await this._cryptoService.dda(res.resultObj);
            var resJson = JSON.parse(resDecrypted);
            if (resJson.IsSuccessed) {
                var noteData = JSON.parse(resJson.ResultObj.Note);
                this._orderDetails = noteData;
                this._isNotValid = true;
                this._orderAvailable = resJson.ResultObj;
                this._PaymentNote = JSON.parse(resJson.ResultObj.PaymentNote);
                this._NoteModel = JSON.parse(resJson.ResultObj.NoteResult);
                console.log(this._PaymentNote);
                this.formatPassenger();
                await this.getInforAirports();
            }
        } catch (error: any) {
            if (error.status === 403) {
                this._cryptoService.ra();
                await this._cryptoService.spu();
                await this.CallAvailableTrip(request);
            }
        } finally {
            this._isLoading = false;
        }
    }
    async getInforAirports() {
        var airportsCode: string[] = [];
        this._orderDetails.full?.InventoriesSelected.forEach((inventory: any) => {
            inventory.segment.Legs.forEach((leg: any) => {
                if (!airportsCode.includes(leg.DepartureCode)) {
                    airportsCode.push(leg.DepartureCode);
                }
                if (!airportsCode.includes(leg.ArrivalCode)) {
                    airportsCode.push(leg.ArrivalCode);
                }
            }
            )
        });
        try {
            var res = await getAirportInfoByCode(airportsCode, this.language || 'vi', this._ApiKey);
            if (res.isSuccessed) {
                this._inforAirports = res.resultObj;
                this.displayMode = res.feature.displayMode || 'total';
                const currencyObj = typeof res.feature.currency === 'string' ? JSON.parse(res.feature.currency) : res.feature.currency;
                this.currencySymbol = currencyObj.symbol || '₫';
                this.convertedVND = currencyObj.convertedVND || 1;
            }
            console.log('mode', this.mode);
            if (this.mode === "online") {
                if (res.feature?.color) {
                    this.color = res.feature.color;
                    if (this.color !== "") {
                        setnmtColors(this.color);
                        this.requestUpdate();
                    }
                }
            }
        } catch (error: any) {
            console.error(error);
        }
    }
    rePayment() {
        const baseUrl = window.location.origin; // Lấy domain hiện tại
        const params = new URLSearchParams({
            OrderCode: this.request.OrderCode,
            PhoneCustomer: this.request.PhoneCustomer,
            EmailCustomer: this.request.EmailCustomer
        });

        // Chỉ thêm language parameter nếu autoLanguageParam được bật
        if (this.autoLanguageParam) {
            params.append('language', this.language);
        }

        window.location.href = `${baseUrl}/${this.urlRePayment}?${params.toString()}`;
    }

    handleLanguageChange(newLang: string) {
        this.language = newLang;
        this.getInforAirports();

        // Tự động cập nhật URL với language mới
        this.updateURLWithLanguage();

        this.requestUpdate();
    }

    render() {
        return TripResultTemplate(
            this.autoFillOrderCode,
            this.uri_searchBox,
            this.language,
            this._isLoading,
            this._isNotValid,
            this._orderAvailable,
            this._orderDetails,
            this._inforAirports,
            this._PaymentNote,
            this._NoteModel,
            this.currencySymbolAv,
            this.convertedVND,
            this.rePayment.bind(this),
            this.handleLanguageChange.bind(this),
            this.showLanguageSelect // truyền thuộc tính mới
        );
    }
}

