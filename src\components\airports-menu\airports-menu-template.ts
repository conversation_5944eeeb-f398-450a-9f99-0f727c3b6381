import { html } from "lit";

export const airportsMenuTemplate = (
    AirportsDefault: any[], // Danh sách sân bay mặc định
    AirportsDefaultFiltered: any[], // Danh sách sân bay được lọc
    searchTerm: string, // Từ khóa tìm kiếm
    language: string,
    searchAirPorts: (event: Event) => void, // Hàm tìm kiếm sân bay
    continentClick: (continentCode: string) => void, // Hàm xử lý click vào châu lục
    itemParentClick: (code: string) => void, // Hàm xử lý click vào item cha
    airportClick: (airport: any) => void // Hàm xử lý click vào sân bay
) =>

    html`
    <div class="w-full relative text-gray-800">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-search absolute left-2 top-2.5 h-4 w-4 ">
                                                <circle cx="11" cy="11" r="8"></circle>
                                                <path d="m21 21-4.3-4.3"></path>
                                            </svg>
                                            <input @input=${searchAirPorts} type="text" value=${searchTerm}
                                                class="flex h-10 w-full rounded-md border border-input bg-background ps-10 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder: focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pl-8"
                                                placeholder=${language === 'vi' ? 'Tìm kiếm quốc gia hoặc tỉnh thành' : 'Search for country or province'}>
                                        </div>
    <div class="w-full flex  text-gray-800">
        ${AirportsDefaultFiltered === null || AirportsDefaultFiltered.length === 0 ? html`
            <div class="border-r border-gray-100">
                ${AirportsDefault.map(continent => html`
                    <div  @click=${() => continentClick(continent.continentCode)}
                        class="p-2 border-b cursor-pointer ${continent.selected ? 'bg-nmt-500 text-white' : 'bg-transparent'}">
                        <h3 class="text-sm font-semibold text-nowrap">${continent.continentName}</h3>
                    </div>              
                `)}
            </div>
            
    <div class="w-full">
        <div class="w-full overflow-y-scroll max-h-80 min-h-80 space-y-1 dark:bg-gray-700">
            ${AirportsDefault.map(
        (continent) =>
            continent.selected
                ? html`
                        ${continent.airports.map(
                    (airport: any, index: number) => html`
                               
                                    <div @click=${() => airportClick(airport)}
                                     class="flex px-2 justify-between border-b border-gray-100 items-start overflow-hidden gap-1 whitespace-nowrap rounded-md text-sm cursor-pointer hover:bg-nmt-400 hover:text-white group font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 w-full max-md:justify-between">
                                        <div class="flex flex-col items-start w-full">
                                            <span class="text-wrap text-left">${airport.cityName}</span>
                                            <span class="text-xs font-light text-wrap text-left">${airport.name}</span>
                                        </div>
                                        <span class="text-nmt-600 font-extrabold group-hover:text-white">${airport.code}</span>
                                    </div>`
                )}` : ""
    )}        
        </div>
    </div>
        ` : html`
            ${AirportsDefaultFiltered.length === 0 ? html`
                <div class="p-2 w-full text-center text-gray-500">
                    ${language === 'vi' ? 'Không tìm thấy sân bay mà quý khách đã nhập' : 'No airports found matching your search'}
                </div>
            ` : html`
                <div class="p-2 w-full overflow-y-scroll max-h-80">
                    ${AirportsDefaultFiltered.map(item => html`
                        ${item.isParent ? html`
                            <div @click=${() => itemParentClick(item.code)}
                             class="w-full">
                                <div class="inline-flex min-h-fit justify-between py-2 border-b border-gray-100 items-start  overflow-hidden gap-1 whitespace-nowrap rounded-md text-sm cursor-pointer hover:bg-nmt-400  hover:text-white group font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50   w-full max-md:justify-between ">
                                   <div class="flex flex-col items-start">
                                   <span class="max-w-48 max-md:min-w-36 text-wrap text-left">${item.name}</span>
                                   <span class="text-xs font-light">${language === 'vi' ? 'Tất cả sân bay tại ' : 'All airports in '}${item.name}</span>
                                </div>
                                <span>
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="w-4 h-4 text-gray-600 dark:text-white group-hover:text-white ${item.selected ? 'rotate-180' : ''}"
                                        viewBox="0 0 320 512">
                                        <path
                                            d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z" />
                                    </svg>
                                </span>
                            </div>
                                ${item.selected ? html`
                                    <div class="w-full bg-gray-100 px-2">
                                        ${item.child.map((airport: any) => html`
                                            <div @click=${() => airportClick(airport)} class="flex px-2 justify-between border-b border-gray-100 items-start  overflow-hidden gap-1 whitespace-nowrap rounded-md text-sm cursor-pointer hover:bg-nmt-400  hover:text-white group font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50   w-full max-md:justify-between ">
                                                            <div class="flex flex-col items-start w-full">
                                                                <span class="text-wrap text-left">${airport.cityName}</span>
                                                                <span class="text-xs font-light text-wrap text-left">${airport.name}</span>
                                                            </div>
                                                            <span
                                                                class="text-nmt-600 font-extrabold group-hover:text-white">${airport.code}</span>
                                                        </div>
                                        `)}
                                    </div>
                                ` : ""}
                            </div>
                        ` : html`
                            <div @click=${() => airportClick(item)} class="flex px-2 justify-between border-b border-gray-100 items-start  overflow-hidden gap-1 whitespace-nowrap rounded-md text-sm cursor-pointer hover:bg-nmt-400  hover:text-white group font-medium  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50   w-full max-md:justify-between ">
                                                            <div class="flex flex-col items-start w-full">
                                                                <span class="text-wrap text-left">${item.cityName}</span>
                                                                <span class="text-xs font-light text-wrap  text-left">${item.name}</span>
                                                            </div>
                                                            <span class="text-nmt-600 font-extrabold group-hover:text-white">${item.code}</span>
                                                        </div>
                        `}
                    `)}
                </div>
            `}
        `}
    </div>
`;