
export function getGroupCodeRefSelected(searchTripResponseMain: any, InventoriesSelected: any): any {
    if (!Array.isArray(InventoriesSelected) || InventoriesSelected.length === 0) {
        return null;
    }

    var result = {
        sessionID: '',
        groupCodeRef: '',
        listCode: [] as { codeRef: string }[]
    };

    var isCombine = InventoriesSelected[0].combine;
    if (isCombine) {

        var groupCodeEnd = getGroupCodeRefFromCodeRef(searchTripResponseMain, InventoriesSelected[1].inventorySelected.CodeRef);
        result.groupCodeRef = groupCodeEnd;
        result.sessionID = InventoriesSelected[1].segment.KeyRef.split('_')[0];

        //get key advantage segment for find segment start of combine in air end
        const { key, sumprice } = createKeySegment(InventoriesSelected[0].segment);

        // Search air by airCodeRef
        var airEnd = searchTripResponseMain.find((response: any) => {
            return response.SearchTripAirs.find((air: any) => {
                return air.AirCodeRef === InventoriesSelected[1].airCodeRef;
            });
        })?.SearchTripAirs.find((air: any) => {
            return air.AirCodeRef === InventoriesSelected[1].airCodeRef;
        });

        // Get segment start of combine in air end
        var segmentStart = airEnd?.AirItinerary[0]?.AirSegments.find((segment: any) => {
            const { key: keySegment } = createKeySegment(segment);
            return keySegment === key;
        });

        result.listCode.push({ codeRef: segmentStart?.LowestInventory.CodeRef });
        result.listCode.push({ codeRef: InventoriesSelected[1].segment.LowestInventory.CodeRef });
    } else {
        var listSessionID = InventoriesSelected.map((item: any) => item.segment.KeyRef.split('_')[0]);
        var allSameSessionID = listSessionID.every((val: any, i: number, arr: any[]) => val === arr[0]);

        if (allSameSessionID) {
            result.sessionID = listSessionID[0];
            result.listCode = InventoriesSelected.map((air: any) => ({ codeRef: air.inventorySelected.CodeRef }));
            return result;
        } else {
            return listSessionID.map((sessionID: string, index: number) => ({
                sessionID: sessionID,
                groupCodeRef: '',
                listCode: [{ codeRef: InventoriesSelected[index].inventorySelected.CodeRef }]
            }));
        }
    }

    return result;
}

export async function filterTimeDepartureDateInRange(hourStart: number, hourEnd: number, allAirlineResult: any[], searchTripResponseMain: any[], airItinerarySelected: number): Promise<any> {
    var airlinesChecked = allAirlineResult.filter((item: any) => item.checked).map((item: any) => item.airlines);

    // Sao chép để không ảnh hưởng dữ liệu gốc
    var searchTripResponse = JSON.parse(JSON.stringify(searchTripResponseMain));

    // Lọc các segment theo khoảng thời gian và hãng hàng không
    searchTripResponse.forEach((response: any) => {
        response.SearchTripAirs = response.SearchTripAirs.filter((air: any) => {
            // Lọc các segment theo khoảng thời gian
            air.AirItinerary[airItinerarySelected].AirSegments = air.AirItinerary[airItinerarySelected].AirSegments.filter((segment: any) => {
                const departureDate = new Date(segment.DepartureDate);
                const hour = departureDate.getHours();
                return hour >= hourStart && hour <= hourEnd && (airlinesChecked.length > 0 ? airlinesChecked.includes(segment.Airlines) : true);
            });
            // Chỉ giữ lại các airItinerary có airSegments sau khi lọc
            return air.AirItinerary[airItinerarySelected].AirSegments.length > 0;
        });
    });
    searchTripResponse = combineGroupsStart(searchTripResponse);
    return searchTripResponse;
}

export async function filterLegsInRange(minLegs: number, maxLegs: number, allAirlineResult: any[], searchTripResponseMain: any[], airItinerarySelected: number): Promise<any> {
    var airlinesChecked = allAirlineResult.filter((item: any) => item.checked).map((item: any) => item.airlines);

    // Sao chép để không ảnh hưởng dữ liệu gốc
    var searchTripResponse = JSON.parse(JSON.stringify(searchTripResponseMain));

    // Lọc các segment theo số chặng và hãng hàng không
    searchTripResponse.forEach((response: any) => {
        response.SearchTripAirs = response.SearchTripAirs.filter((air: any) => {
            // Lọc các segment theo số chặng
            air.AirItinerary[airItinerarySelected].AirSegments = air.AirItinerary[airItinerarySelected].AirSegments.filter((segment: any) => {
                const legCount = segment.Legs?.length || 0;
                return legCount >= minLegs && legCount <= maxLegs && (airlinesChecked.length > 0 ? airlinesChecked.includes(segment.Airlines) : true);
            });
            // Chỉ giữ lại các airItinerary có airSegments sau khi lọc
            return air.AirItinerary[airItinerarySelected].AirSegments.length > 0;
        });
    });
    searchTripResponse = combineGroupsStart(searchTripResponse);
    return searchTripResponse;
}

export function sortWithPrice(searchTripResponse: any): any {
    searchTripResponse = searchTripResponse.map((response: any) => {
        return {
            ...response,
            SearchTripAirs: response.SearchTripAirs.map((air: any) => {
                return {
                    ...air,
                    AirItinerary: air.AirItinerary.map((itinerary: any) => {
                        return {
                            ...itinerary,
                            AirSegments: itinerary.AirSegments.sort((a: any, b: any) => {
                                const priceA = parseFloat(a.LowestInventory.SumPrice);
                                const priceB = parseFloat(b.LowestInventory.SumPrice);
                                if (isNaN(priceA) || isNaN(priceB)) {
                                    return 0;
                                }
                                return priceA - priceB;
                            })
                        };
                    })
                };
            })
        };
    });
    return searchTripResponse;
}

export function sortWithAirline(searchTripResponse: any): any {
    searchTripResponse = searchTripResponse.map((response: any) => {
        return {
            ...response,
            SearchTripAirs: response.SearchTripAirs.map((air: any) => {
                return {
                    ...air,
                    AirItinerary: air.AirItinerary.map((itinerary: any) => {
                        return {
                            ...itinerary,
                            AirSegments: itinerary.AirSegments.sort((a: any, b: any) => {
                                if (a.Airlines < b.Airlines) {
                                    return -1;
                                }
                                if (a.Airlines > b.Airlines) {
                                    return 1;
                                }
                                return 0;
                            })
                        };
                    })
                };
            })
        };
    });
    return searchTripResponse;
}

export function sortWithDepartureDate(searchTripResponse: any): any {
    searchTripResponse = searchTripResponse.map((response: any) => {
        return {
            ...response,
            SearchTripAirs: response.SearchTripAirs.map((air: any) => {
                return {
                    ...air,
                    AirItinerary: air.AirItinerary.map((itinerary: any) => {
                        return {
                            ...itinerary,
                            AirSegments: itinerary.AirSegments.sort((a: any, b: any) => {
                                const dateA = new Date(a.DepartureDate);
                                const dateB = new Date(b.DepartureDate);
                                return dateA.getTime() - dateB.getTime();
                            })
                        };
                    })
                };
            })
        };
    });
    return searchTripResponse;
}

export function filterSearchTripResponseByAirline(searchTripResponseMain: any[], airline: string, bookGDS: string, airCodeRef: string, sumPrice: number): any {
    var searchTripResponse = searchTripResponseMain
        .map((response: any) => ({
            ...response,
            SearchTripAirs: response.SearchTripAirs.filter((air: any) =>
                air.Airlines === airline &&
                air.BookGDS === bookGDS &&
                air.AirCodeRef === airCodeRef
            )
        }))
        .filter((response: any) => response.SearchTripAirs.length > 0)
        .map((response: any) => ({
            ...response,
            SearchTripAirs: response.SearchTripAirs.map((air: any) => ({
                ...air,
                AirItinerary: air.AirItinerary.map((itinerary: any) => ({
                    ...itinerary,
                    AirSegments: itinerary.AirSegments.map((segment: any) => ({
                        ...segment,
                        Inventories: segment.Inventories.filter((inventory: any) =>
                            inventory.SumPrice === sumPrice
                        )
                    })).filter((segment: any) => segment.Inventories.length > 0)
                }))
            })).filter((air: any) => air.AirItinerary.some((itinerary: any) => itinerary.AirSegments.length > 0))
        })).filter((response: any) => response.SearchTripAirs.length > 0);

    searchTripResponse = combineGroupsStart(searchTripResponse);
    return searchTripResponse;
}

export function filterSearchTripResponseByCombine(searchTripResponseMain: any[], combine: boolean): any {
    var searchTripResponse = searchTripResponseMain.map((response: any) => {
        return {
            ...response,
            SearchTripAirs: response.SearchTripAirs.filter((air: any) => air.Combine === combine)
        };
    }).filter((response: any) => response.SearchTripAirs.length > 0);

    searchTripResponse = combineGroupsStart(searchTripResponse);
    return searchTripResponse;
}

export function getGroupCodeRefFromCodeRef(searchTripResponseMain: any[], codeRef: string): string {
    let groupCodeRef = "";
    let groupPrice = 0;
    let dataGroupInventory: any[] = [];

    // Tìm kiếm inventory có codeRef khớp và lấy groupPrice và dataGroupInventory
    searchTripResponseMain.some((response: any) => {
        return response.SearchTripAirs.some((air: any) => {
            return air.AirItinerary.some((itinerary: any) => {
                return itinerary.AirSegments.some((segment: any) => {
                    return segment.Inventories.some((inventory: any) => {
                        if (inventory.CodeRef === codeRef) {
                            groupPrice = inventory.SumPrice;
                            dataGroupInventory = air.Inventories;
                            return true; // Dừng tìm kiếm khi tìm thấy
                        }
                        return false;
                    });
                });
            });
        });
    });

    // Tìm kiếm inventory có groupPrice khớp và lấy groupCodeRef
    if (dataGroupInventory.length > 0) {
        const matchingInventory = dataGroupInventory.find((inventory: any) => inventory.SumPrice === groupPrice);
        if (matchingInventory) {
            groupCodeRef = matchingInventory.CodeRef;
        }
    }

    return groupCodeRef;
}

export function combineGroupsStart(searchTripResponse: any): any {
    const groupedSegments: {
        key: string;
        sumprice: number;
    }[] = [];

    // Create a deep copy of searchTripResponse to avoid modifying the original data
    const tempSearchTripResponse = JSON.parse(JSON.stringify(searchTripResponse));

    // Lấy danh sách keySegment từ chiều đi với giá thấp nhất
    tempSearchTripResponse.forEach((response: any) => {
        response.SearchTripAirs.forEach((air: any) => {
            air.AirItinerary[0]?.AirSegments?.forEach((segment: any) => {
                if (segment) {
                    const { key, sumprice } = createKeySegment(segment);
                    var foundKeyGroup = groupedSegments.find((item) => item.key === key);
                    if (!foundKeyGroup) {
                        groupedSegments.push({ key, sumprice });
                    } else if (foundKeyGroup && foundKeyGroup.sumprice > sumprice) {
                        foundKeyGroup.sumprice = sumprice;
                    }
                }
            });
        });
    });

    // Xóa chiều đi của các chuyến bay trùng key nhưng giá khác với giá key
    tempSearchTripResponse.forEach((response: any) => {
        response.SearchTripAirs.forEach((air: any) => {
            if (air.AirItinerary[0] && air.AirItinerary[0].AirSegments) {
                air.AirItinerary[0].AirSegments = air.AirItinerary[0].AirSegments.filter((segment: any) => {
                    const { key, sumprice } = createKeySegment(segment);
                    var foundKeyGroup = groupedSegments.find((item) => item.key === key);
                    return foundKeyGroup && foundKeyGroup.sumprice === sumprice;
                });
            }
        });
    });
    return tempSearchTripResponse;
}

export function combineGroupsEnd(searchTripResponse: any, segment: any, combine: boolean): any {
    const segmentKey = createKeySegment(segment).key;
    if (segmentKey === "") {
        return searchTripResponse;
    }

    // Create a deep copy of searchTripResponse to avoid modifying the original data
    const tempSearchTripResponse = JSON.parse(JSON.stringify(searchTripResponse));

    tempSearchTripResponse?.forEach((response: any) => {
        response.SearchTripAirs = response.SearchTripAirs?.filter((air: any) => air.Combine === combine)
            .map((air: any) => {
                air.AirItinerary[0].AirSegments = air.AirItinerary[0]?.AirSegments?.filter((segment: any) => {
                    const { key } = createKeySegment(segment);
                    return key === segmentKey;
                });

                // Nếu không còn segment nào, xóa luôn `AirItinerary`
                if (!air.AirItinerary[0]?.AirSegments?.length) {
                    air.AirItinerary = [];
                }
                return air;
            });
    });
    combineGroupsEndPlus(tempSearchTripResponse, combine);
    return tempSearchTripResponse;
}

function combineGroupsEndPlus(searchTripResponse: any, combine: boolean): any {
    const groupedSegments: { key: string; sumprice: number }[] = [];

    // Lấy danh sách keySegment từ chiều về với giá thấp nhất
    searchTripResponse.forEach((response: any) => {
        response.SearchTripAirs.filter((air: any) => air.Combine === combine).forEach((air: any) => {
            air.AirItinerary[1]?.AirSegments?.forEach((segment: any) => {
                if (segment) {
                    const { key, sumprice } = createKeySegment(segment);
                    const foundKeyGroup = groupedSegments.find((item) => item.key === key);
                    if (!foundKeyGroup) {
                        groupedSegments.push({ key, sumprice });
                    } else if (foundKeyGroup.sumprice > sumprice) {
                        foundKeyGroup.sumprice = sumprice;
                    }
                }
            });
        });
    });

    // Xóa chiều về của các chuyến bay trùng key nhưng giá khác với giá key
    searchTripResponse.forEach((response: any) => {
        response.SearchTripAirs.filter((air: any) => air.Combine === combine).forEach((air: any) => {
            if (air.AirItinerary[1] && air.AirItinerary[1].AirSegments) {
                air.AirItinerary[1].AirSegments = air.AirItinerary[1].AirSegments.filter((segment: any) => {
                    const { key, sumprice } = createKeySegment(segment);
                    const foundKeyGroup = groupedSegments.find((item) => item.key === key);
                    return foundKeyGroup && foundKeyGroup.sumprice === sumprice;
                });
            }
        });
    });

    return searchTripResponse;
}

function listFlightToString(listFlightNumber: string[]): string {
    return listFlightNumber.join('_');
}

function createKeySegment(segment: any): any {
    if (!segment)
        return null;
    return {
        key: `${segment?.Airlines}_${listFlightToString(segment?.ListFlightNumber)}_${segment?.DepartureDate}`,
        sumprice: segment?.Inventories[0]?.SumPrice
    };
}

