import { LitElement, PropertyValues } from "lit";
import { CryptoService } from "../../services/CryptoService";
import { FlightService } from "../../services/FlightService";
export declare class TripPayment extends LitElement {
    private _cryptoService;
    private _flightService;
    static styles: import("lit").CSSResult[];
    mode: string;
    font: string;
    googleFontsUrl: string;
    ApiKey: string;
    color: string;
    termsUrl: string;
    uri_searchBox: string;
    autoFillOrderCode: boolean;
    showLanguageSelect: boolean;
    autoLanguageParam: boolean;
    private _language;
    private _hasCheckedURL;
    get language(): string;
    set language(value: string);
    get currencySymbolAv(): string;
    private _ApiKey;
    private _isLoading;
    private _agree;
    private _isSubmit;
    private dataCartSticket;
    private _isShowDetailsTrip;
    private _inforAirports;
    private _pricePaxInfor;
    private _phoneCodes;
    private _servicePrice;
    private _sumPrice;
    private _paymentMethod;
    private titleModal;
    private isCountDown;
    private countdown;
    private isShowModal;
    private agent;
    private displayMode;
    private convertedVND;
    private currencySymbol;
    private banks;
    private bankNote;
    private transferContent;
    private cashInfo;
    constructor(_cryptoService: CryptoService, _flightService: FlightService);
    connectedCallback(): void;
    private checkLanguageFromURL;
    private updateURLWithLanguage;
    protected firstUpdated(_changedProperties: PropertyValues): Promise<void>;
    protected updated(_changedProperties: PropertyValues): void;
    loadPaymentValue(): void;
    getSumServicePrice(): number;
    getInforAirports(): Promise<void>;
    getSumPrice(): number;
    getPricePax(): void;
    showDetailsTrip(): void;
    setPaymentMethod(method: string): void;
    selectBank(bank: any): void;
    setAgree(agree: boolean): void;
    RequestEncrypt(data: any): Promise<any>;
    reSearchTrip(): void;
    openModal(title?: string, content?: string, isCountDown?: boolean): void;
    CallRequestTrip(): Promise<void>;
    onPayment(): Promise<void>;
    handleLanguageChange(newLang: string): void;
    render(): import("lit-html").TemplateResult<1>;
}
