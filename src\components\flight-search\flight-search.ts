import { css, LitElement, PropertyValues, unsafeCSS } from "lit";
import { customElement, property, state } from "lit/decorators.js";
import { flightSearchTemplate } from "./flight-search-template";
import { getAirportsDefault } from "../../services/WorldServices";
import { LunarService } from "../../services/LunarService";
import flatpickr from "flatpickr";
import { formatDate } from "../../utils/dateUtils";
import { localStorageService } from "../../services/LocalStorageService";
import { setnmtColors } from "../../services/ColorService";
import styles from '../../styles/styles.css';



@customElement("flight-search")
export class FlightSearch extends LitElement {
  static styles = [
    unsafeCSS(styles),
    css`
    :host {
      contain: none !important;
      overflow: visible !important;
      font-family: var(--nmt-font, 'Roboto', sans-serif);
    }
    `
  ];
  // use can set color same: orange, blue, green, red, yellow, purple, pink, gray, ...
  // or set color hex code: #ff0000, #00ff00, #0000ff, #ffff00, #ff00ff, #00ffff, #808080, ...
  @property({ type: String }) mode = "online";
  @property({ type: String }) ApiKey = '';
  @property({ type: String }) color = "";
  @property({ type: String }) font = "";
  @property({ type: String }) googleFontsUrl = "";
  @property({ type: Boolean }) vertical = false;
  @property({ type: Boolean }) isChild = false;
  @property({ type: Boolean }) isSetColorTitle = false;

  @property({ type: String }) redirect_uri = "TripSelection";
  @property({ type: Boolean }) _isShowBox = true;
  @property({ type: Boolean }) autoLanguageParam = false; // Bật/tắt tính năng tự động thêm language param

  private _language = "vi";
  private _hasCheckedURL = false; // Flag để tránh kiểm tra URL nhiều lần

  @property({ type: String })
  get language(): string {
    return this._language;
  }

  set language(value: string) {
    const oldValue = this.autoLanguageParam;
    console.log("set language", value);

    // Chỉ kiểm tra URL nếu autoLanguageParam được bật
    if (this.autoLanguageParam) {
      const urlParams = new URLSearchParams(window.location.search);
      const languageParam = urlParams.get('language');

      if (languageParam && languageParam !== this._language) {
        // URL có language parameter - luôn ưu tiên URL
        this._language = languageParam;
        console.log('Language overridden from URL parameter:', this._language);
        this.initDatePicker();
      } else {
        // URL không có language parameter - sử dụng giá trị được set
        this._language = value;
        this.initDatePicker();
        console.log('Language set from property:', this._language);
        // Tự động thêm vào URL nếu chưa có
        if (!this._hasCheckedURL) {
          this.updateURLWithLanguage();
          this._hasCheckedURL = true;
        }
      }
    } else {
      // Không sử dụng URL parameter, chỉ set giá trị trực tiếp
      this._language = value;
      this.initDatePicker();
      console.log('Language set from property (autoLanguageParam disabled):', this._language);
    }

    this.requestUpdate('language', oldValue);
  }
  @property({ type: Boolean }) _isShowBottom = false;
  @property({ type: Boolean }) showLanguageSelect = false;
  @state() private _ApiKey: string = '';
  @state() isMobile: boolean = false;
  @state() departure = "";
  @state() arrival = "";
  @state() AirportsDefault = [];
  @state() isReady = true;

  @state() departureAirport: string = "";
  @state() arrivalAirport: string = "";
  @state() isRT: boolean = false;
  @state() adult: number = 1;
  @state() child: number = 0;
  @state() infant: number = 0;
  @state() passengerString: string = '';
  @state() selectedDates: Date[] = [];
  @state() isSubmitForm: boolean = false;
  @state() _vertical: boolean = false;

  AirportListSelected: any[] = [];
  departureCode = "";
  arrivalCode = "";
  private _userToggledBox = false;
  private datePickerInstance: flatpickr.Instance | null = null;

  constructor() {
    super();
    this.departure = "";
    this.arrival = "";
    this.isRT = false;
  }

  connectedCallback(): void {
    super.connectedCallback();
    this._ApiKey = this.ApiKey;
    this.removeAttribute("ApiKey");

    // Kiểm tra URL ngay khi component được kết nối, trường hợp không có attribute language
    this.checkLanguageFromURL();
  }

  private checkLanguageFromURL(): void {
    // Chỉ kiểm tra URL nếu autoLanguageParam được bật
    if (!this.autoLanguageParam) {
      console.log('autoLanguageParam disabled, skipping URL check');
      return;
    }

    const urlParams = new URLSearchParams(window.location.search);
    const languageParam = urlParams.get('language');

    if (languageParam) {
      // URL có language parameter - set giá trị từ URL
      this._language = languageParam;
      console.log('Language initialized from URL parameter:', this._language);
      this.requestUpdate('language');
    } else if (!this._hasCheckedURL) {
      // URL không có language parameter - tự động thêm vào URL với giá trị mặc định
      this.updateURLWithLanguage();
      this._hasCheckedURL = true;
    }
  }



  private updateURLWithLanguage(): void {
    const currentUrl = new URL(window.location.href);
    const params = new URLSearchParams(currentUrl.search);

    // Thêm hoặc cập nhật parameter language
    params.set('language', this._language);

    // Cập nhật URL mà không reload trang
    const newUrl = `${currentUrl.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
    console.log('URL updated with language parameter:', newUrl);
  }


  getQueryDefault() {
    var searchFlight = localStorageService.getItem('searchFlight', 'searchFlight');
    if (searchFlight) {
      searchFlight = JSON.parse(searchFlight);
      const params = new URLSearchParams(searchFlight.params);
      this.departureCode = params.get('departure') || '';
      this.arrivalCode = params.get('arrival') || '';
      this.adult = parseInt(params.get('Adult') || '1');
      this.child = parseInt(params.get('Child') || '0');
      this.infant = parseInt(params.get('Infant') || '0');
      this.passengerString = this.getValueDisplayQuantity();
      const dateStart = params.get('dateStart');
      const dateEnd = params.get('dateEnd');
      var dateArr: Date[] = [];
      if (dateStart) {
        dateArr.push(new Date(dateStart));
      }
      if (dateEnd) {
        dateArr.push(new Date(dateEnd));
      }
      this.selectedDates = dateArr;
      var departureAirport = searchFlight.airports.find((x: any) => x.type === 'departure');
      var arrivalAirport = searchFlight.airports.find((x: any) => x.type === 'arrival');

      this.departureAirport = `${departureAirport.airport.cityName} (${departureAirport.airport.code})`;
      this.arrivalAirport = `${arrivalAirport.airport.cityName} (${arrivalAirport.airport.code})`;
      this.AirportListSelected = searchFlight.airports;
      this.isRT = dateEnd ? true : false;
      this.requestUpdate();
      console.log("selectedDates", this.selectedDates);
    }
  }

  async getAirports() {
    const result = await getAirportsDefault(this.language, this._ApiKey);
    this.AirportsDefault = result.resultObj;
    this.AirportsDefault.forEach((continent: any) => {
      if (continent.continentCode === 'VN') {
        continent.selected = true;
        continent.airports.sort((a: any, b: any) => b.regionCode.localeCompare(a.regionCode));
      }
    });
    if (result.feature?.color) {
      if (this.mode == 'online') {
        this.color = result.feature.color;
      }
    }
    if (!this.isChild) {
      if (result.feature?.IsBoxVertical && this.mode == 'online') {
        this._vertical = result.feature.IsBoxVertical === "True" ? true : false;
        this.vertical = this._vertical;
      }
    } else {
      this._vertical = false;
      this.vertical = false;
    }

  }

  async firstUpdated() {
    if (this._isShowBottom) {
      this._isShowBox = false;
    }
    this.passengerString = this.getValueDisplayQuantity();

    await this.getAirports();
    this.isReady = true;
    if (this.color !== "") {
      setnmtColors(this.color);
      this.requestUpdate();
    }
    console.log(this.googleFontsUrl);
    // Handle Google Fonts
    if (this.googleFontsUrl) {
      const googleFontsLink = document.createElement('link');
      googleFontsLink.rel = 'stylesheet';
      googleFontsLink.href = this.googleFontsUrl;
      document.head.appendChild(googleFontsLink);
    } else {
      // Default font if no Google Fonts URL provided
      const googleFontsLink = document.createElement('link');
      googleFontsLink.rel = 'stylesheet';
      googleFontsLink.href = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap';
      document.head.appendChild(googleFontsLink);
    }

    console.log('font', this.font);
    if (this.font !== "") {
      const root = document.documentElement;
      root.style.setProperty('--nmt-font', this.font);
    }
    this.checkDevice();
    window.addEventListener("resize", () => this.checkDevice());

    this.renderRoot.addEventListener("click", (event) => {
      this.renderRoot.querySelectorAll(".dropdown").forEach((dropdown) => {
        if (!dropdown.contains(event.target as Node)) {
          dropdown.classList.remove("open");
        }
      });
    });

    // Wait for the next render cycle to ensure DOM is ready
    await this.updateComplete;

    this.getQueryDefault();
    this.initDatePicker();
    this.passengerString = this.getValueDisplayQuantity();
  }

  protected updated(_changedProperties: PropertyValues): void {
    super.updated(_changedProperties);
    this.checkDevice();

    // Only initialize date picker if isRT has changed
    if (_changedProperties.has('isRT') || _changedProperties.has('isMobile')) {
      this.initDatePicker();
    }
  }

  openDatePicker() {
    if (this.datePickerInstance) {
      this.datePickerInstance.open();
    }
  }

  initDatePicker() {
    console.log("initDatePicker");
    const datePicker = this.renderRoot?.querySelector("#datePicker") as HTMLInputElement;
    if (!datePicker) {
      console.log("Date picker element not found");
      return;
    }

    // Destroy instance nếu đã có
    if (this.datePickerInstance) {
      console.log("Destroying existing date picker instance");
      this.datePickerInstance.destroy();
    }

    console.log("Initializing date picker with isRT:", this.isRT);

    const config = {
      dateFormat: this.language === 'vi' ? "d/m/Y" : "d M, Y",
      disableMobile: true,
      mode: this.isRT ? "range" : "single",
      minDate: new Date(),
      maxDate: new Date().setFullYear(new Date().getFullYear() + 2),
      showMonths: this.isMobile ? 1 : 2,
      defaultDate: this.selectedDates,
      locale: {
        weekdays: {
          shorthand: this.language === 'vi' ? ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'] : ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],
          longhand: this.language === 'vi' ? ['Chủ Nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'] : ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
        },
        months: {
          shorthand: this.language === 'vi' ? ['Th1', 'Th2', 'Th3', 'Th4', 'Th5', 'Th6', 'Th7', 'Th8', 'Th9', 'Th10', 'Th11', 'Th12'] : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          longhand: this.language === 'vi' ? ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'] : ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
        },
        rangeSeparator: ' - '
      },
      onChange: (selectedDates: Date[]) => {
        console.log("onChange triggered - isRT:", this.isRT, "selectedDates:", selectedDates.length);
        this.selectedDates = selectedDates;
      },

      onReady: (selectedDates: Date[], dateStr: string, instance: flatpickr.Instance) => {
        console.log("Date picker ready");
        this.hidePrevMonthButton(instance);
      },
      onMonthChange: (selectedDates: Date[], dateStr: string, instance: flatpickr.Instance) => {
        this.hidePrevMonthButton(instance);
      },
      onDayCreate: (dObj: Date, dStr: string, fp: flatpickr.Instance, dayElem: HTMLElement & { dateObj: Date }) => {
        const date = new Date(dayElem.dateObj);
        const lunarDay = this.convertToLunar(date);
        dayElem.innerHTML = `
              <div class="relative">
                  <span>${date.getDate()}</span>
                  <span class="absolute text-[10px] text-gray-300 -top-3 right-0">${lunarDay}</span>
              </div>`;
      }
    };

    try {
      this.datePickerInstance = flatpickr(datePicker, config as any);
      console.log("Date picker initialized successfully");
    } catch (error) {
      console.error("Error initializing date picker:", error);
    }
  }

  hidePrevMonthButton(instance: any) {
    const prevMonthButton = instance.calendarContainer.querySelector(".flatpickr-prev-month");
    if (prevMonthButton) {
      const currentMonth = instance.currentMonth;
      const currentYear = instance.currentYear;
      const minDate = instance.config.minDate;
      if (minDate && (currentYear < minDate.getFullYear() || (currentYear === minDate.getFullYear() && currentMonth <= minDate.getMonth()))) {
        prevMonthButton.style.display = "none";
      } else {
        prevMonthButton.style.display = "";
      }
    }
  }


  checkDevice() {
    const width = window.innerWidth;
    if (width <= 768) {
      this._vertical = true;
    } else {
      this._vertical = this.vertical;
    }

    this.isMobile = width <= 768;
    if (this._isShowBottom && !this._userToggledBox) {
      this._isShowBox = width > 768;
    }
  }

  convertToLunar(date: any): string {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const lunarDate: Date = LunarService.convertToLunar(year, month, day);
    if (lunarDate) {
      return lunarDate.getDate() === 1 ? `${lunarDate.getDate()}/${lunarDate.getMonth() + 1}` : `${lunarDate.getDate()}`;
    } else {
      throw new Error('Không thể chuyển đổi ngày dương lịch sang âm lịch.');
    }
  }

  handleAirportClick(event: CustomEvent) {
    this.clickAirportItem(event.detail.airport, event.detail.tripType);

    this.renderRoot.querySelectorAll(".dropdown").forEach((dropdown) => {
      dropdown.classList.remove("open");
    });
  }
  toggleDropdown(event: Event) {
    event.stopPropagation();
    const currentDropdown = (event.target as HTMLElement).closest(".dropdown");

    this.renderRoot.querySelectorAll(".dropdown").forEach((dropdown) => {
      if (dropdown !== currentDropdown) {
        dropdown.classList.remove("open");
      }
    });
    var dropdownMenu = currentDropdown?.querySelector('.dropdown-menu');
    if (dropdownMenu) {
      if (!dropdownMenu?.contains(event.target as Node)) {
        currentDropdown?.classList.toggle("open");
      }
    }
  }
  private toggleShowBox() {
    this._isShowBox = !this._isShowBox;
    this._userToggledBox = true;
  }
  clickAirportItem(airport: any, type: any) {
    if (type === 'departure') {
      //update value for input name departure
      this.departureAirport = `${airport.cityName} (${airport.code})`;
      this.departureCode = airport.code;
    }
    if (type === 'arrival') {
      this.arrivalAirport = `${airport.cityName} (${airport.code})`;
      this.arrivalCode = airport.code;
    }

    //save airport to list
    if (this.AirportListSelected.length === 0) {
      this.AirportListSelected.push(
        {
          type: type,
          airport: airport
        }
      );
    }
    else {
      var index = this.AirportListSelected.findIndex(x => x.type === type);
      if (index === -1) {
        this.AirportListSelected.push(
          {
            type: type,
            airport: airport
          }
        );
      }
      else {
        this.AirportListSelected[index].airport = airport;
      }
    }
    this.requestUpdate();
  }
  changeTypeTrip(isRT: boolean) {
    this.isRT = isRT;
    if (!isRT && this.selectedDates.length === 2) {
      this.selectedDates = [this.selectedDates[0]];
    }
    this.initDatePicker();
    this.requestUpdate();
  }

  searchFlights() {
    this.isSubmitForm = true;
    if (!this.validateForm()) {
      return;
    }
    const params = new URLSearchParams({
      departure: this.departureCode,
      arrival: this.arrivalCode,
      dateStart: formatDate(this.selectedDates[0]),
      Adult: this.adult.toString(),
      Child: this.child.toString(),
      Infant: this.infant.toString()
    });

    // Chỉ thêm language parameter nếu autoLanguageParam được bật
    if (this.autoLanguageParam) {
      params.append('language', this.language);
    }

    if (this.isRT) {
      params.append('dateEnd', formatDate(this.selectedDates[1]));
    }
    const searchFlight =
    {
      params: params.toString(),
      airports: this.AirportListSelected
    };
    localStorageService.setItem('searchFlight', JSON.stringify(searchFlight), 'searchFlight');
    window.location.href = `${this.redirect_uri}?${params.toString()}`;
  }

  validateForm() {
    if (this.departureCode === '' || this.arrivalCode === '' || (this.selectedDates.length !== 1 && !this.isRT) || (this.selectedDates.length !== 2 && this.isRT)) {
      return false;
    }
    return true;
  }

  changeQuantity(event: Event, type: string, isIncrease: boolean) {
    event.preventDefault();
    if ((type === 'adult' || type === 'child') && this.adult + this.child === 9 && isIncrease) {
      return;
    } else if (type === 'infant' && this.infant === this.adult && isIncrease) {
      return;
    }

    if (type === 'adult') {
      if (!isIncrease && this.adult === 1) {
        return;
      }
      this.adult = isIncrease ? this.adult + 1 : this.adult - 1;
      this.infant > this.adult ? this.infant = this.adult : this.infant;
    }
    if (type === 'child') {
      if (!isIncrease && this.child === 0) {
        return;
      }
      this.child = isIncrease ? this.child + 1 : this.child - 1;
    }
    if (type === 'infant') {
      if (!isIncrease && this.infant === 0) {
        return;
      }
      this.infant = isIncrease ? this.infant + 1 : this.infant - 1;
    }

    this.passengerString = this.getValueDisplayQuantity();
    this.requestUpdate();
  }

  getValueDisplayQuantity(): string {
    var result = '';
    const adult = this.language === 'vi' ? 'người lớn' : 'Adult';
    const child = this.language === 'vi' ? 'trẻ em' : 'Child';
    const infant = this.language === 'vi' ? 'em bé' : 'Infant';
    if (this.adult > 0) {
      result += `${this.adult} ${adult}`;
    }
    if (this.child > 0) {
      result += result.length > 0 ? `, ${this.child} ${child}` : `${this.child} ${child}`;
    }
    if (this.infant > 0) {
      result += result.length > 0 ? `, ${this.infant} ${infant}` : `${this.infant} ${infant}`;
    }

    return result;
  }

  swapAirport(event: Event) {
    event.preventDefault();
    const temp = this.departureAirport;
    this.departureAirport = this.arrivalAirport;
    this.arrivalAirport = temp;

    const tempCode = this.departureCode;
    this.departureCode = this.arrivalCode;
    this.arrivalCode = tempCode;
  }

  render() {
    if (this.isReady) {
      return flightSearchTemplate(
        this.language,
        this.isChild,
        this._isShowBox,
        this._isShowBottom,
        this.isMobile,
        this.isRT,
        this.adult,
        this.child,
        this.infant,
        this.passengerString,
        this.AirportsDefault,
        this.departureAirport,
        this.arrivalAirport,
        this.selectedDates,
        this.isSubmitForm,
        this._vertical,
        this.isSetColorTitle,
        this.searchFlights.bind(this),
        this.handleAirportClick.bind(this),
        this.toggleDropdown.bind(this),
        this.toggleShowBox.bind(this),
        this.changeTypeTrip.bind(this),
        this.changeQuantity.bind(this),
        this.swapAirport.bind(this),
        this.openDatePicker.bind(this),
        this.handleLanguageChange.bind(this),
        this.showLanguageSelect // truyền thuộc tính mới
      );
    }

  }

  handleLanguageChange(newLang: string) {
    this.language = newLang;
    this.passengerString = this.getValueDisplayQuantity();
    this.getAirports(); // load lại sân bay nếu cần
    this.initDatePicker(); // cập nhật lại datepicker

    // Tự động cập nhật URL với language mới
    this.updateURLWithLanguage();

    this.requestUpdate();
  }
}


