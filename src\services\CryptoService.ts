import { environment } from "../environments/environment";
import { fetchWithDeviceId } from "../utils/deviceUtils";
import FingerprintJS from '@fingerprintjs/fingerprintjs';

const apiUrl = environment.apiUrl;
const publicKey = atob(environment.publicKey);


export class CryptoService {
    private keyPair: CryptoKeyPair | null = null;
    encryptionKeyPair: any = null;

    constructor() { }


    async gra(): Promise<{ publicKey: string, privateKey: string }> {
        this.keyPair = await crypto.subtle.generateKey(
            {
                name: "RSA-OAEP",
                modulusLength: 2048,
                publicExponent: new Uint8Array([1, 0, 1]),
                hash: "SHA-256"
            },
            true,
            ["encrypt", "decrypt"]
        );

        const publicKey = await crypto.subtle.exportKey("spki", this.keyPair.publicKey);
        const privateKey = await crypto.subtle.exportKey("pkcs8", this.keyPair.privateKey);

        return {
            publicKey: this.arrayBufferToPEM(publicKey, "PUBL<PERSON> KEY"),
            privateKey: this.arrayBufferToPEM(privateKey, "PRIVATE KEY")
        };
    }


    async ga(): Promise<CryptoKey> {
        return await crypto.subtle.generateKey(
            { name: 'AES-GCM', length: 256 },
            true,
            ['encrypt', 'decrypt']
        );
    }

    async ea(aesKey: CryptoKey, data: string): Promise<{ encryptedData: ArrayBuffer, iv: Uint8Array }> {
        const encoder = new TextEncoder();
        const dataBuffer = encoder.encode(data);
        const iv = crypto.getRandomValues(new Uint8Array(12));

        const encryptedData = await crypto.subtle.encrypt(
            { name: 'AES-GCM', iv },
            aesKey,
            dataBuffer
        );

        return { encryptedData, iv };
    }

    async irpu(pem: string): Promise<CryptoKey> {
        const binaryDer = this.pemToArrayBuffer(pem);
        return await crypto.subtle.importKey(
            'spki',
            binaryDer,
            { name: 'RSA-OAEP', hash: 'SHA-256' },
            true,
            ['encrypt']
        );
    }

    async irpr(pem: string): Promise<CryptoKey> {
        const binaryDer = this.pemToArrayBuffer(pem);
        return await crypto.subtle.importKey(
            'pkcs8',
            binaryDer,
            { name: 'RSA-OAEP', hash: 'SHA-256' },
            true,
            ['decrypt']
        );
    }

    async era(publicKey: CryptoKey, aesKey: CryptoKey): Promise<ArrayBuffer> {
        const exportedAESKey = await crypto.subtle.exportKey('raw', aesKey);
        return await crypto.subtle.encrypt({ name: 'RSA-OAEP' }, publicKey, exportedAESKey);
    }

    async dra(privateKey: CryptoKey, encryptedData: any): Promise<ArrayBuffer> {
        return await crypto.subtle.decrypt({ name: 'RSA-OAEP' }, privateKey, encryptedData);
    }

    async he(publicKeyPem: string, data: string): Promise<string> {
        const aesKey = await this.ga();
        const { encryptedData, iv } = await this.ea(aesKey, data);

        const publicKey = await this.irpu(publicKeyPem);
        const encryptedAESKey = await this.era(publicKey, aesKey);

        const combinedData = new Uint8Array(encryptedAESKey.byteLength + iv.byteLength + encryptedData.byteLength);
        combinedData.set(new Uint8Array(encryptedAESKey), 0);
        combinedData.set(iv, encryptedAESKey.byteLength);
        combinedData.set(new Uint8Array(encryptedData), encryptedAESKey.byteLength + iv.byteLength);

        return btoa(String.fromCharCode(...combinedData));
    }

    async hd(privateKeyBem: string, encryptedText: string): Promise<string> {
        try {
            const combinedData = Uint8Array.from(atob(encryptedText), c => c.charCodeAt(0));

            const encryptedAesKey = combinedData.slice(0, 256);
            const encryptedData = combinedData.slice(256, combinedData.length);

            const privateKey = await this.irpr(privateKeyBem);
            const aesKeyBuffer = await this.dra(privateKey, encryptedAesKey);

            const decryptedData = await this.da(aesKeyBuffer, encryptedData);
            return decryptedData;

        } catch (error) {
            console.error("Error during decryption:", error);
            throw new Error("Decryption failed");
        }
    }

    bts(buffer: ArrayBuffer): string {
        return btoa(String.fromCharCode(...new Uint8Array(buffer)));
    }

    async da(aesKeyBuffer: ArrayBuffer, encryptedData: Uint8Array): Promise<string> {
        try {
            const aesKey = await crypto.subtle.importKey(
                "raw",
                aesKeyBuffer,
                { name: "AES-GCM" },
                false,
                ["decrypt"]
            );

            const iv = encryptedData.slice(0, 12);
            const tag = encryptedData.slice(12, 28);
            const cipherText = encryptedData.slice(28);

            const encryptedBuffer = new Uint8Array([...cipherText, ...tag]);

            const decryptedBuffer = await crypto.subtle.decrypt(
                { name: "AES-GCM", iv: iv },
                aesKey,
                encryptedBuffer
            );

            return new TextDecoder().decode(decryptedBuffer);
        } catch (error) {
            throw new Error("AES-GCM Decryption failed");
        }
    }

    async encrypt(publicKey: string, plainText: string): Promise<string> {
        const key = await this.importPublicKey(publicKey);
        const encryptedData = await crypto.subtle.encrypt(
            {
                name: "RSA-OAEP"
            },
            key,
            new TextEncoder().encode(plainText)
        );

        return this.arrayBufferToBase64(encryptedData);
    }

    async decrypt(privateKey: string, encryptedText: string): Promise<string> {
        const key = await this.importPrivateKey(privateKey);
        const decryptedData = await crypto.subtle.decrypt(
            {
                name: "RSA-OAEP"
            },
            key,
            this.base64ToArrayBuffer(encryptedText)
        );

        return new TextDecoder().decode(decryptedData);
    }

    private async importPublicKey(pem: string): Promise<CryptoKey> {
        return crypto.subtle.importKey(
            "spki",
            this.pemToArrayBuffer(pem),
            {
                name: "RSA-OAEP",
                hash: "SHA-256"
            },
            true,
            ["encrypt"]
        );
    }

    private async importPrivateKey(pem: string): Promise<CryptoKey> {
        return crypto.subtle.importKey(
            "pkcs8",
            this.pemToArrayBuffer(pem),
            {
                name: "RSA-OAEP",
                hash: "SHA-256"
            },
            true,
            ["decrypt"]
        );
    }

    private arrayBufferToPEM(buffer: ArrayBuffer, type: string): string {
        const base64 = this.arrayBufferToBase64(buffer);
        const pem = `-----BEGIN ${type}-----\n${base64.match(/.{1,64}/g)?.join('\n')}\n-----END ${type}-----`;
        return pem;
    }

    private arrayBufferToBase64(buffer: ArrayBuffer): string {
        let binary = '';
        const bytes = new Uint8Array(buffer);
        const len = bytes.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return window.btoa(binary);
    }

    private base64ToArrayBuffer(base64: string): ArrayBuffer {
        const binaryString = window.atob(base64);
        const len = binaryString.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes.buffer;
    }

    private pemToArrayBuffer(pem: string): ArrayBuffer {
        const base64 = pem.replace(/-----BEGIN [A-Z ]+-----|-----END [A-Z ]+-----|\n/g, '');
        return this.base64ToArrayBuffer(base64);
    }

    async gr(): Promise<{ ep: string, sp: string, ss: string, s: string }> {
        this.encryptionKeyPair = await this.gra();
        const signingKeyPair = await crypto.subtle.generateKey(
            { name: "RSASSA-PKCS1-v1_5", modulusLength: 2048, publicExponent: new Uint8Array([0x01, 0x00, 0x01]), hash: "SHA-256" },
            true,
            ["sign", "verify"]
        );
        const ep = this.textToBase64(this.encryptionKeyPair.publicKey);
        const spBuffer = await crypto.subtle.exportKey("spki", signingKeyPair.publicKey);
        const sp = btoa(String.fromCharCode(...new Uint8Array(spBuffer)));

        const s = crypto.randomUUID();
        const xdi = await this.gdi();
        const encoder = new TextEncoder();
        const sBuffer = encoder.encode(s + xdi);

        const signatureBuffer = await crypto.subtle.sign(
            { name: "RSASSA-PKCS1-v1_5" },
            signingKeyPair.privateKey,
            sBuffer
        );
        const ss = btoa(String.fromCharCode(...new Uint8Array(signatureBuffer)));
        return { ep, sp, ss, s };
    }

    textToBase64(text: string): string {
        return btoa(unescape(encodeURIComponent(text)));
    }

    private sc(name: string, value: string, minutes: number): void {
        const date = new Date();
        date.setTime(date.getTime() + (minutes * 60 * 1000));
        const expires = "expires=" + date.toUTCString();
        document.cookie = name + "=" + value + ";" + expires + ";path=/";
    }

    public gc(name: string): string | null {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    //remove cookie
    public rc(name: string): void {
        document.cookie = name + '=; Max-Age=-99999999;';
    }

    //remove all cookies
    public ra(): void {
        const cookies = document.cookie.split(";");
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i];
            const eqPos = cookie.indexOf("=");
            const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
            document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT";
        }
    }

    async spu() {
        const { ep, sp, ss, s } = await this.gr();
        const request = {
            ep: ep,
            sp: sp,
            ss: ss,
            s: s
        }
        const requestJson = JSON.stringify(request);
        const encryptedData = await this.he(publicKey, requestJson);
        const requestEncrypt = {
            EncryptData: encryptedData
        };

        try {
            const response = await fetchWithDeviceId(apiUrl + '/api/Crypto/dr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestEncrypt)
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const responseData = await response.json();
            if (responseData && responseData.resultObj && responseData.resultObj.encryptedData) {
                this.sc('s', responseData.resultObj.encryptedData, 5);
                const privateKeyBase64String = this.textToBase64(this.encryptionKeyPair.privateKey);
                this.sc('c', privateKeyBase64String, 5);
            } else {
                console.error('Invalid response from server:', responseData);
            }
        } catch (error) {
            console.error('Error in spu:', error);
        }
    }

    async dsk(): Promise<string> {
        var c = this.gc('c');
        var s = this.gc('s');
        if (!c || !s) {
            return "";
        }
        var cText = atob(c);
        var serverKey = await this.hd(cText, s);
        return serverKey;
    }

    async eda(data: string): Promise<string> {
        var s = await this.dsk();
        var sText = atob(s);
        if (!s) {
            return "";
        }
        var encryptedData = await this.he(sText, data);
        return encryptedData;
    }

    async dda(encryptedData: string): Promise<string> {
        var c = this.gc('c');
        if (!c) {
            return "";
        }
        var cText = atob(c);
        var decryptedData = await this.hd(cText, encryptedData);
        return decryptedData;
    }

    async csi() {
        try {
            const response = await fetchWithDeviceId(apiUrl + '/api/Crypto/check-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: null
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const responseData = await response.json();
            console.log(responseData);
        } catch (error) {
            console.error('Error in csi:', error);
        }
    }

    async iih() {
        if (!this.ch()) {
            await this.spu();
        }
    }

    ch() {
        if (this.gc('s') && this.gc('c')) {
            return true;
        }
        return false;
    }

    async wk() {
        let retries = 10;
        while (!this.gc('s') && retries > 0) {
            await new Promise(resolve => setTimeout(resolve, 200));
            retries--;
        }
    }
    async gdi(): Promise<string> {
        const fp = await FingerprintJS.load();
        const result = await fp.get();
        return result.visitorId; // Device ID duy nhất
    }
}