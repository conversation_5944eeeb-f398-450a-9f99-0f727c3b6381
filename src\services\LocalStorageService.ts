import * as CryptoJS from 'crypto-js';

export class localStorageService {
    public static setItem(key: string, value: any, encryptionKey: string) {
        const encryptedData = this.encryptData(value, encryptionKey);
        localStorage.setItem(key, encryptedData);
    }

    public static getItem(key: string, encryptionKey: string) {
        const value = localStorage.getItem(key);
        if (value) {
            try {
                const data = this.decryptData(value, encryptionKey);
                return data;
            } catch (error) {
                console.error('Error while decrypting data', error);
                return null;
            }
        }
        return null;
    }

    public static removeItem(key: string) {
        localStorage.removeItem(key);
    }

    static encryptData(data: any, encryptionKey: string): string {
        try {
            return CryptoJS.AES.encrypt(JSON.stringify(data), encryptionKey).toString();
        } catch (error) {
            console.error('Error while encrypting data', error);
            return '';
        }
    }

    static decryptData(data: string, encryptionKey: string): any {
        try {
            const bytes = CryptoJS.AES.decrypt(data, encryptionKey);
            const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
            return JSON.parse(decryptedData);
        } catch (error) {
            console.error('Error while decrypting data', error);
            return null;
        }
    }
}