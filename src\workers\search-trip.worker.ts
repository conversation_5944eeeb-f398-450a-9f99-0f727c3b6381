
export function CheckResponseAdvance(resultObj: any) {
    if (checkResponse(resultObj) && checkExpired(resultObj) === false) {
        return { success: true, resultObj: formatResponse(resultObj), done: false };
    } else {
        return { success: false, resultObj: null, done: true };
    }
}

function checkExpired(SearchTripResponse: any) {
    const expired = new Date(SearchTripResponse.ExpDate);
    const now = new Date();
    return now > expired;
}

function checkResponse(SearchTripResponse: any): boolean {
    if (SearchTripResponse?.SearchTripAirs?.length > 0) {
        for (const searchTripAir of SearchTripResponse.SearchTripAirs) {
            for (const itinerary of searchTripAir?.AirItinerary || []) {
                if (itinerary?.AirSegments?.length > 0) {
                    return true;
                }
            }
        }
    }
    return false;
}
function formatResponse(_searchTripResponse: any) {
    var segmentID = 0;
    let airCodeRef = 0;
    _searchTripResponse?.SearchTripAirs.forEach((searchTripAir: any) => {
        searchTripAir.AirCodeRef = `${_searchTripResponse.SessionID}^${airCodeRef}`;
        airCodeRef += 1;
        searchTripAir.AirItinerary.forEach((itinerary: any) => {
            itinerary.AirSegments = itinerary.AirSegments.filter((segment: any) => {
                if (segment?.LowestInventory === null) {
                    return false;
                }
                if (searchTripAir.Combine && segment.Inventories?.length !== searchTripAir.Inventories?.length) {
                    segment.Inventories = searchTripAir.Inventories;
                }
                segment.KeyRef = `${_searchTripResponse.SessionID}_${segmentID}`;
                segmentID += 1;

                return true; // Giữ lại segment này
            });
        });
    });

    //sort by departure time
    _searchTripResponse?.SearchTripAirs.forEach((searchTripAir: any) => {
        searchTripAir.AirItinerary.forEach((itinerary: any) => {
            itinerary.AirSegments = itinerary.AirSegments.sort((a: any, b: any) =>
                new Date(a.DepartureDate).getTime() - new Date(b.DepartureDate).getTime()
            );
        });
    });

    //foreach segment, get lowest price of cabin
    _searchTripResponse?.SearchTripAirs.forEach((searchTripAir: any) => {
        searchTripAir.AirItinerary.forEach((itinerary: any) => {
            itinerary.AirSegments.forEach((segment: any) => {
                segment.Inventories = getInventoriesOfFareTypeMinPrice(segment?.Inventories);
            });
        });
    });

    return _searchTripResponse;
}
//get lowest price of fareType
function getInventoriesOfFareTypeMinPrice(inventories: any[]): any[] {
    var result: any[] = [];

    inventories.forEach(inventory => {
        var fareType = getFareType(inventory?.BookingInfos);
        //check fareType exist in result array ==> next inventory
        if (result.find((item: any) => getFareType(item?.BookingInfos) === fareType)) {
            return;
        }

        //get all inventories of fareType
        var inventoriesOfFareType = inventories.filter((inventory: any) => getFareType(inventory?.BookingInfos) === fareType);
        //get lowest price of fareType
        var lowestPrice = inventoriesOfFareType.reduce((prev, current) => (prev.SumPrice < current.SumPrice) ? prev : current);
        result.push(lowestPrice);
    });

    return result;
}

//get fareType of bookingInfos
function getFareType(bookingInfos: any[]): string {
    return bookingInfos.map(bookingInfo => bookingInfo.FareType).join(' - ');
}
