import { css, html, LitElement, PropertyValues, unsafeCSS } from "lit";
import { customElement, property, state } from "lit/decorators.js";
import styles from '../../styles/styles.css';
import { TripAvailableTemplate } from "./trip-available-template";
import { CryptoService } from "../../services/CryptoService";
import { FlightService } from "../../services/FlightService";
import { getAirportInfoByCode } from "../../services/WorldServices";
import {
    formatDateTo_ddMMyyyy,
    getDurationByArray,
    convertDurationToHour,
    getTimeFromDateTime,
    getDayInWeek,
    formatddMMyyyy,
    getDuration
} from "../../utils/dateUtils";
import { setnmtColors } from "../../services/ColorService";

const cryptoService = new CryptoService();
const flightService = new FlightService();

@customElement("trip-available")
export class TripAvailable extends LitElement {
    static styles = [
        unsafeCSS(styles),
        css`
        :host {
          font-family: var(--nmt-font, 'Roboto', sans-serif);
        }
  
          `
    ];

    @property({ type: Boolean }) autoFillOrderCode = false;
    @property({ type: String }) uri_searchBox = "";
    @property({ type: Boolean }) showLanguageSelect = false;
    @property({ type: Boolean }) autoLanguageParam = false;
    @property({ type: String }) ApiKey = '';
    @property({ type: String }) color = "";
    @property({ type: String }) mode = "online";
    @property({ type: String }) googleFontsUrl = "";
    @property({ type: String }) font = "";

    @state() private _ApiKey: string = '';
    @state() private isLoading: boolean = false;
    @state() private isNotValid: boolean = false;
    @state() private orderAvailable: any = null;
    @state() private orderDetails: any = null;
    @state() private inforAirports: any[] = [];
    @state() private bankSelected: string = '';
    @state() private errorString: string = '';
    @state() private formSubmitted: boolean = false;
    @state() private orderCode: string = '';
    @state() private contact: string = '';
    @state() private _NoteModel: any = null;
    @state() private _PaymentNote: any = null;
    @state() private convertedVND: number = 1;
    @state() private currencySymbol: string = '₫';

    private request = {
        OrderCode: '',
        PhoneCustomer: '',
        EmailCustomer: ''
    };

    private _language = "vi";
    private _hasCheckedURL = false;
    private _cryptoService = cryptoService;
    private _flightService = flightService;

    @property({ type: String })
    get language(): string {
        return this._language;
    }

    set language(value: string) {
        const oldValue = this._language;

        // Chỉ kiểm tra URL nếu autoLanguageParam được bật
        if (this.autoLanguageParam) {
            const urlParams = new URLSearchParams(window.location.search);
            const languageParam = urlParams.get('language');

            if (languageParam && languageParam !== this._language) {
                // URL có language parameter - luôn ưu tiên URL
                this._language = languageParam;
                console.log('Language overridden from URL parameter:', this._language);
            } else {
                // URL không có language parameter - sử dụng giá trị được set
                this._language = value;
                console.log('Language set from property:', this._language);
                // Tự động thêm vào URL nếu chưa có
                if (!this._hasCheckedURL) {
                    this.updateURLWithLanguage();
                    this._hasCheckedURL = true;
                }
            }
        } else {
            // Không sử dụng URL parameter, chỉ set giá trị trực tiếp
            this._language = value;
            console.log('Language set from property (autoLanguageParam disabled):', this._language);
        }

        this.requestUpdate('language', oldValue);
    }

    get currencySymbolAv(): string{
        return this.convertedVND === 1 || this.language === 'vi'  ? '₫' : this.currencySymbol;
    }

    constructor() {
        super();
        this.checkLanguageFromURL();
        this.checkUrlParams();
    }

    protected async firstUpdated(_changedProperties: PropertyValues): Promise<void> {
        super.firstUpdated(_changedProperties);

        if (this.color !== "") {
            setnmtColors(this.color);
            this.requestUpdate();
        }

        console.log(this.googleFontsUrl);
        // Handle Google Fonts
        if (this.googleFontsUrl) {
            const googleFontsLink = document.createElement('link');
            googleFontsLink.rel = 'stylesheet';
            googleFontsLink.href = this.googleFontsUrl;
            document.head.appendChild(googleFontsLink);
        } else {
            // Default font if no Google Fonts URL provided
            const googleFontsLink = document.createElement('link');
            googleFontsLink.rel = 'stylesheet';
            googleFontsLink.href = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap';
            document.head.appendChild(googleFontsLink);
        }

        console.log('font', this.font);
        if (this.font !== "") {
            const root = document.documentElement;
            root.style.setProperty('--nmt-font', this.font);
        }
    }

    connectedCallback(): void {
        super.connectedCallback();
        this._ApiKey = this.ApiKey;
        this.removeAttribute("ApiKey");

        // Kiểm tra URL ngay khi component được kết nối, trường hợp không có attribute language
        this.checkLanguageFromURL();
    }

    private checkLanguageFromURL(): void {
        if (!this.autoLanguageParam) {
            console.log('autoLanguageParam disabled, skipping URL check');
            return;
        }
        const urlParams = new URLSearchParams(window.location.search);
        const languageParam = urlParams.get('language');
        if (languageParam) {
            this._language = languageParam;
            console.log('Language initialized from URL parameter:', this._language);
            this.requestUpdate('language');
        }
        else if (!this._hasCheckedURL) {
            this.updateURLWithLanguage();
            this._hasCheckedURL = true;
        }
    }

    private updateURLWithLanguage(): void {
        const currentUrl = new URL(window.location.href);
        const params = new URLSearchParams(currentUrl.search);
        params.set('language', this._language);
        const newUrl = `${currentUrl.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newUrl);
        console.log('URL updated with language parameter:', newUrl);
    }

    private checkUrlParams(): void {
        const urlParams = new URLSearchParams(window.location.search);
        this.request.OrderCode = urlParams.get('OrderCode') || '';
        this.request.PhoneCustomer = urlParams.get('Contact') || '';
        this.request.EmailCustomer = urlParams.get('Contact') || '';

        if (this.request.OrderCode && this.request.PhoneCustomer && this.request.EmailCustomer) {
            this.AvailableTrip(this.request);
        } else {
            this.orderAvailable = null;
        }
    }

    protected updated(_changedProperties: PropertyValues): void {
        super.updated(_changedProperties);
    }

    async onSubmitForm(e: Event) {
        e.preventDefault(); // Prevent form from submitting and reloading page
        this.formSubmitted = true;

        if (!this.orderCode || !this.contact) {
            this.errorString = 'Vui lòng nhập đầy đủ thông tin.';
            return;
        }

        this.errorString = '';

        // Update URL parameters without page reload
        const params = new URLSearchParams();
        params.append('OrderCode', this.orderCode);
        params.append('Contact', this.contact);

        const currentUrl = new URL(window.location.href);
        currentUrl.search = params.toString();
        window.history.pushState({}, '', currentUrl.toString());

        // Update the request and call AvailableTrip
        this.request.OrderCode = this.orderCode;
        this.request.PhoneCustomer = this.contact;
        this.request.EmailCustomer = this.contact;

        await this.AvailableTrip(this.request);
    }

    async RequestEncrypt(data: any): Promise<any> {
        const encryptedData = await this._cryptoService.eda(JSON.stringify(data));
        return {
            EncryptData: encryptedData
        };
    }

    rePayment() {
        const params = new URLSearchParams();
        params.append('OrderCode', this.request.OrderCode);
        params.append('PhoneCustomer', this.request.PhoneCustomer);
        params.append('EmailCustomer', this.request.EmailCustomer);

        // Navigate to repayment page
        const currentUrl = new URL(window.location.href);
        currentUrl.pathname = '/TripRePayment';
        currentUrl.search = params.toString();
        window.location.href = currentUrl.toString();
    }

    async CallAvailableTrip(request: any) {
        this.isLoading = true;

        try {
            const payloadsEncrypted = await this.RequestEncrypt(request);
            const res = await this._flightService.AvailableTrip(payloadsEncrypted, this._ApiKey);
            const resDecrypted = await this._cryptoService.dda(res.resultObj);
            const resJson = JSON.parse(resDecrypted);

            console.log(resJson);
            if (resJson.IsSuccessed) {
                const noteData = JSON.parse(resJson.ResultObj.Note);
                this.orderDetails = noteData;
                this.isNotValid = true;
                this.orderAvailable = resJson.ResultObj;
                this._PaymentNote = JSON.parse(resJson.ResultObj.PaymentNote);
                console.log('this._PaymentNote', this._PaymentNote);
                this._NoteModel = JSON.parse(resJson.ResultObj.NoteResult);
                this.formatPassenger();
                await this.getInforAirports();
                if (this.orderAvailable?.PaymentMethod.includes('bank-transfer')) {
                    this.bankSelected = this.orderAvailable?.PaymentMethod.split('_')[1];
                }
            } else {
                this.errorString = 'Không tìm thấy thông tin đơn hàng này';
            }
        } catch (error: any) {
            console.error('Error in CallAvailableTrip:', error);
            if (error.status !== 200) {
                this._cryptoService.ra();
                await this._cryptoService.spu();
                await this.CallAvailableTrip(request);
            } else {
                this.errorString = 'Có lỗi xảy ra khi tìm kiếm đơn hàng';
            }
        } finally {
            this.isLoading = false;
        }
    }

    formatPassenger() {
        if (!this.orderDetails?.paxList) return;

        let indexInfant = 0;
        this.orderDetails.paxList.forEach((pax: any, index: number) => {
            if (pax.type === 'infant') {
                const paxAdult = this.orderDetails.paxList.find((p: any) => p.type === 'adult' && p.index === indexInfant);
                if (paxAdult) {
                    paxAdult.withInfant = pax;
                    this.orderDetails.paxList.splice(index, 1);
                }
                indexInfant++;
            } else {
                pax.index = index;
            }
        });
    }

    async AvailableTrip(request: any) {
        if (!this._cryptoService.ch()) {
            await this._cryptoService.spu();
        }
        await this.CallAvailableTrip(request);
    }

    async getInforAirports() {
        if (!this.orderDetails?.full?.InventoriesSelected) return;

        const airportsCode: string[] = [];
        this.orderDetails.full.InventoriesSelected.forEach((inventory: any) => {
            inventory.segment.Legs.forEach((leg: any) => {
                if (!airportsCode.includes(leg.DepartureCode)) {
                    airportsCode.push(leg.DepartureCode);
                }
                if (!airportsCode.includes(leg.ArrivalCode)) {
                    airportsCode.push(leg.ArrivalCode);
                }
            });
        });


        try {
            var res = await getAirportInfoByCode(airportsCode, this.language || 'vi', this._ApiKey);
            if (res.isSuccessed) {
                this.inforAirports = res.resultObj;
                const currencyObj = typeof res.feature.currency === 'string' ? JSON.parse(res.feature.currency) : res.feature.currency;
                this.currencySymbol = currencyObj.symbol || '₫';
                this.convertedVND = currencyObj.convertedVND || 1;
            }
            console.log('mode', this.mode);
            if (this.mode === "online") {
                if (res.feature?.color) {
                    this.color = res.feature.color;
                    if (this.color !== "") {
                        setnmtColors(this.color);
                        this.requestUpdate();
                    }
                }
            }
        } catch (error: any) {
            console.error('Error getting airport info:', error);
        }
    }

    // Form input handlers
    onOrderCodeChange(e: Event) {
        const target = e.target as HTMLInputElement;
        this.orderCode = target.value;
    }

    onContactChange(e: Event) {
        const target = e.target as HTMLInputElement;
        this.contact = target.value;
    }

    // Utility methods
    formatDateTo_ddMMyyyy(date: string): string | null {
        return formatDateTo_ddMMyyyy(new Date(date), this.language);
    }

    getDurationByArray(legs: any[]): string {
        return getDurationByArray(legs);
    }

    convertDurationToHour(duration: number): string {
        return convertDurationToHour(duration);
    }

    getTimeFromDateTime(dateTime: string): string {
        return getTimeFromDateTime(dateTime);
    }

    getDayInWeek(date: string): string {
        return getDayInWeek(date);
    }

    formatddMMyyyy(date: string): string {
        return formatddMMyyyy(date);
    }

    getDuration(leg: any): string {
        return getDuration(leg);
    }

    handleLanguageChange(newLang: string) {
        console.log('newLang', newLang);
        this.language = newLang;
        this.getInforAirports();

        // Tự động cập nhật URL với language mới
        this.updateURLWithLanguage();

        this.requestUpdate();
    }

    render() {
        return TripAvailableTemplate(
            this.autoFillOrderCode,
            this.language,
            this.uri_searchBox,
            this.showLanguageSelect,
            this.isLoading,
            this.orderAvailable,
            this.isNotValid,
            this.orderDetails,
            this.inforAirports,
            this.bankSelected,
            this.errorString,
            this.formSubmitted,
            this.orderCode,
            this.contact,
            this.request,
            this._PaymentNote,
            this._NoteModel,
            this.currencySymbolAv,
            this.convertedVND,
            {
                onSubmitForm: this.onSubmitForm.bind(this),
                onOrderCodeChange: this.onOrderCodeChange.bind(this),
                onContactChange: this.onContactChange.bind(this),
                rePayment: this.rePayment.bind(this),
                formatDateTo_ddMMyyyy: this.formatDateTo_ddMMyyyy.bind(this),
                getDurationByArray: this.getDurationByArray.bind(this),
                convertDurationToHour: this.convertDurationToHour.bind(this),
                getTimeFromDateTime: this.getTimeFromDateTime.bind(this),
                getDayInWeek: this.getDayInWeek.bind(this),
                formatddMMyyyy: this.formatddMMyyyy.bind(this),
                getDuration: this.getDuration.bind(this),
                handleLanguageChange: this.handleLanguageChange.bind(this),
            }
        );
    }
}