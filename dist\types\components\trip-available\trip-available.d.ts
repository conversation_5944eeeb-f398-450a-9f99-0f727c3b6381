import { LitElement, PropertyValues } from "lit";
export declare class TripAvailable extends LitElement {
    static styles: import("lit").CSSResult[];
    autoFillOrderCode: boolean;
    uri_searchBox: string;
    showLanguageSelect: boolean;
    autoLanguageParam: boolean;
    ApiKey: string;
    color: string;
    mode: string;
    googleFontsUrl: string;
    font: string;
    private _ApiKey;
    private isLoading;
    private isNotValid;
    private orderAvailable;
    private orderDetails;
    private inforAirports;
    private bankSelected;
    private errorString;
    private formSubmitted;
    private orderCode;
    private contact;
    private _NoteModel;
    private _PaymentNote;
    private convertedVND;
    private currencySymbol;
    private request;
    private _language;
    private _hasCheckedURL;
    private _cryptoService;
    private _flightService;
    get language(): string;
    set language(value: string);
    get currencySymbolAv(): string;
    constructor();
    protected firstUpdated(_changedProperties: PropertyValues): Promise<void>;
    connectedCallback(): void;
    private checkLanguageFromURL;
    private updateURLWithLanguage;
    private checkUrlParams;
    protected updated(_changedProperties: PropertyValues): void;
    onSubmitForm(e: Event): Promise<void>;
    RequestEncrypt(data: any): Promise<any>;
    rePayment(): void;
    CallAvailableTrip(request: any): Promise<void>;
    formatPassenger(): void;
    AvailableTrip(request: any): Promise<void>;
    getInforAirports(): Promise<void>;
    onOrderCodeChange(e: Event): void;
    onContactChange(e: Event): void;
    formatDateTo_ddMMyyyy(date: string): string | null;
    getDurationByArray(legs: any[]): string;
    convertDurationToHour(duration: number): string;
    getTimeFromDateTime(dateTime: string): string;
    getDayInWeek(date: string): string;
    formatddMMyyyy(date: string): string;
    getDuration(leg: any): string;
    handleLanguageChange(newLang: string): void;
    render(): import("lit-html").TemplateResult<1>;
}
