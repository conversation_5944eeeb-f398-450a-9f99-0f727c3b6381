{
  "compilerOptions": {
    "target": "ES2022", // <PERSON><PERSON><PERSON> tiêu phiên bản JavaScript
    "module": "ESNext", // Sử dụng module ESNext
    "moduleResolution": "node", // Giải quyết module theo Node.js
    "resolveJsonModule": true, // Hỗ trợ import file JSON
    "lib": [
      "ES2022",
      "DOM"
    ], // Bao gồm các API ES2022 và DOM
    "outDir": "dist", // Thư mục đầu ra
    "declaration": true, // Xuất file .d.ts
    "declarationDir": "dist/types", // Thư mục chứa file .d.ts
    "experimentalDecorators": true, // Hỗ trợ decorator
    "emitDecoratorMetadata": true, // Xuất metadata cho decorator
    "useDefineForClassFields": false, // Không sử dụng define cho class fields
    "strict": true, // Bật chế độ kiểm tra nghiêm ngặt
    "esModuleInterop": true, // Hỗ trợ import CommonJS module
    "skipLibCheck": true, // Bỏ qua kiểm tra type trong thư viện
    "forceConsistentCasingInFileNames": true, // Đảm bảo tên file nhất quán
    "importHelpers": true, // Tối ưu hóa import helper từ tslib
    "sourceMap": true // Xuất file source map
  },
  "include": [
    "src/**/*",
    "src/types.ts",
    "src/**/*.css"
  ], // Bao gồm tất cả file trong thư mục src
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts"
  ] // Loại trừ node_modules, dist và file test
}