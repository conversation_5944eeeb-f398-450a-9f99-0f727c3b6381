export declare class FlightService {
    request(endpoint: string, request: any, useDeviceId: boolean | undefined, apiKey: string): Promise<any>;
    SearchTrip(request: any, api: string): Promise<any>;
    PriceAncillary(request: any, api: string): Promise<any>;
    FareRules(request: any, language: string): Promise<any>;
    AvailableTrip(request: any, api: string): Promise<any>;
    RequestTrip(request: any, api: string): Promise<any>;
    RePayment(request: any, api: string): Promise<any>;
}
