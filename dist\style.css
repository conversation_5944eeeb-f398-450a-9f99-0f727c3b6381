*,
:after,
:before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/
*,
:after,
:before {
    border: 0 solid #e5e7eb;
    box-sizing: border-box
}

:after,
:before {
    --tw-content: ""
}

:host,
html {
    -webkit-text-size-adjust: 100%;
    font-feature-settings: normal;
    -webkit-tap-highlight-color: transparent;
    font-family: Inter, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
    font-variation-settings: normal;
    line-height: 1.5;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4
}

body {
    line-height: inherit;
    margin: 0
}

hr {
    border-top-width: 1px;
    color: inherit;
    height: 0
}

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit
}

a {
    color: inherit;
    text-decoration: inherit
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
pre,
samp {
    font-feature-settings: normal;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
    font-size: 1em;
    font-variation-settings: normal
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    border-collapse: collapse;
    border-color: inherit;
    text-indent: 0
}

button,
input,
optgroup,
select,
textarea {
    font-feature-settings: inherit;
    color: inherit;
    font-family: inherit;
    font-size: 100%;
    font-variation-settings: inherit;
    font-weight: inherit;
    letter-spacing: inherit;
    line-height: inherit;
    margin: 0;
    padding: 0
}

button,
select {
    text-transform: none
}

button,
input:where([type=button]),
input:where([type=reset]),
input:where([type=submit]) {
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none
}

:-moz-focusring {
    outline: auto
}

:-moz-ui-invalid {
    box-shadow: none
}

progress {
    vertical-align: baseline
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

summary {
    display: list-item
}

blockquote,
dd,
dl,
figure,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
pre {
    margin: 0
}

fieldset {
    margin: 0
}

fieldset,
legend {
    padding: 0
}

menu,
ol,
ul {
    list-style: none;
    margin: 0;
    padding: 0
}

dialog {
    padding: 0
}

textarea {
    resize: vertical
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    color: #9ca3af;
    opacity: 1
}

input::placeholder,
textarea::placeholder {
    color: #9ca3af;
    opacity: 1
}

[role=button],
button {
    cursor: pointer
}

:disabled {
    cursor: default
}

audio,
canvas,
embed,
iframe,
img,
object,
svg,
video {
    display: block;
    vertical-align: middle
}

img,
video {
    height: auto;
    max-width: 100%
}

[hidden]:where(:not([hidden=until-found])) {
    display: none
}

.\!container {
    width: 100% !important
}

.container {
    width: 100%
}

@media (min-width:640px) {
    .\!container {
        max-width: 640px !important
    }

    .container {
        max-width: 640px
    }
}

@media (min-width:768px) {
    .\!container {
        max-width: 768px !important
    }

    .container {
        max-width: 768px
    }
}

@media (min-width:1024px) {
    .\!container {
        max-width: 1024px !important
    }

    .container {
        max-width: 1024px
    }
}

@media (min-width:1280px) {
    .\!container {
        max-width: 1280px !important
    }

    .container {
        max-width: 1280px
    }
}

@media (min-width:1536px) {
    .\!container {
        max-width: 1536px !important
    }

    .container {
        max-width: 1536px
    }
}

.sr-only {
    clip: rect(0, 0, 0, 0);
    border-width: 0;
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    white-space: nowrap;
    width: 1px
}

.pointer-events-none {
    pointer-events: none
}

.static {
    position: static
}

.fixed {
    position: fixed
}

.absolute {
    position: absolute
}

.relative {
    position: relative
}

.sticky {
    position: sticky
}

.inset-0 {
    inset: 0
}

.inset-y-0 {
    bottom: 0;
    top: 0
}

.-bottom-9 {
    bottom: -2.25rem
}

.-bottom-\[5px\] {
    bottom: -5px
}

.-left-1 {
    left: -.25rem
}

.-left-2 {
    left: -.5rem
}

.-right-3 {
    right: -.75rem
}

.-top-0 {
    top: 0
}

.-top-2 {
    top: -.5rem
}

.-top-3 {
    top: -.75rem
}

.-top-4 {
    top: -1rem
}

.-top-5 {
    top: -1.25rem
}

.-top-\[1px\] {
    top: -1px
}

.bottom-0 {
    bottom: 0
}

.left-0 {
    left: 0
}

.left-1\/2 {
    left: 50%
}

.left-2 {
    left: .5rem
}

.left-\[7px\] {
    left: 7px
}

.right-0 {
    right: 0
}

.right-2 {
    right: .5rem
}

.start-0 {
    inset-inline-start: 0
}

.start-\[62\.5px\] {
    inset-inline-start: 62.5px
}

.top-0 {
    top: 0
}

.top-1 {
    top: .25rem
}

.top-1\/2 {
    top: 50%
}

.top-11 {
    top: 2.75rem
}

.top-2 {
    top: .5rem
}

.top-2\.5 {
    top: .625rem
}

.top-24 {
    top: 6rem
}

.top-6 {
    top: 1.5rem
}

.top-\[-7px\] {
    top: -7px
}

.top-\[53px\] {
    top: 53px
}

.top-\[64px\] {
    top: 64px
}

.top-\[calc\(50\%-8px\)\] {
    top: calc(50% - 8px)
}

.top-full {
    top: 100%
}

.z-10 {
    z-index: 10
}

.z-20 {
    z-index: 20
}

.z-30 {
    z-index: 30
}

.z-50 {
    z-index: 50
}

.z-\[9999\] {
    z-index: 9999
}

.col-span-12 {
    grid-column: span 12/span 12
}

.col-span-2 {
    grid-column: span 2/span 2
}

.col-span-3 {
    grid-column: span 3/span 3
}

.col-span-4 {
    grid-column: span 4/span 4
}

.col-span-5 {
    grid-column: span 5/span 5
}

.col-span-6 {
    grid-column: span 6/span 6
}

.col-span-7 {
    grid-column: span 7/span 7
}

.m-auto {
    margin: auto
}

.mx-1\.5 {
    margin-left: .375rem;
    margin-right: .375rem
}

.mx-auto {
    margin-left: auto;
    margin-right: auto
}

.my-1 {
    margin-bottom: .25rem;
    margin-top: .25rem
}

.my-3 {
    margin-bottom: .75rem;
    margin-top: .75rem
}

.my-4 {
    margin-bottom: 1rem;
    margin-top: 1rem
}

.my-8 {
    margin-bottom: 2rem;
    margin-top: 2rem
}

.-mb-2 {
    margin-bottom: -.5rem
}

.-mt-1 {
    margin-top: -.25rem
}

.-mt-2 {
    margin-top: -.5rem
}

.-mt-6 {
    margin-top: -1.5rem
}

.mb-1 {
    margin-bottom: .25rem
}

.mb-2 {
    margin-bottom: .5rem
}

.mb-4 {
    margin-bottom: 1rem
}

.mb-6 {
    margin-bottom: 1.5rem
}

.mb-8 {
    margin-bottom: 2rem
}

.mb-\[8px\] {
    margin-bottom: 8px
}

.me-2 {
    margin-inline-end: .5rem
}

.me-3 {
    margin-inline-end: .75rem
}

.me-\[43px\] {
    margin-inline-end: 43px
}

.ml-0 {
    margin-left: 0
}

.ml-12 {
    margin-left: 3rem
}

.ml-2 {
    margin-left: .5rem
}

.ml-\[1px\] {
    margin-left: 1px
}

.mr-2 {
    margin-right: .5rem
}

.ms-2 {
    margin-inline-start: .5rem
}

.mt-0 {
    margin-top: 0
}

.mt-0\.5 {
    margin-top: .125rem
}

.mt-1 {
    margin-top: .25rem
}

.mt-10 {
    margin-top: 2.5rem
}

.mt-2 {
    margin-top: .5rem
}

.mt-4 {
    margin-top: 1rem
}

.mt-5 {
    margin-top: 1.25rem
}

.mt-6 {
    margin-top: 1.5rem
}

.mt-\[8px\] {
    margin-top: 8px
}

.box-border {
    box-sizing: border-box
}

.box-content {
    box-sizing: content-box
}

.line-clamp-1 {
    -webkit-line-clamp: 1
}

.line-clamp-1,
.line-clamp-2 {
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden
}

.line-clamp-2 {
    -webkit-line-clamp: 2
}

.block {
    display: block
}

.inline-block {
    display: inline-block
}

.inline {
    display: inline
}

.flex {
    display: flex
}

.inline-flex {
    display: inline-flex
}

.table {
    display: table
}

.grid {
    display: grid
}

.contents {
    display: contents
}

.hidden {
    display: none
}

.\!h-auto {
    height: auto !important
}

.h-0 {
    height: 0
}

.h-1 {
    height: .25rem
}

.h-1\.5 {
    height: .375rem
}

.h-10 {
    height: 2.5rem
}

.h-11 {
    height: 2.75rem
}

.h-12 {
    height: 3rem
}

.h-2 {
    height: .5rem
}

.h-2\.5 {
    height: .625rem
}

.h-24 {
    height: 6rem
}

.h-3 {
    height: .75rem
}

.h-3\.5 {
    height: .875rem
}

.h-4 {
    height: 1rem
}

.h-40 {
    height: 10rem
}

.h-5 {
    height: 1.25rem
}

.h-6 {
    height: 1.5rem
}

.h-8 {
    height: 2rem
}

.h-\[18px\] {
    height: 18px
}

.h-\[1px\] {
    height: 1px
}

.h-\[22px\] {
    height: 22px
}

.h-\[24px\] {
    height: 24px
}

.h-\[26px\] {
    height: 26px
}

.h-\[2px\] {
    height: 2px
}

.h-\[37px\] {
    height: 37px
}

.h-\[3px\] {
    height: 3px
}

.h-\[5rem\] {
    height: 5rem
}

.h-\[calc\(100\%-1\.5rem\)\] {
    height: calc(100% - 1.5rem)
}

.h-\[calc\(100vh-5\.5rem\)\] {
    height: calc(100vh - 5.5rem)
}

.h-auto {
    height: auto
}

.h-fit {
    height: -moz-fit-content;
    height: fit-content
}

.h-full {
    height: 100%
}

.h-max {
    height: -moz-max-content;
    height: max-content
}

.\!max-h-\[1000px\] {
    max-height: 1000px !important
}

.max-h-0 {
    max-height: 0
}

.max-h-80 {
    max-height: 20rem
}

.max-h-\[60vh\] {
    max-height: 60vh
}

.min-h-16 {
    min-height: 4rem
}

.min-h-80 {
    min-height: 20rem
}

.min-h-\[50px\] {
    min-height: 50px
}

.min-h-\[60vh\] {
    min-height: 60vh
}

.min-h-\[70vh\] {
    min-height: 70vh
}

.min-h-fit {
    min-height: -moz-fit-content;
    min-height: fit-content
}

.min-h-screen {
    min-height: 100vh
}

.\!w-full {
    width: 100% !important
}

.w-0 {
    width: 0
}

.w-0\.5 {
    width: .125rem
}

.w-1\.5 {
    width: .375rem
}

.w-1\/2 {
    width: 50%
}

.w-12 {
    width: 3rem
}

.w-16 {
    width: 4rem
}

.w-2\.5 {
    width: .625rem
}

.w-20 {
    width: 5rem
}

.w-24 {
    width: 6rem
}

.w-3 {
    width: .75rem
}

.w-3\.5 {
    width: .875rem
}

.w-3\/4 {
    width: 75%
}

.w-32 {
    width: 8rem
}

.w-4 {
    width: 1rem
}

.w-5 {
    width: 1.25rem
}

.w-6 {
    width: 1.5rem
}

.w-72 {
    width: 18rem
}

.w-96 {
    width: 24rem
}

.w-\[1px\] {
    width: 1px
}

.w-\[45px\] {
    width: 45px
}

.w-\[97px\] {
    width: 97px
}

.w-\[calc\(100\%-1\.5rem\)\] {
    width: calc(100% - 1.5rem)
}

.w-auto {
    width: auto
}

.w-fit {
    width: -moz-fit-content;
    width: fit-content
}

.w-full {
    width: 100%
}

.w-max {
    width: -moz-max-content;
    width: max-content
}

.min-w-0 {
    min-width: 0
}

.min-w-12 {
    min-width: 3rem
}

.min-w-20 {
    min-width: 5rem
}

.min-w-3\.5 {
    min-width: .875rem
}

.min-w-36 {
    min-width: 9rem
}

.min-w-5 {
    min-width: 1.25rem
}

.min-w-\[200px\] {
    min-width: 200px
}

.min-w-\[6\.5rem\] {
    min-width: 6.5rem
}

.min-w-\[84px\] {
    min-width: 84px
}

.min-w-\[calc\(33\%-0\.5rem\)\] {
    min-width: calc(33% - .5rem)
}

.max-w-32 {
    max-width: 8rem
}

.max-w-48 {
    max-width: 12rem
}

.max-w-4xl {
    max-width: 56rem
}

.max-w-6xl {
    max-width: 72rem
}

.max-w-7xl {
    max-width: 80rem
}

.max-w-\[86px\] {
    max-width: 86px
}

.max-w-\[8rem\] {
    max-width: 8rem
}

.max-w-full {
    max-width: 100%
}

.max-w-lg {
    max-width: 32rem
}

.max-w-md {
    max-width: 28rem
}

.max-w-xs {
    max-width: 20rem
}

.flex-1 {
    flex: 1 1 0%
}

.flex-\[0_0_auto\] {
    flex: 0 0 auto
}

.flex-\[1_0_0\] {
    flex: 1 0 0
}

.flex-\[1_1_0\] {
    flex: 1 1 0
}

.flex-shrink-0 {
    flex-shrink: 0
}

.-translate-x-1\/2 {
    --tw-translate-x: -50%
}

.-translate-x-1\/2,
.-translate-y-1\/2 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-translate-y-1\/2 {
    --tw-translate-y: -50%
}

.translate-x-\[-50\%\] {
    --tw-translate-x: -50%
}

.translate-x-\[-50\%\],
.translate-x-\[0\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-\[0\] {
    --tw-translate-x: 0
}

.translate-y-4 {
    --tw-translate-y: 1rem
}

.translate-y-4,
.translate-y-\[-50\%\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-y-\[-50\%\] {
    --tw-translate-y: -50%
}

.translate-y-\[46\.4px\] {
    --tw-translate-y: 46.4px
}

.translate-y-\[46\.4px\],
.translate-y-\[69\.4px\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-y-\[69\.4px\] {
    --tw-translate-y: 69.4px
}

.rotate-180 {
    --tw-rotate: 180deg
}

.rotate-180,
.rotate-45 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.rotate-45 {
    --tw-rotate: 45deg
}

.rotate-\[135deg\] {
    --tw-rotate: 135deg
}

.rotate-\[135deg\],
.rotate-\[315deg\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.rotate-\[315deg\] {
    --tw-rotate: 315deg
}

.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

@keyframes pulse {
    50% {
        opacity: .5
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite
}

.\!cursor-pointer {
    cursor: pointer !important
}

.cursor-not-allowed {
    cursor: not-allowed
}

.cursor-pointer {
    cursor: pointer
}

.select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.resize {
    resize: both
}

.list-disc {
    list-style-type: disc
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr))
}

.grid-cols-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr))
}

.grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr))
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr))
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr))
}

.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr))
}

.grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr))
}

.flex-row {
    flex-direction: row
}

.flex-row-reverse {
    flex-direction: row-reverse
}

.flex-col {
    flex-direction: column
}

.flex-wrap-reverse {
    flex-wrap: wrap-reverse
}

.flex-nowrap {
    flex-wrap: nowrap
}

.items-start {
    align-items: flex-start
}

.items-end {
    align-items: flex-end
}

.items-center {
    align-items: center
}

.items-stretch {
    align-items: stretch
}

.justify-start {
    justify-content: flex-start
}

.justify-end {
    justify-content: flex-end
}

.justify-center {
    justify-content: center
}

.justify-between {
    justify-content: space-between
}

.gap-0 {
    gap: 0
}

.gap-1 {
    gap: .25rem
}

.gap-2 {
    gap: .5rem
}

.gap-3 {
    gap: .75rem
}

.gap-4 {
    gap: 1rem
}

.gap-6 {
    gap: 1.5rem
}

.gap-x-4 {
    -moz-column-gap: 1rem;
    column-gap: 1rem
}

.gap-y-2 {
    row-gap: .5rem
}

.space-x-2>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(.5rem*(1 - var(--tw-space-x-reverse)));
    margin-right: calc(.5rem*var(--tw-space-x-reverse))
}

.space-x-3>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(.75rem*(1 - var(--tw-space-x-reverse)));
    margin-right: calc(.75rem*var(--tw-space-x-reverse))
}

.space-y-1>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(.25rem*var(--tw-space-y-reverse));
    margin-top: calc(.25rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-10>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(2.5rem*var(--tw-space-y-reverse));
    margin-top: calc(2.5rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-2>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(.5rem*var(--tw-space-y-reverse));
    margin-top: calc(.5rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-3>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(.75rem*var(--tw-space-y-reverse));
    margin-top: calc(.75rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-4>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(1rem*var(--tw-space-y-reverse));
    margin-top: calc(1rem*(1 - var(--tw-space-y-reverse)))
}

.divide-y>:not([hidden])~:not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-bottom-width: calc(1px*var(--tw-divide-y-reverse));
    border-top-width: calc(1px*(1 - var(--tw-divide-y-reverse)))
}

.divide-gray-100>:not([hidden])~:not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(243 244 246/var(--tw-divide-opacity, 1))
}

.overflow-hidden {
    overflow: hidden
}

.overflow-x-auto {
    overflow-x: auto
}

.overflow-y-auto {
    overflow-y: auto
}

.overflow-y-hidden {
    overflow-y: hidden
}

.overflow-x-scroll {
    overflow-x: scroll
}

.overflow-y-scroll {
    overflow-y: scroll
}

.scroll-smooth {
    scroll-behavior: smooth
}

.text-ellipsis {
    text-overflow: ellipsis
}

.whitespace-nowrap {
    white-space: nowrap
}

.text-wrap {
    text-wrap: wrap
}

.text-nowrap {
    text-wrap: nowrap
}

.rounded {
    border-radius: .25rem
}

.rounded-2xl {
    border-radius: 1rem
}

.rounded-\[13px\] {
    border-radius: 13px
}

.rounded-\[40px\] {
    border-radius: 40px
}

.rounded-full {
    border-radius: 9999px
}

.rounded-lg {
    border-radius: .5rem
}

.rounded-md {
    border-radius: .375rem
}

.rounded-none {
    border-radius: 0
}

.rounded-sm {
    border-radius: .125rem
}

.rounded-xl {
    border-radius: .75rem
}

.rounded-b {
    border-bottom-left-radius: .25rem;
    border-bottom-right-radius: .25rem
}

.rounded-b-\[50\%_1rem\] {
    border-bottom-left-radius: 50% 1rem;
    border-bottom-right-radius: 50% 1rem
}

.rounded-b-lg {
    border-bottom-left-radius: .5rem;
    border-bottom-right-radius: .5rem
}

.rounded-e-\[20px\] {
    border-end-end-radius: 20px;
    border-start-end-radius: 20px
}

.rounded-e-full {
    border-end-end-radius: 9999px;
    border-start-end-radius: 9999px
}

.rounded-e-lg {
    border-end-end-radius: .5rem;
    border-start-end-radius: .5rem
}

.rounded-e-md {
    border-end-end-radius: .375rem;
    border-start-end-radius: .375rem
}

.rounded-s-\[20px\] {
    border-end-start-radius: 20px;
    border-start-start-radius: 20px
}

.rounded-s-lg {
    border-end-start-radius: .5rem;
    border-start-start-radius: .5rem
}

.rounded-s-md {
    border-end-start-radius: .375rem;
    border-start-start-radius: .375rem
}

.rounded-t {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem
}

.rounded-t-\[40px\] {
    border-top-left-radius: 40px;
    border-top-right-radius: 40px
}

.rounded-t-lg {
    border-top-left-radius: .5rem;
    border-top-right-radius: .5rem
}

.rounded-bl-2xl {
    border-bottom-left-radius: 1rem
}

.rounded-bl-lg {
    border-bottom-left-radius: .5rem
}

.rounded-br-lg {
    border-bottom-right-radius: .5rem
}

.rounded-br-sm {
    border-bottom-right-radius: .125rem
}

.rounded-tl-lg {
    border-top-left-radius: .5rem
}

.rounded-tr-2xl {
    border-top-right-radius: 1rem
}

.rounded-tr-full {
    border-top-right-radius: 9999px
}

.rounded-tr-lg {
    border-top-right-radius: .5rem
}

.border {
    border-width: 1px
}

.border-0 {
    border-width: 0
}

.border-2 {
    border-width: 2px
}

.border-\[1px\] {
    border-width: 1px
}

.border-\[3px\] {
    border-width: 3px
}

.border-\[8px\] {
    border-width: 8px
}

.border-x-0 {
    border-left-width: 0;
    border-right-width: 0
}

.border-b {
    border-bottom-width: 1px
}

.border-b-2 {
    border-bottom-width: 2px
}

.border-e-0 {
    border-inline-end-width: 0
}

.border-r {
    border-right-width: 1px
}

.border-t {
    border-top-width: 1px
}

.border-t-0 {
    border-top-width: 0
}

.border-dashed {
    border-style: dashed
}

.border-none {
    border-style: none
}

.\!border-nmt-500 {
    border-color: var(--color-nmt-500, #b3b7c1) !important
}

.border-black {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 0/var(--tw-border-opacity, 1))
}

.border-gray-100 {
    --tw-border-opacity: 1;
    border-color: rgb(243 244 246/var(--tw-border-opacity, 1))
}

.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-border-opacity, 1))
}

.border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity, 1))
}

.border-gray-400 {
    --tw-border-opacity: 1;
    border-color: rgb(156 163 175/var(--tw-border-opacity, 1))
}

.border-gray-600 {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99/var(--tw-border-opacity, 1))
}

.border-nmt-100 {
    border-color: var(--color-nmt-100, #b3b7c1)
}

.border-nmt-200 {
    border-color: var(--color-nmt-200, #b3b7c1)
}

.border-nmt-300 {
    border-color: var(--color-nmt-300, #b3b7c1)
}

.border-nmt-500 {
    border-color: var(--color-nmt-500, #b3b7c1)
}

.border-nmt-600 {
    border-color: var(--color-nmt-600, #b3b7c1)
}

.border-red-500 {
    --tw-border-opacity: 1;
    border-color: rgb(239 68 68/var(--tw-border-opacity, 1))
}

.border-transparent {
    border-color: transparent
}

.border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255/var(--tw-border-opacity, 1))
}

.border-r-white {
    --tw-border-opacity: 1;
    border-right-color: rgb(255 255 255/var(--tw-border-opacity, 1))
}

.border-t-white {
    --tw-border-opacity: 1;
    border-top-color: rgb(255 255 255/var(--tw-border-opacity, 1))
}

.\!bg-nmt-500 {
    background-color: var(--color-nmt-500, #b3b7c1) !important
}

.\!bg-white {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1)) !important
}

.bg-\[\#c9efff\] {
    --tw-bg-opacity: 1;
    background-color: rgb(201 239 255/var(--tw-bg-opacity, 1))
}

.bg-\[\#dadfe6\] {
    --tw-bg-opacity: 1;
    background-color: rgb(218 223 230/var(--tw-bg-opacity, 1))
}

.bg-\[\#f8e2de\] {
    --tw-bg-opacity: 1;
    background-color: rgb(248 226 222/var(--tw-bg-opacity, 1))
}

.bg-\[\#fcfdff\] {
    --tw-bg-opacity: 1;
    background-color: rgb(252 253 255/var(--tw-bg-opacity, 1))
}

.bg-\[\#fffbb3\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 251 179/var(--tw-bg-opacity, 1))
}

.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity, 1))
}

.bg-black\/10 {
    background-color: rgba(0, 0, 0, .1)
}

.bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity, 1))
}

.bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235/var(--tw-bg-opacity, 1))
}

.bg-gray-200\/50 {
    background-color: rgba(229, 231, 235, .5)
}

.bg-gray-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(209 213 219/var(--tw-bg-opacity, 1))
}

.bg-gray-400 {
    --tw-bg-opacity: 1;
    background-color: rgb(156 163 175/var(--tw-bg-opacity, 1))
}

.bg-gray-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251/var(--tw-bg-opacity, 1))
}

.bg-gray-50\/80 {
    background-color: rgba(249, 250, 251, .8)
}

.bg-gray-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(75 85 99/var(--tw-bg-opacity, 1))
}

.bg-nmt-100 {
    background-color: var(--color-nmt-100, #b3b7c1)
}

.bg-nmt-300 {
    background-color: var(--color-nmt-300, #b3b7c1)
}

.bg-nmt-50 {
    background-color: var(--color-nmt-50, #b3b7c1)
}

.bg-nmt-500 {
    background-color: var(--color-nmt-500, #b3b7c1)
}

.bg-nmt-600 {
    background-color: var(--color-nmt-600, #b3b7c1)
}

.bg-red-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 242 242/var(--tw-bg-opacity, 1))
}

.bg-transparent {
    background-color: transparent
}

.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1))
}

.bg-white\/20 {
    background-color: hsla(0, 0%, 100%, .2)
}

.bg-white\/70 {
    background-color: hsla(0, 0%, 100%, .7)
}

.bg-white\/80 {
    background-color: hsla(0, 0%, 100%, .8)
}

.bg-opacity-20 {
    --tw-bg-opacity: 0.2
}

.bg-\[linear-gradient\(90deg\2c _transparent\2c _rgba\(0\2c _0\2c _0\2c _\.4\)\2c _transparent\)\] {
    background-image: linear-gradient(90deg, transparent, rgba(0, 0, 0, .4), transparent)
}

.bg-\[linear-gradient\(to_bottom\2c _transparent_50\%\2c _\#fb7740_50\%\)\] {
    background-image: linear-gradient(180deg, transparent 50%, #fb7740 0)
}

.bg-\[linear-gradient\(to_right\2c _transparent_50\%\2c _\#d2d3d3_50\%\)\] {
    background-image: linear-gradient(90deg, transparent 50%, #d2d3d3 0)
}

.bg-\[linear-gradient\(to_right\2c _transparent_50\%\2c _\#fb7740_50\%\)\] {
    background-image: linear-gradient(90deg, transparent 50%, #fb7740 0)
}

.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops))
}

.bg-gradient-to-l {
    background-image: linear-gradient(to left, var(--tw-gradient-stops))
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops))
}

.bg-gradient-to-tr {
    background-image: linear-gradient(to top right, var(--tw-gradient-stops))
}

.from-nmt-300 {
    --tw-gradient-from: var(--color-nmt-300, #b3b7c1) var(--tw-gradient-from-position);
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-nmt-400 {
    --tw-gradient-from: var(--color-nmt-400, #b3b7c1) var(--tw-gradient-from-position);
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-nmt-500 {
    --tw-gradient-from: var(--color-nmt-500, #b3b7c1) var(--tw-gradient-from-position);
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-nmt-600 {
    --tw-gradient-from: var(--color-nmt-600, #b3b7c1) var(--tw-gradient-from-position);
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-nmt-700 {
    --tw-gradient-from: var(--color-nmt-700, #b3b7c1) var(--tw-gradient-from-position);
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.via-nmt-300 {
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--color-nmt-300, #b3b7c1) var(--tw-gradient-via-position), var(--tw-gradient-to)
}

.via-nmt-400 {
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--color-nmt-400, #b3b7c1) var(--tw-gradient-via-position), var(--tw-gradient-to)
}

.via-nmt-600 {
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--color-nmt-600, #b3b7c1) var(--tw-gradient-via-position), var(--tw-gradient-to)
}

.via-nmt-700 {
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--color-nmt-700, #b3b7c1) var(--tw-gradient-via-position), var(--tw-gradient-to)
}

.to-nmt-300 {
    --tw-gradient-to: var(--color-nmt-300, #b3b7c1) var(--tw-gradient-to-position)
}

.to-nmt-400 {
    --tw-gradient-to: var(--color-nmt-400, #b3b7c1) var(--tw-gradient-to-position)
}

.to-nmt-600 {
    --tw-gradient-to: var(--color-nmt-600, #b3b7c1) var(--tw-gradient-to-position)
}

.to-red-500 {
    --tw-gradient-to: #ef4444 var(--tw-gradient-to-position)
}

.bg-\[length\:100\%_10px\] {
    background-size: 100% 10px
}

.bg-\[length\:10px_100\%\] {
    background-size: 10px 100%
}

.bg-\[100\%_10px\] {
    background-position: 100% 10px
}

.fill-gray-500 {
    fill: #6b7280
}

.fill-gray-800 {
    fill: #1f2937
}

.fill-nmt-600 {
    fill: var(--color-nmt-600, #b3b7c1)
}

.fill-white {
    fill: #fff
}

.p-0 {
    padding: 0
}

.p-1 {
    padding: .25rem
}

.p-2 {
    padding: .5rem
}

.p-2\.5 {
    padding: .625rem
}

.p-3 {
    padding: .75rem
}

.p-4 {
    padding: 1rem
}

.p-8 {
    padding: 2rem
}

.px-1 {
    padding-left: .25rem;
    padding-right: .25rem
}

.px-1\.5 {
    padding-left: .375rem;
    padding-right: .375rem
}

.px-2 {
    padding-left: .5rem;
    padding-right: .5rem
}

.px-3 {
    padding-left: .75rem;
    padding-right: .75rem
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem
}

.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem
}

.py-1 {
    padding-bottom: .25rem;
    padding-top: .25rem
}

.py-1\.5 {
    padding-bottom: .375rem;
    padding-top: .375rem
}

.py-2 {
    padding-bottom: .5rem;
    padding-top: .5rem
}

.py-2\.5 {
    padding-bottom: .625rem;
    padding-top: .625rem
}

.py-3 {
    padding-bottom: .75rem;
    padding-top: .75rem
}

.py-4 {
    padding-bottom: 1rem;
    padding-top: 1rem
}

.py-6 {
    padding-bottom: 1.5rem;
    padding-top: 1.5rem
}

.py-8 {
    padding-bottom: 2rem;
    padding-top: 2rem
}

.py-\[2px\] {
    padding-bottom: 2px;
    padding-top: 2px
}

.py-\[5\.5px\] {
    padding-bottom: 5.5px;
    padding-top: 5.5px
}

.pb-2 {
    padding-bottom: .5rem
}

.pb-2\.5 {
    padding-bottom: .625rem
}

.pb-3 {
    padding-bottom: .75rem
}

.pb-4 {
    padding-bottom: 1rem
}

.pb-6 {
    padding-bottom: 1.5rem
}

.pb-7 {
    padding-bottom: 1.75rem
}

.pb-8 {
    padding-bottom: 2rem
}

.pe-1\.5 {
    padding-inline-end: .375rem
}

.pe-2 {
    padding-inline-end: .5rem
}

.pe-2\.5 {
    padding-inline-end: .625rem
}

.pe-4 {
    padding-inline-end: 1rem
}

.pe-6 {
    padding-inline-end: 1.5rem
}

.pe-7 {
    padding-inline-end: 1.75rem
}

.pl-1 {
    padding-left: .25rem
}

.pl-2 {
    padding-left: .5rem
}

.pl-5 {
    padding-left: 1.25rem
}

.pl-6 {
    padding-left: 1.5rem
}

.pl-8 {
    padding-left: 2rem
}

.pr-0 {
    padding-right: 0
}

.pr-1 {
    padding-right: .25rem
}

.ps-10 {
    padding-inline-start: 2.5rem
}

.ps-2 {
    padding-inline-start: .5rem
}

.ps-3\.5 {
    padding-inline-start: .875rem
}

.ps-4 {
    padding-inline-start: 1rem
}

.ps-5 {
    padding-inline-start: 1.25rem
}

.ps-\[80px\] {
    padding-inline-start: 80px
}

.pt-2 {
    padding-top: .5rem
}

.pt-20 {
    padding-top: 5rem
}

.pt-3 {
    padding-top: .75rem
}

.pt-4 {
    padding-top: 1rem
}

.pt-5 {
    padding-top: 1.25rem
}

.pt-6 {
    padding-top: 1.5rem
}

.text-left {
    text-align: left
}

.text-center {
    text-align: center
}

.text-right {
    text-align: right
}

.text-start {
    text-align: start
}

.text-end {
    text-align: end
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem
}

.text-\[10px\] {
    font-size: 10px
}

.text-\[15px\] {
    font-size: 15px
}

.text-\[16px\] {
    font-size: 16px
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem
}

.text-sm {
    font-size: .875rem;
    line-height: 1.25rem
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem
}

.text-xs {
    font-size: .75rem;
    line-height: 1rem
}

.font-bold {
    font-weight: 700
}

.font-extrabold {
    font-weight: 800
}

.font-light {
    font-weight: 300
}

.font-medium {
    font-weight: 500
}

.font-normal {
    font-weight: 400
}

.font-semibold {
    font-weight: 600
}

.uppercase {
    text-transform: uppercase
}

.italic {
    font-style: italic
}

.leading-\[18px\] {
    line-height: 18px
}

.leading-\[24px\] {
    line-height: 24px
}

.leading-\[26px\] {
    line-height: 26px
}

.leading-tight {
    line-height: 1.25
}

.tracking-\[0px\] {
    letter-spacing: 0
}

.tracking-tight {
    letter-spacing: -.025em
}

.tracking-wide {
    letter-spacing: .025em
}

.\!text-red-500 {
    --tw-text-opacity: 1 !important;
    color: rgb(239 68 68/var(--tw-text-opacity, 1)) !important
}

.\!text-white {
    --tw-text-opacity: 1 !important;
    color: rgb(255 255 255/var(--tw-text-opacity, 1)) !important
}

.text-\[\#0f294d\] {
    --tw-text-opacity: 1;
    color: rgb(15 41 77/var(--tw-text-opacity, 1))
}

.text-\[\#8592a6\] {
    --tw-text-opacity: 1;
    color: rgb(133 146 166/var(--tw-text-opacity, 1))
}

.text-\[\#acb4bf\] {
    --tw-text-opacity: 1;
    color: rgb(172 180 191/var(--tw-text-opacity, 1))
}

.text-black {
    --tw-text-opacity: 1;
    color: rgb(0 0 0/var(--tw-text-opacity, 1))
}

.text-blue-500 {
    --tw-text-opacity: 1;
    color: rgb(59 130 246/var(--tw-text-opacity, 1))
}

.text-blue-600 {
    --tw-text-opacity: 1;
    color: rgb(37 99 235/var(--tw-text-opacity, 1))
}

.text-gray-300 {
    --tw-text-opacity: 1;
    color: rgb(209 213 219/var(--tw-text-opacity, 1))
}

.text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175/var(--tw-text-opacity, 1))
}

.text-gray-50 {
    --tw-text-opacity: 1;
    color: rgb(249 250 251/var(--tw-text-opacity, 1))
}

.text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128/var(--tw-text-opacity, 1))
}

.text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99/var(--tw-text-opacity, 1))
}

.text-gray-700 {
    --tw-text-opacity: 1;
    color: rgb(55 65 81/var(--tw-text-opacity, 1))
}

.text-gray-800 {
    --tw-text-opacity: 1;
    color: rgb(31 41 55/var(--tw-text-opacity, 1))
}

.text-gray-800\/80 {
    color: rgba(31, 41, 55, .8)
}

.text-gray-900 {
    --tw-text-opacity: 1;
    color: rgb(17 24 39/var(--tw-text-opacity, 1))
}

.text-green-600 {
    --tw-text-opacity: 1;
    color: rgb(22 163 74/var(--tw-text-opacity, 1))
}

.text-nmt-500 {
    color: var(--color-nmt-500, #b3b7c1)
}

.text-nmt-600 {
    color: var(--color-nmt-600, #b3b7c1)
}

.text-nmt-700 {
    color: var(--color-nmt-700, #b3b7c1)
}

.text-nmt-800 {
    color: var(--color-nmt-800, #b3b7c1)
}

.text-nmt-900 {
    color: var(--color-nmt-900, #b3b7c1)
}

.text-red-500 {
    --tw-text-opacity: 1;
    color: rgb(239 68 68/var(--tw-text-opacity, 1))
}

.text-red-600 {
    --tw-text-opacity: 1;
    color: rgb(220 38 38/var(--tw-text-opacity, 1))
}

.text-red-800 {
    --tw-text-opacity: 1;
    color: rgb(153 27 27/var(--tw-text-opacity, 1))
}

.text-red-900 {
    --tw-text-opacity: 1;
    color: rgb(127 29 29/var(--tw-text-opacity, 1))
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

.text-white\/90 {
    color: hsla(0, 0%, 100%, .9)
}

.text-yellow-500 {
    --tw-text-opacity: 1;
    color: rgb(234 179 8/var(--tw-text-opacity, 1))
}

.underline {
    text-decoration-line: underline
}

.placeholder-red-700::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(185 28 28/var(--tw-placeholder-opacity, 1))
}

.placeholder-red-700::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(185 28 28/var(--tw-placeholder-opacity, 1))
}

.accent-nmt-600 {
    accent-color: var(--color-nmt-600, #b3b7c1)
}

.\!opacity-100 {
    opacity: 1 !important
}

.opacity-0 {
    opacity: 0
}

.opacity-100 {
    opacity: 1
}

.opacity-25 {
    opacity: .25
}

.opacity-50 {
    opacity: .5
}

.shadow {
    --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color)
}

.shadow,
.shadow-2xl {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-2xl {
    --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, .25);
    --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color)
}

.shadow-\[0_8px_30px_rgb\(0\2c 0\2c 0\2c 0\.04\)\] {
    --tw-shadow: 0 8px 30px rgb(0, 0, 0, 0.04);
    --tw-shadow-colored: 0 8px 30px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0px_0px_15px_\#cccccc96\2c 0px_0px_15px_\#44444400_inset\] {
    --tw-shadow: 0px 0px 15px #cccccc96, 0px 0px 15px #44444400 inset;
    --tw-shadow-colored: 0px 0px 15px var(--tw-shadow-color), inset 0px 0px 15px var(--tw-shadow-color)
}

.shadow-\[0px_0px_15px_\#cccccc96\2c 0px_0px_15px_\#44444400_inset\],
.shadow-lg {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color)
}

.shadow-md {
    --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color)
}

.shadow-md,
.shadow-sm {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color)
}

.shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-white {
    --tw-shadow-color: #fff;
    --tw-shadow: var(--tw-shadow-colored)
}

.ring-2 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.ring-2,
.ring-4 {
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.ring-4 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.ring-gray-100 {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(243 244 246/var(--tw-ring-opacity, 1))
}

.ring-white {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(255 255 255/var(--tw-ring-opacity, 1))
}

.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.backdrop-blur-lg {
    --tw-backdrop-blur: blur(16px)
}

.backdrop-blur-lg,
.backdrop-blur-sm {
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)
}

.backdrop-blur-sm {
    --tw-backdrop-blur: blur(4px)
}

.transition {
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.transition-all {
    transition-duration: .15s;
    transition-property: all;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.transition-colors {
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.duration-150,
.transition-transform {
    transition-duration: .15s
}

.duration-200 {
    transition-duration: .2s
}

.duration-300 {
    transition-duration: .3s
}

.duration-500 {
    transition-duration: .5s
}

.duration-700 {
    transition-duration: .7s
}

.ease-in-out {
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.dropdown {
    display: inline-block
}

.dropdown-menu {
    --tw-bg-opacity: 1;
    --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
    background-color: rgb(249 249 249/var(--tw-bg-opacity, 1));
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    display: none;
    position: absolute
}

.dropdown.open .dropdown-menu {
    display: block
}

.dropdown-item,
.dropdown-toggle {
    cursor: pointer
}

.dropdown-item:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(221 221 221/var(--tw-bg-opacity, 1))
}

.mask-top-circle-cut {
    -webkit-mask-image: radial-gradient(circle at 50% 0, transparent 50%, #000 51%);
    mask-image: radial-gradient(circle at 50% 0, transparent 50%, #000 51%)
}

.mask-bottom-circle-cut {
    -webkit-mask-image: radial-gradient(circle at 50% 100%, transparent 50%, #000 51%);
    mask-image: radial-gradient(circle at 50% 100%, transparent 50%, #000 51%)
}

@keyframes indeterminate {
    0% {
        transform: translateX(-100%)
    }

    50% {
        transform: translateX(0)
    }

    to {
        transform: translateX(100%)
    }
}

.animate-progress-indeterminate {
    animation: indeterminate 2s infinite
}

@keyframes waveMove {
    0% {
        background-position: 0 0
    }

    25% {
        background-position: 25% 0
    }

    50% {
        background-position: 50% 0
    }

    75% {
        background-position: 75% 0
    }

    to {
        background-position: 100% 0
    }
}

@keyframes waveEffect {
    0% {
        background-position: 0 50%
    }

    50% {
        background-position: 100% 50%
    }

    to {
        background-position: 0 50%
    }
}

.wave-animation {
    animation: waveEffect 2s linear infinite;
    background: linear-gradient(90deg, var(--color-nmt-500), var(--color-nmt-300), #fff);
    background-size: 200% 200%
}

.loader-container {
    align-items: center;
    background-color: rgba(0, 0, 0, .085);
    display: flex;
    height: 100%;
    justify-content: center;
    left: 0;
    opacity: .8;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1099
}

.loader-container img {
    border-radius: 50%;
    height: 100px;
    position: absolute;
    width: 100px;
    z-index: 1100
}

.loader-container .loadidng-vertical {
    --tw-text-opacity: 1;
    border-radius: .5rem;
    color: rgb(255 255 255/var(--tw-text-opacity, 1));
    margin-top: 13rem;
    padding: .25rem .5rem;
    position: absolute;
    z-index: 1110
}

.loader {
    animation: rotate 1s linear infinite;
    border-radius: 50%;
    height: 100px;
    position: absolute;
    width: 100px;
    z-index: 1100
}

.loader:after,
.loader:before {
    animation: prixClipFix 2s linear infinite;
    border: 5px solid #fff;
    border-radius: 50%;
    box-sizing: border-box;
    content: "";
    inset: 0;
    position: absolute
}

.loader:after {
    animation: prixClipFix 2s linear infinite, rotate .5s linear infinite reverse;
    border-color: #ff3d00;
    inset: 6px
}

@keyframes rotate {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
}

@keyframes prixClipFix {
    0% {
        clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0)
    }

    25% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0)
    }

    50% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%)
    }

    75% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%)
    }

    to {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0)
    }
}

.slide-in {
    height: auto;
    opacity: 1;
    transform: translateY(0)
}

.slide-in,
.slide-out {
    transition: height .5s ease-in-out, opacity .5s ease-in-out, transform .5s ease-in-out
}

.slide-out {
    height: 0;
    opacity: 0;
    transform: translateY(100%)
}

.flatpickr-day.endRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.endRange.nextMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.endRange:focus,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected,
.flatpickr-day.selected.inRange,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.selected:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.startRange:focus,
.flatpickr-day.startRange:hover {
    background: var(--color-nmt-400) !important;
    border-color: var(--color-nmt-400) !important
}

.file\:border-0::file-selector-button {
    border-width: 0
}

.file\:bg-transparent::file-selector-button {
    background-color: transparent
}

.file\:text-sm::file-selector-button {
    font-size: .875rem;
    line-height: 1.25rem
}

.file\:font-medium::file-selector-button {
    font-weight: 500
}

.before\:absolute:before {
    content: var(--tw-content);
    position: absolute
}

.before\:start-\[68px\]:before {
    content: var(--tw-content);
    inset-inline-start: 68px
}

.before\:top-0:before {
    content: var(--tw-content);
    top: 0
}

.before\:top-\[10\%\]:before {
    content: var(--tw-content);
    top: 10%
}

.before\:top-\[5\%\]:before {
    content: var(--tw-content);
    top: 5%
}

.before\:inline-block:before {
    content: var(--tw-content);
    display: inline-block
}

.before\:h-1\.5:before {
    content: var(--tw-content);
    height: .375rem
}

.before\:h-\[30\%\]:before {
    content: var(--tw-content);
    height: 30%
}

.before\:h-\[32\%\]:before {
    content: var(--tw-content);
    height: 32%
}

.before\:h-\[80\%\]:before {
    content: var(--tw-content);
    height: 80%
}

.before\:w-0:before {
    content: var(--tw-content);
    width: 0
}

.before\:w-1\.5:before {
    content: var(--tw-content);
    width: .375rem
}

.before\:w-\[4px\]:before {
    content: var(--tw-content);
    width: 4px
}

.before\:rounded-\[2px\]:before {
    border-radius: 2px;
    content: var(--tw-content)
}

.before\:border-2:before {
    border-width: 2px;
    content: var(--tw-content)
}

.before\:border-l-\[4px\]:before {
    border-left-width: 4px;
    content: var(--tw-content)
}

.before\:border-dotted:before {
    border-style: dotted;
    content: var(--tw-content)
}

.before\:border-\[\#dadfe6\]:before {
    --tw-border-opacity: 1;
    border-color: rgb(218 223 230/var(--tw-border-opacity, 1));
    content: var(--tw-content)
}

.before\:bg-\[\#dadfe6\]:before {
    --tw-bg-opacity: 1;
    background-color: rgb(218 223 230/var(--tw-bg-opacity, 1));
    content: var(--tw-content)
}

.before\:bg-white:before {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1));
    content: var(--tw-content)
}

.before\:content-\[\'\'\]:before {
    --tw-content: "";
    content: var(--tw-content)
}

.after\:absolute:after {
    content: var(--tw-content);
    position: absolute
}

.after\:bottom-\[5\%\]:after {
    bottom: 5%;
    content: var(--tw-content)
}

.after\:start-\[68px\]:after {
    content: var(--tw-content);
    inset-inline-start: 68px
}

.after\:top-\[70\%\]:after {
    content: var(--tw-content);
    top: 70%
}

.after\:inline-block:after {
    content: var(--tw-content);
    display: inline-block
}

.after\:h-\[30\%\]:after {
    content: var(--tw-content);
    height: 30%
}

.after\:h-\[35\%\]:after {
    content: var(--tw-content);
    height: 35%
}

.after\:w-0:after {
    content: var(--tw-content);
    width: 0
}

.after\:w-\[4px\]:after {
    content: var(--tw-content);
    width: 4px
}

.after\:rounded-\[2px\]:after {
    border-radius: 2px;
    content: var(--tw-content)
}

.after\:border-l-\[4px\]:after {
    border-left-width: 4px;
    content: var(--tw-content)
}

.after\:border-dotted:after {
    border-style: dotted;
    content: var(--tw-content)
}

.after\:border-\[\#dadfe6\]:after {
    --tw-border-opacity: 1;
    border-color: rgb(218 223 230/var(--tw-border-opacity, 1));
    content: var(--tw-content)
}

.after\:bg-\[\#dadfe6\]:after {
    --tw-bg-opacity: 1;
    background-color: rgb(218 223 230/var(--tw-bg-opacity, 1));
    content: var(--tw-content)
}

.after\:content-\[\'\'\]:after {
    --tw-content: "";
    content: var(--tw-content)
}

.odd\:bg-white:nth-child(odd) {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1))
}

.even\:h-6:nth-child(2n) {
    height: 1.5rem
}

.even\:bg-gray-50:nth-child(2n) {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251/var(--tw-bg-opacity, 1))
}

.even\:bg-gray-500:nth-child(2n) {
    --tw-bg-opacity: 1;
    background-color: rgb(107 114 128/var(--tw-bg-opacity, 1))
}

.hover\:cursor-pointer:hover {
    cursor: pointer
}

.hover\:rounded-full:hover {
    border-radius: 9999px
}

.hover\:border-gray-300:hover {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity, 1))
}

.hover\:bg-gray-200:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235/var(--tw-bg-opacity, 1))
}

.hover\:bg-gray-50:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251/var(--tw-bg-opacity, 1))
}

.hover\:bg-nmt-100:hover {
    background-color: var(--color-nmt-100, #b3b7c1)
}

.hover\:bg-nmt-400:hover {
    background-color: var(--color-nmt-400, #b3b7c1)
}

.hover\:bg-nmt-600:hover {
    background-color: var(--color-nmt-600, #b3b7c1)
}

.hover\:bg-gradient-to-bl:hover {
    background-image: linear-gradient(to bottom left, var(--tw-gradient-stops))
}

.hover\:from-nmt-300:hover {
    --tw-gradient-from: var(--color-nmt-300, #b3b7c1) var(--tw-gradient-from-position);
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.hover\:from-nmt-600:hover {
    --tw-gradient-from: var(--color-nmt-600, #b3b7c1) var(--tw-gradient-from-position);
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.hover\:via-nmt-500:hover {
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--color-nmt-500, #b3b7c1) var(--tw-gradient-via-position), var(--tw-gradient-to)
}

.hover\:to-nmt-300:hover {
    --tw-gradient-to: var(--color-nmt-300, #b3b7c1) var(--tw-gradient-to-position)
}

.hover\:to-red-600:hover {
    --tw-gradient-to: #dc2626 var(--tw-gradient-to-position)
}

.hover\:text-nmt-800:hover {
    color: var(--color-nmt-800, #b3b7c1)
}

.hover\:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

.hover\:underline:hover {
    text-decoration-line: underline
}

.hover\:opacity-90:hover {
    opacity: .9
}

.hover\:shadow-lg:hover {
    --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color)
}

.hover\:shadow-lg:hover,
.hover\:shadow-md:hover {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.hover\:shadow-md:hover {
    --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color)
}

.hover\:shadow-xl:hover {
    --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.hover\:shadow-white:hover {
    --tw-shadow-color: #fff;
    --tw-shadow: var(--tw-shadow-colored)
}

.hover\:ring-2:hover {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.hover\:ring-nmt-300:hover {
    --tw-ring-color: var(--color-nmt-300, #b3b7c1)
}

.focus\:border:focus {
    border-width: 1px
}

.focus\:border-none:focus {
    border-style: none
}

.focus\:border-nmt-500:focus {
    border-color: var(--color-nmt-500, #b3b7c1)
}

.focus\:border-nmt-600:focus {
    border-color: var(--color-nmt-600, #b3b7c1)
}

.focus\:border-red-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(239 68 68/var(--tw-border-opacity, 1))
}

.focus\:shadow-none:focus {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus\:outline-nmt-200:focus {
    outline-color: var(--color-nmt-200, #b3b7c1)
}

.focus\:ring-0:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.focus\:ring-0:focus,
.focus\:ring-2:focus {
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.focus\:ring-4:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus\:ring-blue-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(59 130 246/var(--tw-ring-opacity, 1))
}

.focus\:ring-gray-100:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(243 244 246/var(--tw-ring-opacity, 1))
}

.focus\:ring-nmt-300:focus {
    --tw-ring-color: var(--color-nmt-300, #b3b7c1)
}

.focus\:ring-nmt-500:focus {
    --tw-ring-color: var(--color-nmt-500, #b3b7c1)
}

.focus\:ring-nmt-600:focus {
    --tw-ring-color: var(--color-nmt-600, #b3b7c1)
}

.focus\:ring-red-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(239 68 68/var(--tw-ring-opacity, 1))
}

.focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px
}

.focus-visible\:outline-none:focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus-visible\:ring-2:focus-visible {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-visible\:ring-offset-2:focus-visible {
    --tw-ring-offset-width: 2px
}

.disabled\:pointer-events-none:disabled {
    pointer-events: none
}

.disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed
}

.disabled\:opacity-50:disabled {
    opacity: .5
}

.group:hover .group-hover\:-translate-y-1 {
    --tw-translate-y: -0.25rem
}

.group:hover .group-hover\:-translate-y-1,
.group:hover .group-hover\:scale-105 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:hover .group-hover\:scale-105 {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05
}

.group:hover .group-hover\:transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

@keyframes spin {
    to {
        transform: rotate(1turn)
    }
}

.group:hover .group-hover\:animate-spin {
    animation: spin 1s linear infinite
}

.group:hover .group-hover\:border {
    border-width: 1px
}

.group:hover .group-hover\:border-nmt-500 {
    border-color: var(--color-nmt-500, #b3b7c1)
}

.group:hover .group-hover\:bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235/var(--tw-bg-opacity, 1))
}

.group:hover .group-hover\:bg-nmt-500 {
    background-color: var(--color-nmt-500, #b3b7c1)
}

.group:hover .group-hover\:fill-white {
    fill: #fff
}

.group:hover .group-hover\:text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

.group:hover .group-hover\:shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.group:hover .group-hover\:transition-all {
    transition-duration: .15s;
    transition-property: all;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.group:hover .group-hover\:duration-300 {
    transition-duration: .3s
}

.group:hover .group-hover\:ease-in-out {
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.group-selected .group-\[-selected\]\:border-\[1px\] {
    border-width: 1px
}

.group-selected .group-\[-selected\]\:border-l-0 {
    border-left-width: 0
}

.group-selected .group-\[-selected\]\:border-r-0 {
    border-right-width: 0
}

.group-selected .group-\[-selected\]\:border-nmt-500 {
    border-color: var(--color-nmt-500, #b3b7c1)
}

.group-selected .group-\[-selected\]\:bg-nmt-500 {
    background-color: var(--color-nmt-500, #b3b7c1)
}

.group-selected .group-\[-selected\]\:text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

.group-selected .group-\[-selected\]\:shadow-\[0px_0px_5px_nmt-500\2c 0px_0px_5px_nmt-500\] {
    --tw-shadow: 0px 0px 5px nmt-500, 0px 0px 5px nmt-500;
    --tw-shadow-colored: 0px 0px 5px var(--tw-shadow-color), 0px 0px 5px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.groupselected .group-\[selected\]\:shadow-\[0px_0px_15px_var\(--color-nmt-500\)\] {
    --tw-shadow: 0px 0px 15px var(--color-nmt-500);
    --tw-shadow-colored: 0px 0px 15px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.dark\:border-gray-600:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99/var(--tw-border-opacity, 1))
}

.dark\:border-gray-700:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(55 65 81/var(--tw-border-opacity, 1))
}

.dark\:bg-gray-600:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(75 85 99/var(--tw-bg-opacity, 1))
}

.dark\:bg-gray-700:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81/var(--tw-bg-opacity, 1))
}

.dark\:bg-gray-800:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55/var(--tw-bg-opacity, 1))
}

.dark\:bg-opacity-80:is(.dark *) {
    --tw-bg-opacity: 0.8
}

.dark\:text-gray-300:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(209 213 219/var(--tw-text-opacity, 1))
}

.dark\:text-gray-400:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(156 163 175/var(--tw-text-opacity, 1))
}

.dark\:text-red-400:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(248 113 113/var(--tw-text-opacity, 1))
}

.dark\:text-white:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

.dark\:placeholder-gray-400:is(.dark *)::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(156 163 175/var(--tw-placeholder-opacity, 1))
}

.dark\:placeholder-gray-400:is(.dark *)::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(156 163 175/var(--tw-placeholder-opacity, 1))
}

.dark\:shadow-lg:is(.dark *) {
    --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.dark\:ring-offset-gray-800:is(.dark *) {
    --tw-ring-offset-color: #1f2937
}

.odd\:dark\:bg-gray-900:is(.dark *):nth-child(odd) {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39/var(--tw-bg-opacity, 1))
}

.even\:dark\:bg-gray-800:is(.dark *):nth-child(2n) {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55/var(--tw-bg-opacity, 1))
}

.dark\:hover\:bg-gray-600:hover:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(75 85 99/var(--tw-bg-opacity, 1))
}

.dark\:focus\:border-nmt-500:focus:is(.dark *) {
    border-color: var(--color-nmt-500, #b3b7c1)
}

.dark\:focus\:ring-blue-600:focus:is(.dark *) {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(37 99 235/var(--tw-ring-opacity, 1))
}

.dark\:focus\:ring-nmt-500:focus:is(.dark *) {
    --tw-ring-color: var(--color-nmt-500, #b3b7c1)
}

.dark\:focus\:ring-nmt-600:focus:is(.dark *) {
    --tw-ring-color: var(--color-nmt-600, #b3b7c1)
}

.dark\:focus\:ring-nmt-800:focus:is(.dark *) {
    --tw-ring-color: var(--color-nmt-800, #b3b7c1)
}

@media not all and (min-width:768px) {
    .max-md\:fixed {
        position: fixed
    }

    .max-md\:order-1 {
        order: 1
    }

    .max-md\:order-2 {
        order: 2
    }

    .max-md\:ml-2 {
        margin-left: .5rem
    }

    .max-md\:mt-4 {
        margin-top: 1rem
    }

    .max-md\:flex {
        display: flex
    }

    .max-md\:hidden {
        display: none
    }

    .max-md\:h-fit {
        height: -moz-fit-content;
        height: fit-content
    }

    .max-md\:min-w-36 {
        min-width: 9rem
    }

    .max-md\:flex-col {
        flex-direction: column
    }

    .max-md\:justify-center {
        justify-content: center
    }

    .max-md\:justify-between {
        justify-content: space-between
    }

    .max-md\:space-x-2>:not([hidden])~:not([hidden]) {
        --tw-space-x-reverse: 0;
        margin-left: calc(.5rem*(1 - var(--tw-space-x-reverse)));
        margin-right: calc(.5rem*var(--tw-space-x-reverse))
    }

    .max-md\:overflow-x-auto {
        overflow-x: auto
    }

    .max-md\:overflow-y-hidden {
        overflow-y: hidden
    }

    .max-md\:overflow-x-scroll {
        overflow-x: scroll
    }

    .max-md\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem
    }

    .max-md\:pb-2 {
        padding-bottom: .5rem
    }

    .max-md\:pb-\[10px\] {
        padding-bottom: 10px
    }
}

@media (min-width:640px) {
    .sm\:rounded-lg {
        border-radius: .5rem
    }
}

@media (min-width:768px) {
    .md\:sticky {
        position: sticky
    }

    .md\:top-0 {
        top: 0
    }

    .md\:top-11 {
        top: 2.75rem
    }

    .md\:col-span-1 {
        grid-column: span 1/span 1
    }

    .md\:col-span-2 {
        grid-column: span 2/span 2
    }

    .md\:col-span-3 {
        grid-column: span 3/span 3
    }

    .md\:col-span-9 {
        grid-column: span 9/span 9
    }

    .md\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem
    }

    .md\:mb-10 {
        margin-bottom: 2.5rem
    }

    .md\:mt-4 {
        margin-top: 1rem
    }

    .md\:block {
        display: block
    }

    .md\:flex {
        display: flex
    }

    .md\:hidden {
        display: none
    }

    .md\:h-12 {
        height: 3rem
    }

    .md\:h-\[25px\] {
        height: 25px
    }

    .md\:w-1\/3 {
        width: 33.333333%
    }

    .md\:w-24 {
        width: 6rem
    }

    .md\:w-fit {
        width: -moz-fit-content;
        width: fit-content
    }

    .md\:w-full {
        width: 100%
    }

    .md\:min-w-28 {
        min-width: 7rem
    }

    .md\:min-w-40 {
        min-width: 10rem
    }

    .md\:min-w-44 {
        min-width: 11rem
    }

    .md\:min-w-\[230px\] {
        min-width: 230px
    }

    .md\:max-w-2xl {
        max-width: 42rem
    }

    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr))
    }

    .md\:grid-cols-5 {
        grid-template-columns: repeat(5, minmax(0, 1fr))
    }

    .md\:gap-8 {
        gap: 2rem
    }

    .md\:whitespace-nowrap {
        white-space: nowrap
    }

    .md\:p-4 {
        padding: 1rem
    }

    .md\:p-5 {
        padding: 1.25rem
    }

    .md\:p-6 {
        padding: 1.5rem
    }

    .md\:p-8 {
        padding: 2rem
    }

    .md\:px-10 {
        padding-left: 2.5rem;
        padding-right: 2.5rem
    }

    .md\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem
    }

    .md\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem
    }

    .md\:px-5 {
        padding-left: 1.25rem;
        padding-right: 1.25rem
    }

    .md\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem
    }

    .md\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .md\:py-3 {
        padding-bottom: .75rem;
        padding-top: .75rem
    }

    .md\:py-4 {
        padding-bottom: 1rem;
        padding-top: 1rem
    }

    .md\:pb-6 {
        padding-bottom: 1.5rem
    }

    .md\:pe-12 {
        padding-inline-end: 3rem
    }

    .md\:ps-6 {
        padding-inline-start: 1.5rem
    }

    .md\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .md\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem
    }

    .md\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }

    .md\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }

    .md\:text-sm {
        font-size: .875rem;
        line-height: 1.25rem
    }

    .md\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem
    }

    .md\:text-xs {
        font-size: .75rem;
        line-height: 1rem
    }
}

@media (min-width:1024px) {
    .lg\:w-1\/4 {
        width: 25%
    }

    .lg\:w-full {
        width: 100%
    }

    .lg\:min-w-40 {
        min-width: 10rem
    }

    .lg\:min-w-60 {
        min-width: 15rem
    }

    .lg\:min-w-\[156px\] {
        min-width: 156px
    }

    .lg\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem
    }
}

.rtl\:text-right:where([dir=rtl], [dir=rtl] *) {
    text-align: right
}

.flatpickr-calendar {
    -webkit-animation: none;
    animation: none;
    background: transparent;
    background: #fff;
    border: 0;
    border-radius: 5px;
    -webkit-box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, .08);
    box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, .08);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    direction: ltr;
    display: none;
    font-size: 14px;
    line-height: 24px;
    opacity: 0;
    padding: 0;
    position: absolute;
    text-align: center;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    visibility: hidden;
    width: 307.875px
}

.flatpickr-calendar.inline,
.flatpickr-calendar.open {
    max-height: 640px;
    opacity: 1;
    visibility: visible
}

.flatpickr-calendar.open {
    display: inline-block;
    z-index: 99999
}

.flatpickr-calendar.animate.open {
    -webkit-animation: fpFadeInDown .3s cubic-bezier(.23, 1, .32, 1);
    animation: fpFadeInDown .3s cubic-bezier(.23, 1, .32, 1)
}

.flatpickr-calendar.inline {
    display: block;
    position: relative;
    top: 2px
}

.flatpickr-calendar.static {
    position: absolute;
    top: calc(100% + 2px)
}

.flatpickr-calendar.static.open {
    display: block;
    z-index: 999
}

.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7) {
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1) {
    -webkit-box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
    box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6
}

.flatpickr-calendar .hasTime .dayContainer,
.flatpickr-calendar .hasWeeks .dayContainer {
    border-bottom: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.flatpickr-calendar .hasWeeks .dayContainer {
    border-left: 0
}

.flatpickr-calendar.hasTime .flatpickr-time {
    border-top: 1px solid #e6e6e6;
    height: 40px
}

.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
    height: auto
}

.flatpickr-calendar:after,
.flatpickr-calendar:before {
    border: solid transparent;
    content: "";
    display: block;
    height: 0;
    left: 22px;
    pointer-events: none;
    position: absolute;
    width: 0
}

.flatpickr-calendar.arrowRight:after,
.flatpickr-calendar.arrowRight:before,
.flatpickr-calendar.rightMost:after,
.flatpickr-calendar.rightMost:before {
    left: auto;
    right: 22px
}

.flatpickr-calendar.arrowCenter:after,
.flatpickr-calendar.arrowCenter:before {
    left: 50%;
    right: 50%
}

.flatpickr-calendar:before {
    border-width: 5px;
    margin: 0 -5px
}

.flatpickr-calendar:after {
    border-width: 4px;
    margin: 0 -4px
}

.flatpickr-calendar.arrowTop:after,
.flatpickr-calendar.arrowTop:before {
    bottom: 100%
}

.flatpickr-calendar.arrowTop:before {
    border-bottom-color: #e6e6e6
}

.flatpickr-calendar.arrowTop:after {
    border-bottom-color: #fff
}

.flatpickr-calendar.arrowBottom:after,
.flatpickr-calendar.arrowBottom:before {
    top: 100%
}

.flatpickr-calendar.arrowBottom:before {
    border-top-color: #e6e6e6
}

.flatpickr-calendar.arrowBottom:after {
    border-top-color: #fff
}

.flatpickr-calendar:focus {
    outline: 0
}

.flatpickr-wrapper {
    display: inline-block;
    position: relative
}

.flatpickr-months {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

.flatpickr-months .flatpickr-month {
    -webkit-box-flex: 1;
    background: transparent;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    line-height: 1;
    overflow: hidden;
    position: relative;
    text-align: center
}

.flatpickr-months .flatpickr-month,
.flatpickr-months .flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month {
    fill: rgba(0, 0, 0, .9);
    color: rgba(0, 0, 0, .9);
    height: 34px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.flatpickr-months .flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month {
    cursor: pointer;
    padding: 10px;
    position: absolute;
    text-decoration: none;
    top: 0;
    z-index: 3
}

.flatpickr-months .flatpickr-next-month.flatpickr-disabled,
.flatpickr-months .flatpickr-prev-month.flatpickr-disabled {
    display: none
}

.flatpickr-months .flatpickr-next-month i,
.flatpickr-months .flatpickr-prev-month i {
    position: relative
}

.flatpickr-months .flatpickr-next-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month {
    left: 0
}

.flatpickr-months .flatpickr-next-month.flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month {
    right: 0
}

.flatpickr-months .flatpickr-next-month:hover,
.flatpickr-months .flatpickr-prev-month:hover {
    color: #959ea9
}

.flatpickr-months .flatpickr-next-month:hover svg,
.flatpickr-months .flatpickr-prev-month:hover svg {
    fill: #f64747
}

.flatpickr-months .flatpickr-next-month svg,
.flatpickr-months .flatpickr-prev-month svg {
    height: 14px;
    width: 14px
}

.flatpickr-months .flatpickr-next-month svg path,
.flatpickr-months .flatpickr-prev-month svg path {
    fill: inherit;
    -webkit-transition: fill .1s;
    transition: fill .1s
}

.numInputWrapper {
    height: auto;
    position: relative
}

.numInputWrapper input,
.numInputWrapper span {
    display: inline-block
}

.numInputWrapper input {
    width: 100%
}

.numInputWrapper input::-ms-clear {
    display: none
}

.numInputWrapper input::-webkit-inner-spin-button,
.numInputWrapper input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

.numInputWrapper span {
    border: 1px solid rgba(57, 57, 57, .15);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
    height: 50%;
    line-height: 50%;
    opacity: 0;
    padding: 0 4px 0 2px;
    position: absolute;
    right: 0;
    width: 14px
}

.numInputWrapper span:hover {
    background: rgba(0, 0, 0, .1)
}

.numInputWrapper span:active {
    background: rgba(0, 0, 0, .2)
}

.numInputWrapper span:after {
    content: "";
    display: block;
    position: absolute
}

.numInputWrapper span.arrowUp {
    border-bottom: 0;
    top: 0
}

.numInputWrapper span.arrowUp:after {
    border-bottom: 4px solid rgba(57, 57, 57, .6);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    top: 26%
}

.numInputWrapper span.arrowDown {
    top: 50%
}

.numInputWrapper span.arrowDown:after {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid rgba(57, 57, 57, .6);
    top: 40%
}

.numInputWrapper span svg {
    height: auto;
    width: inherit
}

.numInputWrapper span svg path {
    fill: rgba(0, 0, 0, .5)
}

.numInputWrapper:hover {
    background: rgba(0, 0, 0, .05)
}

.numInputWrapper:hover span {
    opacity: 1
}

.flatpickr-current-month {
    color: inherit;
    display: inline-block;
    font-size: 135%;
    font-weight: 300;
    height: 34px;
    left: 12.5%;
    line-height: inherit;
    line-height: 1;
    padding: 7.48px 0 0;
    position: absolute;
    text-align: center;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    width: 75%
}

.flatpickr-current-month span.cur-month {
    color: inherit;
    display: inline-block;
    font-family: inherit;
    font-weight: 700;
    margin-left: .5ch;
    padding: 0
}

.flatpickr-current-month span.cur-month:hover {
    background: rgba(0, 0, 0, .05)
}

.flatpickr-current-month .numInputWrapper {
    display: inline-block;
    width: 6ch;
    width: 7ch\0
}

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
    border-bottom-color: rgba(0, 0, 0, .9)
}

.flatpickr-current-month .numInputWrapper span.arrowDown:after {
    border-top-color: rgba(0, 0, 0, .9)
}

.flatpickr-current-month input.cur-year {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield;
    background: transparent;
    border: 0;
    border-radius: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: inherit;
    cursor: text;
    display: inline-block;
    font-family: inherit;
    font-size: inherit;
    font-weight: 300;
    height: auto;
    line-height: inherit;
    margin: 0;
    padding: 0 0 0 .5ch;
    vertical-align: initial
}

.flatpickr-current-month input.cur-year:focus {
    outline: 0
}

.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
    background: transparent;
    color: rgba(0, 0, 0, .5);
    font-size: 100%;
    pointer-events: none
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
    appearance: menulist;
    -webkit-appearance: menulist;
    -moz-appearance: menulist;
    background: transparent;
    border: none;
    border-radius: 0;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    color: inherit;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    font-weight: 300;
    height: auto;
    line-height: inherit;
    margin: -1px 0 0;
    outline: none;
    padding: 0 0 0 .5ch;
    position: relative;
    vertical-align: initial;
    width: auto
}

.flatpickr-current-month .flatpickr-monthDropdown-months:active,
.flatpickr-current-month .flatpickr-monthDropdown-months:focus {
    outline: none
}

.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
    background: rgba(0, 0, 0, .05)
}

.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
    background-color: transparent;
    outline: none;
    padding: 0
}

.flatpickr-weekdays {
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
    background: transparent;
    height: 28px;
    overflow: hidden;
    text-align: center;
    width: 100%
}

.flatpickr-weekdays,
.flatpickr-weekdays .flatpickr-weekdaycontainer {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

.flatpickr-weekdays .flatpickr-weekdaycontainer,
span.flatpickr-weekday {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1
}

span.flatpickr-weekday {
    background: transparent;
    color: rgba(0, 0, 0, .54);
    cursor: default;
    display: block;
    font-size: 90%;
    font-weight: bolder;
    line-height: 1;
    margin: 0;
    text-align: center
}

.dayContainer,
.flatpickr-weeks {
    padding: 1px 0 0
}

.flatpickr-days {
    -webkit-box-align: start;
    -ms-flex-align: start;
    -webkit-align-items: flex-start;
    align-items: flex-start;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden;
    position: relative;
    width: 307.875px
}

.flatpickr-days:focus {
    outline: 0
}

.dayContainer {
    -ms-flex-pack: justify;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    display: -ms-flexbox;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    -webkit-justify-content: space-around;
    justify-content: space-around;
    max-width: 307.875px;
    min-width: 307.875px;
    opacity: 1;
    outline: 0;
    padding: 0;
    text-align: left;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    width: 307.875px
}

.dayContainer+.dayContainer {
    -webkit-box-shadow: -1px 0 0 #e6e6e6;
    box-shadow: -1px 0 0 #e6e6e6
}

.flatpickr-day {
    -ms-flex-preferred-size: 14.2857143%;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    background: none;
    border: 1px solid transparent;
    border-radius: 150px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #393939;
    cursor: pointer;
    display: inline-block;
    -webkit-flex-basis: 14.2857143%;
    flex-basis: 14.2857143%;
    font-weight: 400;
    height: 39px;
    -webkit-justify-content: center;
    justify-content: center;
    line-height: 39px;
    margin: 0;
    max-width: 39px;
    position: relative;
    text-align: center;
    width: 14.2857143%
}

.flatpickr-day.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day.nextMonthDay:focus,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.today.inRange,
.flatpickr-day:focus,
.flatpickr-day:hover {
    background: #e6e6e6;
    border-color: #e6e6e6;
    cursor: pointer;
    outline: 0
}

.flatpickr-day.today {
    border-color: #959ea9
}

.flatpickr-day.today:focus,
.flatpickr-day.today:hover {
    background: #959ea9;
    border-color: #959ea9;
    color: #fff
}

.flatpickr-day.endRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.endRange.nextMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.endRange:focus,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected,
.flatpickr-day.selected.inRange,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.selected:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.startRange:focus,
.flatpickr-day.startRange:hover {
    background: #569ff7;
    border-color: #569ff7;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #fff
}

.flatpickr-day.endRange.startRange,
.flatpickr-day.selected.startRange,
.flatpickr-day.startRange.startRange {
    border-radius: 50px 0 0 50px
}

.flatpickr-day.endRange.endRange,
.flatpickr-day.selected.endRange,
.flatpickr-day.startRange.endRange {
    border-radius: 0 50px 50px 0
}

.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)),
.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),
.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)) {
    -webkit-box-shadow: -10px 0 0 #569ff7;
    box-shadow: -10px 0 0 #569ff7
}

.flatpickr-day.endRange.startRange.endRange,
.flatpickr-day.selected.startRange.endRange,
.flatpickr-day.startRange.startRange.endRange {
    border-radius: 50px
}

.flatpickr-day.inRange {
    border-radius: 0;
    -webkit-box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
    box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.nextMonthDay,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.prevMonthDay {
    background: transparent;
    border-color: transparent;
    color: rgba(57, 57, 57, .3);
    cursor: default
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
    color: rgba(57, 57, 57, .1);
    cursor: not-allowed
}

.flatpickr-day.week.selected {
    border-radius: 0;
    -webkit-box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7;
    box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7
}

.flatpickr-day.hidden {
    visibility: hidden
}

.rangeMode .flatpickr-day {
    margin-top: 1px
}

.flatpickr-weekwrapper {
    float: left
}

.flatpickr-weekwrapper .flatpickr-weeks {
    -webkit-box-shadow: 1px 0 0 #e6e6e6;
    box-shadow: 1px 0 0 #e6e6e6;
    padding: 0 12px
}

.flatpickr-weekwrapper .flatpickr-weekday {
    float: none;
    line-height: 28px;
    width: 100%
}

.flatpickr-weekwrapper span.flatpickr-day,
.flatpickr-weekwrapper span.flatpickr-day:hover {
    background: transparent;
    border: none;
    color: rgba(57, 57, 57, .3);
    cursor: default;
    display: block;
    max-width: none;
    width: 100%
}

.flatpickr-innerContainer {
    display: block;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden
}

.flatpickr-innerContainer,
.flatpickr-rContainer {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.flatpickr-rContainer {
    display: inline-block;
    padding: 0
}

.flatpickr-time {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: block;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 0;
    line-height: 40px;
    max-height: 40px;
    outline: 0;
    overflow: hidden;
    text-align: center
}

.flatpickr-time:after {
    clear: both;
    content: "";
    display: table
}

.flatpickr-time .numInputWrapper {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    float: left;
    height: 40px;
    width: 40%
}

.flatpickr-time .numInputWrapper span.arrowUp:after {
    border-bottom-color: #393939
}

.flatpickr-time .numInputWrapper span.arrowDown:after {
    border-top-color: #393939
}

.flatpickr-time.hasSeconds .numInputWrapper {
    width: 26%
}

.flatpickr-time.time24hr .numInputWrapper {
    width: 49%
}

.flatpickr-time input {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield;
    background: transparent;
    border: 0;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #393939;
    font-size: 14px;
    height: inherit;
    line-height: inherit;
    margin: 0;
    padding: 0;
    position: relative;
    text-align: center
}

.flatpickr-time input.flatpickr-hour {
    font-weight: 700
}

.flatpickr-time input.flatpickr-minute,
.flatpickr-time input.flatpickr-second {
    font-weight: 400
}

.flatpickr-time input:focus {
    border: 0;
    outline: 0
}

.flatpickr-time .flatpickr-am-pm,
.flatpickr-time .flatpickr-time-separator {
    -ms-flex-item-align: center;
    -webkit-align-self: center;
    align-self: center;
    color: #393939;
    float: left;
    font-weight: 700;
    height: inherit;
    line-height: inherit;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    width: 2%
}

.flatpickr-time .flatpickr-am-pm {
    cursor: pointer;
    font-weight: 400;
    outline: 0;
    text-align: center;
    width: 18%
}

.flatpickr-time .flatpickr-am-pm:focus,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time input:focus,
.flatpickr-time input:hover {
    background: #eee
}

.flatpickr-input[readonly] {
    cursor: pointer
}

@-webkit-keyframes fpFadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes fpFadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}