import { html, unsafeCSS } from "lit";
import { range } from "../../utils/dateUtils";
import { customElement } from "lit/decorators.js";
import styles from '../../styles/styles.css';

@customElement("skeleton-segment")
export class skeletonSegment extends HTMLElement {
    static styles = [
        unsafeCSS(styles)
    ];
    constructor() {
        super();
    }

    render() {
        return html`
        ${range(1, 5).map(loading => html`
<div class="grid grid-cols-10 rounded-lg bg-gray-100 relative animate-pulse mt-2">
                        <div class="col-span-4 relative">
                            <div
                                class="absolute border-2 py-2 px-3 border-gray-300 inline-flex justify-center items-center rounded-[13px] rounded-tl-lg rounded-br-lg h-[26px] max-w-full bg-gray-200 box-border">
                                <div class="h-4 w-16 bg-gray-300 rounded"></div>
                            </div>
                            <div
                                class="flex flex-col justify-between items-center pb-4 pt-6 border-r border-white bg-gray-200 rounded-tl-lg h-40">
                                <div class="flex justify-center items-center w-full px-4 gap-2">
                                    <div class="flex flex-col justify-center items-center">
                                        <div class="h-6 w-16 bg-gray-300 rounded mb-2"></div>
                                        <div class="h-6 w-16 bg-gray-300 rounded"></div>
                                    </div>
                                    <div class="w-full flex-col justify-center items-center">
                                        <div class="w-full h-4 bg-gray-300 rounded mb-2"></div>
                                        <div class="w-full h-2 bg-gray-300 rounded"></div>
                                        <div class="w-full h-4 bg-gray-300 rounded mt-2"></div>
                                    </div>
                                    <div class="flex flex-col justify-center items-center">
                                        <div class="h-6 w-16 bg-gray-300 rounded mb-2"></div>
                                        <div class="h-6 w-16 bg-gray-300 rounded"></div>
                                    </div>
                                </div>
                                <div class="w-3/4 h-4 bg-gray-300 rounded"></div>
                            </div>
                        </div>
                        <div class="col-span-3 border-r bg-white border border-gray-200">
                            <div class="p-4 w-full h-full flex justify-center items-center">
                                <div class="h-8 w-32 bg-gray-200 rounded"></div>
                            </div>
                        </div>
                        <div class="col-span-3 bg-white border border-gray-200 rounded-e-lg">
                            <div class="p-4 w-full h-full flex justify-center items-center">
                                <div class="h-8 w-32 bg-gray-200 rounded"></div>
                            </div>
                        </div>
                    </div>
        `)}
                    `;
    }
}

