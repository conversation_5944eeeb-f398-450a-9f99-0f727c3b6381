import { html } from "lit";
import { convertDurationToHour, formatDateTo_ddMMyyyy, formatNumber, getDurarionLeg, getFlights, getPassengerDescriptionV2, getTimeFromDateTime } from "../../utils/dateUtils";
import "../modal/modal";
import { environment } from "../../environments/environment";

const apiUrl = environment.apiUrl;

export const TripRePaymentTemplate = (
    autoFillOrderCode: boolean,
    request: any,
    uri_searchBox: string,
    language: string,
    _agent: string,
    _termsUrl: string,
    _isLoading: boolean,
    _agree: boolean,
    _isSubmit: boolean,
    _isShowDetailsTrip: boolean,
    _orderDetails: any,
    _inforAirports: any[],
    _pricePaxInfor: any[],
    _servicePrice: number,
    _sumPrice: number,
    _paymentMethod: string,
    _banks: any[],
    _bankNote: string,
    _transferContent: string,
    _cashInfo: any,
    _currencySymbol: string,
    _convertedVND: number,
    showDetailsTrip: () => void,
    setPaymentMethod: (method: string) => void,
    selectBank: (bank: any) => void,
    setAgree: (agree: boolean) => void,
    onPayment: () => void,
    handleLanguageChange: (value: string) => void,
    showLanguageSelect: boolean
) => html`
    ${_isLoading ? html`
    <div class="static" *ngIf="isLoading">
        <div class="loader-container">
            <span class="loader"></span>
            <img src="${apiUrl}/assets/img/background/trip_loading2.gif"/>
            <span class="loadidng-vertical  bg-gradient-to-br from-nmt-600 to-nmt-400 shadow-lg shadow-white">${language === 'vi' ? `Đang kiểm tra chuyến bay, vui lòng chờ trong giây lát...` : `Checking flight, please wait a moment...`}</span>
        </div>
    </div>
    ` : ""}
    <div class="w-full min-h-screen bg-gray-100 relative  max-md:pb-24">
    <div class="max-w-7xl mx-auto min-h-[70vh]  pb-8 relative">
        <div class="min-h-screen  py-8 ">
            <div class="w-full md:px-4 ">
                <div class="pt-4 pb-8">
                    <div
                        class="w-full max-w-4xl mx-auto max-md:overflow-x-scroll max-md:overflow-y-hidden max-md:pb-[10px]">
                        <div class="flex items-center justify-center max-md:space-x-2">
                            <div class="flex items-center">
                                <div class="relative group">
                                    <div
                                        class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white  cursor-pointer">
                                        <a href="/${uri_searchBox}"
                                            class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                            <div class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-search w-5 h-5 text-white">
                                                    <circle cx="11" cy="11" r="8"></circle>
                                                    <path d="m21 21-4.3-4.3"></path>
                                                </svg>
                                            </div><span
                                                class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden">
                                                ${language === 'vi' ? `Tìm kiếm` : `Search`}
                                            </span>
    </a>

                                    </div>
                                </div>
                                <div
                                    class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-white text-gray-400 max-md:hidden">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5">
                                        <path d="m9 18 6-6-6-6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="relative group">
                                    <div
                                        class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-pointer">
                                        <div (click)="goToTripSelection()"
                                            class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                            <div class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-ticket w-5 h-5 text-white">
                                                    <path
                                                        d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z">
                                                    </path>
                                                    <path d="M13 5v2"></path>
                                                    <path d="M13 17v2"></path>
                                                    <path d="M13 11v2"></path>
                                                </svg>
                                            </div><span
                                                class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden">
                                                ${language === 'vi' ? `Chọn vé` : `Select Ticket`}
                                            </span>
                                        </div>

                                    </div>
                                </div>
                                <div
                                    class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-white text-gray-400 max-md:hidden">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5">
                                        <path d="m9 18 6-6-6-6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="relative group">
                                    <div
                                        class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed">
                                        <div
                                            class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                            <div class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-user w-5 h-5 text-white">
                                                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                                    <circle cx="12" cy="7" r="4"></circle>
                                                </svg>
                                            </div><span
                                                class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600  max-md:hidden">
                                                ${language === 'vi' ? `Thông tin` : `Information`}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-white text-gray-400  max-md:hidden">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5">
                                        <path d="m9 18 6-6-6-6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="relative group">
                                    <div
                                        class="flex items-center h-12 md:px-6 px-2 rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed">
                                        <div
                                            class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                            <div class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-credit-card w-5 h-5 text-white">
                                                    <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                                    <line x1="2" x2="22" y1="10" y2="10"></line>
                                                </svg>
                                            </div>
                                            <span
                                                class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600">
                                                ${language === 'vi' ? `Thanh toán` : `Payment`}
                                            </span>
                                        </div>
                                        <div class="absolute -bottom-[5px] left-1/2 transform -translate-x-1/2">
                                            <div class="w-2.5 h-2.5 rounded-full bg-nmt-500 ring-4 ring-white"></div>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5">
                                        <path d="m9 18 6-6-6-6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="relative group">
                                    <div
                                        class="flex items-center h-12 md:px-6 px-2 bg-white rounded-lg transition-all duration-300 ease-in-out border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 cursor-not-allowed">
                                        <div
                                            class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                            <div
                                                class="rounded-lg p-2 transition-colors duration-300 bg-gray-100 group-hover:bg-gray-200">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-check w-5 h-5 text-gray-600">
                                                    <path d="M20 6 9 17l-5-5"></path>
                                                </svg>
                                            </div><span
                                                class="font-medium tracking-wide whitespace-nowrap text-[15px] text-gray-600 max-md:hidden">
                                                ${language === 'vi' ? `Hoàn tất` : `Complete`}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid md:grid-cols-3 gap-6">
                    <!-- Payment Methods -->
                    <div class="md:col-span-2">
                    
                        <div
                            class=" border  rounded-lg shadow-sm space-y-1  bg-card text-card-foreground bg-white mb-8">
                            ${_orderDetails?.full?.InventoriesSelected.length > 0 ? html`
                                                        <div class="rounded-lg bg-white shadow-lg">
                                                <div
                                                    class="rounded-t-lg py-2  border-b border-gray-200 w-full px-4 flex justify-between items-center">
                                                    <h1 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                                                        ${language === 'vi' ? `Chi tiết chuyến bay` : `Flight Details`}
                                                    </h1>
                            <div class="flex justify-end items-center  ">
                                    ${showLanguageSelect ? html`
                                    <select id="language" 
                                        class=" text-sm bg-gray-200 rounded-lg px-2 py-1  border-none focus:outline-none text-gray-600 "
                                        .value=${language}
                                        @change=${(e: Event) => handleLanguageChange((e.target as HTMLSelectElement).value)}
                                    >
                                        <option style="background-color: #f0f0f0; color: black;" value="en">English</option>
                                        <option style="background-color: #f0f0f0; color: black;" value="vi">Tiếng Việt</option>
                                    </select>
                                    ` : ''}
                                </div>
                                                </div>
                            
                                                <div class=" bg-gray-100 border-gray-200 ">
                                                    
                                                    ${_orderDetails?.full?.InventoriesSelected.map((itinerarySelected: any, index: number) => html`
                                                    <div class="w-full bg-gray-100 mt-4 border-b border-gray-200 shadow-md rounded-lg">
                            <!-- start flight infor -->
                            <div class="bg-white rounded-e-lg rounded-bl-lg ">
                                <div class="py-[2px]">
                                    <span
                                        class=" bg-gradient-to-br from-nmt-600 to-nmt-300 md:text-base text-sm text-white font-extrabold pl-2 py-1 pe-6 md:pe-12 rounded-tr-full">
                                        ${language === 'vi' ? `Chi tiết hành trình:` : `Itinerary Details:`}
                                        ${_orderDetails?.full?.InventoriesSelected.length > 1 && index % 2 === 1 ?
        (language === 'vi' ? `Chiều về` : `Return`) :
        (language === 'vi' ? `Chiều đi` : `Departure`)}
                                       
                                    </span>
                                </div>
                                <div class="w-full  rounded-lg">
                                ${itinerarySelected.segment.Legs.map((leg: any, index: number) => html`
                                ${index > 0 ? html`
                                <div class="w-full bg-gray-600 p-1 text-white text-center text-sm">
                                    ${language === 'vi' ? `Trung chuyển tại` : `Transit at`} ${_inforAirports[leg.DepartureCode]?.name}
                                    <strong>(${leg.DepartureCode})</strong> - ${language === 'vi' ? `Thời gian:` : `Time:`}
                                    <strong>${convertDurationToHour(itinerarySelected.segment.Legs[index].StopTime)}</strong>
                                </div>
                                ` : ""}
                               
                                <div class="grid grid-cols-12 border-t border-gray-200  rounded-lg">
                                    <div
                                        class="col-span-7 flex flex-col justify-center items-center text-black py-2 md:px-10  border-black rounded-lg">
                                        <div
                                            class="w-full flex justify-between items-center px-4 md:text-sm  text-[10px]">
                                            <div class="text-left">
                                                <span class="font-extrabold">
                                                    (${leg?.DepartureCode})
                                                </span>
                                                ${_inforAirports[leg?.DepartureCode]?.cityName}
                                                <div>
                                                    <span class="text-gray-400">
                                                        ${_inforAirports[leg?.DepartureCode]?.name}</span>
                                                </div>
                                            </div>
                                            <div class="text-right">
                            
                                                ${_inforAirports[leg?.ArrivalCode]?.cityName}
                                                <span class="font-extrabold">
                                                    (${leg?.ArrivalCode})
                                                </span>
                                                <div>
                                                    <span class="text-gray-400">
                                                        ${_inforAirports[leg?.ArrivalCode]?.name}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex justify-center items-center w-full px-4 gap-2">
                                            <div class="flex flex-col justify-center items-center">
                                                <strong
                                                    class="md:text-3xl text-base font-extrabold text-nmt-600">
                                                    ${getTimeFromDateTime(leg?.DepartureDate)}</strong>
                                                <span class="md:text-sm text-[10px]">
                                                    ${formatDateTo_ddMMyyyy(leg?.DepartureDate, language)}
                                                </span>
                                                <strong class="md:text-base text-sm font-semibold text-gray-600">
                                                    ${language === 'vi' ? `Nhà ga:` : `Terminal:`} ${leg?.DepartureTerminal || '-'}
                                                </strong>
                                            </div>
                                            <div
                                                class="w-full flex-col justify-center items-center px-4 text-sm md:px-6">
                                                <div class="w-full text-center -mb-2">
                                                    ${leg?.Equipment}
                                                </div>
                                                <div class="w-full flex justify-center items-center">
                                                    <div class="w-full h-[2px] rounded-full bg-nmt-600">
                                                    </div>
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        class="w-5 h-5 fill-nmt-600 inline-block ml-[1px]"
                                                        viewBox="0 0 576 512">
                                                        <path
                                                            d="M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z" />
                                                    </svg>
                                                </div>
                                                <div class="w-full text-center -mt-2">
                            
                                                    ${convertDurationToHour(leg?.Duration)}
                                                </div>
                                            </div>
                                            <div class="flex flex-col justify-center items-center">
                                                <strong
                                                    class="md:text-2xl text-base font-extrabold text-nmt-500">
                                                    ${getTimeFromDateTime(leg?.ArrivalDate)}</strong>
                                                <span class="md:text-sm text-[10px]">
                                                    ${formatDateTo_ddMMyyyy(leg?.ArrivalDate, language)}
                                                </span>
                                                <strong class="md:text-base text-sm font-semibold text-gray-600">
                                                    ${language === 'vi' ? `Nhà ga:` : `Terminal:`} ${leg?.ArrivalTerminal || '-'}
                                                </strong>
                                            </div>
                                        </div>
                            
                                    </div>
                                    <div class="col-span-5 flex flex-row  rounded-lg">
                                        <div
                                            class="w-[1px] h-full bg-[100%_10px] bg-[linear-gradient(to_bottom,_transparent_50%,_#fb7740_50%)] bg-[length:100%_10px]">
                                        </div>
                                        <div
                                            class="px-4 py-2 flex flex-col items-center justify-center w-full md:text-sm text-[10px]">
                                            <span class="text-xs font-bold">${language === 'vi' ? `Hãng vận chuyển` : `Airline`}</span>
                                            <img src="${apiUrl}/assets/img/airlines/${leg?.OperatingAirlines}.png"
                                                class=" w-auto h-12 mx-auto my-1">
                            
                                            <span>${language === 'vi' ? `Chuyến bay:` : `Flight:`} <span
                                                    class="text-nmt-500 font-extrabold tracking-wide">${leg?.Airlines + leg?.FlightNumber}</span></span>
                                            <span>${language === 'vi' ? `Loại vé:` : `Ticket Type:`}
                                                <strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.CabinName}</strong>
                            
                                            </span>
                                        </div>
                                    </div>
                                    <div
                                        class="col-span-12 border-t border-gray-200 p-2 flex justify-end items-center bg-gray-50/80  rounded-lg">
                                        <span class="text-end text-xs text-gray-800">
                                            ${language === 'vi' ? `Loại vé:` : `Ticket Type:`} <strong>
                                                ${itinerarySelected.inventorySelected?.BookingInfos[index]?.FareType || itinerarySelected.inventorySelected?.BookingInfos[index]?.CabinName}
                                            </strong>
                                            | ${language === 'vi' ? `Hành lý xách tay:` : `Hand Baggage:`}
                                            ${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandBaggage > 1 && itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag !== 0 ? html`<strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandBaggage}</strong>` : ``}
                                            
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-luggage w-4 h-4 text-blue-500 flex-shrink-0  inline-block">
                                                <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16">
                                                </path>
                                                <rect width="20" height="14" x="2" y="6" rx="2">
                                                </rect>
                                            </svg>
                                            ${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag > 0 ? html`<strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag}</strong>` : html`<strong>7</strong>`}
                            
                                            kg
                                            | ${language === 'vi' ? `Hành lý ký gửi:` : `Checked Baggage:`}
                                            ${itinerarySelected.inventorySelected?.BookingInfos[index]?.BagPieces > 1 && itinerarySelected.inventorySelected?.BookingInfos[index]?.WeightBag !== 0 ? html`<strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.BagPieces}</strong>` : ``}
                            
                                         
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-luggage w-4 h-4 text-blue-500 flex-shrink-0  inline-block">
                                                <path
                                                    d="M6 20h0a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h0">
                                                </path>
                                                <path d="M8 18V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v14">
                                                </path>
                                                <path d="M10 20h4"></path>
                                                <circle cx="16" cy="20" r="2"></circle>
                                                <circle cx="8" cy="20" r="2"></circle>
                                            </svg>
                            
                                            <strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.WeightBag}</strong>
                                            kg
                                            | <svg class="w-4 h-4 inline-block text-gray-800 dark:text-white"
                                                aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                                height="24" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round"
                                                    stroke-linejoin="round" stroke-width="2"
                                                    d="M12 8v4l3 3m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                            </svg>
                                            ${language === 'vi' ? `Thời gian bay:` : `Flight Time:`}
                                            <strong>${getDurarionLeg(leg)}</strong>
                                            | ${language === 'vi' ? `Máy bay:` : `Aircraft:`} <strong>${leg.Equipment}</strong>
                                        </span>
                                    </div>
                                </div>
                                `)}
                                </div>
                            
                            </div>
                            <!-- end flight infor -->
                            </div>
                            `)}
                                                </div>
                            
                                                <div
                                                    class="col-span-12 border-t transition-all duration-700  ease-in-out    grid grid-cols-1 justify-end items-center bg-[#f8e2de] rounded-b-lg border-[1px] border-white shadow-[0px_0px_15px_#cccccc96,0px_0px_15px_#44444400_inset] ${_isShowDetailsTrip ? '!h-auto !w-full !opacity-100 p-2' : 'opacity-0 w-0 h-0 overflow-hidden'}">
                                                    <div class="grid grid-cols-4 border-b border-gray-600 md:text-sm text-xs">
                                                        <div class="text-start font-bold">
                                                            ${language === 'vi' ? `Hành khách` : `Passenger`}
                                                        </div>
                                                        <div class="text-end font-bold">
                                                            ${language === 'vi' ? `Giá vé` : `Ticket Price`}
                                                        </div>
                                                        <div class="text-end font-bold">
                                                            ${language === 'vi' ? `Thuế` : `Tax`}
                                                        </div>
                                                        <div class="text-end font-bold">
                                                            ${language === 'vi' ? `Giá Bán` : `Total Price`}
                                                        </div>
                                                    </div>
                            
                            
                                                    ${_pricePaxInfor.map((fareInfo: any) => html`<div class="grid grid-cols-4 border-b border-gray-600 py-1 md:text-sm text-xs">
                                                    <div class="text-start">
                                                        ${getPassengerDescriptionV2(fareInfo?.PaxType, _orderDetails?.full?.adult, _orderDetails?.full?.child, _orderDetails?.full?.infant, language)}
                                                    </div>
                                                    <div class="text-end">
                                                        ${formatNumber(fareInfo.Fare, _convertedVND, language)}
                            
                                                    </div>
                                                    <div class="text-end">
                                                        ${formatNumber(fareInfo.Tax, _convertedVND, language)}
                            
                                                    </div>
                                                    <div class="text-end">
                                                        ${formatNumber(fareInfo.Fare + fareInfo.Tax, _convertedVND, language)}
                                                    </div>
                                                </div>`)}
                                                 
                                                    <div class=" text-right md:text-sm text-xs">
                                                        ${language === 'vi' ? `Phí dịch vụ:` : `Service Fee:`} <strong class="md:text-xl text-base font-bold text-nmt-600">
                                                            <strong class="text-base text-nmt-600">
                                                                ${formatNumber(_servicePrice, _convertedVND, language)} </strong>
                                                        </strong>${_currencySymbol}
                                                    </div>
                            
                                                </div>
                                                <div class="w-full text-right md:text-sm text-xs py-4 pe-2">
                                                    <span @click="${showDetailsTrip}"
                                                    class="text-nmt-600 cursor-pointer hover:underline text-sm underline ${_isShowDetailsTrip ? 'text-red-600' : ''}">
                                                         ${_isShowDetailsTrip ? (language === 'vi' ? `Ẩn chi tiết` : `Hide Details`) : (language === 'vi' ? `Chi tiết` : `Details`)}
                                                    </span>
                                                    ${language === 'vi' ? `Tổng cộng:` : `Total:`} <strong class="md:text-xl text-base font-bold text-nmt-600">
                            
                                                        ${formatNumber(_sumPrice + _servicePrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>
                                                </div>
                                            </div>
                                        ` : ""}
                        </div>

                        <div class=" border  rounded-lg shadow-sm space-y-1  bg-card text-card-foreground bg-white">
                            <div class=" rounded-t-lg px-4 pt-4 pb-2 border-b">
                                <h2 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                                    ${language === 'vi' ? `Phương Thức Thanh Toán` : `Payment Method`}
                                </h2>
                                <p class="text-sm text-gray-600">${language === 'vi' ? `Chọn phương thức thanh toán của bạn` : `Choose your payment method`}</p>
                            </div>
                            <div class="md:p-6 p-2">
                                <div class="space-y-4">
                                    
                                    <!-- Bank Transfer -->
                                    <div class="flex items-start space-x-3 border rounded-lg p-4 transition-all border-gray-200 ${_paymentMethod === 'bank-transfer' ? 'border-nmt-500' : ''}">
                                        <div class="mt-1">
                                            <input type="radio" id="bank-transfer" name="paymentMethod"
                                                .checked="${_paymentMethod.includes('bank-transfer')}"
                                                @change="${() => setPaymentMethod('bank-transfer')}"
                                                class="h-4 w-4 text-nmt-600 focus:ring-nmt-500" />
                                        </div>
                                        <div class="flex-1">
                                            <label for="bank-transfer"
                                                class="flex items-center text-lg font-medium cursor-pointer">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="mr-2 h-5 w-5 text-nmt-600">
                                                    <rect width="20" height="16" x="2" y="6" rx="2"></rect>
                                                    <path d="M12 3v10"></path>
                                                    <path d="m5 9 7-4 7 4"></path>
                                                </svg>
                                                ${language === 'vi' ? `Chuyển Khoản Ngân Hàng` : `Bank Transfer`}
                                            </label>
                                            <p class="text-sm text-gray-500 mt-1">${language === 'vi' ? `Thanh toán trực tiếp từ tài khoản ngân hàng của bạn` : `Pay directly from your bank account`}</p>
                                            <!-- banks list -->
                                            ${_paymentMethod.includes('bank-transfer') ? html`
                                                                                        <div class="mt-4 space-y-4 pt-3 border-t border-nmt-100">
                                                                                            <div class="grid grid-cols-3 gap-3">
                                                                                            ${_banks?.map((bank: any) => html`
                                                                                             <button @click="${() => selectBank(bank)}" type="button"
                                                                                                    class="border flex md:flex-row flex-col md:gap-1 justify-center items-center rounded-md py-2 md:px-4 px-1 text-center hover:bg-nmt-50 hover:text-nmt-800 transition-colors
                                                                                                    ${bank.selected === true ? 'border-nmt-400 bg-nmt-400 text-white' : 'border-nmt-200 bg-white'}">
                                                                                                    <img src="${apiUrl}/${bank?.logoPath}" alt="${bank?.bankName}" class="md:h-8 h-6 rounded-md " />
                                                                                                    <span class="md:text-sm text-xs font-medium">${bank?.bankName}</span>
                                                                                                </button>
                                                                                            `)}
                                                                                            </div>
                                                                                        </div>
                                                                                        `: ``}
                                            
                                            ${_paymentMethod.includes('bank-transfer') ? html`
                                            <div class="mt-4 space-y-4 pt-3 border-t border-nmt-100">
                                                <div class="bg-nmt-100 p-3 rounded-md text-sm">
                                                    <p class="font-medium">${language === 'vi' ? `Thông tin chuyển khoản` : `Transfer Information`}</p>
                                                </div>

                                                <div class="space-y-3">
                                                    ${_banks?.map((bank: any) => html`
                                                         <div class="grid grid-cols-3 gap-2 text-sm ${bank.selected === true ? 'block' : 'hidden'}">
                                                        <div class="text-gray-600">${language === 'vi' ? `Chủ Tài Khoản:` : `Account Holder:`}</div>
                                                        <div class="col-span-2 font-medium">${bank.accountHolder}</div>

                                                        <div class="text-gray-600">${language === 'vi' ? `Ngân hàng:` : `Bank:`}</div>
                                                        <div class="col-span-2 font-medium">${bank.bankName}</div>

                                                        <div class="text-gray-600">${language === 'vi' ? `Chi nhánh:` : `Branch:`}</div>
                                                        <div class="col-span-2 font-medium">${bank.branch}</div>

                                                        <div class="text-gray-600">${language === 'vi' ? `Số tài khoản:` : `Account Number:`}</div>
                                                        <div class="col-span-2 font-medium">${bank.accountNumber}</div>

                                                        <div class="text-gray-600">${language === 'vi' ? `Nội dung CK:` : `Transfer Content:`}</div>
                                                        <div class="col-span-2 font-medium">${_transferContent} ${autoFillOrderCode ? request.OrderCode : ''}</div>
                                                        ${bank.qrImageUrl ? html`
                                                            <div class="text-gray-600">${language === 'vi' ? 'QR Code:' : 'QR Code:'}</div>
                                                            <div class="col-span-2 font-medium">
                                                                <img src="${bank.qrImageUrl}" alt="${bank?.bankName}" class="h-36 rounded-md bg-gray-100 shadow-md" />
                                                            </div>
                                                            ` : ''}
                                                    </div>
                                                    `)}
                                                </div>

                                                <div class="bg-nmt-50 border border-nmt-200 p-3 rounded-md mt-4">
                                                    <p class="text-sm font-medium text-nmt-800">${language === 'vi' ? `Hướng dẫn xác nhận thanh toán:` : `Payment Confirmation Guide:`}</p>
                                                    <p class="text-sm mt-2">
                                                        ${_bankNote}
                                                    </p>
                                                </div>

                                            </div>
                                            `: ``}
                                            
                                        </div>
                                    </div>



                                    <!-- Cash Payment -->
                                    <div class="flex items-start space-x-3 border rounded-lg p-4 transition-all ${_paymentMethod === 'cash' ? 'border-nmt-500' : ''}">
                                        <div class="mt-1">
                                            <input type="radio" id="cash" name="paymentMethod"
                                            .checked="${_paymentMethod === 'cash'}" @change="${() => setPaymentMethod('cash')}"
                                                class="h-4 w-4 text-nmt-600 focus:ring-nmt-500" />
                                        </div>
                                        <div class="flex-1">
                                            <label for="cash"
                                                class="flex items-center text-lg font-medium cursor-pointer">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="mr-2 h-5 w-5 text-nmt-600">
                                                    <rect width="20" height="12" x="2" y="6" rx="2"></rect>
                                                    <circle cx="12" cy="12" r="2"></circle>
                                                    <path d="M6 12h.01M18 12h.01"></path>
                                                </svg>
                                                ${language === 'vi' ? `Thanh Toán Tiền Mặt` : `Cash Payment`}
                                            </label>
                                            <p class="text-sm text-gray-500 mt-1">${language === 'vi' ? `Thanh toán trực tiếp bằng tiền mặt tại quầy` : `Pay directly in cash at the counter`}</p>
                                            ${_paymentMethod === 'cash' ? html`
                                            <div class="mt-4 space-y-4 pt-3 border-t border-nmt-100">
                                                <div class="bg-nmt-100 p-3 rounded-md text-sm">
                                                    <p>${language === 'vi' ? `Thông tin thanh toán tiền mặt:` : `Cash Payment Information:`}</p>
                                                </div>

                                                <div class="space-y-3">
                                                    <div class="flex items-start gap-2">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            class="mt-0.5 text-nmt-600 min-w-5">
                                                            <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z">
                                                            </path>
                                                            <circle cx="12" cy="10" r="3"></circle>
                                                        </svg>
                                                        <div>
                                                            <p class="font-medium">${language === 'vi' ? `Địa điểm thanh toán:` : `Payment Location:`}</p>
                                                            <p class="text-sm text-gray-600">${language === 'vi' ? `Quầy vé tại văn phòng đại lý của chúng tôi:` : `Ticket counter at our agency office:`} 
                                                            <span class="font-medium text-gray-800">${_cashInfo.paymentAddress}</span>
                                                            </p>
                                                        </div>
                                                    </div>

                                                    <div class="flex items-start gap-2">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="mt-0.5 text-nmt-600">
                                                            <circle cx="12" cy="12" r="10"></circle>
                                                            <polyline points="12 6 12 12 16 14"></polyline>
                                                        </svg>
                                                        <div>
                                                            <p class="font-medium">${language === 'vi' ? `Thời gian:` : `Time:`}</p>
                                                            <ul class="list-disc pl-6 space-y-1">
                                                            <li>${_cashInfo.paymentDeadline}</li>
                                                            <li>${_cashInfo.workingHours}</li>
                                                            </ul>
                                                        </div>
                                                    </div>

                                                    <div class="flex items-start gap-2">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="mt-0.5 text-nmt-600">
                                                            <rect width="18" height="18" x="3" y="3" rx="2" ry="2">
                                                            </rect>
                                                            <line x1="9" x2="15" y1="9" y2="9"></line>
                                                            <line x1="9" x2="15" y1="15" y2="15"></line>
                                                        </svg>
                                                        <div>
                                                            <p class="font-medium">${language === 'vi' ? `Ghi chú:` : `Note:`}</p>
                                                            <p class="text-sm text-gray-600">${_cashInfo.note}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            `: ``}
                                            
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Flight Summary -->
                    <div
                        class="md:col-span-1 border  rounded-lg shadow-sm space-y-1  bg-card text-card-foreground bg-white max-md:hidden">
                        <div class=" rounded-t-lg p-4 border-b">
                            <h2 class=" text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="h-5 w-5 inline-flex">
                                    <path
                                        d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                    </path>
                                </svg>
                                ${language === 'vi' ? `Thông Tin Chuyến Bay` : `Flight Information`}
                            </h2>
                        </div>
                        <div>
                            ${_orderDetails?.full?.InventoriesSelected.length > 0 ? html`
                                            <div>
                                            ${_orderDetails?.full?.InventoriesSelected.map((itinerarySelected: any, index: number) => html`
                                                <div class="w-full relative ">
                                                    <!-- Depart -->
                                                    <h1 class="bg-[#c9efff] text-gray-800 border-b border-gray-200 w-full px-4 py-2 text-base font-bold ${index % 2 === 1 ? 'bg-[#fffbb3]' : ''} ">
                                                        ${index % 2 === 0 ? (language === 'vi' ? `Chuyến đi` : `Departure`) : (language === 'vi' ? `Chuyến về` : `Return`)}
                                                    </h1>
                                                    <div
                                                        class="flex flex-col justify-between items-center text-gray-800 py-2 border-r border-white ">
                                                        <div class="w-full flex justify-between items-center px-4 text-xs font-bold">
                                                            <div class="text-left">
                                                                ${_inforAirports[itinerarySelected?.segment?.DepartureCode]?.cityName}
                                                            </div>
                                                            <div class="text-right">
                                                                ${_inforAirports[itinerarySelected?.segment?.ArrivalCode]?.cityName}
                                                            </div>
                            
                                                        </div>
                                                        <div class="flex justify-start items-center w-full px-4 gap-2 ">
                                                            <div class="flex flex-col justify-start items-start">
                                                                <strong class="text-base font-bold rounded-full bg-white text-nmt-600">
                                                                    ${itinerarySelected?.segment?.DepartureCode}
                                                                </strong>
                                                                <strong class="text-xl font-bold ">
                                                                    ${getTimeFromDateTime(itinerarySelected?.segment?.DepartureDate)}
                                                                </strong>
                            
                                                            </div>
                                                            <div class="w-full flex flex-col justify-center items-center">
                                                                <div
                                                                    class="px-3  inline-flex justify-center items-center max-w-full text-nmt-600 bg-white box-border">
                                                                    <span
                                                                        class="relative  text-nmt-600 font-normal h-[18px] tracking-[0px] leading-[18px] overflow-hidden text-ellipsis whitespace-nowrap">
                            
                                                                        <img src="${apiUrl}/assets/img/airlines/${itinerarySelected?.segment?.Airlines}.png" class="h-full w-auto">
                                                                    </span>
                                                                </div>
                                                                <div class="w-full flex justify-center items-center">
                                                                    <div class="w-full h-[2px] rounded-full bg-nmt-600">
                                                                    </div>
                                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                                        class="w-5 h-5 fill-nmt-600 inline-block ml-[1px]"
                                                                        viewBox="0 0 576 512">
                                                                        <path
                                                                            d="M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z" />
                                                                        </svg>
                                                                </div>
                            
                                                            </div>
                                                            <div class="flex flex-col justify-end items-end">
                                                                <strong class="text-base font-bold rounded-full bg-white text-nmt-600">
                                                                    ${itinerarySelected?.segment?.ArrivalCode}
                                                                </strong>
                                                                <strong class="text-xl font-bold ">
                                                                    ${getTimeFromDateTime(itinerarySelected?.segment?.ArrivalDate)}
                                                                </strong>
                                                            </div>
                            
                                                        </div>
                                                        <div
                                                            class="w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#d2d3d3_50%)] bg-[length:10px_100%]">
                                                        </div>
                                                        <div class="flex justify-between items-center w-full px-4 mt-2 text-sm">
                                                            <div [ngClass]="{'flex flex-col': itinerarySelected?.segment?.Legs.length > 1}">
                                                                <span>
                                                                    ${language === 'vi' ? `Ngày:` : `Date:`}
                                                                </span>
                                                                <strong class="text-xs">
                                                                    ${formatDateTo_ddMMyyyy(itinerarySelected?.segment?.DepartureDate, language)}
                                                                </strong>
                                                            </div>
                                                            <div
                                                                [ngClass]="{'flex flex-col items-end justify-end': itinerarySelected?.segment?.Legs.length > 1}">
                                                                <span>
                                                                    ${language === 'vi' ? `Chuyến:` : `Flight:`}
                                                                </span>
                                                                <strong class="py-1 px-2 text-xs rounded-full bg-gray-200 text-right">
                                                                    ${getFlights(itinerarySelected?.segment?.Legs)}
                                                                </strong>
                                                            </div>
                                                        </div>
                            
                                                    </div>
                                                </div>
                                            `)}
                                                
                                              
                                            
                                            `: html`
                                            <div class="py-4 text-center text-gray-600">
                                                    Chưa chọn chuyến bay
                                                </div>
                                            `}
                                              <div
                                                    class="w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#d2d3d3_50%)] bg-[length:10px_100%]">
                                                </div>

                            <div class="w-full flex justify-between items-center px-4 mt-2 font-normal">
                    <div>
                        <span>
                            ${language === 'vi' ? `Giá vé:` : `Ticket Price:`}
                        </span>
                    </div>
                    <div>
                        <strong class="text-base text-nmt-600">
                            ${formatNumber(_sumPrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>
                    </div>
                </div>
                <div class="w-full flex justify-between items-center px-4 mt-2 font-normal">
                    <div>
                        <span>
                            ${language === 'vi' ? `Giá dịch vụ:` : `Service Fee:`}
                        </span>
                    </div>
                    <div>
                        <strong class="text-base text-nmt-600">
                            ${formatNumber(_servicePrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>
                    </div>
                </div>
                            <div
                                class="w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#fb7740_50%)] bg-[length:10px_100%]">
                            </div>
                            <div class="w-full flex justify-between items-center px-4 mt-2 font-bold">
                    <div>
                        <span>
                            ${language === 'vi' ? `Tổng giá:` : `Total Price:`}
                        </span>
                    </div>
                    <div class=" flex justify-end max-md:flex-col items-end">
                        <strong class="text-xl text-nmt-600 text-right w-full">
                            ${formatNumber(_sumPrice + _servicePrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>
                    </div>
                </div>
                            <div
                                class=" flex flex-col items-center justify-center w-full bg-white p-4  mt-4 sticky bottom-0 ">
                                <div class="mt-4 ">
                                    <div>
                                        <input type="checkbox" id="agree"
                                        .checked="${_agree}"
                                        @change="${(e: any) => setAgree(e.target.checked)}"
                                            class="w-4 h-4 inline-block text-nmt-600 bg-gray-100 border-gray-300 rounded focus:ring-nmt-500 dark:focus:ring-nmt-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="agree"
                                            class="ms-2 text-sm cursor-pointer font-medium text-gray-900 dark:text-gray-300">
                                                ${language === 'vi' ? `Tôi đồng ý với` : `I agree with the`}
                                                <a href="/${_termsUrl}" target="_blank" class="cursor-pointer hover:underline text-nmt-600 underline">
                                                    ${language === 'vi' ? `Quy Định và Điều Khoản` : `Terms and Conditions`}
                                                </a>
                                                ${language === 'vi' ? `của` : `of`} ${_agent}
                                            </label>
                                    </div>
                                    ${!_agree && _isSubmit ? html`
                                    <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400"
                                        role="alert">
                                        <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                                        </svg>
                                        <span class="sr-only">Info</span>
                                        <div>
                                            <span class="font-medium">${language === 'vi' ? `Bắt buộc!` : `Required!`}</span>
                                            ${language === 'vi' ? `Bạn phải đồng ý trước khi thanh toán` : `You must agree before payment`}
                                        </div>
                                    </div>
                                    `: ``}
                                    
                                </div>
                                <span class="relative group">
                                    <button @click="${() => onPayment()}"
                                        class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                                        ${_paymentMethod === 'credit-card' || _paymentMethod === 'e-wallet' ?
        (language === 'vi' ? `Thanh toán ngay` : `Pay Now`) :
        (language === 'vi' ? `Đặt giữ chỗ` : `Hold Booking`)}
                                    </button>
                                </span>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="z-50  w-full bg-white p-4 rounded-lg mt-4 shadow-2xl sticky bottom-0 border md:hidden">
    <div class="mt-4 w-full">
        <div>
            <input type="checkbox"
                                        .checked="${_agree}" .value="${_agree}" @change="${(e: any) => setAgree(e.target.checked)}"
                class="w-4 h-4 inline-block text-nmt-600 bg-gray-100 border-gray-300 rounded focus:ring-nmt-500 dark:focus:ring-nmt-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
            <label for="agree" class="ms-2  cursor-pointer text-sm font-medium text-gray-900 dark:text-gray-300">
                ${language === 'vi' ? `Tôi đồng ý với` : `I agree with the`}
                <a href="/${_termsUrl}" target="_blank"
                    class="cursor-pointer hover:underline text-nmt-600 underline">${language === 'vi' ? `Quy Định và Điều Khoản` : `Terms and Conditions`}</a>
                ${language === 'vi' ? `của` : `of`} ${_agent}</label>
        </div>
        ${!_agree && _isSubmit ? html`
                                    <div class="flex items-center text-sm text-red-800 rounded-lg dark:text-red-400"
                                        role="alert">
                                        <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                                        </svg>
                                        <span class="sr-only">Info</span>
                                        <div>
                                            <span class="font-medium">${language === 'vi' ? `Bắt buộc!` : `Required!`}</span> ${language === 'vi' ? `Bạn phải đồng ý trước khi thanh toán` : `You must agree before payment`}
                                        </div>
                                    </div>
                                    `: ``}
                                    
    </div>
    <div class="flex items-center z-50 justify-between w-full bg-white rounded-lg   ">
        <div class=" flex justify-end  items-end">
            <strong class="text-xl text-nmt-600 text-right w-full">
                ${formatNumber(_sumPrice + _servicePrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>
        </div>
        <button @click="${() => onPayment()}"
            class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2">
            ${_paymentMethod === 'credit-card' || _paymentMethod === 'e-wallet' ?
        (language === 'vi' ? 'Thanh toán ngay' : 'Pay Now') :
        (language === 'vi' ? 'Đặt giữ chỗ' : 'Hold Booking')}
            <div><svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                    viewBox="0 0 24 24" class="w-6 h-6 text-white  animate-pulse">
                    <path fill-rule="evenodd"
                        d="M3 4a1 1 0 0 0-.822 1.57L6.632 12l-4.454 6.43A1 1 0 0 0 3 20h13.153a1 1 0 0 0 .822-.43l4.847-7a1 1 0 0 0 0-1.14l-4.847-7a1 1 0 0 0-.822-.43H3Z"
                        clip-rule="evenodd"></path>
                </svg></div>
        </button>
    </div>
</div>

<modal-notification  uri_searchBox="${uri_searchBox}"></modal-notification>
`