import { html, render } from "lit"

export const modalTemplate = (
    isOpen: boolean,
    title: string,
    content: string,
    isCountDown: boolean,
    countdown: number,
    close: () => void,
    reSearch: () => void
) => {
    if (!isOpen) {
        // Clean up portal when modal is closed
        const portalContainer = document.getElementById('modal-portal');
        if (portalContainer) {
            render('', portalContainer);
        }
        return;
    }

    // Create portal container if it doesn't exist
    let portalContainer = document.getElementById('modal-portal');
    if (!portalContainer) {
        portalContainer = document.createElement('div');
        portalContainer.id = 'modal-portal';
        document.body.appendChild(portalContainer);
    }

    // Render modal content
    const modalContent = html`
    <div class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-20 dark:bg-opacity-80">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-bold text-nmt-600 dark:text-white">
                    ${title}
                </h3>
            </div>
            <!-- Modal body -->
            <div class="p-4 md:p-5 py-4 overflow-y-auto world-map">
                <div class="max-h-[60vh] h-full max-w-lg">
                    <!-- content notification -->
                    ${content}
                </div>
            </div>
            <!-- Modal footer -->
            <div class="flex items-center justify-end pb-4 px-4 rounded-b dark:border-gray-600">
            ${isCountDown ? html`
            <button @click="${reSearch}"
                    class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-400 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                    Tải Lại (${countdown})
                </button>
            ` : html`
            <button @click="${close}"
                    class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-400 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                    Đóng
                </button>
            `}
            </div>
        </div>
    </div>
    `;

    // Render the modal content into the portal container
    render(modalContent, portalContainer);
}
