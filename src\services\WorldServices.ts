import { environment } from "../environments/environment";
import { fetchWithDeviceIdandApiKey } from "../utils/deviceUtils";

const apiUrl = environment.apiUrl;


export const getAirportInfoByCode = async (airportsCode: string[], language: string, apiKey: string) => {
    const requestBody = {
        airportsCode: airportsCode.join(';'),
        language: language
    };
    try {
        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/airport-info`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody)
        }, apiKey);

        if (!response.ok) {
            throw response;
        }

        return await response.json();
    } catch (error) {
        throw error;
    }
};


export const phones = async () => {
    const response = await fetch(`${apiUrl}/api/World/phones`, {
        method: 'GET'
    });

    return response.json();
};

export const getAirportsDefault = async (language: string, apiKey: string) => {
    const requestBody = {
        language: language
    }

    try {
        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/airports-default`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody)
        }, apiKey);

        if (!response.ok) {
            throw response;
        }

        return await response.json();
    } catch (error) {
        throw error;
    }
};

export const getFeatures = async (features: string, apiKey: string) => {
    try {
        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/feature/${features}`, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
        }, apiKey);

        if (!response.ok) {
            throw response;
        }

        return await response.json();
    } catch (error) {
        throw error;
    }
};


export const searchAirport = async (request: any) => {
    const requestBody = JSON.stringify(request);

    const response = await fetch(`${apiUrl}/api/World/flight/airport-search`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: requestBody
    });

    return response.json();
}

