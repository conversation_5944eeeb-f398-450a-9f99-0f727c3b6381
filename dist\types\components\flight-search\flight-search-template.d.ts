import "../airports-menu/airports-menu";
export declare const flightSearchTemplate: (language: string, isChild: boolean, _isShowBox: boolean, _isShowBottom: boolean, isMobile: boolean, isRT: boolean, adult: number, child: number, infant: number, passengerString: string, AirportsDefault: Array<any>, departureAirport: string, arrivalAirport: string, selectedDates: Date[], isSubmitForm: boolean, vertical: boolean, isSetColorTitle: boolean, searchFlights: () => any, handleAirportClick: (event: CustomEvent) => void, toggleDropdown: (event: Event) => void, toggleShowBox: () => void, changeTypeTrip: (isRT: boolean) => void, changeQuantity: (event: Event, type: string, isIncrease: boolean) => void, swapAirport: (event: Event) => void, openDatePicker: () => void, handleLanguageChange: (value: string) => void, showLanguageSelect: boolean) => import("lit-html").TemplateResult<1>;
