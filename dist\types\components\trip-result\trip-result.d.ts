import { LitElement, PropertyValues } from "lit";
import { CryptoService } from "../../services/CryptoService";
import { FlightService } from "../../services/FlightService";
export declare class TripResult extends LitElement {
    private _cryptoService;
    private _flightService;
    static styles: import("lit").CSSResult[];
    autoFillOrderCode: boolean;
    mode: string;
    googleFontsUrl: string;
    font: string;
    urlRePayment: string;
    ApiKey: string;
    color: string;
    uri_searchBox: string;
    showLanguageSelect: boolean;
    autoLanguageParam: boolean;
    private _language;
    private _hasCheckedURL;
    get language(): string;
    set language(value: string);
    get currencySymbolAv(): string;
    private _ApiKey;
    private _isLoading;
    private _isNotValid;
    private _orderAvailable;
    private _orderDetails;
    private _inforAirports;
    private _PaymentNote;
    private _NoteModel;
    private displayMode;
    private convertedVND;
    private currencySymbol;
    private request;
    constructor(_cryptoService: CryptoService, _flightService: FlightService);
    connectedCallback(): void;
    private checkLanguageFromURL;
    private updateURLWithLanguage;
    protected firstUpdated(_changedProperties: PropertyValues): Promise<void>;
    protected updated(_changedProperties: PropertyValues): void;
    getRequest(): Promise<void>;
    AvailableTrip(request: any): Promise<void>;
    RequestEncrypt(data: any): Promise<any>;
    formatPassenger(): void;
    CallAvailableTrip(request: any): Promise<void>;
    getInforAirports(): Promise<void>;
    rePayment(): void;
    handleLanguageChange(newLang: string): void;
    render(): import("lit-html").TemplateResult<1>;
}
