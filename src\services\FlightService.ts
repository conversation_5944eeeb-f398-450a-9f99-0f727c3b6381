import { environment } from "../environments/environment";
import { fetchWithDeviceIdandApiKey } from "../utils/deviceUtils";

const apiUrl = environment.apiUrl;

export class FlightService {
    async request(endpoint: string, request: any, useDeviceId = true, apiKey: string) {
        try {
            const fetchFn = useDeviceId ? fetchWithDeviceIdandApiKey : fetch;
            const response = await fetchFn(`${apiUrl}/api/Library/${endpoint}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(request)
            }, apiKey);

            if (!response.ok) {
                throw response;
            }

            return await response.json();
        } catch (error) {
            throw error;
        }
    }

    SearchTrip(request: any, api: string) { return this.request('SearchTrip', request, true, api); }
    PriceAncillary(request: any, api: string) { return this.request('PriceAncillary', request, true, api); }
    FareRules(request: any, language: string) { return this.request('../FareRules/get-fare-rules/'+ language, request, false, ''); }
    AvailableTrip(request: any, api: string) { return this.request('AvailableTrip', request, true, api); }
    RequestTrip(request: any, api: string) { return this.request('RequestTrip', request, true, api); }
    RePayment(request: any, api: string) { return this.request('RePayment', request, true, api); }
}
