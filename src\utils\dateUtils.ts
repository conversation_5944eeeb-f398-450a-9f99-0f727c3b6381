/**
 * Chuyển đổi đối tượng Date thành chuỗi định dạng yyyy-MM-dd
 * @param date - Đối tượng Date cần chuyển đổi
 * @returns Chuỗi định dạng yyyy-MM-dd
 */
export const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Thêm số 0 ở đầu nếu cần
    const day = date.getDate().toString().padStart(2, '0'); // Thêm số 0 ở đầu nếu cần
    return `${year}-${month}-${day}`; // Định dạng yyyy-MM-dd
};

export function formatDateTo_ddMMyyyy(date: Date, language: string): string | null {
    if (!date || date === undefined) {
        return null;
    }
    var date1 = new Date(date)
    if (language === 'vi') {
        // Trả về dạng dd/MM/yyyy
        return date1.toLocaleDateString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    // Trả về dạng 11 Jul, 2025
    const day = date1.getDate().toString().padStart(2, '0');
    const month = date1.toLocaleString('en-US', { month: 'short' }); // Jul
    const year = date1.getFullYear();
    return `${day} ${month}, ${year}`;
}

export function getDurationByArray(legs: any[]): string {
    if (legs == null) return '';
    var duration = 0;
    var departure = new Date(legs[0].DepartureDate);
    var arrival = new Date(legs[legs.length - 1].ArrivalDate);
    duration = arrival.getTime() - departure.getTime();
    var hours = Math.floor(duration / 3600000);
    var minutes = Math.floor((duration % 3600000) / 60000);
    return hours.toString().padStart(2, '0') + 'h' + minutes.toString().padStart(2, '0');
}

export function formatDateToString(date: any, language: string): string | null {
    if (!date) return null;

    let day: number, month: number, year: number;

    // Trường hợp là đối tượng Date
    if (date instanceof Date) {
        day = date.getDate();
        month = date.getMonth() + 1;
        year = date.getFullYear();
    }
    // Trường hợp là object có day/month/year
    else if (typeof date === 'object' && ('day' in date || 'month' in date || 'year' in date)) {
        day = date.day || 1;
        month = date.month || 1;
        year = date.year || 2000;
    }
    // Trường hợp là string có thể parse được
    else if (typeof date === 'string') {
        const parsed = new Date(date);
        if (isNaN(parsed.getTime())) return null;
        day = parsed.getDate();
        month = parsed.getMonth() + 1;
        year = parsed.getFullYear();
    } else {
        return null;
    }

    const dd = day.toString().padStart(2, '0');
    const mm = month.toString().padStart(2, '0');
    const yyyy = year.toString();

    // Trả về theo ngôn ngữ
    return language === 'vi' ? `${dd}/${mm}/${yyyy}` : `${mm}/${dd}/${yyyy}`;
}


export function validatePhone(phone: string): boolean {
    return phone.match(/^[0-9]{6,12}$/) ? true : false;
}
export function validateEmail(email: string): boolean {
    return email.match(/^([\w.%+-]+)@([\w-]+\.)+([\w]{2,})$/i) ? true : false;
}

export function getTimeFromDateTime(dateTime: string, language: string): string {
    if(language === 'en'){
        const date = new Date(dateTime);
        //retun 12h am/pm
        return date.toLocaleString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true });
    }
    const date = new Date(dateTime);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
}

export function convertDurationToHour(duration: number): string {
    const hours = Math.floor(duration / 60).toString().padStart(2, '0');
    const minutes = (duration % 60).toString().padStart(2, '0');
    return `${hours}h${minutes}`;
}
export function getDuration(leg: any): string {
    if (leg == null) return '';
    var departure = new Date(leg.DepartureDate);
    var arrival = new Date(leg.ArrivalDate);
    var duration = arrival.getTime() - departure.getTime();
    var hours = Math.floor(duration / 3600000);
    var minutes = Math.floor((duration % 3600000) / 60000);
    return hours.toString().padStart(2, '0') + 'h' + minutes.toString().padStart(2, '0');
}
export function formatddMMyyyy(date: string): string {
    if (date == null) return '';
    var dateObj = new Date(date);
    return dateObj.getDate().toString().padStart(2, '0') + '/' + ((dateObj.getMonth() + 1).toString().padStart(2, '0')) + '/' + dateObj.getFullYear();
}

export function getDayInWeek(date: string): string {
    const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
    const dateObj = new Date(date);
    return days[dateObj.getDay()];
}

export function getFlights(legs: any[]): string {
    return legs?.map(leg => leg.OperatingAirlines + leg.FlightNumber).join(' - ');
}

export function getDirect(legs: any[], language: string): string {
    return legs.length > 1 ? (language === 'vi' ? "Nhiều chặng" : "Multiple stops") : (language === 'vi' ? "Bay thẳng" : "Direct flight");
}

export function getDurationLeg(leg: any): string {
    var departure = new Date(leg.DepartureDate);
    var arrival = new Date(leg.ArrivalDate);
    var duration = (arrival.getTime() - departure.getTime()) / 60000;
    return convertDurationToHour(duration);
}

export function getPassengerDescription(searchTripRequest: TripSelectionModelRq | null, paxType: string, language: string): string {
    if (!searchTripRequest) {
        return '';
    }
    switch (paxType) {
        case 'ADT':
            return `${searchTripRequest?.Adult} x ${language === 'vi' ? 'Người lớn' : 'Adult'}`;
        case 'CHD':
            return `${searchTripRequest?.Child} x ${language === 'vi' ? 'Trẻ em' : 'Child'}`;
        case 'INF':
            return `${searchTripRequest?.Infant} x ${language === 'vi' ? 'Em bé' : 'Infant'}`;
        default:
            return '';
    }
}

export function getPassengerDescriptionV2(paxType: string, ADT: number = 0, CHD: number = 0, INF: number = 0, language: string): string {

    switch (paxType) {
        case 'ADT':
            return `${ADT} x ${language === 'vi' ? 'Người lớn' : 'Adult'}`;
        case 'CHD':
            return `${CHD} x ${language === 'vi' ? 'Trẻ em' : 'Child'}`;
        case 'INF':
            return `${INF} x ${language === 'vi' ? 'Em bé' : 'Infant'}`;
        default:
            return '';
    }
}
export function getTypePassenger(passenger: Passenger, passengers: Passenger[], language: string): string {
    var type = '';
    switch (passenger.type) {
        case 'adult':
            type = language === 'vi' ? 'Người lớn' : 'Adult';
            break;
        case 'child':
            type = language === 'vi' ? 'Trẻ em' : 'Child';
            break;
        case 'infant':
            type = language === 'vi' ? 'Em bé' : 'Infant';
            break;
    }
    //get index of passenger in type
    var indexPassenger = passengers.filter(p => p.type === passenger.type).indexOf(passenger);

    var result = `${type} ${indexPassenger + 1}`;
    return result;
}

export function formatNumber(value: number, convertedVND: number, language: string): string {
    if (value === null || value === undefined) return '';
    const result = language === 'vi' ? value : value / convertedVND;

    if (language === 'vi' || convertedVND === 1) {
        return Math.round(result).toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    } else {
        const [integerPart, decimalPart] = result.toFixed(2).split('.');
        const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        return `${formattedInteger}.${decimalPart}`;
    }
}

export function range(start: number, end: number): number[] {
    if (start > end) {
        return [];
    }
    return Array(end - start + 1).fill(0).map((_, idx) => start + idx);
}

export function getDayOfWeek(date: Date, language: string): string {
    const dayOfWeek = new Date(date).getDay();
    return language === 'vi' ? ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'][dayOfWeek] : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayOfWeek];
}

export function formatDate_ddMM(date: Date, language: string): string {
    const day = date.getDate().toString().padStart(2, '0');

    if (language === 'vi') {
        const monthStr = (date.getMonth() + 1).toString().padStart(2, '0');
        return `${day}/${monthStr}`;
    }
    const monthStr = date.toLocaleString('en', { month: 'short' });
    return `${day} ${monthStr}`;
}

export function formatDateS(dateString: string, language: string): string {
    var date = new Date(dateString);
    return formatDateTypeDate(date, language);
}

export function formatDateTypeDate(date: Date, language: string): string {
    const daysOfWeek = language === 'vi'
        ? ["Chủ nhật", "Thứ hai", "Thứ ba", "Thứ tư", "Thứ năm", "Thứ sáu", "Thứ bảy"]
        : ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

    const dayOfWeek = daysOfWeek[date.getDay()];
    if (!dayOfWeek) {
        return '';
    }

    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();

    if (language === 'vi') {
        const monthStr = (date.getMonth() + 1).toString().padStart(2, '0');
        return `${dayOfWeek}, ${day}/${monthStr}/${year}`;
    }

    // Format for English: Thursday, 31 July 2023
    const monthStr = date.toLocaleString('en', { month: 'long' });
    return `${dayOfWeek}, ${day} ${monthStr} ${year}`;
}

export function getFareType(bookingInfos: any[]): string {
    return bookingInfos.map(bookingInfo => bookingInfo.FareType || bookingInfo.CabinName).join(' - ');
}

export function debounce(func: (...args: any[]) => void, wait: number) {
    let timeout: number | undefined;
    return (...args: any[]) => {
        clearTimeout(timeout);
        timeout = window.setTimeout(() => func(...args), wait);
    };
}