export declare const TripPassengerTemplate: (uri_searchBox: string, language: string, _isLoading: boolean, _isGlobal: boolean, _isSubmit: boolean, _isShowDetailsTrip: boolean, _dataCartSticket: any, _inforContact: any, _inforAirports: any[], _pricePaxInfor: any[], _passengers: any[], _phoneCodes: any[], _servicePrice: number, _sumPrice: number, _currencySymbol: string, _convertedVND: number, reSearchTrip: () => void, showDetailsTrip: () => void, getBaggageOfType: (airline: string, type: string) => any, togglePassportVisibility: (passenger: any) => void, updateBirthday: (event: any, passenger: any, index: number) => void, openDatePicker: (index: number) => void, validateBirthday: (birthday: string, type: string) => boolean, updateFullname: (event: Event, passenger: any) => void, updateGender: (event: Event, passenger: any) => void, updateCountry: (event: Event, passenger: any) => void, openDatePickerPS: (index: number) => void, updatepassportDate: (event: Event, passenger: any, index: number) => void, changeBaggage: (event: Event, passenger: any, baggage: any) => void, updatePhoneCode: (event: Event) => void, updateLemailMain: (event: Event) => void, updatePassport: (event: Event, passenger: any) => void, updatePhoneMain: (event: Event) => void, updateEmailMain: (event: Event) => void, goToPayment: () => void, handleLanguageChange: (value: string) => void, showLanguageSelect: boolean) => import("lit-html").TemplateResult<1>;
