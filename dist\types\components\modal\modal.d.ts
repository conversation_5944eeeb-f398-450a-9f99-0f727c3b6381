import { LitElement, PropertyValues } from "lit";
export declare class Modal extends LitElement {
    static styles: import("lit").CSSResult[];
    uri_searchBox: string;
    private isOpen;
    private _title;
    private content;
    private isCountDown;
    private countdown;
    constructor();
    static get properties(): {
        isOpen: {
            type: BooleanConstructor;
        };
        _title: {
            type: StringConstructor;
        };
        content: {
            type: StringConstructor;
        };
        isCountDown: {
            type: BooleanConstructor;
        };
        countdown: {
            type: NumberConstructor;
        };
    };
    protected firstUpdated(_changedProperties: PropertyValues): void;
    protected update(changedProperties: PropertyValues): void;
    startCountdown(): void;
    start({ title, content, isCountDown, countdown, }?: {
        title?: string | undefined;
        content?: string | undefined;
        isCountDown?: boolean | undefined;
        countdown?: number | undefined;
    }): void;
    close(): void;
    reSearch(): void;
    render(): void;
}
