import { LitElement, css, html, unsafeCSS } from "lit";
import { customElement, property, state } from "lit/decorators.js";
import styles from '../../styles/styles.css';

@customElement("progress-bar")
export class ProgressBar extends LitElement {
    static styles = [
        unsafeCSS(styles),
        css`
        :host {
        font-family: var(--nmt-font, 'Roboto', sans-serif);
        }`
    ];
    @property({ type: String }) language = "vi";
    @property() _indeterminate = false;
    @property() _mode = false;
    @property({ type: Number }) _progress = 0;

    @state() _template = '';

    constructor() {
        super();
    }

    render() {

        return html`
        <div class="w-full flex flex-col items-center space-x-2 mb-4 -mt-2 relative  px-2 pt-4 text-white rounded-lg ">
            <div class="w-full flex items-center space-x-2 relative">
                <div class="h-1 w-full bg-gray-200 overflow-hidden rounded-full relative">
                    <div class="absolute top-0 left-0 h-full bg-nmt-300 opacity-50 transition-all duration-300 ease-in-out ${this._indeterminate ? 'animate-progress-indeterminate' : ''}" style="width: 100%;">
                    </div>
                    <div class="h-full bg-nmt-500 transition-all duration-300 ease-in-out"
                        style="width: ${this._progress}%;">
                    </div>
                </div>
                <div>
                    <span class="text-xs text-gray-500">${this._progress.toFixed(2)}%</span>
                </div>
            </div>
            <div>
                <span class="px-2 py-1 text-white rounded-lg line-clamp-1 ${this._mode ? 'wave-animation' : ''}">
                    ${this.language === 'vi' ? 'Đang tìm chuyến bay, vui lòng chờ trong giây lát...' : 'Searching for flights, please wait a moment...'}
                    
                </span>
            </div>
        </div>
        `;
    }
}

