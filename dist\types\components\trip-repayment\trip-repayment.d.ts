import { LitElement, PropertyValues } from "lit";
import { CryptoService } from "../../services/CryptoService";
import { FlightService } from "../../services/FlightService";
export declare class TripRePayment extends LitElement {
    private _cryptoService;
    private _flightService;
    static styles: import("lit").CSSResult[];
    autoFillOrderCode: boolean;
    mode: string;
    googleFontsUrl: string;
    font: string;
    termsUrl: string;
    ApiKey: string;
    color: string;
    uri_searchBox: string;
    showLanguageSelect: boolean;
    autoLanguageParam: boolean;
    private _language;
    private _hasCheckedURL;
    get language(): string;
    set language(value: string);
    get currencySymbolAv(): string;
    private _ApiKey;
    private _isLoading;
    private _agree;
    private _isSubmit;
    private _isNotValid;
    private _orderDetails;
    private _orderAvailable;
    private _isShowDetailsTrip;
    private _passengers;
    private _inforAirports;
    private _pricePaxInfor;
    private _servicePrice;
    private _sumPrice;
    private _totalPrice;
    private _paymentMethod;
    private titleModal;
    private contentModal;
    private isCountDown;
    private countdown;
    private isShowModal;
    private request;
    private banks;
    private bankNote;
    private transferContent;
    private cashInfo;
    private agent;
    private displayMode;
    private convertedVND;
    private currencySymbol;
    constructor(_cryptoService: CryptoService, _flightService: FlightService);
    connectedCallback(): void;
    private checkLanguageFromURL;
    private updateURLWithLanguage;
    protected firstUpdated(_changedProperties: PropertyValues): Promise<void>;
    protected updated(_changedProperties: PropertyValues): void;
    loadPaymentValue(): void;
    getRequest(): Promise<void>;
    AvailableTrip(request: any): Promise<void>;
    CallAvailableTrip(request: any): Promise<void>;
    getSumServicePrice(): number;
    formatPassenger(): void;
    getInforAirports(): Promise<void>;
    getSumPrice(): number;
    getPricePax(): void;
    showDetailsTrip(): void;
    setPaymentMethod(method: string): void;
    selectBank(bank: any): void;
    setAgree(agree: boolean): void;
    RequestEncrypt(data: any): Promise<any>;
    reSearchTrip(): void;
    openModal(title?: string, content?: string, isCountDown?: boolean): void;
    CallRequestTrip(): Promise<void>;
    onPayment(): Promise<void>;
    handleLanguageChange(newLang: string): void;
    render(): import("lit-html").TemplateResult<1>;
}
