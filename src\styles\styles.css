@tailwind base;
@tailwind components;
@tailwind utilities;



/* start dropdown */
.dropdown {
    @apply inline-block;
}


.dropdown-menu {
    @apply hidden absolute bg-[#f9f9f9] shadow-md;
}

.dropdown.open .dropdown-menu {
    @apply !block;
}


.dropdown-item,
.dropdown-toggle {
    @apply cursor-pointer;
}

.dropdown-item:hover {
    @apply bg-[#ddd];
}

/* end dropdown */

.mask-top-circle-cut {
    -webkit-mask-image: radial-gradient(circle at 50% 0%, transparent 50%, black 51%);
    mask-image: radial-gradient(circle at 50% 0%, transparent 50%, black 51%);
}


.mask-bottom-circle-cut {
    -webkit-mask-image: radial-gradient(circle at 50% 100%, transparent 50%, black 51%);
    mask-image: radial-gradient(circle at 50% 100%, transparent 50%, black 51%);
}


@keyframes indeterminate {
    0% {
        transform: translateX(-100%);
    }

    50% {
        transform: translateX(0%);
    }

    100% {
        transform: translateX(100%);
    }
}

.animate-progress-indeterminate {
    animation: indeterminate 2s infinite;
}

@keyframes waveMove {
    0% {
        background-position: 0% 0px;
    }


    25% {
        background-position: 25% 0px;
    }

    50% {
        background-position: 50% 0px;
    }

    75% {
        background-position: 75% 0px;
    }



    100% {
        background-position: 100% 0px;
    }
}


@keyframes waveEffect {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

.wave-animation {
    background: linear-gradient(90deg, var(--color-nmt-500), var(--color-nmt-300), #ffffff);
    background-size: 200% 200%;
    animation: waveEffect 2s infinite linear;
}

/* start loader */

.loader-container {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.085);
    z-index: 1099;
    opacity: 0.8;
    /* Điều chỉnh độ tối của nền */
    top: 0;
    left: 0;
    display: flex;
    /* Dùng flexbox để căn giữa nội dung */
    justify-content: center;
    /* Căn giữa theo chiều ngang */
    align-items: center;
    /* Căn giữa theo chiều dọc */
}

.loader-container img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    position: absolute;
    /* Chuyển từ absolute sang relative */
    z-index: 1100;
    /* Đảm bảo nổi trên nền */
}

.loader-container .loadidng-vertical {
    @apply absolute z-[1110] mt-52 px-2 py-1 text-white rounded-lg;
}

.loader {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    animation: rotate 1s linear infinite;
    position: absolute;
    z-index: 1100;
    /* Đảm bảo nổi trên nền */
}

.loader::before,
.loader::after {
    content: "";
    box-sizing: border-box;
    position: absolute;
    inset: 0px;
    border-radius: 50%;
    border: 5px solid #fff;
    animation: prixClipFix 2s linear infinite;
}

.loader::after {
    border-color: #ff3d00;
    animation: prixClipFix 2s linear infinite, rotate 0.5s linear infinite reverse;
    inset: 6px;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes prixClipFix {
    0% {
        clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);
    }

    25% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);
    }

    50% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);
    }

    75% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);
    }

    100% {
        clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);
    }
}

/* end loader */

.slide-in {
    height: auto;
    opacity: 1;
    transform: translateY(0);
    transition: height 500ms ease-in-out, opacity 500ms ease-in-out, transform 500ms ease-in-out;
}

.slide-out {
    height: 0;
    opacity: 0;
    transform: translateY(100%);
    transition: height 500ms ease-in-out, opacity 500ms ease-in-out, transform 500ms ease-in-out;
}

.flatpickr-day.endRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.endRange.nextMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.endRange:focus,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected,
.flatpickr-day.selected.inRange,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.selected:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.startRange:focus,
.flatpickr-day.startRange:hover {
    background: var(--color-nmt-400) !important;
    border-color: var(--color-nmt-400) !important;
}

.offcanvas-backdrop.show {
    @apply opacity-50;
}

.offcanvas-backdrop {
    @apply fixed top-0 left-0 inset-0 z-[1040] w-screen h-screen bg-black;
}

.fade {
    @apply transition-opacity duration-150 ease-linear;
}

.offcanvas.showing,
.offcanvas.show:not(.hiding) {
    @apply transform-none;
}

.offcanvas.showing,
.offcanvas.hiding,
.offcanvas.show {
    @apply visible;
}

.offcanvas.offcanvas-end {
    @apply top-0 right-0 w-[400px] border-l border-white/25 transform translate-x-full;
}

.offcanvas {
    @apply fixed bottom-0 z-[1045] flex flex-col max-w-full text-black bg-white outline-none transition-transform duration-300 ease-in-out;
}

.offcanvas-header {
    @apply flex items-center px-4 py-3 border-b border-gray-500/25;
}

.offcanvas-body {
    @apply grow p-4 overflow-y-auto;
}

.text-end {
    @apply text-right;
}

.offcanvas-title {
    @apply flex-grow text-lg font-semibold;
}

h4,
.h4 {
    @apply text-lg font-semibold;
}

.offcanvas-header .btn-close {
    @apply p-2 ml-auto;
}

.slide-in {
    height: auto !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
    transition: height 500ms ease-in-out, opacity 500ms ease-in-out, transform 500ms ease-in-out !important;
}

.slide-out {
    height: 0;
    opacity: 0;
    transform: translateY(100%);
    transition: height 500ms ease-in-out, opacity 500ms ease-in-out, transform 500ms ease-in-out;
}