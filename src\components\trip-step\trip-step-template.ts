import { html } from "lit";

export const tripStepTemplate = (
    language: string,
    uri_searchBox: string
) => html`
<div class="pt-4 pb-8">
    <div class="w-full max-w-4xl mx-auto max-md:overflow-x-auto max-md:overflow-y-hidden max-md:pb-[10px]">
        <div class="flex items-center justify-center max-md:space-x-2">
            <div class="flex items-center">
                <div class="relative group">
                    <div
                        class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white  cursor-pointer">
                        <a href="/${uri_searchBox}"
                            class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105 cursor-pointer">
                            <div class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500 cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-search w-5 h-5 text-white">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="m21 21-4.3-4.3"></path>
                                </svg>
                            </div><span
                                class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden">${language === 'vi' ? 'Tìm kiếm' : 'Search'}</span>
</a>

                    </div>
                </div>
                <div
                    class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-white text-gray-400 max-md:hidden">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-chevron-right w-5 h-5">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </div>
            </div>
            <div class="flex items-center">
                <div class="relative group">
                    <div
                        class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed">
                        <div class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                            <div class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-ticket w-5 h-5 text-white">
                                    <path
                                        d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z">
                                    </path>
                                    <path d="M13 5v2"></path>
                                    <path d="M13 17v2"></path>
                                    <path d="M13 11v2"></path>
                                </svg>
                            </div><span
                                class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 ">${language === 'vi' ? 'Chọn vé' : 'Select ticket'}</span>
                        </div>
                        <div class="absolute -bottom-[5px] left-1/2 transform -translate-x-1/2">
                            <div class="w-2.5 h-2.5 rounded-full bg-nmt-500 ring-4 ring-white"></div>
                        </div>
                    </div>
                </div>
                <div class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400 ">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-chevron-right w-5 h-5">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </div>
            </div>
            <div class="flex items-center">
                <div class="relative group">
                    <div
                        class="flex items-center h-12 md:px-6 px-2 bg-white rounded-lg transition-all duration-300 ease-in-out border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 cursor-not-allowed">
                        <div class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                            <div
                                class="rounded-lg p-2 transition-colors duration-300 bg-gray-100 group-hover:bg-gray-200">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-user w-5 h-5 text-gray-600">
                                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                            </div><span
                                class="font-medium tracking-wide whitespace-nowrap text-[15px] text-gray-600 max-md:hidden">${language === 'vi' ? 'Thông tin' : 'Information'}</span>
                        </div>
                    </div>
                </div>
                <div
                    class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400 max-md:hidden">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-chevron-right w-5 h-5">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </div>
            </div>
            <div class="flex items-center">
                <div class="relative group">
                    <div
                        class="flex items-center h-12 md:px-6 px-2 bg-white rounded-lg transition-all duration-300 ease-in-out border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 cursor-not-allowed">
                        <div class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                            <div
                                class="rounded-lg p-2 transition-colors duration-300 bg-gray-100 group-hover:bg-gray-200">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-check w-5 h-5 text-gray-600">
                                    <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                    <line x1="2" x2="22" y1="10" y2="10"></line>
                                </svg>
                            </div>
                            <span
                                class="font-medium tracking-wide whitespace-nowrap text-[15px] text-gray-600 max-md:hidden">${language === 'vi' ? 'Thanh toán' : 'Payment'}</span>
                        </div>

                    </div>
                </div>
                <div
                    class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400  max-md:hidden">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-chevron-right w-5 h-5">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </div>
            </div>
            <div class="flex items-center">
                <div class="relative group">
                    <div
                        class="flex items-center h-12 md:px-6 px-2 bg-white rounded-lg transition-all duration-300 ease-in-out border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 cursor-not-allowed">
                        <div class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                            <div
                                class="rounded-lg p-2 transition-colors duration-300 bg-gray-100 group-hover:bg-gray-200">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-check w-5 h-5 text-gray-600">
                                    <path d="M20 6 9 17l-5-5"></path>
                                </svg>
                            </div><span
                                class="font-medium tracking-wide whitespace-nowrap text-[15px] text-gray-600 max-md:hidden">${language === 'vi' ? 'Hoàn tất' : 'Complete'}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
`;