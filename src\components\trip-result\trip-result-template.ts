import { html } from "lit";
import { convertDurationToHour, formatDateTo_ddMMyyyy, formatDateToString, formatddMMyyyy, formatNumber, getDayInWeek, getDuration, getDurationByArray, getTimeFromDateTime } from "../../utils/dateUtils";
import { environment } from "../../environments/environment";

const apiUrl = environment.apiUrl;

export const TripResultTemplate = (
    autoFillOrderCode: boolean,
    uri_searchBox: string,
    language: string,
    _isLoading: boolean,
    _isNotValid: boolean,
    _orderAvailable: any,
    _orderDetails: any,
    _inforAirports: any[],
    _PaymentNote: any,
    __NoteModel: any,
    _currencySymbol: string,
    _convertedVND: number,
    rePayment: () => void,
    handleLanguageChange: (value: string) => void,
    showLanguageSelect: boolean
) => html`
 ${_isLoading ? html`
    <div class="static" *ngIf="isLoading">
        <div class="loader-container">
            <span class="loader"></span>
            <img src="${apiUrl}/assets/img/background/trip_loading2.gif"/>
            <span class="loadidng-vertical  bg-gradient-to-br from-nmt-600 to-nmt-400 shadow-lg shadow-white">${language === 'vi' ? `Đang kiểm tra chuyến bay, vui lòng chờ trong giây lát...` : `Checking flight, please wait a moment...`}</span>
        </div>
    </div>
    ` : ""}

    <div class="w-full bg-gray-100  relative min-h-screen  max-md:pb-24">
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        <section class="text-gray-600 body-font">
            <div class="container md:px-5   mx-auto">
            ${_isLoading ? html`
            <div class="flex flex-col text-center w-full md:mb-10 ">
                    <h1 class="inline-block mb-2 text-3xl font-extrabold tracking-tight text-gray-900 dark:text-white">
                        ${language === 'vi' ? `Đang tìm đơn hàng của bạn` : `Finding your order`}
                    </h1>
                    <p class="text-gray-500 dark:text-gray-400">${language === 'vi' ? `Vui lòng chờ trong giây lát...` : `Please wait a moment...`}</p>
                </div>
            `: html`
            ${_isNotValid ? html`
            <div>
                    <div class="pt-4 pb-8">
                        <div
                            class="w-full max-w-4xl mx-auto max-md:overflow-x-scroll max-md:overflow-y-hidden max-md:pb-[10px]">
                            <div class="flex items-center justify-center max-md:space-x-2">
                                <div class="flex items-center">
                                    <div class="relative group">
                                        <div
                                            class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white  cursor-pointer">
                                            <a href="/${uri_searchBox}"
                                                class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                                <div
                                                    class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-search w-5 h-5 text-white">
                                                        <circle cx="11" cy="11" r="8"></circle>
                                                        <path d="m21 21-4.3-4.3"></path>
                                                    </svg>
                                                </div><span
                                                    class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden">
                                                    ${language === 'vi' ? `Tìm kiếm` : `Search`}
                                                </span>
            </a>

                                        </div>
                                    </div>
                                    <div
                                        class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400 max-md:hidden">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-chevron-right w-5 h-5">
                                            <path d="m9 18 6-6-6-6"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="relative group">
                                        <div
                                            class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed">
                                            <div
                                                class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                                <div
                                                    class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-ticket w-5 h-5 text-white">
                                                        <path
                                                            d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z">
                                                        </path>
                                                        <path d="M13 5v2"></path>
                                                        <path d="M13 17v2"></path>
                                                        <path d="M13 11v2"></path>
                                                    </svg>
                                                </div><span
                                                    class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden">${language === 'vi' ? `Chọn vé` : `Select ticket`}</span>
                                            </div>

                                        </div>
                                    </div>
                                    <div
                                        class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400 max-md:hidden">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-chevron-right w-5 h-5">
                                            <path d="m9 18 6-6-6-6"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="relative group">
                                        <div
                                            class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed">
                                            <div
                                                class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                                <div
                                                    class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-user w-5 h-5 text-white">
                                                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                                        <circle cx="12" cy="7" r="4"></circle>
                                                    </svg>
                                                </div><span
                                                    class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600  max-md:hidden">${language === 'vi' ? `Thông tin` : `Information`}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400  max-md:hidden">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-chevron-right w-5 h-5">
                                            <path d="m9 18 6-6-6-6"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="relative group">
                                        <div
                                            class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed">
                                            <div
                                                class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                                <div
                                                    class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-credit-card w-5 h-5 text-white">
                                                        <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                                        <line x1="2" x2="22" y1="10" y2="10"></line>
                                                    </svg>
                                                </div>
                                                <span
                                                    class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden">${language === 'vi' ? `Thanh toán` : `Payment`}</span>
                                            </div>

                                        </div>
                                    </div>
                                    <div
                                        class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400  max-md:hidden">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-chevron-right w-5 h-5">
                                            <path d="m9 18 6-6-6-6"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="relative group">
                                        <div
                                            class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed">
                                            <div
                                                class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                                <div
                                                    class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-credit-card w-5 h-5 text-white">
                                                        <path d="M20 6 9 17l-5-5"></path>
                                                    </svg>
                                                </div><span
                                                    class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600">${language === 'vi' ? `Hoàn tất` : `Complete`}</span>
                                            </div>
                                            <div class="absolute -bottom-[5px] left-1/2 transform -translate-x-1/2">
                                                <div class="w-2.5 h-2.5 rounded-full bg-nmt-500 ring-4 ring-white">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid md:grid-cols-5 grid-cols-1 md:gap-8 mt-6">
                    <div class="col-span-3">
                        <div class="w-full overflow-x-auto">
                            <div class="flex justify-between items-center mb-2">
                                <h1 class="inline-block  text-xl font-extrabold tracking-tight text-gray-900 dark:text-white">
                                ${language === 'vi' ? `Chi Tiết Đơn Hàng` : `Order Details`}</h1>

                                <div class="flex justify-end items-center  ">
                                    ${showLanguageSelect ? html`
                                    <select id="language" 
                                        class=" text-sm  bg-gray-200 rounded-lg px-2 py-1  border-none focus:outline-none text-gray-600 "
                                        .value=${language}
                                        @change=${(e: Event) => handleLanguageChange((e.target as HTMLSelectElement).value)}
                                    >
                                        <option style="background-color: #f0f0f0; color: black;" value="en">English</option>
                                        <option style="background-color: #f0f0f0; color: black;" value="vi">Tiếng Việt</option>
                                    </select>
                                    ` : ''}
                                </div>
                            </div>
                            
                            <div class="relative overflow-x-auto shadow border border-gray-100 rounded-lg">
                                <table
                                    class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 shadow">
                                    <tbody>
                                        <tr class="bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700">
                                            <td scope="row"
                                                class="md:ps-6 ps-2 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                ${language === 'vi' ? `Mã đơn hàng` : `Order code`}
                                            </td>
                                            <td class="md:px-6 px-2 py-2">
                                                <span
                                                    class="font-extrabold text-lg text-nmt-500">${_orderAvailable?.OrderCode}</span>
                                            </td>
                                        </tr>
                                        <tr class="bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700">
                                            <td scope="row"
                                                class="md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white">
                                                ${language === 'vi' ? `Ngày đặt` : `Order date`}
                                            </td>
                                            <td class="md:px-6 px-2 py-2">
                                                <span
                                                    class="text-base text-gray-700 dark:text-gray-400">${_orderAvailable?.TimeCreate}</span>
                                            </td>
                                        </tr>
                                        <tr class="bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700">
                                            <td scope="row"
                                                class="md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white">
                                                ${language === 'vi' ? `Tình trạng` : `Status`}
                                            </td>
                                            <td class="md:px-6 px-2 py-2">
                                                <span
                                                    class="text-base text-gray-700 dark:text-gray-400 flex items-center flex-row gap-2">
                                                    ${_orderAvailable?.Status === 0 ? html`
                                                    <span class="text-red-600">
                                                        ${language === 'vi' ? `Chờ thanh toán` : `Pending payment`}</span>
                                                    <button @click=${rePayment}
                                                        class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm md:px-6 px-3 md:py-3 py-1.5 text-center me-2">
                                                        ${language === 'vi' ? `Thanh toán ngay` : `Pay now`}
                                                    </button>
                                                    ` : _orderAvailable?.Status === 1 ? html`
                                                    <span class="text-green-600">${language === 'vi' ? `Đã thanh toán` : `Paid`}</span>
                                                    ` : _orderAvailable?.Status === -1 ? html`
                                                    <span class="text-red-600">${language === 'vi' ? `Đã hủy` : `Cancelled`}</span>
                                                    `: ''}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr class="bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700">
                                            <td scope="row"
                                                class="md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white">
                                                ${language === 'vi' ? `Giá trị đơn hàng` : `Order value`}
                                            </td>
                                            <td class="md:px-6 px-2 py-2">
                                                <span class="font-extrabold text-lg text-nmt-500">${formatNumber(_orderDetails?.totalPrice, _convertedVND, language)} ${_currencySymbol}</span>
                                            </td>
                                        </tr>
                                        <tr class="bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700">
                                            <td scope="row"
                                                class="md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white">
                                                ${language === 'vi' ? `Hình thức thanh toán` : `Payment method`}
                                            </td>
                                            <td class="md:px-6 px-2 py-2">
                                            ${_orderAvailable?.PaymentMethod.includes('bank-transfer') ? html`
                                            <div class="flex-1">
                                                    <label for="bank-transfer"
                                                        class="items-center  cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            class="mr-2 h-5 w-5 text-nmt-600 inline-flex">
                                                            <rect width="20" height="16" x="2" y="6" rx="2"></rect>
                                                            <path d="M12 3v10"></path>
                                                            <path d="m5 9 7-4 7 4"></path>
                                                        </svg>
                                                        ${language === 'vi' ? `Chuyển Khoản Ngân Hàng` : `Bank Transfer`}
                                                    </label>
                                                    <p class="text-sm text-gray-500 mt-1">${language === 'vi' ? `Thanh toán trực tiếp từ tài khoản ngân hàng của bạn` : `Pay directly from your bank account`}</p>

                                                    <div class="mt-4 space-y-4 pt-3 border-t border-nmt-100">
                                                        <div class="bg-nmt-100 p-3 rounded-md text-sm">
                                                            <p class="font-medium">${language === 'vi' ? `Thông tin chuyển khoản` : `Transfer information`}</p>
                                                        </div>

                                                        <div class="space-y-3">
                                                            <div class="grid grid-cols-3 gap-2 text-sm">
                                                                <div class="text-gray-600">${language === 'vi' ? `Chủ Tài Khoản:` : `Account Holder:`}</div>
                                                                <div class="col-span-2 font-medium">
                                                                    ${_PaymentNote?.banksInfo[0]?.accountHolder}
                                                                </div>

                                                                <div class="text-gray-600">${language === 'vi' ? `Ngân hàng:` : `Bank:`}</div>
                                                                <div class="col-span-2 font-medium"> ${_PaymentNote?.banksInfo[0]?.bankName} </div>

                                                                <div class="text-gray-600">${language === 'vi' ? `Chi nhánh:` : `Branch:`}</div>
                                                                <div class="col-span-2 font-medium"> ${_PaymentNote?.banksInfo[0]?.branch}</div>

                                                                <div class="text-gray-600">${language === 'vi' ? `Số tài khoản:` : `Account Number:`}</div>
                                                                <div class="col-span-2 font-medium"> ${_PaymentNote?.banksInfo[0]?.accountNumber}</div>

                                                                <div class="text-gray-600">${language === 'vi' ? `Nội dung CK:` : `Transfer Content:`}</div>
                                                                <div class="col-span-2 font-medium">${_PaymentNote?.transferContent} ${autoFillOrderCode ? _orderAvailable?.OrderCode : ''}</div>
                                                                
                                                                ${_PaymentNote?.banksInfo[0]?.qrImageUrl ? html`
                                                                <div class="text-gray-600">${language === 'vi' ? 'QR Code:' : 'QR Code:'}</div>
                                                                <div class="col-span-2 font-medium">
                                                                    <img src="${_PaymentNote?.banksInfo[0]?.qrImageUrl}" alt="${_PaymentNote?.banksInfo[0]?.bankName}" class="h-36 rounded-md bg-gray-100 shadow-md" />
                                                                </div>
                                                                ` : ''}
                                                            </div>
                                                        </div>
                                                        <div
                                                            class="bg-nmt-50 border border-nmt-200 p-3 rounded-md mt-4">
                                                            <p class="text-sm font-medium text-nmt-800">${language === 'vi' ? `Hướng dẫn xác nhận thanh toán:` : `Payment confirmation instructions:`}</p>
                                                            <p class="text-sm mt-2"> ${_PaymentNote?.note}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            ` : _orderAvailable?.PaymentMethod.includes('cash') ? html`
                                            <div class="flex-1">
                                                    <label for="cash"
                                                        class="items-center cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            class="mr-2 h-5 w-5 text-nmt-600 inline-flex">
                                                            <rect width="20" height="12" x="2" y="6" rx="2"></rect>
                                                            <circle cx="12" cy="12" r="2"></circle>
                                                            <path d="M6 12h.01M18 12h.01"></path>
                                                        </svg>
                                                        ${language === 'vi' ? `Thanh Toán Tiền Mặt` : `Cash Payment`}
                                                    </label>
                                                    <p class="text-sm text-gray-500 mt-1">${language === 'vi' ? `Thanh toán trực tiếp bằng tiền mặt tại quầy` : `Pay in cash at the counter`}</p>

                                                    <div class="mt-4 space-y-4 pt-3 border-t border-nmt-100">
                                                        <div class="bg-nmt-100 p-3 rounded-md text-sm">
                                                            <p>${language === 'vi' ? `Thông tin thanh toán tiền mặt:` : `Cash payment information:`}</p>
                                                        </div>

                                                        <div class="space-y-3">
                                                            <div class="flex items-start gap-2">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="18"
                                                                    height="18" viewBox="0 0 24 24" fill="none"
                                                                    stroke="currentColor" stroke-width="2"
                                                                    stroke-linecap="round" stroke-linejoin="round"
                                                                    class="mt-0.5 text-nmt-600 min-w-5">
                                                                    <path
                                                                        d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z">
                                                                    </path>
                                                                    <circle cx="12" cy="10" r="3"></circle>
                                                                </svg>
                                                                <div>
                                                                    <p class="font-medium">${language === 'vi' ? `Địa điểm thanh toán:` : `Payment location:`}</p>
                                                                    <p class="text-sm text-gray-600">${language === 'vi' ? `Quầy vé tại văn phòng đại lý của chúng tôi:` : `Ticket counter at our agency's office:`}
                                                                         <span class="font-medium text-gray-800">
                                                                        ${_PaymentNote?.paymentAddress}
                                                                    </span>
                                                                    </p>
                                                                </div>
                                                            </div>

                                                            <div class="flex items-start gap-2">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="18"
                                                                    height="18" viewBox="0 0 24 24" fill="none"
                                                                    stroke="currentColor" stroke-width="2"
                                                                    stroke-linecap="round" stroke-linejoin="round"
                                                                    class="mt-0.5 text-nmt-600">
                                                                    <circle cx="12" cy="12" r="10"></circle>
                                                                    <polyline points="12 6 12 12 16 14"></polyline>
                                                                </svg>
                                                                <div>
                                                                    <p class="font-medium">${language === 'vi' ? `Thời gian:` : `Time:`}</p>
                                                                    <ul class="list-disc pl-6 space-y-1">
                                                                        <li>${_PaymentNote?.paymentDeadline}</li>
                                                                        <li>${_PaymentNote?.workingHours}</li>
                                                                    </ul>
                                                                </div>
                                                            </div>

                                                            <div class="flex items-start gap-2">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="18"
                                                                    height="18" viewBox="0 0 24 24" fill="none"
                                                                    stroke="currentColor" stroke-width="2"
                                                                    stroke-linecap="round" stroke-linejoin="round"
                                                                    class="mt-0.5 text-nmt-600">
                                                                    <rect width="18" height="18" x="3" y="3" rx="2"
                                                                        ry="2">
                                                                    </rect>
                                                                    <line x1="9" x2="15" y1="9" y2="9"></line>
                                                                    <line x1="9" x2="15" y1="15" y2="15"></line>
                                                                </svg>
                                                                <div>
                                                                    <p class="font-medium">${language === 'vi' ? `Giấy tờ cần mang theo:` : `Documents to bring:`}</p>
                                                                    <p class="text-sm text-gray-600">
                                                                    ${_PaymentNote?.note}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ` : _orderAvailable?.PaymentMethod?.includes('e-wallet') ? html`
                                                <div class="flex-1">
                                                    <label for="e-wallet"
                                                        class="items-center cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            class="mr-2 h-5 w-5 text-nmt-600 inline-flex">
                                                            <rect width="20" height="12" x="2" y="6" rx="2"></rect>
                                                            <circle cx="12" cy="12" r="2"></circle>
                                                            <path d="M6 12h.01M18 12h.01"></path>
                                                        </svg>
                                                        ${language === 'vi' ? `Thanh Toán Ví Điện Tử` : `E-Wallet Payment`}
                                                    </label>
                                                    <p class="text-sm text-gray-500 mt-1">${language === 'vi' ? `Thanh toán trực tuyến bằng ví điện tử ${_orderAvailable?.PaymentMethod?.replace('e-wallet', '')}` : `Pay online using your e-wallet ${_orderAvailable?.PaymentMethod?.replace('e-wallet', '')}`}</p>
                                                </div>
                                            `: _orderAvailable?.PaymentMethod === 'credit-card' ? html`
                                            <div class="flex-1">
                                                    <label for="credit-card"
                                                        class="items-center cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            class="mr-2 h-5 w-5 text-nmt-600 inline-flex">
                                                            <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                                            <line x1="2" x2="22" y1="10" y2="10"></line>
                                                        </svg>
                                                        ${language === 'vi' ? `Thanh Toán Thẻ Tín Dụng` : `Credit Card Payment`}
                                                    </label>
                                                    <p class="text-sm text-gray-500 mt-1">${language === 'vi' ? `Thanh toán trực tuyến bằng thẻ tín dụng` : `Pay online using credit card`}</p>
                                                </div>
                                            `: ``}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="mt-10">
                            <h1
                                class="inline-block mb-2 text-xl font-extrabold tracking-tight text-gray-900 dark:text-white">
                                ${language === 'vi' ? `Thông Tin Liên Hệ` : `Contact Information`}</h1>
                            <div class="flex flex-col space-y-2 w-fit">
                                <h2
                                    class="gap-2  text-base tracking-tight text-gray-600  dark:text-white grid grid-cols-2">
                                    <span class="font-bold  whitespace-nowrap">${language === 'vi' ? `Họ và tên` : `Full name`} </span>
                                    <span class=" text-gray-500 dark:text-white font-semibold">:
                                        ${_orderAvailable?.CustomerName}</span>
                                </h2>
                                <h2
                                    class="gap-2  text-base tracking-tight text-gray-600  dark:text-white grid grid-cols-2">
                                    <span class="font-bold  whitespace-nowrap">${language === 'vi' ? `Số điện thoại` : `Phone number`} </span>
                                    <span class=" text-gray-500 dark:text-white font-semibold">:
                                        ${_orderAvailable?.PhoneNumber}</span>
                                </h2>
                                <h2
                                    class="gap-2  text-base tracking-tight text-gray-600  dark:text-white grid grid-cols-2">
                                    <span class="font-bold  whitespace-nowrap">${language === 'vi' ? `Email` : `Email`} </span>
                                    <span class=" text-gray-500 dark:text-white font-semibold inline-flex">:
                                        ${_orderAvailable?.Email}</span>
                                </h2>
                            </div>
                        </div>

                        <div class="w-full overflow-x-auto mt-10">
                            <h1
                                class="inline-block mb-2 text-xl font-extrabold tracking-tight text-gray-900 dark:text-white">
                                ${language === 'vi' ? `Danh Sách Khách` : `Passenger List`}</h1>
                            <div class="relative overflow-x-auto shadow dark:shadow-lg sm:rounded-lg">
                                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                    <thead
                                        class="text-xs text-gray-700 uppercase bg-gray-200 dark:bg-gray-800 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-6 py-3">
                                                ${language === 'vi' ? `Họ và tên` : `Full name`}
                                            </th>
                                            <th scope="col" class="px-6 py-3">
                                                ${language === 'vi' ? `Ngày sinh` : `Date of birth`}
                                            </th>
                                            <th scope="col" class="px-6 py-3">
                                                ${language === 'vi' ? `Giới tính` : `Gender`}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    ${_orderDetails?.paxList.map((pax: any) => html`
                                    <tr
                                            class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                            <td scope="row"
                                                class="md:px-6 px-2 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                <div>
                                                    ${pax?.fullname}
                                                </div>
                                                ${pax?.withInfant ? html`
                                                <div>
                                                    <span class="text-xs text-red-500 ">
                                                        * ${language === 'vi' ? `Em bé` : `Infant`}: ${pax?.withInfant?.fullname} - ${pax?.withInfant?.birthday.day + '/' + pax?.withInfant?.birthday.month + '/' + pax?.withInfant?.birthday.year} - 
                                                    ${pax?.withInfant?.gender === 'MSTR' ? language === 'vi' ? `Bé trai` : `Boy` : pax?.withInfant?.gender === 'MISS' ? language === 'vi' ? `Bé gái` : `Girl` : language === 'vi' ? `Khác` : `Other`}
                                                    
                                                </div>
                                                `: ``}
                                                <div>
                                                ${pax.baggages.map((baggage: any) => html`
                                                ${baggage?.SsrCode ? html`
                                                <div class="text-xs text-gray-500 dark:text-gray-400">${baggage?.type} - ${baggage?.WeightBag} KG</div>
                                                ` : ``}
                                                `)}
                                                </div>
                                            </td>
                                            <td class="md:px-6 px-2 py-2">
                                                ${formatDateToString(pax?.birthday, language)}
                                            </td>
                                            <td class="md:px-6 px-2 py-2">
                                                ${pax.gender === 'MR' ? language === 'vi' ? `Nam` : `Male` : pax.gender === 'MRS' ? language === 'vi' ? `Nữ` : `Female` : language === 'vi' ? `Khác` : `Other`}
                                            </td>
                                        </tr>
                                    `)}
                                        
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>
                    <div class="col-span-2 relative max-md:mt-4">
                        <div class="sticky top-24">
                            <div class="border-2 border-dashed border-nmt-500 rounded-lg md:p-4 p-2">
                                <h1
                                    class="w-full inline-block text-center text-xl font-extrabold tracking-tight text-nmt-500  dark:text-white">
                                    ${language === 'vi' ? `THÔNG TIN CHUYẾN BAY` : `FLIGHT INFORMATION`}</h1>
                                <div>
                                    <h2
                                        class="mt-4 flex gap-2  text-base tracking-tight text-gray-600  dark:text-white">
                                        <span class="font-extrabold">${language === 'vi' ? `Điểm Khởi Hành:` : `Departure point:`}</span> <strong
                                            class="text-gray-500 dark:text-white font-semibold">
                                            ${_inforAirports[_orderAvailable?.Depart]?.cityName}
                                            (${_orderAvailable?.Depart})
                                        </strong>
                                    </h2>
                                    <h2 class="mt-2  gap-2  text-base tracking-tight text-gray-600  dark:text-white">
                                        <span class="font-extrabold  whitespace-nowrap">${language === 'vi' ? `Điểm Đến:` : `Arrival point:`} </span>
                                        <span class=" text-gray-500 dark:text-white font-semibold">

                                            ${_inforAirports[_orderAvailable?.Arrival]?.cityName}
                                            (${_orderAvailable?.Arrival})</span>
                                    </h2>
                                    <h2
                                        class="mt-2 flex gap-2  text-base tracking-tight text-gray-600  dark:text-white">
                                        <span class="font-extrabold inline-flex whitespace-nowrap">${language === 'vi' ? `Ngày khởi hành:` : `Departure date:`}</span>
                                        <span class="inline-flex text-gray-500 dark:text-white font-semibold">
                                            ${_orderAvailable?.DepartDate}
                                        </span>
                                    </h2>
                                    ${_orderAvailable?.ReturnDate ? html`
                                    <h2 class="mt-2 flex gap-2  text-base tracking-tight text-gray-600  dark:text-white">
                                        <span class="font-extrabold inline-flex whitespace-nowrap">${language === 'vi' ? `Ngày trở về:` : `Return date:`}</span>
                                        <span class="inline-flex text-gray-500 dark:text-white font-semibold">
                                            ${_orderAvailable?.ReturnDate}
                                        </span>
                                    </h2>
                                    `: ``}
                                    
                                    <h2
                                        class="mt-2 flex flex-col gap-2  text-base tracking-tight text-gray-600  dark:text-white">
                                        <span class="font-extrabold inline-flex whitespace-nowrap">${language === 'vi' ? `Chi tiết chuyến bay:` : `Flight details:`}</span>
                                        ${_orderDetails.full?.InventoriesSelected.length > 0 ? html`
                                        <div class="w-full space-y-10">
                                        ${_orderDetails.full?.InventoriesSelected.map((itinerarySelected: any, index: number) => html`
                                        <div class="w-full bg-gray-100 my-4 ">
                                                <!-- start flight infor -->
                                                <div class="bg-white rounded-e-lg rounded-bl-lg ">
                                                    <div class="py-[2px] flex gap-2">
                                                        <button
                                                            class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-md text-sm px-2 py-1 text-center me-2">
                                                            <span>
                                                            ${_orderDetails.full?.InventoriesSelected.length > 1 ? html`${index % 2 === 1 ? language === 'vi' ? `Chiều về` : `Return` : language === 'vi' ? `Chiều đi` : `Departure`}` : ``}
                                                            </span>
                                                        </button>

                                                        <span>
                                                            ${formatDateTo_ddMMyyyy(itinerarySelected.segment.Legs[0]?.DepartureDate, language)}
                                                            |
                                                            ${language === 'vi' ? `Thời gian bay` : `Flight time`}
                                                            ${getDurationByArray(itinerarySelected.segment.Legs)}
                                                        </span>
                                                    </div>
                                                    <div class="w-full">
                                                    ${itinerarySelected.segment.Legs.map((leg: any, $index: number) => html`
                                                    ${index > 0 ? html`
                                                    <div
                                                            class="relative flex flex-1 flex-row items-center justify-start min-h-[50px] w-full  before:content-[''] before:absolute before:border-l-[4px] before:border-dotted before:border-[#dadfe6] before:h-[30%] before:w-0 before:start-[68px] before:top-0 before:inline-block after:content-[''] after:absolute after:border-l-[4px] after:border-dotted after:border-[#dadfe6] after:h-[30%] after:w-0 after:start-[68px] after:top-[70%] after:inline-block">
                                                            <div class="flex py-4 ps-[80px] w-full">
                                                                <div
                                                                    class="flex flex-row items-center justify-start w-full">
                                                                    <div
                                                                        class="w-full text-sm py-2 px-1 bg-gray-100 rounded-lg ">
                                                                        ${language === 'vi' ? `Trung chuyển tại` : `Transit at`}
                                                                        ${_inforAirports[leg.DepartureCode]?.cityName}
                                                                        ${convertDurationToHour(itinerarySelected.segment.Legs[$index].StopTime)}

                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <!-- icon -->
                                                            <div
                                                                class="absolute inline-block start-[62.5px] top-[calc(50%-8px)]">
                                                                <svg class="w-4 h-4 text-[#acb4bf] dark:text-white"
                                                                    aria-hidden="true"
                                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                                    height="24" fill="none" viewBox="0 0 24 24">
                                                                    <path stroke="currentColor" stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="2"
                                                                        d="M17.651 7.65a7.131 7.131 0 0 0-12.68 3.15M18.001 4v4h-4m-7.652 8.35a7.13 7.13 0 0 0 12.68-3.15M6 20v-4h4" />
                                                                </svg>
                                                            </div>
                                                        </div>
                                                    ` : ``}
                                                    <div class="flex flex-col items-start overflow-hidden relative">
                                                            <div>
                                                                <div class="flex flex-row items-center justify-start">
                                                                    <div
                                                                        class="flex flex-col items-center text-[#0f294d] text-[16px]  leading-[24px] me-[43px] whitespace-nowrap w-[45px] font-extrabold  ">
                                                                        <span>
                                                                            ${getTimeFromDateTime(leg?.DepartureDate)
    }
                                                                        </span>
                                                                    </div>
                                                                    <span
                                                                        class="text-[#0f294d] text-[16px] font-semibold leading-[24px]">
                                                                        <span>(${leg?.DepartureCode})</span>
                                                                        ${_inforAirports[leg?.DepartureCode]?.name}
                                                                    </span>
                                                                </div>

                                                                <!-- class="flex flex-1 flex-row items-center justify-start min-h-[50px] w-full  before:content-[''] before:absolute before:bg-[#dadfe6] before:rounded-[2px] before:w-[4px] before:start-[68px] before:inline-block before:h-[32%] before:top-[5%] after:content-[''] after:absolute after:bg-[#dadfe6] after:rounded-[2px] after:w-[4px] after:h-[35%] after:start-[68px] after:bottom-[5%] after:inline-block"> -->
                                                                <div
                                                                    class="flex flex-1 flex-row items-center justify-start min-h-[50px] w-full  before:content-[''] before:absolute before:bg-[#dadfe6] before:rounded-[2px] before:w-[4px] before:start-[68px] before:inline-block before:h-[80%] before:top-[10%] ">
                                                                    <div
                                                                        class="flex flex-row-reverse flex-wrap-reverse items-center justify-end w-[97px] overflow-hidden ">
                                                                        <div
                                                                            class="flex items-center justify-center bg-white  h-[24px]">
                                                                            <img src="${apiUrl}/assets/img/airlines/${leg?.OperatingAirlines}.png"
                                                                                class="h-[22px] pe-7 max-w-[86px]">
                                                                        </div>
                                                                    </div>

                                                                    <div class="flex md:py-4 py-3">
                                                                        <div
                                                                            class="flex flex-row items-center justify-start">
                                                                            <span
                                                                                class="md:text-sm text-xs text-[#8592a6]">
                                                                                ${leg?.OperatingAirlinesName}${leg?.OperatingAirlines + leg?.FlightNumber}${itinerarySelected.inventorySelected?.BookingInfos[$index]?.FareType || itinerarySelected.inventorySelected?.BookingInfos[$index]?.CabinName}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="flex flex-row items-center justify-start">
                                                                    <div
                                                                        class="flex flex-col items-center text-[#0f294d] font-extrabold text-[16px]  leading-[24px] me-[43px] whitespace-nowrap w-[45px]">
                                                                        <span>${getTimeFromDateTime(leg?.ArrivalDate)}</span>
                                                                    </div>
                                                                    <span
                                                                        class="text-nmt-600 text-[16px] font-semibold leading-[24px]">
                                                                        <span>(${leg?.ArrivalCode})</span>
                                                                        ${_inforAirports[leg?.ArrivalCode]?.name}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    `)}
                                                    </div>
                                                </div>
                                                <!-- end flight infor -->
                                            </div>
                                        `)}
                                        </div>
                                        ` : html``}
                                    </h2>


                                    <h2
                                        class="mt-2  gap-2 text-center  text-base tracking-tight text-nmt-500  dark:text-white font-extrabold  whitespace-nowrap">
                                        --------OOOOO-------
                                    </h2>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
                ${_orderDetails.full?.InventoriesSelected.length > 0 ? html`
                <div class="w-full space-y-10 my-8">
                ${_orderDetails.full?.InventoriesSelected.map((itinerarySelected: any, index: number) => html`
                ${itinerarySelected.segment.Legs.map((leg: any, $index: number) => html`
                <div class="space-y-4  bg-card mb-8  ">
                        <div class="max-md:overflow-x-scroll w-auto h-max  max-md:overflow-y-hidden max-md:pb-2">
                            <div class="md:w-full w-max m-auto ">
                                <div class="grid grid-cols-10 rounded-lg  relative ">
                                    <div class="col-span-4 shadow-lg  relative rounded-s-lg ">
                                        <div
                                            class="w-full h-[37px] flex justify-between items-center  md:px-8 px-4 py-1 bg-gradient-to-l from-nmt-600 via-nmt-600 to-nmt-300 rounded-tl-lg">
                                            <span class="text-white md:text-lg text-base font-extrabold line-clamp-1">
                                                <svg class="fill-white w-6 h-6 inline-block"
                                                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
                                                    <path
                                                        d="M381 114.9L186.1 41.8c-16.7-6.2-35.2-5.3-51.1 2.7L89.1 67.4C78 73 77.2 88.5 87.6 95.2l146.9 94.5L136 240 77.8 214.1c-8.7-3.9-18.8-3.7-27.3 .6L18.3 230.8c-9.3 4.7-11.8 16.8-5 24.7l73.1 85.3c6.1 7.1 15 11.2 24.3 11.2l137.7 0c5 0 9.9-1.2 14.3-3.4L535.6 212.2c46.5-23.3 82.5-63.3 100.8-112C645.9 75 627.2 48 600.2 48l-57.4 0c-20.2 0-40.2 4.8-58.2 14L381 114.9zM0 480c0 17.7 14.3 32 32 32l576 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 448c-17.7 0-32 14.3-32 32z" />
                                                </svg>
                                                <span class="ms-2">${_inforAirports[leg.DepartureCode]?.cityName} - ${_inforAirports[leg.ArrivalCode]?.cityName}</span>
                                            </span>
                                        </div>
                                        <div
                                            class=" flex flex-col justify-start items-center text-gray-800 bg-white  rounded-bl-lg pb-4">
                                            <div class="w-full h-12 flex justify-center items-center mt-4">
                                                <img src="${apiUrl}/assets/img/airlines/${leg.OperatingAirlines}.png" class="h-full w-auto">
                                            </div>
                                            <div
                                                class="w-full flex flex-col justify-start items-start mt-2 space-y-2 md:px-8 px-4">
                                                <div class="flex flex-col ">
                                                    <span class="text-xs font-semibold text-gray-700">${language === 'vi' ? `HÃNG CHUYÊN CHỞ/ CARRIER` : `CARRIER`}</span>
                                                    <span
                                                        class="text-base font-bold uppercase">${leg.OperatingAirlinesName}</span>
                                                </div>

                                                <div class="flex flex-col ">
                                                    <span class="text-xs font-semibold text-gray-700">${language === 'vi' ? `SỐ HIỆU/ FLIGHT` : `FLIGHT`}</span>
                                                    <span class="text-base font-bold">
                                                        ${leg.OperatingAirlines + leg.FlightNumber}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-span-6 relative  flex flex-row rounded-br-lg">
                                        <div class="w-3.5 min-w-3.5 h-full m-auto  bg-transparent z-20 ">
                                            <div class="w-3.5 h-3 bg-nmt-600 mask-top-circle-cut">
                                            </div>
                                            <div
                                                class="w-3.5 h-[calc(100%-1.5rem)] bg-white  flex justify-center items-center relative z-10">
                                                <div
                                                    style="background-image: linear-gradient(to bottom, #fff 50%, #ea580c 50%) !important; width: 1px; height: 100%; position: relative; background-size: 100% 10px;">
                                                    <div
                                                        class="absolute md:top-0 -top-[1px] w-6 -left-2 md:h-[25px] h-[26px] bg-nmt-600">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="w-3.5 h-3 bg-white mask-bottom-circle-cut">
                                            </div>
                                        </div>
                                        <div
                                            class="w-full h-full shadow-lg flex justify-between items-center cursor-pointer relative z-10 rounded-br-lg">
                                            <div
                                                class="w-full h-full text-center cursor-pointer bg-white rounded-br-lg flex flex-col ">
                                                <div class="w-full  text-center cursor-pointer">
                                                    <div
                                                        class="w-full flex justify-end items-center h-[37px]  md:px-8 px-4 py-1  bg-gradient-to-l from-nmt-700 via-nmt-700 to-nmt-600 rounded-tr-lg">
                                                        <span class="text-white md:text-lg text-base font-extrabold">
                                                            (${getDayInWeek(leg.DepartureDate)}) - ${formatddMMyyyy(leg.DepartureDate)}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div
                                                    class="flex justify-center items-center w-full h-full px-4 gap-2 my-4">
                                                    <div class="flex flex-col justify-start items-start">
                                                        <strong class="text-3xl font-extrabold text-nmt-600">
                                                            ${getTimeFromDateTime(leg.DepartureDate)}
                                                        </strong>
                                                        <strong
                                                            class="md:text-base text-sm font-semibold text-gray-600">
                                                            ${formatddMMyyyy(leg.DepartureDate)}
                                                        </strong>
                                                        <strong class="text-lg font-bold text-gray-800 text-nowrap">
                                                            ${leg.DepartureCode + ' - ' + _inforAirports[leg.DepartureCode]?.cityName}
                                                        </strong>
                                                        <strong class="md:text-base text-sm font-semibold text-gray-600">
                                                            ${language === 'vi' ? `Nhà ga:` : `Terminal:`} ${leg.DepartureTerminal || '-'}
                                                        </strong>
                                                    </div>
                                                    <div class="w-full flex-col justify-center items-center">
                                                        <div class="w-full text-lg text-center font-semibold -mb-2">
                                                            ${leg.Equipment}
                                                        </div>
                                                        <div class="w-full flex justify-center items-center md:px-6">
                                                            <div class="w-full h-[3px] rounded-full bg-nmt-600 ">
                                                            </div>
                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                class="w-6 h-6 fill-nmt-600 inline-block ml-[1px]"
                                                                viewBox="0 0 576 512">
                                                                <path
                                                                    d="M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z" />
                                                            </svg>
                                                        </div>
                                                        <div class="w-full text-lg text-center font-semibold -mt-2">
                                                            ${getDuration(leg)}
                                                        </div>
                                                    </div>
                                                    <div class="flex flex-col justify-end items-end">
                                                        <strong class="text-3xl font-bold ">
                                                            ${getTimeFromDateTime(leg.ArrivalDate)}
                                                        </strong>
                                                        <strong
                                                            class="md:text-base text-sm font-semibold text-gray-700">
                                                            ${formatddMMyyyy(leg.ArrivalDate)}
                                                        </strong>
                                                        <strong class="text-lg  font-bold text-gray-800 text-nowrap">
                                                            ${_inforAirports[leg.ArrivalCode]?.cityName + ' - ' + leg.ArrivalCode}
                                                        </strong>
                                                        <strong class="md:text-base text-sm font-semibold text-gray-700">
                                                            ${language === 'vi' ? `Nhà ga:` : `Terminal:`} ${leg.ArrivalTerminal || '-'}
                                                        </strong>
                                                    </div>
                                                </div>

                                                <div class="w-full rounded-br-lg">
                                                    <div
                                                        style="width: 100%; height: 1px; background-image: linear-gradient(to right, transparent 50%, #fb7740 50%); background-size: 10px 100%;">
                                                    </div>
                                                    <div
                                                        class="w-full rounded-br-lg flex justify-between items-center px-4 pb-2">
                                                        <span>
                                                            <span>${language === 'vi' ? `Hành lý xách tay:` : `Carry-on baggage:`} </span>
                                                            ${itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandBaggage > 1 && itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandWeightBag !== 0 ? html`
                                                            <strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandBaggage}x</strong>
                                                            `: ``}
                                                            <strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandWeightBag}KG</strong>
                                                        </span>
                                                        <span>
                                                            <span>${language === 'vi' ? `Hành lý ký gửi:` : `Checked baggage:`} </span>
                                                            ${itinerarySelected.inventorySelected?.BookingInfos[$index]?.BagPieces > 1 && itinerarySelected.inventorySelected?.BookingInfos[$index]?.WeightBag !== 0 ? html`
                                                            <strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.BagPieces}x</strong>
                                                            `: ``}
                                                            ${itinerarySelected.inventorySelected?.BookingInfos[$index]?.WeightBag === 0 ? html`
                                                            <span>${language === 'vi' ? `Không bao gồm` : `Not included`}</span>
                                                            ` : html`<strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.WeightBag}KG</strong>`}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- end bottom -->

                                </div>
                            </div>
                        </div>
                    </div>
                `)}
                `)}
                </div>
                ` : html``}

                <div class=" bg-white border shadow-md rounded-lg overflow-hidden">

                    <div class="px-6 pt-6 space-y-2 ">
                        <div class="space-y-2 text-sm">
                            <h2 class="text-lg font-semibold text-nmt-700 border-b border-nmt-200 pb-2">${language === 'vi' ? `Thời gian check-in` : `Check-in time`}</h2>
                            <ul class="list-disc pl-5 space-y-1">
                                <li>${language === 'vi' ? `Quý khách vui lòng tới sân bay trước` : `Please arrive at the airport before`} 
                                    <span class="font-medium text-nmt-600">${__NoteModel?.TimeCheckIn?.Domestic}</span> 
                                    (${language === 'vi' ? `nội địa` : `domestic`}), 
                                    hoặc <span class="font-medium text-nmt-600">${__NoteModel?.TimeCheckIn?.International}</span> 
                                    (${language === 'vi' ? `quốc tế` : `international`}) 
                                    ${language === 'vi' ? `để làm thủ tục check-in` : `to complete check-in procedures`}.</li>
                            </ul>
                        </div>
                        <div class="space-y-2">
                            <h2 class="text-lg font-semibold text-nmt-700 border-b border-nmt-200 pb-2">${language === 'vi' ? `Giấy tờ tùy thân` : `Identity documents`}</h2>
                            <ul class="list-disc pl-5 space-y-1 text-sm">
                                ${__NoteModel?.IdentityDocuments?.map((identityDoc: any) => html`<li>${identityDoc.value}</li>`)}
                            </ul>
                        </div>
                        <div class="space-y-2">
                            <h2 class="text-lg font-semibold text-nmt-700 border-b border-nmt-200 pb-2">${language === 'vi' ? `Quy định đặc biệt` : `Special rules`}</h2>
                            <ul class="list-disc pl-5 space-y-2 text-sm">
                            ${__NoteModel?.SpecialRules?.map((specialRule: any) => html`<li>${specialRule.value}</li>`)}
                            </ul>
                        </div>
                        <p class="text-nmt-800 font-bold text-lg text-center pb-4">${language === 'vi' ? `CHÚC QUÝ KHÁCH CÓ CHUYẾN BAY TỐT ĐẸP!` : `HAVE A GOOD TRIP!`}</p>

                    </div>
    
                </div>
            ` : html`            <div class="flex flex-col text-center w-full md:mb-10 ">
                    <h1 class="inline-block mb-2 text-3xl font-extrabold tracking-tight text-gray-900 dark:text-white">
                        ${language === 'vi' ? `KHÔNG TÌM THẤY ĐƠN HÀNG` : `ORDER NOT FOUND`}
                        <div class="italic text-lg font-normal">${language === 'vi' ? `(Vui lòng kiểm tra lại mã đơn hàng)` : `(Please check the order code)`}</div>
                    </h1>
                </div>
            `}                
            `}
            </div>
        </section>
    </div>
</div>
`
