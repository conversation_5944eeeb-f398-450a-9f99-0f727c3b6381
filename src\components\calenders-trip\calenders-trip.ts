import { LitElement, css, PropertyValues, unsafeCSS } from "lit";
import { customElement, property, state } from "lit/decorators.js";
import { CalendersTripTemplate } from "./calenders-trip-template";
import styles from '../../styles/styles.css';

@customElement("calenders-trip")
export class CalendersTrip extends LitElement {
    static styles = [
        unsafeCSS(styles),
        css`
        :host {
        font-family: var(--nmt-font, 'Roboto', sans-serif);
        }
        `
    ];

    @state() private _isLoading: boolean = false;
    @state() private _isError: boolean = false;
    @state() private calendarWeek: Date[] = [];
    @property({ type: Date }) dateStart: Date = new Date();
    @property({ type: String }) language: string = 'vi';

    constructor() {
        super();
    }

    firstUpdated(): void {
        this.updateCalendarWeek();
    }

    updated(changedProperties: PropertyValues): void {
        if (changedProperties.has('dateStart')) {
            this.updateCalendarWeek();
        }
    }

    updateCalendarWeek(): void {
        this.calendarWeek = this.createCalendarWeek(this.dateStart);
    }

    createCalendarWeek(date: Date): Date[] {
        if (!(date instanceof Date) || isNaN(date.getTime())) {
            date = new Date(date);
        }
        const calendarWeek: Date[] = [];
        for (let i = -3; i <= 3; i++) {
            const newDate = new Date(date);
            newDate.setDate(date.getDate() + i);
            calendarWeek.push(newDate);
        }
        return calendarWeek;
    }

    calenderWeekChange = (day: Date) => {
        this.dispatchEvent(new CustomEvent('calender-week-change', {
            detail: day,
            bubbles: true,
            composed: true
        }));
    }

    render() {
        return CalendersTripTemplate(
            this.dateStart,
            this.language,
            this._isLoading,
            this._isError,
            this.calendarWeek,
            this.calenderWeekChange
        );
    }
}

