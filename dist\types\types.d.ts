import { Modal } from "./components/modal";
import { ProgressBar } from "./components/progress-bar";
import { RangeSlider } from "./components/range-slider";
import { skeletonSegment } from "./components/skeleton-segment";
import { FlightSearch } from "./components/flight-search";
import { TripPassenger } from "./components/trip-passenger";
import { TripPayment } from "./components/trip-payment";
import { TripResult } from "./components/trip-result";
import { TripRePayment } from "./components/trip-repayment";
declare global {
    interface HTMLElementTagNameMap {
        "modal-notification": Modal;
        'progress-bar': ProgressBar;
        'range-slider': RangeSlider;
        'skeleton-segment': skeletonSegment;
        'flight-search': FlightSearch;
        'trip-passenger': TripPassenger;
        'trip-payment': TripPayment;
        'trip-result': TripResult;
        'trip-repayment': TripRePayment;
    }
}
