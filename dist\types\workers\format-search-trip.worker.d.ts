export declare function getGroupCodeRefSelected(searchTripResponseMain: any, InventoriesSelected: any): any;
export declare function filterTimeDepartureDateInRange(hourStart: number, hourEnd: number, allAirlineResult: any[], searchTripResponseMain: any[], airItinerarySelected: number): Promise<any>;
export declare function filterLegsInRange(minLegs: number, maxLegs: number, allAirlineResult: any[], searchTripResponseMain: any[], airItinerarySelected: number): Promise<any>;
export declare function sortWithPrice(searchTripResponse: any): any;
export declare function sortWithAirline(searchTripResponse: any): any;
export declare function sortWithDepartureDate(searchTripResponse: any): any;
export declare function filterSearchTripResponseByAirline(searchTripResponseMain: any[], airline: string, bookGDS: string, airCodeRef: string, sumPrice: number): any;
export declare function filterSearchTripResponseByCombine(searchTripResponseMain: any[], combine: boolean): any;
export declare function getGroupCodeRefFromCodeRef(searchTripResponseMain: any[], codeRef: string): string;
export declare function combineGroupsStart(searchTripResponse: any): any;
export declare function combineGroupsEnd(searchTripResponse: any, segment: any, combine: boolean): any;
