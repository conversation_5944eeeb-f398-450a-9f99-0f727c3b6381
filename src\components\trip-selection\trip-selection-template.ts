import { html } from "lit";
import "../progress-bar/progress-bar";
import "../skeleton-segment/skeleton-segment";
import "../trip-step/trip-step";
import "../calenders-trip/calenders-trip";
import "../range-slider/range-slider";
import "../skeleton-segment/skeleton-segment";
import "../flight-search/flight-search";
import "../modal/modal";
import { convertDurationToHour, formatDateS, formatDateTo_ddMMyyyy, formatNumber, getDirect, getDurarionLeg, getFareType, getFlights, getPassengerDescription, getTimeFromDateTime, range } from "../../utils/dateUtils";
import { environment } from "../../environments/environment";

const apiUrl = environment.apiUrl;

export const TripSelectionTemplate = (
    uri_searchBox: string,
    mode: string,
    language: string,
    color: string,
    font: string,
    googleFontsUrl: string,
    _ApiKey: string,
    _currencySymbol: string,
    _convertedVND: number,
    _isLoading: boolean,
    _isShowLoading: boolean,
    _isError: boolean,
    _isMobileDevice: boolean,
    _isGlobal: boolean,
    _searchTripResponse: any[],
    departureItem: any,
    arrivalItem: any,
    _isShowPriceDetail: boolean,
    _isShowPriceDetails: boolean,
    InventoriesSelected: any[],
    _airItinerarySelected: number,
    inforAirports: any[],
    pricePaxInfor: any[],
    searchTripRequest: TripSelectionModelRq | null,
    _sumPrice: number,
    _progress: number,
    dateStart: Date,
    segmentsSelected: {
        segmentKeyRef: any,
        sumPrice: any
    }[] = [],
    canScrollLeft: boolean,
    canScrollRight: boolean,
    allAirlineResult: any[] = [],
    rangeSlider: [number, number] = [0, 24],
    rangeSliderLegs: [number, number] = [0, 0],
    maxCountLeg: number,
    showOffcanvas: boolean,
    isopenModalResult: boolean,
    showFromText: boolean,
    showDetailTicket: boolean,
    showDetailFareRule: boolean,
    showMoreClassic: boolean,
    search_redirect: string,
    // event
    getTotalPriceWithMode: () => string,
    getTotalPriceLowestWithMode: (segment: any) => string,
    getInventoryPriceWithMode: (inventory: any) => string,
    showPriceDetail: () => void,
    checkNextStep: () => boolean,
    nextStep: () => void,
    selectSegment: (bookGDS: string, airCodeRef: string, combine: boolean, segmentKeyRef: any, sumPrice: any) => void,
    selectInventory: (bookGDS: string, airCodeRef: string, combine: boolean, segmentKeyRef: any, sumPrice: any) => void,
    isShowDetailSegment: (segment: any) => void,
    showPriceDetails: (event: Event) => void,
    scrollInventory: (arrow: string) => void,
    checkBookingButton: () => boolean,
    nextPage: () => void,
    sortWithPrice: () => void,
    sortWithAirline: () => void,
    sortWithDepartureDate: () => void,
    currentSortOption: 'price' | 'airline' | 'departure',
    filterAirlines: (event: Event, airline: string) => void,
    checkedAllAirlinesStatus: () => boolean,
    handleWeekChange: (event: CustomEvent) => void,
    handleRangeSliderChange: (event: CustomEvent) => void,
    handleRangeSliderLeg: (event: CustomEvent) => void,
    reSelectFlight: () => void,
    setShowOffcanvas: (show: boolean) => void,
    isCurrencyLoaded: boolean, // thêm đối số này
    handleLanguageChange: (value: string) => void,
    showLanguageSelect: boolean,
    hideMultiSegmentFlights: boolean,
    showMultiSegmentFlights: boolean,
    isMultiSegmentFlight: (segment: any) => boolean,
    toggleMultiSegmentFlights: () => void
) => {
    return html`
    ${_isLoading ? html`
    <div class="loader-container">
        <span class="loader"></span>
        <img src="${apiUrl}/assets/img/background/trip_loading2.gif"/>
        <progress-bar language="${language}" class="loadidng-vertical bg-gradient-to-br from-nmt-600 to-nmt-400 shadow-lg shadow-white"  _indeterminate=${true} _progress=${_progress}></progress-bar>
    </div>`: ''}
    <div class="w-full bg-gray-100 relative min-h-screen max-md:pb-24 ">
    <div class="max-w-7xl mx-auto  pb-8 relative">
        <trip-steps language="${language}" uri_searchBox="${uri_searchBox}"></trip-steps>
        <div class="max-w-7xl mx-auto mb-4 bg-white rounded-lg relative z-20 border-[3px] border-white shadow-[0px_0px_15px_#cccccc96,0px_0px_15px_#44444400_inset]">  
            <flight-search language="${language}" mode="${mode}" color="${color}" font="${font}" googleFontsUrl="${googleFontsUrl}"  _isShowBottom="true" ApiKey="${_ApiKey}"  redirect_uri="${search_redirect}" isChild></flight-search>
        </div>

        <div class="grid grid-cols-12 md:gap-8 gap-4 relative z-10 max-md:px-2">
            <div class="md:col-span-9 col-span-12">
            ${InventoriesSelected.length > 0 ? html`
            <div class="space-y-4 my-4 sticky z-50 top-6 max-md:px-4 ${_isShowPriceDetail ? 'bg-white shadow-xl rounded-lg' : ''}">
                        ${InventoriesSelected.map((itinerarySelected: any, index: number) => html`
                        ${index === 0 && _airItinerarySelected > 0 || _isShowPriceDetail ? html`
                        <div class="w-full relative bg-white rounded-lg shadow-lg ">
                        <!-- Depart -->
                        <div class="absolute -top-4 left-0 bg-white/20 rounded-t-lg  ">
                            <h1
                                class="z-10 bg-nmt-500 flight-date md:text-sm text-[10px] text-nowrap relative  border-b border-gray-200 ps-2 pe-4 py-1 w-fit rounded-e-full text-white">
                                ${language === 'vi' ? 'Ngày' : 'Date'} 
                                ${index % 2 === 1 ? (language === 'vi' ? ' Về:' : ' Return:') : (language === 'vi' ? ' Đi:' : ' Departure:')}
                                <strong class="md:text-base text-[10px]">
                                    ${formatDateTo_ddMMyyyy(itinerarySelected?.segment?.DepartureDate, language)}
                                </strong>
                            </h1>
                        </div>

                        <div
                            class="w-full flex flex-col justify-between items-center rounded-lg text-gray-800 border border-nmt-500 ">
                            <div
                                class="w-full flex flex-row justify-between items-center rounded-lg  text-gray-800 py-2 ">
                                <div
                                    class="px-3 flex flex-col justify-center items-center   box-border text-gray-900 text-sm">
                                    <span
                                        class="relative  font-normal h-[26px] tracking-[0px] leading-[26px] overflow-hidden text-ellipsis whitespace-nowrap">
                                        <img src="${apiUrl}/assets/img/airlines/${itinerarySelected?.segment?.Airlines}.png"
                                            class="h-full w-auto">
                                    </span>
                                    <strong
                                        class="text-nowrap max-md:hidden text-xs max-w-32 line-clamp-1">${itinerarySelected?.segment?.AirlinesName}</strong>
                                </div>
                                <div
                                    class="flex flex-col md:px-4 justify-between items-center rounded-lg  text-gray-800 ">
                                    <div class="flex justify-start items-center w-full gap-2 ">
                                        <div class="flex flex-col justify-start items-start">
                                            <strong class="md:text-xl text-base font-bold text-[#0f294d]">
                                                ${getTimeFromDateTime(itinerarySelected?.segment?.DepartureDate)}
                                            </strong>
                                            <strong
                                                class="md:text-base text-sm font-normal rounded-full bg-white text-[#8592a6]">
                                                ${itinerarySelected?.segment?.DepartureCode}
                                            </strong>
                                        </div>
                                        <div class="w-full  flex flex-col justify-center items-center text-[#8592a6]">
                                            <span class="md:text-base text-sm">
                                                ${convertDurationToHour(itinerarySelected?.segment?.Duration)}</span>
                                            <div class="w-full flex">
                                                <div class="bg-[#dadfe6] w-1.5 h-1.5"></div>
                                                <div
                                                    class="w-full flex justify-center items-center relative before:absolute before:bg-white before:h-1.5 before:w-1.5 before:border-2 before:border-[#dadfe6] ">
                                                    <div class="w-full h-[2px] rounded-full bg-[#dadfe6]"></div>
                                                </div>
                                                <div class="bg-[#dadfe6] w-1.5 h-1.5"></div>
                                            </div>
                                            <div class="text-center md:text-xs text-[10px] md:px-4 px-1 line-clamp-2">
                                                <span>
                                                    ${getFlights(itinerarySelected?.segment.Legs)}
                                                </span>
                                                <span>
                                                    -
                                                </span>
                                                <strong>
                                                    ${getDirect(itinerarySelected?.segment.Legs, language)}
                                                </strong>
                                            </div>
                                            ${showDetailTicket ? html`
                                            ${!_isShowPriceDetail ? html` <span @click=${showPriceDetail}
                                                class="text-sm text-nmt-500 underline cursor-pointer">${language === 'vi' ? 'Chi tiết' : 'Details'}</span>` : ``}
                                            `: ``}
                                            
                                           
                                        </div>
                                        <div class="flex flex-col justify-end items-end">
                                            <strong class="md:text-xl text-base font-bold text-[#0f294d]">
                                                ${getTimeFromDateTime(itinerarySelected?.segment?.ArrivalDate)}
                                            </strong>
                                            <strong
                                                class="md:text-base text-sm font-normal rounded-full bg-white text-[#8592a6]">
                                                ${itinerarySelected?.segment?.ArrivalCode}
                                            </strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="md:min-w-40 min-w-12  flex justify-end">
                                ${index === 0 && _airItinerarySelected > 0 ? html`
                                     <button @click=${() => reSelectFlight()}
                                        class="h-auto w-auto border max-md:ml-2 text-nowrap border-white group !cursor-pointer flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br  from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg md:text-sm text-[10px] md:px-4 px-2 py-2 text-center me-2">
                                        ${language === 'vi' ? 'Chọn lại' : 'Reselect'} 
                                        <svg class="w-6 h-6 text-white  animate-pulse inline-block group-hover:animate-spin max-md:hidden"
                                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            fill="none" viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M17.651 7.65a7.131 7.131 0 0 0-12.68 3.15M18.001 4v4h-4m-7.652 8.35a7.13 7.13 0 0 0 12.68-3.15M6 20v-4h4" />
                                        </svg>
                                    </button>
                                ` : ``}
                                </div>
                            </div>

                        </div>
                    </div>
                        ` : ``}          
                        `)}

                    <div
                        class="space-y-4 transition-all duration-700  ease-in-out opacity-0 h-0 w-0 overflow-hidden ${_isShowPriceDetail ? 'h-auto w-full  opacity-100' : ''}">
                        ${InventoriesSelected.map((itinerarySelected: any, index: number) => html`
                        <div class="w-full bg-gray-100  ">
                            <!-- start flight infor -->
                            <div
                                class="bg-white rounded-e-lg rounded-bl-lg border-[3px] border-white shadow-[0px_0px_15px_#cccccc96,0px_0px_15px_#44444400_inset]">
                                <div class="py-[2px]">
                                    <span
                                        class=" bg-gradient-to-br  from-nmt-600 to-nmt-300 text-white md:text-base text-sm font-extrabold pl-2 py-1 pe-6 md:pe-12 rounded-tr-full">
                                        ${language === 'vi' ? 'Chi tiết hành trình' : 'Journey details'}:
                                        ${InventoriesSelected.length > 1 ? html`${index % 2 === 1 ? (language === 'vi' ? 'Chiều về' : 'Return') : (language === 'vi' ? 'Chiều đi' : 'Departure')}` : ``}
                                    </span>
                                </div>
                                <div class="w-full">
                                ${itinerarySelected.segment?.Legs?.map((leg: any, index: number) => html`
                                 ${index > 0 ? html`
                                  <div class="w-full bg-gray-600 p-1 text-white text-center text-sm">
                                        ${language === 'vi' ? 'Trung chuyển tại' : 'Transit at'} ${inforAirports[leg.DepartureCode]?.name}
                                        <strong>(${leg.DepartureCode})</strong> - ${language === 'vi' ? 'Thời gian' : 'Time'}:
                                        <strong>${convertDurationToHour(itinerarySelected.segment.Legs[index].StopTime)}</strong>
                                    </div>
                                 `: ``}
                                                                     <div class="grid grid-cols-12 border-t border-gray-200">
                                        <div
                                            class="col-span-7 flex flex-col justify-center items-center text-black py-2 md:px-8  border-black">
                                            <div
                                                class="w-full flex justify-between items-center px-4 md:text-sm  text-[10px]">
                                                <div class="text-left">
                                                    <span class="font-extrabold">
                                                        (${leg?.DepartureCode})
                                                    </span>
                                                    ${inforAirports[leg?.DepartureCode]?.cityName}
                                                    <div>
                                                        <span class="text-gray-400 text-xs">
                                                            ${inforAirports[leg?.DepartureCode]?.name}</span>
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    ${inforAirports[leg?.ArrivalCode]?.cityName}
                                                    <span class="font-extrabold">
                                                        (${leg?.ArrivalCode})
                                                    </span>
                                                    <div>
                                                        <span class="text-gray-400 text-xs">
                                                            ${inforAirports[leg?.ArrivalCode]?.name}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex justify-center items-center w-full px-4 gap-2">
                                                <div class="flex flex-col justify-start items-start">
                                                    <strong
                                                        class="md:text-3xl text-base font-extrabold text-nmt-600">
                                                        ${getTimeFromDateTime(leg?.DepartureDate)}</strong>
                                                    <span class="${language === 'vi' ? 'md:text-sm' : 'md:text-xs'} text-[10px] text-nowrap">
                                                        ${formatDateTo_ddMMyyyy(leg?.DepartureDate, language)}
                                                    </span>
                                                    <strong class="md:text-base text-sm font-semibold text-gray-600 text-nowrap ">
                                                            ${language === 'vi' ? 'Nhà ga' : 'Terminal'}: ${leg?.DepartureTerminal || '-'}
                                                        </strong>
                                                </div>
                                                <div
                                                    class="w-full flex-col justify-center items-center px-4 text-sm md:px-6">
                                                    <div class="w-full text-center -mb-2">
                                                        ${leg?.Equipment}
                                                    </div>
                                                    <div class="w-full flex justify-center items-center">
                                                        <div class="w-full h-[2px] rounded-full bg-nmt-600">
                                                        </div>
                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                            class="w-5 h-5 fill-nmt-600 inline-block ml-[1px]"
                                                            viewBox="0 0 576 512">
                                                            <path
                                                                d="M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z" />
                                                        </svg>
                                                    </div>
                                                    <div class="w-full text-center -mt-2">
                                                        ${convertDurationToHour(leg?.Duration)}
                                                    </div>
                                                </div>
                                                <div class="flex flex-col justify-end items-end">
                                                    <strong
                                                        class="md:text-2xl text-base font-extrabold text-nmt-500">
                                                        ${getTimeFromDateTime(leg?.ArrivalDate)}</strong>
                                                    <span class="${language === 'vi' ? 'md:text-sm' : 'md:text-xs'} text-[10px]  text-nowrap">
                                                        ${formatDateTo_ddMMyyyy(leg?.ArrivalDate, language)}
                                                    </span>
                                                    <strong class="md:text-base text-sm font-semibold text-gray-600 text-nowrap">
                                                            ${language === 'vi' ? 'Nhà ga' : 'Terminal'}: ${leg?.ArrivalTerminal || '-'}
                                                        </strong>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="col-span-5 flex flex-row">
                                            <div
                                                class="w-[1px] h-full bg-[100%_10px] bg-[linear-gradient(to_bottom,_transparent_50%,_#fb7740_50%)] bg-[length:100%_10px]">
                                            </div>
                                            <div
                                                class="px-4 py-2 flex flex-col items-center justify-center w-full md:text-sm text-[10px]">
                                                <span class="text-xs font-bold">${language === 'vi' ? 'Hãng vận chuyển' : 'Carrier'}</span>
                                                <img src="${apiUrl}/assets/img/airlines/${leg?.OperatingAirlines}.png"
                                                    class=" w-auto h-12 mx-auto my-1">

                                                <span>${language === 'vi' ? 'Chuyến bay' : 'Flight'}: <span
                                                        class="text-nmt-500 font-extrabold tracking-wide">${leg?.Airlines + leg?.FlightNumber}</span></span>
                                                <span>${language === 'vi' ? 'Loại vé' : 'Ticket type'}: <strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.CabinName}</strong></span>
                                            </div>
                                        </div>
                                        <div
                                            class="col-span-12 border-t border-gray-200 p-2 flex justify-end items-center bg-gray-50/80">
                                            <span class="text-end text-xs text-gray-800">
                                                ${language === 'vi' ? 'Loại vé' : 'Ticket type'}: <strong>
                                                    ${itinerarySelected.inventorySelected?.BookingInfos[index]?.FareType || itinerarySelected.inventorySelected?.BookingInfos[index]?.CabinName}
                                                </strong>
                                                | ${language === 'vi' ? 'Hành lý xách tay:' : 'Hand baggage:'}
                                                ${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandBaggage > 1 && itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag !== 0 ? html`
                                                <strong> 
                                                    ${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandBaggage}
                                                </strong>
                                                ` : ``}
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-luggage w-4 h-4 text-blue-500 flex-shrink-0  inline-block">
                                                    <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16">
                                                    </path>
                                                    <rect width="20" height="14" x="2" y="6" rx="2">
                                                    </rect>
                                                </svg>

                                                <strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag > 0 ? itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag : 7}</strong>
                                                kg
                                                | ${language === 'vi' ? 'Hành lý ký gửi:' : 'Checked baggage:'}
                                                ${itinerarySelected.inventorySelected?.BookingInfos[index]?.BagPieces > 1 && itinerarySelected.inventorySelected?.BookingInfos[index]?.WeightBag !== 0 ? html`
                                                <strong>
                                                    ${itinerarySelected.inventorySelected?.BookingInfos[index]?.BagPieces}
                                                </strong>
                                                `: ``}
                                               
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-luggage w-4 h-4 text-blue-500 flex-shrink-0  inline-block">
                                                    <path
                                                        d="M6 20h0a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h0">
                                                    </path>
                                                    <path d="M8 18V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v14">
                                                    </path>
                                                    <path d="M10 20h4"></path>
                                                    <circle cx="16" cy="20" r="2"></circle>
                                                    <circle cx="8" cy="20" r="2"></circle>
                                                </svg>

                                                <strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.WeightBag}</strong>
                                                kg
                                                | <svg class="w-4 h-4 inline-block text-gray-800 dark:text-white"
                                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" fill="none" viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round"
                                                        stroke-linejoin="round" stroke-width="2"
                                                        d="M12 8v4l3 3m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                </svg>
                                                 ${language === 'vi' ? 'Thời ginh bay' : 'Flight time'}:
                                                <strong>${getDurarionLeg(leg)}</strong>
                                                | ${language === 'vi' ? 'Máy bay' : 'Aircraft'}: <strong>${leg.Equipment}</strong>
                                            </span>
                                        </div>
                                    </div>
                                `)}
                                </div>

                            </div>
                            <!-- end flight infor -->
                        </div>
                        `)
            }
                        ${isCurrencyLoaded ? html`
                          <!-- Toàn bộ phần hiển thị giá, ví dụ: -->
                          <div class="col-span-12 border-t  p-2 grid grid-cols-1 justify-end items-center bg-[#f8e2de] rounded-b-lg border-[1px] border-white shadow-[0px_0px_15px_#cccccc96,0px_0px_15px_#44444400_inset]" >
                              <div class="grid grid-cols-4 border-b border-gray-600 md:text-sm text-xs" >
                                  <div class="text-start font-bold">${language === 'vi' ? 'Hành khách' : 'Passenger'}</div>
                                  <div class="text-end font-bold">${language === 'vi' ? 'Giá vé' : 'Fare'}</div>
                                  <div class="text-end font-bold">${language === 'vi' ? 'Thuế' : 'Tax'}</div>
                                  <div class="text-end font-bold">${language === 'vi' ? 'Giá Bán' : 'Total'}</div>
                              </div>
                              ${pricePaxInfor.map((fareInfo: any) => html`
                                  <div class="grid grid-cols-4 border-b border-gray-600 py-1 md:text-sm text-xs" >
                                      <div class="text-start">${getPassengerDescription(searchTripRequest, fareInfo?.PaxType, language)}</div>
                                      <div class="text-end">${formatNumber(fareInfo.Fare, _convertedVND, language)}</div>
                                      <div class="text-end">${formatNumber(fareInfo.Tax, _convertedVND, language)}</div>
                                      <div class="text-end">${formatNumber(fareInfo.Fare + fareInfo.Tax, _convertedVND, language)}</div>
                                  </div>`
            )}
                              <div class=" text-right md:text-sm text-xs" >${language === 'vi' ? 'Tổng cộng' : 'Total'}: <strong class="md:text-xl text-base font-bold text-nmt-600" >
                              ${formatNumber(_sumPrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small >
                              </div>
                          </div>
                        ` : html`
                          <!-- Nếu chưa load xong, có thể hiển thị placeholder hoặc ẩn hoàn toàn phần giá -->
                          <div class="col-span-12 border-t  p-2 grid grid-cols-1 justify-end items-center bg-[#f8e2de] rounded-b-lg border-[1px] border-white shadow-[0px_0px_15px_#cccccc96,0px_0px_15px_#44444400_inset]">
                            <div class="text-center text-gray-400 animate-pulse"></div>
                          </div>
                        `}
                        ${_isShowPriceDetail ? html`
                        <div class="w-full flex justify-center " >
                                <button @click=${showPriceDetail}
                                class="h-auto w-auto border max-md:ml-2 text-nowrap mb-4 border-white group !cursor-pointer flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br  from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg md:text-base text-sm md:px-4 px-2 py-2 text-center me-2 ${_isShowPriceDetail ? 'text-red-500' : ''}" >
                                    ${language === 'vi' ? 'Ẩn chi tiết' : 'Hide details'}
                                </button>
                        </div>
                        `: ''}
                    </div>
                </div>` : ``}
                
                <!-- start calender -->
               <calenders-trip .dateStart=${dateStart} .language="${language}" @calender-week-change="${handleWeekChange}"></calenders-trip>
                <!-- end calender -->
                 <div>
                    ${_isLoading ? html`
                    <div class="mt-4 bg-gray-200 rounded-t-lg animate-pulse">
                        <div class="flex justify-center items-center">
                            <div class="flex flex-col items-center md:px-12 px-6 py-2 bg-gray-200 rounded-lg w-full">
                                <div class="bg-gray-300 h-6 w-3/4 mb-2 rounded"></div>
                                <div class="bg-gray-300 h-4 w-1/2 rounded"></div>
                            </div>
                        </div>
                        <div class="grid grid-cols-10 mt-4">
                            <div class="col-span-4 pr-1"></div>
                            <div class="col-span-3 pr-1">
                                <div class="h-12 bg-gray-300 rounded-t-lg w-full"></div>
                            </div>
                            <div class="col-span-3 pl-1">
                                <div class="h-12 bg-gray-300 rounded-t-lg w-full"></div>
                            </div>
                        </div>
                    </div>
                    ` : html`
                    <div class="mt-4 bg-gray-200 rounded-t-lg">
                        ${!_isLoading && !_isError && _searchTripResponse.length > 0 ? html`
                        <div class="flex justify-center items-center relative">
                            <div
                                class="flex flex-col items-center md:px-12 px-6 py-2 bg-gray-200 rounded-lg text-center">
                                <div
                                    class=" text-nmt-600  md:text-xl text-base font-extrabold tracking-tight  dark:text-white">
                                    ${_airItinerarySelected === 0 ? html`
                                    ${departureItem?.cityName} (${departureItem?.code})
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 inline-block fill-gray-800"
                                        viewBox="0 0 640 512">
                                        <path
                                            d="M381 114.9L186.1 41.8c-16.7-6.2-35.2-5.3-51.1 2.7L89.1 67.4C78 73 77.2 88.5 87.6 95.2l146.9 94.5L136 240 77.8 214.1c-8.7-3.9-18.8-3.7-27.3 .6L18.3 230.8c-9.3 4.7-11.8 16.8-5 24.7l73.1 85.3c6.1 7.1 15 11.2 24.3 11.2l137.7 0c5 0 9.9-1.2 14.3-3.4L535.6 212.2c46.5-23.3 82.5-63.3 100.8-112C645.9 75 627.2 48 600.2 48l-57.4 0c-20.2 0-40.2 4.8-58.2 14L381 114.9zM0 480c0 17.7 14.3 32 32 32l576 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 448c-17.7 0-32 14.3-32 32z" />
                                    </svg>
                                    (${arrivalItem?.code}) ${arrivalItem?.cityName}
                                    ` : html`
                                    ${arrivalItem?.cityName} (${arrivalItem?.code})
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 inline-block fill-gray-800"
                                        viewBox="0 0 640 512">
                                        <path
                                            d="M381 114.9L186.1 41.8c-16.7-6.2-35.2-5.3-51.1 2.7L89.1 67.4C78 73 77.2 88.5 87.6 95.2l146.9 94.5L136 240 77.8 214.1c-8.7-3.9-18.8-3.7-27.3 .6L18.3 230.8c-9.3 4.7-11.8 16.8-5 24.7l73.1 85.3c6.1 7.1 15 11.2 24.3 11.2l137.7 0c5 0 9.9-1.2 14.3-3.4L535.6 212.2c46.5-23.3 82.5-63.3 100.8-112C645.9 75 627.2 48 600.2 48l-57.4 0c-20.2 0-40.2 4.8-58.2 14L381 114.9zM0 480c0 17.7 14.3 32 32 32l576 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 448c-17.7 0-32 14.3-32 32z" />
                                    </svg>
                                    (${departureItem?.code}) ${departureItem?.cityName}
                                    `}
                                </div>
                                <div class="md:text-base text-sm text-gray-900 ml-2">
                                    ${formatDateS(_searchTripResponse[0]?.SearchTripAirs[0]?.AirItinerary[_airItinerarySelected]?.DepartureDate, language)}
                                </div>
                            </div>
                            <div class="flex justify-end items-center space-x-4 absolute md:top-2 -top-6 right-2">
                                ${showLanguageSelect ? html`
                                    <div class="flex items-center space-x-2 ">
                                <select id="language" 
                                    class="bg-gray-200 shadow text-gray-600 text-sm rounded-full  px-2 py-1 border border-white focus:outline-none"
                                    .value=${language}
                                    @change=${(e: Event) => handleLanguageChange((e.target as HTMLSelectElement).value)}
                                >
                                    <option style="background-color: #f0f0f0; color: black;" value="en">English</option>
                                    <option style="background-color: #f0f0f0; color: black;" value="vi">Tiếng Việt</option>
                                </select>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                        ` : ``}
                    </div>

                    ${_isShowLoading ? html`<progress-bar language="${language}" _indeterminate=${true} _progress=${_progress}
                        _mode=${true}></progress-bar>` : ``}
                    `}
                </div>
<!-- start segment -->
<div>
    ${_isLoading
            ? html`<skeleton-segment></skeleton-segment>`
            : _isError
                ? html`
          <!-- Hiển thị lỗi nếu có -->
          <div class="container mx-auto p-4">
            <h1 class="text-2xl font-bold mb-4">${language === 'vi' ? 'Kết quả tìm kiếm chuyến bay' : 'Flight search results'}</h1>
            <div class="flex flex-col items-center justify-center md:p-8 bg-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-24 h-24 text-gray-400 mb-4" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                </svg>
                <h2 class="text-2xl font-bold text-gray-800 mb-2">${language === 'vi' ? 'Lỗi tìm kiếm' : 'Search error'}</h2>
                <p class="text-gray-600 text-center mb-6">
                    ${language === 'vi'
                        ? 'Rất tiếc, đã xảy ra lỗi trong quá trình tìm kiếm chuyến bay của bạn. Vui lòng kiểm tra lại thông tin tìm kiếm của bạn hoặc thử lại sau.'
                        : 'Sorry, an error occurred while searching for your flight. Please check your search information or try again later.'}
                </p>
            </div>
        </div>

        `
                : _searchTripResponse.length > 0
                    ? html`
          <div class="segmentContainer w-auto h-max overflow-x-scroll overflow-y-hidden">
            <!-- Nội dung kết quả tìm kiếm -->
            ${_searchTripResponse.map((response: any) => html`
        ${response?.SearchTripAirs.map((searchTripAir: any) => html`
        ${searchTripAir.AirItinerary[_airItinerarySelected]?.AirSegments?.map((segment: any) => {
            // Kiểm tra nếu cần ẩn chuyến bay đa chặng
            const isMultiSegment = isMultiSegmentFlight(segment);
            const shouldHide = hideMultiSegmentFlights && isMultiSegment && !showMultiSegmentFlights;

            if (shouldHide) {
                return html``;
            }

            return html`
        <!-- start segment left -->
        <div class="segment-item mt-1" data-price="${segment?.LowestInventory?.SumPrice}"
            data-airline="${segment.AirlinesName}" data-departure="${segment.DepartureDate}"
            data-multi-segment="${isMultiSegment ? 'true' : 'false'}" data-legs-count="${segment?.Legs?.length || 0}">
            <div class="w-full flex flex-col rounded-lg  relative cursor-pointer ">
                <div
                    class="w-full flex rounded-lg  relative cursor-pointer ${segmentsSelected[_airItinerarySelected]?.segmentKeyRef === segment.KeyRef ? 'group-selected mb-2' : ''}">
                    ${_isMobileDevice ? html`
                    <div
                        class="group-hover:shadow-lg  w-full relative rounded-s-lg group-[-selected]:border-r-0 group-[-selected]:border-[1px]   group-[-selected]:border-nmt-500 group-[-selected]:shadow-[0px_0px_5px_nmt-500,0px_0px_5px_nmt-500]">
                        <div
                            class="absolute inline-flex  z-30 bg-white border-0 py-2 px-3   justify-center items-center rounded-[13px] rounded-tl-lg rounded-br-lg h-[26px] max-w-full text-nmt-600 box-border">
                            <span
                                class="relative text-nmt-600 font-normal h-[18px] tracking-[0px] leading-[18px] overflow-hidden text-ellipsis whitespace-nowrap">
                                <img src="${apiUrl}/assets/img/airlines/${segment.Airlines}.png"
                                    alt="${segment.AirlinesName}" class="h-full w-auto">

                            </span>
                        </div>
                        <!-- flight info -->
                        <div @click=${() => selectSegment(searchTripAir.BookGDS, searchTripAir.AirCodeRef,
                        searchTripAir.Combine, segment.KeyRef, segment?.LowestInventory?.SumPrice)}
                            class="h-full cursor-pointer flex flex-col justify-between items-center text-gray-800 pb-2
                            pt-6 bg-white/80 rounded-s-lg relative z-20 bg-">
                            <div class="flex justify-center items-center w-full px-4 gap-2">
                                <div class="flex flex-col justify-center items-center">
                                    <strong class="text-base font-bold ">
                                        ${getTimeFromDateTime(segment.DepartureDate)}
                                    </strong>
                                    <strong
                                        class="text-xs font-bold px-4 py-1 rounded-full bg-gray-100 text-nmt-600">
                                        ${segment.DepartureCode}
                                    </strong>
                                </div>
                                <div class="w-full flex-col justify-center items-center">
                                    <div class="w-full text-sm text-center -mb-2">
                                        ${convertDurationToHour(segment.Duration)}
                                    </div>
                                    <div class="w-full flex justify-center items-center md:px-6">
                                        <div
                                            class="w-full h-[2px] rounded-full bg-gradient-to-br  from-nmt-600 to-nmt-300">
                                        </div>
                                        <svg xmlns="http://www.w3.org/2000/svg"
                                            class="w-5 h-5 fill-nmt-600 inline-block ml-[1px]"
                                            viewBox="0 0 576 512">
                                            <path
                                                d="M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z" />
                                        </svg>
                                    </div>

                                    <div class="w-full text-center ">
                                        <div class="text-center text-[10px] line-clamp-2">
                                            <span>
                                                ${getFlights(segment.Legs)}
                                            </span>
                                            <span>
                                                -
                                            </span>
                                            <strong>
                                                ${getDirect(segment.Legs, language)}
                                            </strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex flex-col justify-center items-center">
                                    <strong class="text-base font-bold ">

                                        ${getTimeFromDateTime(segment.ArrivalDate)}
                                    </strong>
                                    <strong
                                        class="text-xs font-bold px-4 py-1 rounded-full bg-gray-100 text-nmt-600">
                                        ${segment.ArrivalCode}
                                    </strong>
                                </div>

                            </div>

                        </div>
                    </div>
                    ` : html`
                    <div
                        class="group-hover:shadow-lg group-[-selected]:border-r-0 w-full relative rounded-s-lg  group-[-selected]:border-[1px]   group-[-selected]:border-nmt-500 group-[-selected]:shadow-[0px_0px_5px_nmt-500,0px_0px_5px_nmt-500]">
                        <div @click=${() => selectSegment(searchTripAir.BookGDS, searchTripAir.AirCodeRef,
                            searchTripAir.Combine, segment.KeyRef, segment?.LowestInventory?.SumPrice)}
                            class="w-full relative bg-white rounded-s-lg text-[#8592a6] flex flex-row flex-[1_1_0]
                            items-center h-full">
                            <div class="w-full flex items-center rounded-lg   flex-col justify-between pe-4">
                                <div class="w-full flex flex-row  items-center rounded-lg">
                                    <div
                                        class="px-3 flex flex-col justify-center text-gray-800/80  box-border  text-sm  flex-[1_0_0] items-start relative">
                                        <span
                                            class="relative  font-normal h-[26px] tracking-[0px] leading-[26px] overflow-hidden text-ellipsis whitespace-nowrap">
                                            <img src="${apiUrl}/assets/img/airlines/${segment?.Airlines}.png"
                                                class="h-full w-auto">

                                        </span>
                                        <span
                                            class="text-nowrap max-md:hidden text-xs font-semibold lg:w-full lg:min-w-[156px] md:w-24 line-clamp-1">${segment?.AirlinesName}</span>
                                    </div>
                                    <div
                                        class="flex flex-col md:px-4 justify-between  rounded-lg   flex-[1_0_0] items-start pr-0 ">
                                        <div class="flex justify-start items-center w-full gap-2 ">
                                            <div class="flex flex-col justify-start items-start">
                                                <strong class="md:text-xl text-base font-bold text-[#0f294d]">
                                                    ${getTimeFromDateTime(segment?.DepartureDate)}
                                                </strong>
                                                <strong class="md:text-base text-sm font-normal  ">
                                                    ${segment?.DepartureCode}
                                                </strong>
                                            </div>
                                            <div class="w-full  flex flex-col justify-center items-center ">
                                                <span class="md:text-base text-sm lg:px-12 md:px-6">
                                                    ${convertDurationToHour(segment?.Duration)}</span>
                                                <div class="w-full flex">
                                                    <div class="bg-[#dadfe6] w-1.5 h-1.5"></div>
                                                    <div
                                                        class="w-full flex justify-center items-center relative before:absolute before:bg-white before:h-1.5 before:w-1.5 before:border-2 before:border-[#dadfe6] ">
                                                        <div class="w-full h-[2px] rounded-full bg-[#dadfe6]">
                                                        </div>
                                                    </div>
                                                    <div class="bg-[#dadfe6] w-1.5 h-1.5"></div>
                                                </div>

                                                <div class="text-center font-normal text-[10px] md:px-4 px-1">
                                                    ${getDirect(segment.Legs, language)}
                                                </div>
                                            </div>
                                            <div class="flex flex-col justify-end items-end">
                                                <strong class="md:text-xl text-base font-bold text-gray-900">
                                                    ${getTimeFromDateTime(segment?.ArrivalDate)}
                                                </strong>
                                                <strong class="md:text-base text-sm font-normal ">
                                                    ${segment?.ArrivalCode}
                                                </strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div
                                    class="text-center flex flex-[1_1_0]  items-end flex-col md:min-w-28 lg:min-w-40  min-w-12  justify-end">
                                    <span class="text-xs font-semibold line-clamp-1 pe-1.5 text-gray-800 bg-gray-100 rounded-full px-1">
                                        ${getFlights(segment.Legs)}
                                    </span>
                                    ${showDetailTicket ? html`
                                    <div @click=${() => isShowDetailSegment(segment)}
                                        class="text-nmt-600 rounded-full text-nowrap w-fit flex gap-1 items-center
                                        justify-center bg-white border border-white font-semibold cursor-pointer
                                        text-[10px] px-1.5">
                                        <svg class="w-4 h-4  inline-block" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                            viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M10 3v4a1 1 0 0 1-1 1H5m4 4 1 5 2-3.333L14 17l1-5m4-8v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1Z" />
                                        </svg>
                                        ${language === 'vi' ? 'Chi tiết' : 'Details'}
                                    </div>
                                    ` : ``}
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                    `}
                    <div class="relative flex-[0_0_auto] lg:min-w-56 md:min-w-40 ${showFromText ? 'min-w-32' : 'min-w-28'}  flex flex-row rounded-e-lg">
                        
                        <div class="w-3.5 min-w-3.5 h-full m-auto  bg-transparent z-20 ">
                            <div class="w-3.5 h-3 bg-white mask-top-circle-cut"></div>
                            <div class="w-3.5 h-[calc(100%-1.5rem)] bg-white  flex justify-center items-center relative z-10">
                                <div style="background-image: linear-gradient(to bottom, #fff 50%, #ea580c 50%) !important; width: 1px; height: 100%; position: relative; background-size: 100% 10px;">
                                    
                                </div>
                            </div>
                            <div class="w-3.5 h-3 bg-white mask-bottom-circle-cut"></div>
                        </div>
                        <div
                            class="w-full h-full  group-hover:shadow-lg   bg-white flex justify-between items-center cursor-pointer relative z-10 rounded-e-lg group-[-selected]:border-[1px]   group-[-selected]:border-nmt-500 group-[-selected]:shadow-[0px_0px_5px_nmt-500,0px_0px_5px_nmt-500] group-[-selected]:border-l-0">
                            <!-- eco class -->
                            <div class="w-full h-full text-center cursor-pointer">
                                ${segment?.LowestInventory ? html`
                                <div @click=${() => selectSegment(searchTripAir.BookGDS, searchTripAir.AirCodeRef,
                                searchTripAir.Combine, segment.KeyRef, segment?.LowestInventory?.SumPrice)}
                                    class=" flex flex-col justify-center items-end pe-2 w-full h-full rounded-e-lg">
                                    <div class="flex max-md:flex-col gap-1 text-gray-400 ">
                                        <div class="flex flex-col items-end justify-end">
                                        ${isCurrencyLoaded ? html`
                                            <span class="relative ps-4 flex  justify-start items-start">
                                                <span class="text-xs font-semibold -top-0 absolute ${language === 'vi' ? '-left-1' : '-left-4'}">
                                                    ${showFromText ? html`${language === 'vi' ? 'Từ' : 'From'}` : ``}
                                                </span>
                                                <span
                                                    class="md:text-lg text-xs text-nmt-500 text-center font-bold ">
                                                    ${(segmentsSelected[_airItinerarySelected]?.segmentKeyRef === segment.KeyRef ?
                                                    getTotalPriceWithMode() : getTotalPriceLowestWithMode(segment))}
                                                    </span>
                                                        <small class="text-[10px] font-semibold -top-2 text-gray-800 ">${_currencySymbol}</small>
                                                    </span>
                                                    ` : html`
                                                    <!-- Nếu chưa load xong, có thể hiển thị placeholder hoặc ẩn hoàn toàn phần giá -->
                                                    <div class="col-span-12 border-t  p-2 grid grid-cols-1 justify-end items-center bg-[#f8e2de] rounded-b-lg border-[1px] border-white shadow-[0px_0px_15px_#cccccc96,0px_0px_15px_#44444400_inset]">
                                                        <div class="text-center text-gray-400 animate-pulse"></div>
                                                    </div>
                                                `}
                                            ${searchTripAir.Combine ? html`<span
                                                class="md:text-xs text-[10px] -mt-1 ">${language === 'vi' ? 'Khứ hồi' : 'Round trip'}</span>` : ``}

                                        </div>
                                    </div>
                                    ${_isMobileDevice && showDetailTicket ? html`
                                    <div @click=${() => isShowDetailSegment(segment)}
                                        class="text-nmt-600 rounded-full w-fit flex gap-1 items-center
                                        justify-center bg-gray-200/50 border border-white font-semibold cursor-pointer
                                        text-[10px] px-1.5 ${segment.IsShowDetail ? '!bg-white' : ''}">
                                        <svg class="w-4 h-4  inline-block" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                            viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M10 3v4a1 1 0 0 1-1 1H5m4 4 1 5 2-3.333L14 17l1-5m4-8v16a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V7.914a1 1 0 0 1 .293-.707l3.914-3.914A1 1 0 0 1 9.914 3H18a1 1 0 0 1 1 1Z" />
                                        </svg>
                                        Chi tiết
                                    </div>
                                    `: ``}
                                    <span class="md:text-xs text-[10px] text-red-600">
                                        ${segmentsSelected[_airItinerarySelected]?.segmentKeyRef === segment.KeyRef
                                ? (() => {
                                    const availability = InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos?.[0]?.Availability;
                                    return availability && availability <= 5 && availability > 0
                                        ? html`<span>${language === 'vi' ? 'Còn lại' : 'Remaining'} <strong>${availability}</strong></span>`
                                        : "";
                                })()
                                : (() => {
                                    const availability = segment?.LowestInventory?.BookingInfos?.[0]?.Availability;
                                    return availability && availability <= 5 && availability > 0
                                        ? html`<span>${language === 'vi' ? 'Còn lại' : 'Remaining'} <strong>${availability}</strong></span>`
                                        : "";
                                })()
                            }
                                </span>
                                    
                                </div>
                                `: html`
                                <div class="p-2 w-full h-full flex flex-col justify-center items-center cursor-pointer">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-plane text-gray-400"
                                        aria-hidden="true">
                                        <path
                                            d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                        </path>
                                    </svg>
                                    <span class="md:text-base text-sm font-bold text-gray-500">${language === 'vi' ? 'Hết vé' : 'Sold out'}</span>
                                </div>
                                `}

                            </div>
                        </div>
                    </div>
                </div>
                <!-- start details segment-->
                <div class="w-full overflow-hidden">
                    <div
                        class=" transition-all duration-700  ease-in-out ${segment.IsShowDetail && segment.KeyRef === segmentsSelected[_airItinerarySelected]?.segmentKeyRef ? '!max-h-[1000px] !w-full !opacity-100' : ' w-0 overflow-hidden opacity-0 max-h-0 '}">
                        <div class=" w-full border border-gray-200 border-t-0 bg-gray-100 p-2">
                            <!-- start flight infor -->
                            <div class="bg-white rounded-e-lg rounded-bl-lg">
                                <div class="py-[2px]">
                                    <span
                                        class=" bg-gradient-to-br from-nmt-600 to-nmt-300 text-white font-extrabold pl-2 py-1 pe-6 md:pe-12 rounded-tr-full">
                                        ${language === 'vi' ? 'Chi tiết hành trình' : 'Journey Details'}
                                    </span>
                                </div>
                                <div class="w-full">
                                    ${segment.Legs.map((leg: any, index: number) => html`
                                    ${index > 0 ? html`
                                    <div class="w-full bg-gray-600 p-1 text-white text-center text-sm">
                                        ${language === 'vi' ? 'Trung chuyển tại' : 'Transit at'} ${inforAirports[leg.DepartureCode]?.name}
                                        <strong>(${leg.DepartureCode})</strong> - ${language === 'vi' ? 'Thời gian' : 'Time'}:
                                        <strong>${convertDurationToHour(segment.Legs[index].StopTime)}</strong>
                                    </div>
                                    `: ``}
                                    <div class="grid grid-cols-12 border-t border-gray-200">
                                        <div
                                            class="col-span-7 flex flex-col justify-center items-center text-black py-2 md:px-8  border-black">
                                            <div
                                                class="w-full flex justify-between items-center px-4 md:text-sm  text-[10px]">
                                                <div class="text-left">
                                                    <span class="font-extrabold">(${leg?.DepartureCode})</span>
                                                    ${inforAirports[leg?.DepartureCode]?.cityName}
                                                    <div><span
                                                            class="text-gray-400 text-xs">${inforAirports[leg?.DepartureCode]?.name}</span>
                                                    </div>
                                                </div>

                                                <div class="text-right">
                                                    ${inforAirports[leg?.ArrivalCode]?.cityName}
                                                    <span class="font-extrabold">(${leg?.ArrivalCode})</span>
                                                    <div>
                                                        <span class="text-gray-400 text-xs">
                                                            ${inforAirports[leg?.ArrivalCode]?.name}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex justify-center items-center w-full px-4 gap-2">
                                                <div class="flex flex-col justify-start items-start">
                                                    <strong
                                                        class="md:text-3xl text-base font-extrabold text-nmt-600">
                                                        ${getTimeFromDateTime(leg?.DepartureDate)}</strong>
                                                    <span class="${language === 'vi' ? 'md:text-sm' : 'md:text-xs'} text-[10px]  text-nowrap">
                                                        ${formatDateTo_ddMMyyyy(leg?.DepartureDate, language)}
                                                    </span>
                                                    <strong class="md:text-base text-sm font-semibold text-gray-600 text-nowrap">
                                                            ${language === 'vi' ? 'Nhà ga' : 'Terminal'}: ${leg?.DepartureTerminal || '-'}
                                                        </strong>
                                                </div>
                                                <div
                                                    class="w-full flex-col justify-center items-center px-4 text-sm md:px-6">
                                                    <div class="w-full text-center -mb-2">
                                                        ${leg?.Equipment}
                                                    </div>
                                                    <div class="w-full flex justify-center items-center">
                                                        <div class="w-full h-[2px] rounded-full bg-nmt-600">
                                                        </div>
                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                            class="w-5 h-5 fill-nmt-600 inline-block ml-[1px]"
                                                            viewBox="0 0 576 512">
                                                            <path
                                                                d="M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z" />
                                                        </svg>
                                                    </div>
                                                    <div class="w-full text-center -mt-2">

                                                        ${convertDurationToHour(leg?.Duration)}
                                                    </div>
                                                </div>
                                                <div class="flex flex-col justify-end items-end">
                                                    <strong
                                                        class="md:text-2xl text-base font-extrabold text-nmt-500">
                                                        ${getTimeFromDateTime(leg?.ArrivalDate)}</strong>
                                                    <span class="${language === 'vi' ? 'md:text-sm' : 'md:text-xs'} text-[10px]  text-nowrap">
                                                        ${formatDateTo_ddMMyyyy(leg?.ArrivalDate, language)}
                                                    </span>
                                                    <strong class="md:text-base text-sm font-semibold text-gray-600 text-nowrap">
                                                            ${language === 'vi' ? 'Nhà ga' : 'Terminal'}: ${leg?.ArrivalTerminal || '-'}
                                                        </strong>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="col-span-5 flex flex-row">
                                            <div
                                                class="w-[1px] h-full bg-[100%_10px] bg-[linear-gradient(to_bottom,_transparent_50%,_#fb7740_50%)] bg-[length:100%_10px]">

                                            </div>
                                            <div
                                                class="px-4 py-2 flex flex-col items-center justify-center w-full md:text-sm text-[10px]">
                                                <span class="text-xs font-bold">${language === 'vi' ? 'Hãng vận chuyển' : 'Carrier'}</span>
                                                <img src="${apiUrl}/assets/img/airlines/${leg.OperatingAirlines}.png"
                                                    class=" w-auto h-12 mx-auto my-1">

                                                <span>${language === 'vi' ? 'Chuyến bay' : 'Flight'}: <span
                                                        class="text-nmt-500 font-extrabold tracking-wide">${leg?.Airlines
                                + leg?.FlightNumber}</span></span>
                                                <span>${language === 'vi' ? 'Loại vé' : 'Ticket type'}: <strong>${InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos[index]?.CabinName}</strong>

                                                </span>
                                            </div>
                                        </div>
                                        <div
                                            class="col-span-12 border-t border-gray-200 p-2 flex justify-end items-center bg-gray-50/80">
                                            <span class="text-end text-xs text-gray-800">
                                                ${language === 'vi' ? 'Loại vé' : 'Ticket type'}: <strong>
                                                    ${InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos[index]?.FareType
                                ||
                                InventoriesSelected[_airItinerarySelected]?.inventorySelected?.CabinName}
                                                </strong>
                                                | ${language === 'vi' ? 'Hành lý xách tay' : 'Hand baggage'}:
                                                ${InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos[index]?.HandBaggage
                                    > 1 &&
                                    InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos[index]?.HandWeightBag
                                    !== 0 ? html`
                                                <strong>${InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos[index]?.HandBaggage}</strong>
                                                `: ``}

                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-luggage w-4 h-4 text-blue-500 flex-shrink-0  inline-block">
                                                    <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16">
                                                    </path>
                                                    <rect width="20" height="14" x="2" y="6" rx="2">
                                                    </rect>
                                                </svg>

                                                <strong>${InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos[index]?.HandWeightBag
                                    > 0 ?
                                    InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos[index]?.HandWeightBag
                                    : 7}</strong>
                                                kg
                                                | ${language === 'vi' ? 'Hành lý ký gửi' : 'Checked baggage'}:
                                                ${InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos[index]?.BagPieces
                                    > 1 &&
                                    InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos[index]?.WeightBag
                                    !== 0 ? html`
                                                <strong>${InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos[index]?.BagPieces}</strong>
                                                `: ``}

                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-luggage w-4 h-4 text-blue-500 flex-shrink-0  inline-block">
                                                    <path
                                                        d="M6 20h0a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h0">
                                                    </path>
                                                    <path d="M8 18V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v14">
                                                    </path>
                                                    <path d="M10 20h4"></path>
                                                    <circle cx="16" cy="20" r="2"></circle>
                                                    <circle cx="8" cy="20" r="2"></circle>
                                                </svg>

                                                <strong>${InventoriesSelected[_airItinerarySelected]?.inventorySelected?.BookingInfos[index]?.WeightBag}</strong>
                                                kg
                                                | <svg class="w-4 h-4 inline-block text-gray-800 dark:text-white"
                                                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" fill="none" viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round"
                                                        stroke-linejoin="round" stroke-width="2"
                                                        d="M12 8v4l3 3m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                </svg>
                                                ${language === 'vi' ? 'Thời gian bay' : 'Flight time'}:
                                                <strong>${getDurarionLeg(leg)}</strong>
                                                | ${language === 'vi' ? 'Máy bay' : 'Aircraft'}: <strong>${leg.Equipment}</strong>
                                            </span>
                                        </div>
                                    </div>
                                    `)}

                                    <div
                                        class="col-span-12 border-t border-gray-200 p-2 grid grid-cols-1 justify-end items-center bg-[#f8e2de] rounded-b-lg">
                                        <div
                                            class=" transition-all ease-in-out duration-300 ${_isShowPriceDetails ? '!opacity-100 !h-auto !w-full' : 'opacity-0 h-0 w-0'}">
                                            <div class="grid grid-cols-4 border-b border-gray-600 md:text-sm text-xs">
                                                <div class="text-start font-bold">
                                                    ${language === 'vi' ? 'Hành khách' : 'Passenger'}
                                                </div>
                                                <div class="text-end font-bold">
                                                    ${language === 'vi' ? 'Giá vé' : 'Ticket price'}
                                                </div>
                                                <div class="text-end font-bold">
                                                    ${language === 'vi' ? 'Thuế' : 'Tax'}
                                                </div>
                                                <div class="text-end font-bold">
                                                    ${language === 'vi' ? 'Giá Bán' : 'Total price'}
                                                </div>
                                            </div>
                                            ${InventoriesSelected[_airItinerarySelected]?.inventorySelected?.FareInfos.map((fareInfo:
                                        any) => html`
                                            <div
                                                class="grid grid-cols-4 border-b border-gray-600 py-1 md:text-sm text-xs">
                                                <div class="text-start">${getPassengerDescription(searchTripRequest, fareInfo?.PaxType, language)}</div>
                                                <div class="text-end">${formatNumber(fareInfo.Fare, _convertedVND, language)}</div>
                                                <div class="text-end">${formatNumber(fareInfo.Tax, _convertedVND, language)}</div>
                                                <div class="text-end">${formatNumber(fareInfo.Fare + fareInfo.Tax, _convertedVND, language)}
                                                </div>
                                            </div>
                                            `)}

                                        </div>

                                        <div class=" text-right md:text-sm text-xs">
                                            ${showDetailTicket ? html`
                                            <span @click=${(event: Event) => showPriceDetails(event)}
                                                class="text-sm text-nmt-500 underline ${_isShowPriceDetail ? '!text-red-500' : ''} cursor-pointer">
                                                ${_isShowPriceDetails ? (language === 'vi' ? 'Ẩn chi tiết' : 'Hide details') : (language === 'vi' ? 'Chi tiết' : 'Details')}
                                            </span>
                                            ` : ``}
                                            
                                            ${language === 'vi' ? 'Tổng cộng' : 'Total'}: <strong class="md:text-xl text-base font-bold text-nmt-600">
                                                ${formatNumber(InventoriesSelected[_airItinerarySelected]?.inventorySelected?.SumPrice, _convertedVND, language)}
                                            </strong>${_currencySymbol}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div 
                        class="col-span-12 relative w-full ${segment.KeyRef === segmentsSelected[_airItinerarySelected]?.segmentKeyRef && showMoreClassic ? 'slide-in' : 'slide-out'}">
                        ${canScrollLeft ? html`
                        <button @click=${() => scrollInventory('left')}
                            class="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-nmt-500 hover:bg-nmt-600
                            text-white p-2.5 rounded-full shadow-lg transition-all duration-200 flex items-center
                            justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chevron-left">
                                <path d="m15 18-6-6 6-6" />
                            </svg>
                        </button>
                        `: ``}

                        ${segment.KeyRef === segmentsSelected[_airItinerarySelected]?.segmentKeyRef ? html`
                        <div class="w-full  border border-gray-200 border-t-0 rounded-b-lg p-2 bg-gray-100">
                            <div
                                class="scrollContainer scroll-container flex flex-nowrap gap-2 overflow-x-auto items-stretch  w-full scroll-smooth ${InventoriesSelected[_airItinerarySelected]?.segment?.Inventories.length <= 3 && !_isMobileDevice || InventoriesSelected[_airItinerarySelected]?.segment?.Inventories.length < 2 && _isMobileDevice ? 'justify-center items-start' : ''}">
                                ${InventoriesSelected[_airItinerarySelected]?.segment?.Inventories.map((inventory: any) => html`
                                <div
                                    class="lg:w-1/4 md:w-1/3 w-1/2 md:min-w-[230px] min-w-[200px] flex flex-col items-center mb-1 group">
                                    <div @click=${() => selectInventory(searchTripAir.BookGDS, searchTripAir.AirCodeRef,
                                            searchTripAir.Combine, segment.KeyRef, inventory.SumPrice)}
                                        class="h-full bg-white rounded-lg w-full flex flex-col justify-start items-start
                                        shadow group-hover:shadow-lg group-hover:transition-all group-hover:duration-300
                                        group-hover:ease-in-out group-hover:transform group-hover:-translate-y-1
                                        group-hover:border group-hover:border-nmt-500
                                        ${segmentsSelected[_airItinerarySelected]?.segmentKeyRef === segment.KeyRef &&
                                                segmentsSelected[_airItinerarySelected]?.sumPrice === inventory.SumPrice ? `shadow-lg transition-all
                                        duration-300 ease-in-out transform border border-nmt-500 group-selected` :
                                                ``}">
                                        <div class="rounded-t-lg rounded-b-[50%_1rem] border-nmt-500  border-b-2 p-2 w-full flex flex-col justify-center items-center cursor-pointer group-hover:bg-nmt-500  group-hover:text-white group-[-selected]:bg-nmt-500  group-[-selected]:text-white">
                                            <input type="radio" name="flight"
                                                .checked=${segmentsSelected[_airItinerarySelected]?.segmentKeyRef === segment.KeyRef &&
                                            segmentsSelected[_airItinerarySelected]?.sumPrice === inventory.SumPrice}
                                                @click=${() => selectInventory(searchTripAir.BookGDS,
                                                searchTripAir.AirCodeRef, searchTripAir.Combine, segment.KeyRef,
                                                inventory.SumPrice)}>
                                            <div
                                                class="text-nmt-600 text-lg font-bold group-hover:text-white group-[-selected]:text-white">
                                                ${getInventoryPriceWithMode(inventory)} ${_currencySymbol}</div>
                                            <div
                                                class="text-gray-900 text-sm font-bold group-hover:text-white text-center group-[-selected]:text-white">
                                                ${getFareType(inventory?.BookingInfos)}</div>
                                        </div>
                                        ${inventory.FareRule && !_isGlobal ?
                                                html`
                                        <div class="w-full px-2 py-4">
                                            <div
                                                class="w-full flex flex-col justify-center items-start space-y-2 text-sm">
                                                <div class="flex items-start space-x-2"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-briefcase w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5">
                                                        <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16">
                                                        </path>
                                                        <rect width="20" height="14" x="2" y="6" rx="2">
                                                        </rect>
                                                    </svg>
                                                    <div>
                                                        <p class="font-semibold">${language === 'vi' ? 'Hành lý xách tay' : 'Hand baggage'}</p>
                                                        ${inventory.FareRule.handBaggage ? html`
                                                        <p class="text-gray-600">
                                                            ${inventory.FareRule.handBaggage > 1 ? html`
                                                            <span>${inventory.FareRule.handBaggage} x </span>` : ``}
                                                            ${inventory.FareRule.handWeightBag}
                                                        </p>
                                                        `: html`
                                                        <p class="text-gray-600">${language === 'vi' ? 'Không bao gồm hành lý xách tay' : 'No hand baggage included'}</p>
                                                        `}
                                                        ${inventory.FareRule.note ? html`<p class="text-nmt-600">*
                                                            ${inventory.FareRule.note}</p>` : ``}
                                                    </div>
                                                </div>
                                                <div class="flex items-start space-x-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-luggage w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5">
                                                        <path
                                                            d="M6 20h0a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h0">
                                                        </path>
                                                        <path d="M8 18V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v14">
                                                        </path>
                                                        <path d="M10 20h4"></path>
                                                        <circle cx="16" cy="20" r="2"></circle>
                                                        <circle cx="8" cy="20" r="2"></circle>
                                                    </svg>
                                                    <div>
                                                        <p class="font-semibold">${language === 'vi' ? 'Hành lý ký gửi' : 'Checked baggage'}</p>
                                                        ${inventory.FareRule.bagPieces ? html`
                                                        <p class="text-gray-600">
                                                            ${inventory.FareRule.bagPieces > 1 ?
                                                            html`<span>${inventory.FareRule.bagPieces} x </span>` : ''}
                                                            ${inventory.FareRule.weightBag}
                                                        </p>
                                                        `: html`<p class="text-gray-600">${language === 'vi' ? 'Không bao gồm hành lý ký gửi' : 'No checked baggage included'}</p>`}
                                                    </div>
                                                </div>
                                                ${showDetailFareRule ? html`
                                                    <div class="flex items-start space-x-2"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-arrow-left-right w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5">
                                                        <path d="M8 3 4 7l4 4"></path>
                                                        <path d="M4 7h16"></path>
                                                        <path d="m16 21 4-4-4-4"></path>
                                                        <path d="M20 17H4"></path>
                                                    </svg>
                                                    <div>
                                                        <p class="font-semibold">${language === 'vi' ? 'Thay đổi vé' : 'Ticket change'}</p>
                                                        <p class="text-gray-600">
                                                            ${inventory.FareRule.rebookLeast03h}
                                                        </p>
                                                        ${inventory.FareRule.rebookWithin03h ? html`
                                                        <p class="text-gray-600">
                                                            <span class="text-red-600">* ${language === 'vi' ? 'Trong vòng 03 tiếng trước giờ khởi hành và sau giờ khởi hành' : 'Within 03 hours before and after departure time'}</span>
                                                            ${inventory.FareRule.rebookWithin03h}
                                                        </p>
                                                        ` : html``}
                                                        ${inventory.FareRule.rebookWithin03Holiday ? html`
                                                        <p class="text-nmt-600 ">
                                                            <span class="text-red-600">* ${language === 'vi' ? 'Giai đoạn bay Tết' : 'Tet holiday period'}</span>
                                                            ${inventory.FareRule.rebookWithin03Holiday}
                                                        </p>
                                                        ` : ``}
                                                    </div>
                                                </div>
                                                <div class="flex items-start space-x-2"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-ban w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5">
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <path d="m4.9 4.9 14.2 14.2"></path>
                                                    </svg>
                                                    <div>
                                                        <p class="font-semibold">${language === 'vi' ? 'Hoàn vé' : 'Refund'}</p>
                                                        ${inventory.FareRule.refundLeast03h ? html`
                                                        <p class="text-gray-600">
                                                            ${inventory.FareRule.refundLeast03h}
                                                        </p>
                                                        `: html`<p class="text-gray-600">${language === 'vi' ? 'Không hoàn vé' : 'No refund'}</p>`}
                                                        ${inventory.FareRule.refundWithin03h ? html`
                                                        <p class="text-gray-600">
                                                            <span class="text-red-600">* ${language === 'vi' ? 'Trong vòng 03 tiếng trước giờ khởi hành và sau giờ khởi hành' : 'Within 03 hours before and after departure time'} </span>
                                                            ${inventory.FareRule.refundWithin03h}
                                                        </p>
                                                        `: html``}
                                                        ${inventory.FareRule.refundWithin03Holiday ? html`
                                                        <p class="text-nmt-600 "> <span class="text-red-600">* ${language === 'vi' ? 'Giai đoạn bay Tết' : 'Tet holiday period'} </span>
                                                            ${inventory.FareRule.refundWithin03Holiday}
                                                        </p>
                                                        `: html``}
                                                    </div>
                                                </div>

                                                <div
                                                    class="flex border-t border-dashed border-gray-200 w-full items-start space-x-2 text-xs">
                                                    <ul>
                                                        ${inventory.FareRule.meal ? html`
                                                        <li class="text-gray-600">
                                                            - ${language === 'vi' ? 'Suất ăn' : 'Meal'}: ${inventory.FareRule.meal}
                                                        </li>
                                                        `: html``}

                                                        ${inventory.FareRule.businessLounge ? html`
                                                        <li class="text-gray-600">
                                                            - ${language === 'vi' ? 'Phòng chờ thương gia' : 'Business lounge'}: ${inventory.FareRule.businessLounge}
                                                        </li>
                                                        `: html``}

                                                        ${inventory.FareRule.seatSelection ? html`
                                                        <li class="text-gray-600">
                                                            - ${language === 'vi' ? 'Chọn ghế' : 'Seat selection'}: ${inventory.FareRule.seatSelection}
                                                        </li>
                                                        `: html``}

                                                        ${inventory.FareRule.priorityCheckIn ? html`
                                                        <li class="text-gray-600">
                                                            - ${language === 'vi' ? 'Làm thủ tục ưu tiên' : 'Priority check-in'}: ${inventory.FareRule.priorityCheckIn}
                                                        </li>
                                                        `: html``}
                                                        ${inventory.FareRule.modifyName ? html`
                                                        <li class="text-gray-600">
                                                            - ${language === 'vi' ? 'Sửa tên' : 'Name modification'}: ${inventory.FareRule.modifyName}
                                                        </li>
                                                        `: html``}
                                                        ${inventory.FareRule.refundNameRetention ? html`
                                                        <li class="text-gray-600">
                                                            - ${language === 'vi' ? 'Hoàn tiền giữ tên' : 'Name retention refund'}: ${inventory.FareRule.refundNameRetention}
                                                        </li>
                                                        `: html``}
                                                        ${inventory.FareRule.noShow ? html`
                                                        <li class="text-gray-600">
                                                            - ${language === 'vi' ? 'No show' : 'No show'}: ${inventory.FareRule.noShow}
                                                        </li>
                                                        `: html``}
                                                        ${inventory.FareRule.noShowHoliday ? html`
                                                        <li class="text-gray-600">
                                                            - ${language === 'vi' ? 'No show dịp lễ' : 'No show holiday'}: ${inventory.FareRule.noShowHoliday}
                                                        </li>
                                                        `: html``}
                                                    </ul>
                                                </div>
                                                ` : ``}
                                                
                                            </div>
                                        </div>
                                        ` : html`
                                        <div class="w-full px-2 py-4">
                                            <!-- detail service of class-->
                                            <div
                                                class="w-full flex flex-col justify-center items-start space-y-2 text-sm">

                                                <div class="flex items-start space-x-2"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-briefcase w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5">
                                                        <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16">
                                                        </path>
                                                        <rect width="20" height="14" x="2" y="6" rx="2">
                                                        </rect>
                                                    </svg>
                                                    <div>

                                                        <p class="font-semibold">${language === 'vi' ? 'Hành lý xách tay' : 'Hand baggage'}</p>

                                                        <p class="text-gray-600">
                                                            ${inventory.BookingInfos[0]?.HandBaggage > 1 ?
                                                        html`<span>${inventory.BookingInfos[0]?.HandBaggage} x
                                                            </span>`
                                                        : html``}
                                                            ${inventory.BookingInfos[0]?.HandWeightBag > 0 ?
                                                        inventory.BookingInfos[0]?.HandWeightBag : 7} kg
                                                        </p>
                                                    </div>

                                                </div>
                                                <div class="flex items-start space-x-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-luggage w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5">
                                                        <path
                                                            d="M6 20h0a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h0">
                                                        </path>
                                                        <path d="M8 18V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v14">
                                                        </path>
                                                        <path d="M10 20h4"></path>
                                                        <circle cx="16" cy="20" r="2"></circle>
                                                        <circle cx="8" cy="20" r="2"></circle>
                                                    </svg>
                                                    <div>
                                                        <p class="font-semibold">${language === 'vi' ? 'Hành lý ký gửi' : 'Checked baggage'}</p>
                                                        <span>
                                                            ${inventory.BookingInfos[0]?.BagPieces > 1 &&
                                                        inventory.BookingInfos[0]?.WeightBag !== 0 ? html`
                                                            <span>${inventory.BookingInfos[0]?.BagPieces} x</span>
                                                            `: html``}
                                                            <span>${inventory.BookingInfos[0]?.WeightBag || 0}</span>kg
                                                        </span>
                                                    </div>
                                                </div>
                                                ${showDetailFareRule ? html`
                                                    <div class="flex items-start space-x-2"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-arrow-left-right w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5">
                                                        <path d="M8 3 4 7l4 4"></path>
                                                        <path d="M4 7h16"></path>
                                                        <path d="m16 21 4-4-4-4"></path>
                                                        <path d="M20 17H4"></path>
                                                    </svg>
                                                    <div>
                                                        <p class="font-semibold">${language === 'vi' ? 'Thay đổi vé' : 'Change ticket'}</p>
                                                        <p class="text-gray-600">
                                                            * ${language === 'vi' ? 'Theo điều kiện vé' : 'According to ticket conditions'}
                                                        </p>

                                                    </div>
                                                </div>
                                                <div class="flex items-start space-x-2"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-ban w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5">
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <path d="m4.9 4.9 14.2 14.2"></path>
                                                    </svg>
                                                    <div>
                                                        <p class="font-semibold">${language === 'vi' ? 'Hoàn vé' : 'Refund ticket'}</p>
                                                        <p class="text-gray-600">
                                                            * ${language === 'vi' ? 'Theo điều kiện vé' : 'According to ticket conditions'}
                                                        </p>
                                                    </div>
                                                </div>

                                                <div
                                                    class="flex border-t border-dashed border-gray-200 w-full items-start space-x-2 text-xs">

                                                </div>
                                                ` : ``}
                                                
                                            </div>
                                        </div>
                                        `}

                                    </div>
                                </div>
                                `)}

                            </div>
                        </div>
                        `: html``}

                        ${canScrollRight ? html`
                        <button @click=${() => scrollInventory('right')}
                            class="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-nmt-500 hover:bg-nmt-600
                            text-white p-2.5 rounded-full shadow-lg transition-all duration-200 flex items-center
                            justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chevron-right">
                                <path d="m9 18 6-6-6-6" />
                            </svg>
                        </button>
                        ` : ``}
                    </div>
                </div>
            </div>
        </div>
        `;
        })}
        `)}
        `)}
          </div>
        `
                    : html`
          <!-- Trường hợp không có dữ liệu -->
           <div class="container mx-auto p-4 h-full">
            <h1 class="text-2xl font-bold mb-4">${language === 'vi' ? 'Kết quả tìm kiếm chuyến bay' : 'Flight Search Results'}</h1>
            <div class="flex flex-col items-center justify-center md:p-8 bg-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-24 h-24 text-gray-400 mb-4" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                </svg>
                <h2 class="text-2xl font-bold text-gray-800 mb-2 text-center">
                    ${language === 'vi' ? 'Không tìm thấy chuyến bay nào' : 'No flights found'}
                </h2>
                <p class="text-gray-600 text-center mb-6">
                    ${language === 'vi'
                            ? 'Rất tiếc, chúng tôi không tìm thấy chuyến bay nào phù hợp với tìm kiếm của bạn.'
                            : 'Sorry, we could not find any flights matching your search.'}
                </p>
            </div>
        </div>
        `}
  </div>
    <!-- end segment -->

                ${_isLoading ? html`
                 <div class="flex items-center justify-center w-full bg-white p-4 rounded-lg mt-4 shadow-2xl sticky bottom-0 border">
                    <div
                        class="h-auto w-auto border border-gray-300 flex tracking-tight gap-2 items-center justify-center rounded-lg px-6 py-3 me-2 bg-gray-200 animate-pulse">
                        <div class="h-4 w-16 bg-gray-300 rounded"></div>
                        <div class="w-6 h-6 bg-gray-300 rounded-full"></div>
                    </div>
                </div>
                ` : html`
                ${checkNextStep() ? html`
                 <div
                    class="flex items-center z-50 justify-center w-full bg-white p-4 rounded-lg mt-4 shadow-2xl sticky bottom-0 border">
                    <button @click="${() => nextStep()}"
                        class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                        <div>${language === 'vi' ? 'Chọn hành trình về' : 'Select return journey'}</div>
                        <div><svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                fill="currentColor" viewBox="0 0 24 24" class="w-6 h-6 text-white  animate-pulse">
                                <path fill-rule="evenodd"
                                    d="M3 4a1 1 0 0 0-.822 1.57L6.632 12l-4.454 6.43A1 1 0 0 0 3 20h13.153a1 1 0 0 0 .822-.43l4.847-7a1 1 0 0 0 0-1.14l-4.847-7a1 1 0 0 0-.822-.43H3Z"
                                    clip-rule="evenodd"></path>
                            </svg></div>
                    </button>
                </div>
                 `: ``}
                `}
                <div class="md:hidden flex  justify-end w-full h-[1px]  sticky  bottom-40 z-50 ">
                    <button   @click="${() => setShowOffcanvas(true)}"
                        class="md:hidden inline-flex  items-center justify-center gap-2 whitespace-nowrap text-sm font-medium bg-[#18181b] transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:bg-primary/90 h-10 w-10  rounded-full p-3"
                        aria-label="Open filter">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white h-6 w-6">
                            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                        </svg>
                    </button>
                </div>
                
                </div>
                
                
                
            <div
                class="md:col-span-3 md:block hidden pb-6 bg-white rounded-lg shadow-lg h-[calc(100vh-5.5rem)] sticky top-11 overflow-y-auto">
                <div class="w-full">
                
                    <div>
                    ${InventoriesSelected.length > 0 ? html`
                        ${InventoriesSelected.map((itinerarySelected: any, index: number) => html`
                        <div class="w-full relative ">
                            <h1 class="bg-[#c9efff] text-gray-800 border-b border-gray-200 w-full px-4 py-2 ${index % 2 === 1 ? 'bg-[#fffbb3]' : ''}">
                                ${index % 2 === 0 ? (language === 'vi' ? "Chuyến đi" : "Outbound flight") : (language === 'vi' ? "Chuyến về" : "Return flight")}
                            </h1>
                            <div
                                class="flex flex-col justify-between items-center text-gray-800 py-2 border-r border-white ">
                                <div class="w-full flex justify-between items-center px-4 text-xs font-extrabold">
                                    <div>${inforAirports[itinerarySelected?.segment?.DepartureCode]?.cityName}</div>
                                    <div>${inforAirports[itinerarySelected?.segment?.ArrivalCode]?.cityName}</div>
                                </div>
                                <div class="flex justify-start items-center w-full px-4 gap-2 ">
                                    <div class="flex flex-col justify-start items-start">
                                        <strong class="text-base font-bold rounded-full bg-white text-nmt-600">${itinerarySelected?.segment?.DepartureCode}</strong>
                                        <strong class="text-xl font-bold ">${getTimeFromDateTime(itinerarySelected?.segment?.DepartureDate)}</strong>
                                    </div>
                                    <div class="w-full flex flex-col justify-center items-center">
                                        <div class="px-3  inline-flex justify-center items-center max-w-full text-nmt-600 bg-white box-border">
                                            <span class="relative  text-nmt-600 font-normal h-[18px] tracking-[0px] leading-[18px] overflow-hidden text-ellipsis whitespace-nowrap">
                                                <img src="${apiUrl}/assets/img/airlines/${itinerarySelected?.segment?.Airlines}.png"
                                                    class="h-full w-auto">
                                            </span>
                                        </div>
                                        <div class="w-full flex justify-center items-center">
                                            <div class="w-full h-[2px] rounded-full bg-nmt-600"></div>
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                class="w-5 h-5 fill-nmt-600 inline-block ml-[1px]"
                                                viewBox="0 0 576 512">
                                                <path
                                                    d="M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z" />
                                            </svg>
                                        </div>

                                    </div>
                                    <div class="flex flex-col justify-end items-end">
                                        <strong class="text-base font-bold rounded-full bg-white text-nmt-600">${itinerarySelected?.segment?.ArrivalCode}</strong>
                                        <strong class="text-xl font-bold ">${getTimeFromDateTime(itinerarySelected?.segment?.ArrivalDate)}</strong>
                                    </div>

                                </div>
                                <div class="w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#d2d3d3_50%)] bg-[length:10px_100%]">
                                </div>
                                <div class="flex justify-between items-center w-full px-4 mt-2 text-sm">
                                    <div class="${itinerarySelected?.segment?.Legs.length > 1 ? `flex flex-col` : ``}">
                                        <span>${language === 'vi' ? 'Ngày:' : 'Date:'}</span>
                                        <strong class="text-xs">${formatDateTo_ddMMyyyy(itinerarySelected?.segment?.DepartureDate, language)}</strong>
                                    </div>
                                    <div class="${itinerarySelected?.segment?.Legs.length > 1 ? `flex flex-col items-end justify-end` : ``}">
                                        <span>${language === 'vi' ? 'Chuyến:' : 'Flight:'}</span>
                                        <strong class="py-1 px-2 text-xs rounded-full bg-gray-200  text-right">
                                            ${getFlights(itinerarySelected?.segment?.Legs)}
                                        </strong>
                                    </div>
                                </div>

                            </div>
                        </div>
                        `)}
                    ` : html`
                    <div class="py-4 text-center text-gray-600">${language === 'vi' ? 'Chưa chọn chuyến bay' : 'No flight selected'}</div>`}
                        <div class="w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#fb7740_50%)] bg-[length:10px_100%]"></div>
                    </div>

                    <div class="w-full flex justify-between items-center px-4 mt-2 font-bold">
                        <div>
                            <span>
                                ${language === 'vi' ? 'Tổng giá:' : 'Total price:'}
                            </span>
                        </div>
                        <div>
                            <strong class="text-xl text-nmt-600">
                                ${formatNumber(_sumPrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>
                        </div>
                    </div>
                    ${checkBookingButton() ? html`
                        <div class="flex items-center justify-center w-full bg-white p-4  mt-4 sticky bottom-0 ">
                        <span class="relative group">
                            <button  @click="${() => nextPage()}"
                                class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                                <div> ${language === 'vi' ? 'Tiếp tục' : 'Continue'}</div>
                            </button>
                        </span>
                    </div>
                    `: ``}
                    
                </div>
                <hr class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">

                ${_airItinerarySelected < 1 ? html`
                <div class="px-4">
                    <h1 class="mb-2 text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                        ${language === 'vi' ? 'Sắp xếp chuyến bay' : 'Sort flights'}
                    </h1>
                    <!-- Debug: ${currentSortOption} -->
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input id="default-radio-1" type="radio" name="sort-radio-desktop" value="price"
                                 .checked=${currentSortOption === 'price'}
                                 @change=${() => sortWithPrice()}
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="default-radio-1"
                                class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer">${language === 'vi' ? 'Giá thấp' : 'Lowest price'}</label>
                        </div>
                        <div class="flex items-center">
                            <input id="default-radio-2" type="radio" name="sort-radio-desktop" value="airline"
                                .checked=${currentSortOption === 'airline'}
                                @change=${() => sortWithAirline()}
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="default-radio-2"
                                class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer">
                                ${language === 'vi' ? 'Hãng vận chuyển' : 'Airline'}</label>
                        </div>
                        <div class="flex items-center">
                            <input id="default-radio-3" type="radio" name="sort-radio-desktop" value="departure"
                                .checked=${currentSortOption === 'departure'}
                                @change=${() => sortWithDepartureDate()}
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="default-radio-3"
                                class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer">${language === 'vi' ? 'Thời gian khởi hành' : 'Departure time'}</label>
                        </div>
                    </div>

                    ${hideMultiSegmentFlights ? html`
                    <hr class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">
                    <div class="px-2">
                        <h1 class="mb-2 text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                            ${language === 'vi' ? 'Loại chuyến bay' : 'Flight Type'}
                        </h1>
                        <div class="flex items-center">
                            <input .checked=${showMultiSegmentFlights} id="multi-segment-checkbox" type="checkbox"
                                @change="${() => toggleMultiSegmentFlights()}"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="multi-segment-checkbox"
                                class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer">
                                ${language === 'vi' ? 'Hiển thị chuyến bay đa chặng' : 'Show multi-segment flights'}
                            </label>
                        </div>
                    </div>
                    ` : ''}
                </div>
                ` : ``}
                ${_airItinerarySelected < 1 ? html`
                    <hr
                    class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">
                <div class="px-4 mb-8">
                    <h1 class="mb-2 text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                        ${language === 'vi' ? 'Thời gian khởi hành' : 'Departure time'}
                    </h1>
                    <div class="w-full">
                        <range-slider min="0" max="24" step="1" .values="${rangeSlider}"
                        @values-changed="${(e: CustomEvent) => handleRangeSliderChange(e)}"></range-slider>
                    </div>
                </div>
                    ` : ``}
                    ${maxCountLeg > 1 && _airItinerarySelected < 1 && showMultiSegmentFlights ? html`
                    <hr
                    class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">
                <div class="px-4 mb-8">
                    <h1 class="mb-2 text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                        ${language === 'vi' ? 'Số điểm dừng' : 'Number of stops'}
                    </h1>

                     <div class="w-full">
                        <range-slider min="0" max="${maxCountLeg}" step="1" .values="${rangeSliderLegs}"  allowSameValue="true"
                        @values-changed="${(e: CustomEvent) => handleRangeSliderLeg(e)}"></range-slider>
                    </div>
                </div>
                    ` : ``}

                <hr
                    class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">
                    ${allAirlineResult.length > 0 ? html`
                    <div class="px-4 mb-8">
                    <h1 class="mb-2 text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                        ${language === 'vi' ? 'Hãng vận chuyển' : 'Airlines'}
                    </h1>
                    <div class="w-full space-y-1">
                        <div class="flex items-center">
                            <input type="checkbox"  @click="${(event: Event) => filterAirlines(event, 'all')}" id="all-airlines"
                             id="all-airlines"
                                .checked=${checkedAllAirlinesStatus()}
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="all-airlines"
                                class="w-full ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 flex gap-1 ">
                                <span class="line-clamp-1">${language === 'vi' ? 'Tất cả' : 'All'}</span>
                            </label>
                        </div>
                        ${allAirlineResult.map((airlines: any) => html`
                        <div class="flex items-center">
                            <input type="checkbox" .checked=${airlines.checked} id="${airlines.airlines}"
                                @click="${(event: Event) => filterAirlines(event, airlines.airlines)}"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer">
                            <label for="${airlines.airlines}"
                                class="w-full ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 flex gap-1  cursor-pointer">
                                <img src="${apiUrl}/assets/img/airlines/${airlines?.airlines}.png" class="h-4 w-auto">
                                <span class="line-clamp-1">${airlines.airlinesName}</span>
                            </label>
                        </div>
                        `)}
                    </div>
                </div>
                    `: ``}
                <hr
                    class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">

            </div>
            
        </div>
        
    </div>
    ${checkBookingButton() ? html`
            <div class="md:hidden flex items-center justify-between w-full bg-white p-4  mt-4 sticky  bottom-0 z-50 rounded-t-lg shadow-lg">
            <div @click="${() => showPriceDetail()}">
            <div>
                <strong class="text-xl text-nmt-600">
                    ${formatNumber(_sumPrice, _convertedVND, language)}</strong> <small>${_currencySymbol}</small>
            </div>
            ${showDetailTicket ? html`
            <div>
                <span [ngClass]="{'text-red-500': isShowPriceDetail}" class="text-sm underline text-nmt-500">
                ${_isShowPriceDetail ? (language === 'vi' ? 'Ẩn tiết giá' : 'Hide price details') : (language === 'vi' ? 'Xem chi tiết' : 'View details')}
                </span>
            </div>
            `: ``}
            
        </div>
        <span class="relative group">
            <button @click="${() => nextPage()}"
                class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br  from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                <div> ${language === 'vi' ? 'Tiếp tục' : 'Continue'}</div>
            </button>
        </span>
    </div>
    `: ``}
    <!-- start offcanvas -->
    <div class="offcanvas offcanvas-end ${showOffcanvas ? 'show' : ''}">
    <div class="offcanvas-header">
        <h4 class="offcanvas-title">Lọc kết quả</h4>
        <button type="button"  @click="${() => setShowOffcanvas(false)}"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
            data-modal-hide="default-modal">
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
            <span class="sr-only">Close modal</span>
        </button>
    </div>
    <div class="offcanvas-body">

        <div class="w-full">
            <div>
            ${InventoriesSelected.length > 0 ? html`
                        ${InventoriesSelected.map((itinerarySelected: any, index: number) => html`
                        <div class="w-full relative ">
                            <h1 class="bg-[#c9efff] text-gray-800 border-b border-gray-200 w-full px-4 py-2 ${index % 2 === 1 ? 'bg-[#fffbb3]' : ''}">
                                ${index % 2 === 0 ? (language === 'vi' ? "Chuyến đi" : "Outbound flight") : (language === 'vi' ? "Chuyến về" : "Return flight")}
                            </h1>
                            <div
                                class="flex flex-col justify-between items-center text-gray-800 py-2 border-r border-white ">
                                <div class="w-full flex justify-between items-center px-4 text-xs font-extrabold">
                                    <div>${inforAirports[itinerarySelected?.segment?.DepartureCode]?.cityName}</div>
                                    <div>${inforAirports[itinerarySelected?.segment?.ArrivalCode]?.cityName}</div>
                                </div>
                                <div class="flex justify-start items-center w-full px-4 gap-2 ">
                                    <div class="flex flex-col justify-start items-start">
                                        <strong class="text-base font-bold rounded-full bg-white text-nmt-600">${itinerarySelected?.segment?.DepartureCode}</strong>
                                        <strong class="text-xl font-bold ">${getTimeFromDateTime(itinerarySelected?.segment?.DepartureDate)}</strong>
                                    </div>
                                    <div class="w-full flex flex-col justify-center items-center">
                                        <div class="px-3  inline-flex justify-center items-center max-w-full text-nmt-600 bg-white box-border">
                                            <span class="relative  text-nmt-600 font-normal h-[18px] tracking-[0px] leading-[18px] overflow-hidden text-ellipsis whitespace-nowrap">
                                                <img src="${apiUrl}/assets/img/airlines/${itinerarySelected?.segment?.Airlines}.png"
                                                    class="h-full w-auto">
                                            </span>
                                        </div>
                                        <div class="w-full flex justify-center items-center">
                                            <div class="w-full h-[2px] rounded-full bg-nmt-600"></div>
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                class="w-5 h-5 fill-nmt-600 inline-block ml-[1px]"
                                                viewBox="0 0 576 512">
                                                <path
                                                    d="M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z" />
                                            </svg>
                                        </div>

                                    </div>
                                    <div class="flex flex-col justify-end items-end">
                                        <strong class="text-base font-bold rounded-full bg-white text-nmt-600">${itinerarySelected?.segment?.ArrivalCode}</strong>
                                        <strong class="text-xl font-bold ">${getTimeFromDateTime(itinerarySelected?.segment?.ArrivalDate)}</strong>
                                    </div>

                                </div>
                                <div class="w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#d2d3d3_50%)] bg-[length:10px_100%]">
                                </div>
                                <div class="flex justify-between items-center w-full px-4 mt-2 text-sm">
                                    <div class="${itinerarySelected?.segment?.Legs.length > 1 ? `flex flex-col` : ``}">
                                        <span>${language === 'vi' ? 'Ngày:' : 'Date:'}</span>
                                        <strong class="text-xs">${formatDateTo_ddMMyyyy(itinerarySelected?.segment?.DepartureDate, language)}</strong>
                                    </div>
                                    <div class="${itinerarySelected?.segment?.Legs.length > 1 ? `flex flex-col items-end justify-end` : ``}">
                                        <span>${language === 'vi' ? 'Chuyến:' : 'Flight:'}</span>
                                        <strong class="py-1 px-2 text-xs rounded-full bg-gray-200  text-right">
                                            ${getFlights(itinerarySelected?.segment?.Legs)}
                                        </strong>
                                    </div>
                                </div>

                            </div>
                        </div>
                        `)}
                    ` : html`
                    <div class="py-4 text-center text-gray-600">${language === 'vi' ? 'Chưa chọn chuyến bay' : 'No flight selected'}</div>`}
                        <div class="w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#fb7740_50%)] bg-[length:10px_100%]"></div>
                    </div>

                    <div class="w-full flex justify-between items-center px-4 mt-2 font-bold">
                        <div>
                            <span>
                                ${language === 'vi' ? 'Tổng giá:' : 'Total price:'}
                            </span>
                        </div>
                        <div>
                            <strong class="text-xl text-nmt-600">
                                ${formatNumber(_sumPrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>
                        </div>
                    </div>
                    ${checkBookingButton() ? html`
                        <div class="flex items-center justify-center w-full bg-white p-4  mt-4 sticky bottom-0 ">
                        <span class="relative group">
                            <button  @click="${() => nextPage()}"
                                class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                                <div> ${language === 'vi' ? 'Tiếp tục' : 'Continue'}</div>
                            </button>
                        </span>
                    </div>
                    `: ``}
        </div>

        <hr class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">

        ${_airItinerarySelected < 1 ? html`
                <div class="px-4">
                    <h1 class="mb-2 text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                        ${language === 'vi' ? 'Sắp xếp chuyến bay' : 'Sort flights'}
                    </h1>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input .checked=${currentSortOption === 'price'} id="default-radio-price_4" type="radio" name="sort-radio-mobile" value="price"
                                 @change="${() => sortWithPrice()}"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="default-radio-price_4"
                                class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer">${language === 'vi' ? 'Giá thấp' : 'Lowest price'}</label>
                        </div>
                        <div class="flex items-center">
                            <input .checked=${currentSortOption === 'airline'} id="default-radio-5" type="radio" name="sort-radio-mobile" value="airline" @change="${() => sortWithAirline()}"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="default-radio-5"
                                class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer">
                                ${language === 'vi' ? 'Hãng vận chuyển' : 'Airline'}</label>
                        </div>
                        <div class="flex items-center">
                            <input .checked=${currentSortOption === 'departure'} id="default-radio-6" type="radio" name="sort-radio-mobile" value="departure"
                                @change="${() => sortWithDepartureDate()}"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="default-radio-6"
                                class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer">${language === 'vi' ? 'Thời gian khởi hành' : 'Departure time'}</label>
                        </div>
                    </div>

                    ${hideMultiSegmentFlights ? html`
                    <hr class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">
                    <div class="px-2">
                        <h1 class="mb-2 text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                            ${language === 'vi' ? 'Loại chuyến bay' : 'Flight Type'}
                        </h1>
                        <div class="flex items-center">
                            <input .checked=${showMultiSegmentFlights} id="multi-segment-checkbox-mobile" type="checkbox"
                                @change="${() => toggleMultiSegmentFlights()}"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="multi-segment-checkbox-mobile"
                                class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer">
                                ${language === 'vi' ? 'Hiển thị chuyến bay đa chặng' : 'Show multi-segment flights'}
                            </label>
                        </div>
                    </div>
                    ` : ''}
                </div>
                ` : ``}
                ${_airItinerarySelected < 1 ? html`
                    <hr
                    class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">
                <div class="px-4 mb-8">
                    <h1 class="mb-2 text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                        ${language === 'vi' ? 'Thời gian khởi hành' : 'Departure time'}
                    </h1>
                    <div class="w-full">
                        <range-slider min="0" max="24" step="1" .values="${rangeSlider}"
                        @values-changed="${(e: CustomEvent) => handleRangeSliderChange(e)}"></range-slider>
                    </div>
                </div>
                    ` : ``}
                    ${maxCountLeg > 1 && _airItinerarySelected < 1 && showMultiSegmentFlights ? html`
                    <hr
                    class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">
                <div class="px-4 mb-8">
                    <h1 class="mb-2 text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                        ${language === 'vi' ? 'Số điểm dừng' : 'Number of stops'}
                    </h1>

                     <div class="w-full">
                        <range-slider min="0" max="${maxCountLeg}" step="1" .values="${rangeSliderLegs}"  allowSameValue="true"
                        @values-changed="${(e: CustomEvent) => handleRangeSliderLeg(e)}"></range-slider>
                    </div>
                </div>
                    ` : ``}

                <hr
                    class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">
                    ${allAirlineResult.length > 0 ? html`
                    <div class="px-4 mb-8">
                    <h1 class="mb-2 text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                        ${language === 'vi' ? 'Hãng vận chuyển' : 'Airlines'}
                    </h1>
                    <div class="w-full space-y-1">
                        <div class="flex items-center">
                            <input type="checkbox"  @click="${(event: Event) => filterAirlines(event, 'all')}" id="all-airlines"
                             id="all-airlines"
                                .checked=${checkedAllAirlinesStatus()}
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="all-airlines"
                                class="w-full ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 flex gap-1 ">
                                <span class="line-clamp-1">${language === 'vi' ? 'Tất cả' : 'All'}</span>
                            </label>
                        </div>
                        ${allAirlineResult.map((airlines: any) => html`
                        <div class="flex items-center">
                            <input type="checkbox" .checked=${airlines.checked} id="${airlines.airlines}"
                                @click="${(event: Event) => filterAirlines(event, airlines.airlines)}"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer">
                            <label for="${airlines.airlines}"
                                class="w-full ms-2 text-sm font-medium text-gray-900 dark:text-gray-300 flex gap-1  cursor-pointer">
                                <img src="${apiUrl}/assets/img/airlines/${airlines?.airlines}.png" class="h-4 w-auto">
                                <span class="line-clamp-1">${airlines.airlinesName}</span>
                            </label>
                        </div>
                        `)}
                    </div>
                </div>
                    `: ``}
                <hr
                    class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">
    </div>
    </div>
                <!-- end offcanvas -->
</div>
${isopenModalResult ? html`
<div
    class="fixed z-50 w-fit h-fit left-0 right-0 top-5 mx-auto flex items-center justify-center">
    <div
        class="relative bg-white rounded-lg shadow-sm shadow-green-400 dark:bg-gray-700 p-2 flex items-center justify-center gap-3">
        <!-- Icon -->
        <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path>
        </svg>

        <!-- Nội dung -->
        <div>
            <p class="text-green-600 text-sm">${language === 'vi' ? 'Tất cả kết quả tìm kiếm đã được hiển thị.' : 'All search results have been displayed.'} </p>
        </div>
    </div>
</div>
`: ''}

<modal-notification uri_searchBox="${uri_searchBox}"></modal-notification>
    `;
}