import { css, LitElement, PropertyValues, unsafeCSS } from "lit";
import { modalTemplate } from "./modal-template";
import { customElement, property, state } from "lit/decorators.js";
import styles from '../../styles/styles.css';

@customElement("modal-notification")
export class Modal extends LitElement {
    static styles = [
        unsafeCSS(styles),
        css`
        :host {
        font-family: var(--nmt-font, 'Roboto', sans-serif);
        }`
    ];

    @property({ type: String }) uri_searchBox = "";

    @state() private isOpen: boolean = false;
    @state() private _title: string = "";
    @state() private content: string = "";
    @state() private isCountDown: boolean = false;
    @state() private countdown: number = 0;

    constructor() {
        super();
    }

    static get properties() {
        return {
            isOpen: { type: Boolean },
            _title: { type: String },
            content: { type: String },
            isCountDown: { type: Boolean },
            countdown: { type: Number },
        };
    }

    protected firstUpdated(_changedProperties: PropertyValues): void {
        super.firstUpdated(_changedProperties);
        console.log('isOpen', this.isOpen);
        console.log('title', this._title);
        console.log('content', this.content);
        console.log('isCountDown', this.isCountDown);
        console.log('countdown', this.countdown);
        if (this.isCountDown) {
            this.startCountdown();
        }
    }

    protected update(changedProperties: PropertyValues): void {
        super.update(changedProperties);
        if (changedProperties.has("isOpen") && this.isOpen) {
            this._title = this._title || "Thông báo";
            this.content = this.content || "Nội dung thông báo";
            this.isCountDown = this.isCountDown || false;
            this.countdown = this.countdown || 0;
        }
    }

    startCountdown() {
        if (this.countdown > 0) {
            setTimeout(() => {
                this.countdown--;
                this.startCountdown();
            }, 1000);
        } else {
            this.isCountDown = false;
            this.reSearch();
        }
    }
    start({
        title = "Thông báo",
        content = "Nội dung thông báo",
        isCountDown = false,
        countdown = 0,
    } = {}) {
        this._title = title;
        this.content = content;
        this.isCountDown = isCountDown;
        this.countdown = countdown;
        this.isOpen = true;

        if (this.isCountDown) {
            this.startCountdown();
        }
    }

    // Phương thức để ẩn modal
    close() {
        this.isOpen = false;
    }

    reSearch() {
        window.location.href = `/${this.uri_searchBox}`;
    }

    render() {
        return modalTemplate(
            this.isOpen,
            this._title || "Thông báo",
            this.content,
            this.isCountDown || false,
            this.countdown || 0,
            this.close.bind(this),
            this.reSearch.bind(this)
        );
    }

}

