import FingerprintJS from '@fingerprintjs/fingerprintjs';

export async function getDeviceId(): Promise<string> {
    const fp = await FingerprintJS.load();
    const result = await fp.get();
    return result.visitorId; // Unique Device ID
}

export async function fetchWithDeviceId(input: RequestInfo, init?: RequestInit): Promise<Response> {
    const deviceId = await getDeviceId();

    // Convert Headers instance to a plain object if needed
    let headers: Headers;
    if (init?.headers instanceof Headers) {
        headers = new Headers();
        init.headers.forEach((value, key) => {
            headers.set(key, value);
        });
    } else {
        headers = new Headers(init?.headers || {});
    }

    // Set the custom header
    headers.set("X-Device-Id", deviceId);

    const modifiedInit: RequestInit = {
        ...init,
        headers,
        credentials: "include", // Ensure credentials are sent if needed
    };

    return fetch(input, modifiedInit);
}


export async function fetchWithDeviceIdandApiKey(input: RequestInfo, init: RequestInit = {}, apiKey: string): Promise<Response> {
    const deviceId = await getDeviceId(); // Lấy deviceId

    // Khởi tạo headers và đảm bảo giữ nguyên headers cũ nếu có
    const headers = new Headers(init.headers);
    headers.set("X-Device-Id", deviceId);
    headers.set("X-Api-Key", apiKey); // Gửi API key trong request

    // Tạo request mới với headers cập nhật
    const modifiedInit: RequestInit = {
        ...init,
        headers,
        credentials: "include", // Giữ cookies nếu cần
    };

    try {
        const response = await fetch(input, modifiedInit);

        return response;
    } catch (error) {
        console.error('Fetch error:', error);
        throw error;
    }
}


