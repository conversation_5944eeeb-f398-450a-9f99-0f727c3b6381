{"version": 3, "file": "index.js", "sources": ["../../node_modules/tslib/tslib.es6.js", "../../node_modules/@lit/reactive-element/css-tag.js", "../../node_modules/@lit/reactive-element/reactive-element.js", "../../node_modules/lit-html/lit-html.js", "../../node_modules/lit-element/lit-element.js", "../../src/environments/environment.ts", "../../node_modules/@fingerprintjs/fingerprintjs/dist/fp.esm.js", "../../src/utils/deviceUtils.ts", "../../src/services/CryptoService.ts", "../../src/services/FlightService.ts", "../../node_modules/@lit/reactive-element/decorators/custom-element.js", "../../node_modules/@lit/reactive-element/decorators/property.js", "../../node_modules/@lit/reactive-element/decorators/state.js", "../../src/services/WorldServices.ts", "../../src/utils/dateUtils.ts", "../../src/components/modal/modal-template.ts", "../../src/components/modal/modal.ts", "../../src/components/trip-repayment/trip-repayment-template.ts", "../../src/interface/DefaultColors.ts", "../../src/services/ColorService.ts", "../../src/share/data/bank-logos.ts", "../../src/components/trip-repayment/trip-repayment.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\r\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\r\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nvar ownKeys = function(o) {\r\n    ownKeys = Object.getOwnPropertyNames || function (o) {\r\n        var ar = [];\r\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\r\n        return ar;\r\n    };\r\n    return ownKeys(o);\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose, inner;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n            if (async) inner = dispose;\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    var r, s = 0;\r\n    function next() {\r\n        while (r = env.stack.pop()) {\r\n            try {\r\n                if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\r\n                if (r.dispose) {\r\n                    var result = r.dispose.call(r.value);\r\n                    if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n                }\r\n                else s |= 1;\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\r\n    if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\r\n        return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\r\n            return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\r\n        });\r\n    }\r\n    return path;\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __esDecorate: __esDecorate,\r\n    __runInitializers: __runInitializers,\r\n    __propKey: __propKey,\r\n    __setFunctionName: __setFunctionName,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n    __rewriteRelativeImportExtension: __rewriteRelativeImportExtension,\r\n};\r\n", "/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t=globalThis,e=t.ShadowRoot&&(void 0===t.ShadyCSS||t.ShadyCSS.nativeShadow)&&\"adoptedStyleSheets\"in Document.prototype&&\"replace\"in CSSStyleSheet.prototype,s=Symbol(),o=new WeakMap;class n{constructor(t,e,o){if(this._$cssResult$=!0,o!==s)throw Error(\"CSSResult is not constructable. Use `unsafeCSS` or `css` instead.\");this.cssText=t,this.t=e}get styleSheet(){let t=this.o;const s=this.t;if(e&&void 0===t){const e=void 0!==s&&1===s.length;e&&(t=o.get(s)),void 0===t&&((this.o=t=new CSSStyleSheet).replaceSync(this.cssText),e&&o.set(s,t))}return t}toString(){return this.cssText}}const r=t=>new n(\"string\"==typeof t?t:t+\"\",void 0,s),i=(t,...e)=>{const o=1===t.length?t[0]:e.reduce(((e,s,o)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if(\"number\"==typeof t)return t;throw Error(\"Value passed to 'css' function must be a 'css' function result: \"+t+\". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.\")})(s)+t[o+1]),t[0]);return new n(o,t,s)},S=(s,o)=>{if(e)s.adoptedStyleSheets=o.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet));else for(const e of o){const o=document.createElement(\"style\"),n=t.litNonce;void 0!==n&&o.setAttribute(\"nonce\",n),o.textContent=e.cssText,s.appendChild(o)}},c=e?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e=\"\";for(const s of t.cssRules)e+=s.cssText;return r(e)})(t):t;export{n as CSSResult,S as adoptStyles,i as css,c as getCompatibleStyle,e as supportsAdoptingStyleSheets,r as unsafeCSS};\n//# sourceMappingURL=css-tag.js.map\n", "import{getCompatibleStyle as t,adoptStyles as s}from\"./css-tag.js\";export{CSSResult,adoptStyles,css,getCompatibleStyle,supportsAdoptingStyleSheets,unsafeCSS}from\"./css-tag.js\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */const{is:i,defineProperty:e,getOwnPropertyDescriptor:r,getOwnPropertyNames:h,getOwnPropertySymbols:o,getPrototypeOf:n}=Object,a=globalThis,c=a.trustedTypes,l=c?c.emptyScript:\"\",p=a.reactiveElementPolyfillSupport,d=(t,s)=>t,u={toAttribute(t,s){switch(s){case Boolean:t=t?l:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,s){let i=t;switch(s){case Boolean:i=null!==t;break;case Number:i=null===t?null:Number(t);break;case Object:case Array:try{i=JSON.parse(t)}catch(t){i=null}}return i}},f=(t,s)=>!i(t,s),y={attribute:!0,type:String,converter:u,reflect:!1,hasChanged:f};Symbol.metadata??=Symbol(\"metadata\"),a.litPropertyMetadata??=new WeakMap;class b extends HTMLElement{static addInitializer(t){this._$Ei(),(this.l??=[]).push(t)}static get observedAttributes(){return this.finalize(),this._$Eh&&[...this._$Eh.keys()]}static createProperty(t,s=y){if(s.state&&(s.attribute=!1),this._$Ei(),this.elementProperties.set(t,s),!s.noAccessor){const i=Symbol(),r=this.getPropertyDescriptor(t,i,s);void 0!==r&&e(this.prototype,t,r)}}static getPropertyDescriptor(t,s,i){const{get:e,set:h}=r(this.prototype,t)??{get(){return this[s]},set(t){this[s]=t}};return{get(){return e?.call(this)},set(s){const r=e?.call(this);h.call(this,s),this.requestUpdate(t,r,i)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)??y}static _$Ei(){if(this.hasOwnProperty(d(\"elementProperties\")))return;const t=n(this);t.finalize(),void 0!==t.l&&(this.l=[...t.l]),this.elementProperties=new Map(t.elementProperties)}static finalize(){if(this.hasOwnProperty(d(\"finalized\")))return;if(this.finalized=!0,this._$Ei(),this.hasOwnProperty(d(\"properties\"))){const t=this.properties,s=[...h(t),...o(t)];for(const i of s)this.createProperty(i,t[i])}const t=this[Symbol.metadata];if(null!==t){const s=litPropertyMetadata.get(t);if(void 0!==s)for(const[t,i]of s)this.elementProperties.set(t,i)}this._$Eh=new Map;for(const[t,s]of this.elementProperties){const i=this._$Eu(t,s);void 0!==i&&this._$Eh.set(i,t)}this.elementStyles=this.finalizeStyles(this.styles)}static finalizeStyles(s){const i=[];if(Array.isArray(s)){const e=new Set(s.flat(1/0).reverse());for(const s of e)i.unshift(t(s))}else void 0!==s&&i.push(t(s));return i}static _$Eu(t,s){const i=s.attribute;return!1===i?void 0:\"string\"==typeof i?i:\"string\"==typeof t?t.toLowerCase():void 0}constructor(){super(),this._$Ep=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Em=null,this._$Ev()}_$Ev(){this._$ES=new Promise((t=>this.enableUpdating=t)),this._$AL=new Map,this._$E_(),this.requestUpdate(),this.constructor.l?.forEach((t=>t(this)))}addController(t){(this._$EO??=new Set).add(t),void 0!==this.renderRoot&&this.isConnected&&t.hostConnected?.()}removeController(t){this._$EO?.delete(t)}_$E_(){const t=new Map,s=this.constructor.elementProperties;for(const i of s.keys())this.hasOwnProperty(i)&&(t.set(i,this[i]),delete this[i]);t.size>0&&(this._$Ep=t)}createRenderRoot(){const t=this.shadowRoot??this.attachShadow(this.constructor.shadowRootOptions);return s(t,this.constructor.elementStyles),t}connectedCallback(){this.renderRoot??=this.createRenderRoot(),this.enableUpdating(!0),this._$EO?.forEach((t=>t.hostConnected?.()))}enableUpdating(t){}disconnectedCallback(){this._$EO?.forEach((t=>t.hostDisconnected?.()))}attributeChangedCallback(t,s,i){this._$AK(t,i)}_$EC(t,s){const i=this.constructor.elementProperties.get(t),e=this.constructor._$Eu(t,i);if(void 0!==e&&!0===i.reflect){const r=(void 0!==i.converter?.toAttribute?i.converter:u).toAttribute(s,i.type);this._$Em=t,null==r?this.removeAttribute(e):this.setAttribute(e,r),this._$Em=null}}_$AK(t,s){const i=this.constructor,e=i._$Eh.get(t);if(void 0!==e&&this._$Em!==e){const t=i.getPropertyOptions(e),r=\"function\"==typeof t.converter?{fromAttribute:t.converter}:void 0!==t.converter?.fromAttribute?t.converter:u;this._$Em=e,this[e]=r.fromAttribute(s,t.type),this._$Em=null}}requestUpdate(t,s,i){if(void 0!==t){if(i??=this.constructor.getPropertyOptions(t),!(i.hasChanged??f)(this[t],s))return;this.P(t,s,i)}!1===this.isUpdatePending&&(this._$ES=this._$ET())}P(t,s,i){this._$AL.has(t)||this._$AL.set(t,s),!0===i.reflect&&this._$Em!==t&&(this._$Ej??=new Set).add(t)}async _$ET(){this.isUpdatePending=!0;try{await this._$ES}catch(t){Promise.reject(t)}const t=this.scheduleUpdate();return null!=t&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){if(!this.isUpdatePending)return;if(!this.hasUpdated){if(this.renderRoot??=this.createRenderRoot(),this._$Ep){for(const[t,s]of this._$Ep)this[t]=s;this._$Ep=void 0}const t=this.constructor.elementProperties;if(t.size>0)for(const[s,i]of t)!0!==i.wrapped||this._$AL.has(s)||void 0===this[s]||this.P(s,this[s],i)}let t=!1;const s=this._$AL;try{t=this.shouldUpdate(s),t?(this.willUpdate(s),this._$EO?.forEach((t=>t.hostUpdate?.())),this.update(s)):this._$EU()}catch(s){throw t=!1,this._$EU(),s}t&&this._$AE(s)}willUpdate(t){}_$AE(t){this._$EO?.forEach((t=>t.hostUpdated?.())),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$EU(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$ES}shouldUpdate(t){return!0}update(t){this._$Ej&&=this._$Ej.forEach((t=>this._$EC(t,this[t]))),this._$EU()}updated(t){}firstUpdated(t){}}b.elementStyles=[],b.shadowRootOptions={mode:\"open\"},b[d(\"elementProperties\")]=new Map,b[d(\"finalized\")]=new Map,p?.({ReactiveElement:b}),(a.reactiveElementVersions??=[]).push(\"2.0.4\");export{b as ReactiveElement,u as defaultConverter,f as notEqual};\n//# sourceMappingURL=reactive-element.js.map\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t=globalThis,i=t.trustedTypes,s=i?i.createPolicy(\"lit-html\",{createHTML:t=>t}):void 0,e=\"$lit$\",h=`lit$${Math.random().toFixed(9).slice(2)}$`,o=\"?\"+h,n=`<${o}>`,r=document,l=()=>r.createComment(\"\"),c=t=>null===t||\"object\"!=typeof t&&\"function\"!=typeof t,a=Array.isArray,u=t=>a(t)||\"function\"==typeof t?.[Symbol.iterator],d=\"[ \\t\\n\\f\\r]\",f=/<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g,v=/-->/g,_=/>/g,m=RegExp(`>|${d}(?:([^\\\\s\"'>=/]+)(${d}*=${d}*(?:[^ \\t\\n\\f\\r\"'\\`<>=]|(\"|')|))|$)`,\"g\"),p=/'/g,g=/\"/g,$=/^(?:script|style|textarea|title)$/i,y=t=>(i,...s)=>({_$litType$:t,strings:i,values:s}),x=y(1),b=y(2),w=y(3),T=Symbol.for(\"lit-noChange\"),E=Symbol.for(\"lit-nothing\"),A=new WeakMap,C=r.createTreeWalker(r,129);function P(t,i){if(!a(t)||!t.hasOwnProperty(\"raw\"))throw Error(\"invalid template strings array\");return void 0!==s?s.createHTML(i):i}const V=(t,i)=>{const s=t.length-1,o=[];let r,l=2===i?\"<svg>\":3===i?\"<math>\":\"\",c=f;for(let i=0;i<s;i++){const s=t[i];let a,u,d=-1,y=0;for(;y<s.length&&(c.lastIndex=y,u=c.exec(s),null!==u);)y=c.lastIndex,c===f?\"!--\"===u[1]?c=v:void 0!==u[1]?c=_:void 0!==u[2]?($.test(u[2])&&(r=RegExp(\"</\"+u[2],\"g\")),c=m):void 0!==u[3]&&(c=m):c===m?\">\"===u[0]?(c=r??f,d=-1):void 0===u[1]?d=-2:(d=c.lastIndex-u[2].length,a=u[1],c=void 0===u[3]?m:'\"'===u[3]?g:p):c===g||c===p?c=m:c===v||c===_?c=f:(c=m,r=void 0);const x=c===m&&t[i+1].startsWith(\"/>\")?\" \":\"\";l+=c===f?s+n:d>=0?(o.push(a),s.slice(0,d)+e+s.slice(d)+h+x):s+h+(-2===d?i:x)}return[P(t,l+(t[s]||\"<?>\")+(2===i?\"</svg>\":3===i?\"</math>\":\"\")),o]};class N{constructor({strings:t,_$litType$:s},n){let r;this.parts=[];let c=0,a=0;const u=t.length-1,d=this.parts,[f,v]=V(t,s);if(this.el=N.createElement(f,n),C.currentNode=this.el.content,2===s||3===s){const t=this.el.content.firstChild;t.replaceWith(...t.childNodes)}for(;null!==(r=C.nextNode())&&d.length<u;){if(1===r.nodeType){if(r.hasAttributes())for(const t of r.getAttributeNames())if(t.endsWith(e)){const i=v[a++],s=r.getAttribute(t).split(h),e=/([.?@])?(.*)/.exec(i);d.push({type:1,index:c,name:e[2],strings:s,ctor:\".\"===e[1]?H:\"?\"===e[1]?I:\"@\"===e[1]?L:k}),r.removeAttribute(t)}else t.startsWith(h)&&(d.push({type:6,index:c}),r.removeAttribute(t));if($.test(r.tagName)){const t=r.textContent.split(h),s=t.length-1;if(s>0){r.textContent=i?i.emptyScript:\"\";for(let i=0;i<s;i++)r.append(t[i],l()),C.nextNode(),d.push({type:2,index:++c});r.append(t[s],l())}}}else if(8===r.nodeType)if(r.data===o)d.push({type:2,index:c});else{let t=-1;for(;-1!==(t=r.data.indexOf(h,t+1));)d.push({type:7,index:c}),t+=h.length-1}c++}}static createElement(t,i){const s=r.createElement(\"template\");return s.innerHTML=t,s}}function S(t,i,s=t,e){if(i===T)return i;let h=void 0!==e?s._$Co?.[e]:s._$Cl;const o=c(i)?void 0:i._$litDirective$;return h?.constructor!==o&&(h?._$AO?.(!1),void 0===o?h=void 0:(h=new o(t),h._$AT(t,s,e)),void 0!==e?(s._$Co??=[])[e]=h:s._$Cl=h),void 0!==h&&(i=S(t,h._$AS(t,i.values),h,e)),i}class M{constructor(t,i){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=i}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(t){const{el:{content:i},parts:s}=this._$AD,e=(t?.creationScope??r).importNode(i,!0);C.currentNode=e;let h=C.nextNode(),o=0,n=0,l=s[0];for(;void 0!==l;){if(o===l.index){let i;2===l.type?i=new R(h,h.nextSibling,this,t):1===l.type?i=new l.ctor(h,l.name,l.strings,this,t):6===l.type&&(i=new z(h,this,t)),this._$AV.push(i),l=s[++n]}o!==l?.index&&(h=C.nextNode(),o++)}return C.currentNode=r,e}p(t){let i=0;for(const s of this._$AV)void 0!==s&&(void 0!==s.strings?(s._$AI(t,s,i),i+=s.strings.length-2):s._$AI(t[i])),i++}}class R{get _$AU(){return this._$AM?._$AU??this._$Cv}constructor(t,i,s,e){this.type=2,this._$AH=E,this._$AN=void 0,this._$AA=t,this._$AB=i,this._$AM=s,this.options=e,this._$Cv=e?.isConnected??!0}get parentNode(){let t=this._$AA.parentNode;const i=this._$AM;return void 0!==i&&11===t?.nodeType&&(t=i.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,i=this){t=S(this,t,i),c(t)?t===E||null==t||\"\"===t?(this._$AH!==E&&this._$AR(),this._$AH=E):t!==this._$AH&&t!==T&&this._(t):void 0!==t._$litType$?this.$(t):void 0!==t.nodeType?this.T(t):u(t)?this.k(t):this._(t)}O(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}T(t){this._$AH!==t&&(this._$AR(),this._$AH=this.O(t))}_(t){this._$AH!==E&&c(this._$AH)?this._$AA.nextSibling.data=t:this.T(r.createTextNode(t)),this._$AH=t}$(t){const{values:i,_$litType$:s}=t,e=\"number\"==typeof s?this._$AC(t):(void 0===s.el&&(s.el=N.createElement(P(s.h,s.h[0]),this.options)),s);if(this._$AH?._$AD===e)this._$AH.p(i);else{const t=new M(e,this),s=t.u(this.options);t.p(i),this.T(s),this._$AH=t}}_$AC(t){let i=A.get(t.strings);return void 0===i&&A.set(t.strings,i=new N(t)),i}k(t){a(this._$AH)||(this._$AH=[],this._$AR());const i=this._$AH;let s,e=0;for(const h of t)e===i.length?i.push(s=new R(this.O(l()),this.O(l()),this,this.options)):s=i[e],s._$AI(h),e++;e<i.length&&(this._$AR(s&&s._$AB.nextSibling,e),i.length=e)}_$AR(t=this._$AA.nextSibling,i){for(this._$AP?.(!1,!0,i);t&&t!==this._$AB;){const i=t.nextSibling;t.remove(),t=i}}setConnected(t){void 0===this._$AM&&(this._$Cv=t,this._$AP?.(t))}}class k{get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}constructor(t,i,s,e,h){this.type=1,this._$AH=E,this._$AN=void 0,this.element=t,this.name=i,this._$AM=e,this.options=h,s.length>2||\"\"!==s[0]||\"\"!==s[1]?(this._$AH=Array(s.length-1).fill(new String),this.strings=s):this._$AH=E}_$AI(t,i=this,s,e){const h=this.strings;let o=!1;if(void 0===h)t=S(this,t,i,0),o=!c(t)||t!==this._$AH&&t!==T,o&&(this._$AH=t);else{const e=t;let n,r;for(t=h[0],n=0;n<h.length-1;n++)r=S(this,e[s+n],i,n),r===T&&(r=this._$AH[n]),o||=!c(r)||r!==this._$AH[n],r===E?t=E:t!==E&&(t+=(r??\"\")+h[n+1]),this._$AH[n]=r}o&&!e&&this.j(t)}j(t){t===E?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,t??\"\")}}class H extends k{constructor(){super(...arguments),this.type=3}j(t){this.element[this.name]=t===E?void 0:t}}class I extends k{constructor(){super(...arguments),this.type=4}j(t){this.element.toggleAttribute(this.name,!!t&&t!==E)}}class L extends k{constructor(t,i,s,e,h){super(t,i,s,e,h),this.type=5}_$AI(t,i=this){if((t=S(this,t,i,0)??E)===T)return;const s=this._$AH,e=t===E&&s!==E||t.capture!==s.capture||t.once!==s.once||t.passive!==s.passive,h=t!==E&&(s===E||e);e&&this.element.removeEventListener(this.name,this,s),h&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){\"function\"==typeof this._$AH?this._$AH.call(this.options?.host??this.element,t):this._$AH.handleEvent(t)}}class z{constructor(t,i,s){this.element=t,this.type=6,this._$AN=void 0,this._$AM=i,this.options=s}get _$AU(){return this._$AM._$AU}_$AI(t){S(this,t)}}const Z={M:e,P:h,A:o,C:1,L:V,R:M,D:u,V:S,I:R,H:k,N:I,U:L,B:H,F:z},j=t.litHtmlPolyfillSupport;j?.(N,R),(t.litHtmlVersions??=[]).push(\"3.2.1\");const B=(t,i,s)=>{const e=s?.renderBefore??i;let h=e._$litPart$;if(void 0===h){const t=s?.renderBefore??null;e._$litPart$=h=new R(i.insertBefore(l(),t),t,void 0,s??{})}return h._$AI(t),h};export{Z as _$LH,x as html,w as mathml,T as noChange,E as nothing,B as render,b as svg};\n//# sourceMappingURL=lit-html.js.map\n", "import{ReactiveElement as t}from\"@lit/reactive-element\";export*from\"@lit/reactive-element\";import{render as e,noChange as s}from\"lit-html\";export*from\"lit-html\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */class r extends t{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){const t=super.createRenderRoot();return this.renderOptions.renderBefore??=t.firstChild,t}update(t){const s=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Do=e(s,this.renderRoot,this.renderOptions)}connectedCallback(){super.connectedCallback(),this._$Do?.setConnected(!0)}disconnectedCallback(){super.disconnectedCallback(),this._$Do?.setConnected(!1)}render(){return s}}r._$litElement$=!0,r[\"finalized\"]=!0,globalThis.litElementHydrateSupport?.({LitElement:r});const i=globalThis.litElementPolyfillSupport;i?.({LitElement:r});const o={_$AK:(t,e,s)=>{t._$AK(e,s)},_$AL:t=>t._$AL};(globalThis.litElementVersions??=[]).push(\"4.1.1\");export{r as LitElement,o as _$LE};\n//# sourceMappingURL=lit-element.js.map\n", "export const environment = {\n    production: true,\n    // apiUrl: 'https://api.ngocmaitravel.vn',\n    // apiUrl: 'https://************',\n    // apiUrl: 'https://test01.ngocmaitravel.vn',\n    apiUrl: 'https://abi-ota.nmbooking.vn',\n    // apiUrl: 'https://localhost:7065',\n    publicKey: \"LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0NCk1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBeC9DT1ZhTWpQMGt5c0RiRlhkZkhyLzhQOEg5bEsveVoNCis2NERvZWdTSERSeENqb0NQMGxMZEtqb2VMa1hWaWE4UTBzQ3dzU1VIeHhIc3lkeWVhY0tUN0hHWG1WbFBaQXlJbHh1RjRPc0ZvWkoNCmEzQUVrSTdPMEpBeFFrcmFENzdURGJoeHBzSHdKUmtnWEI4aVR5NzQwOS83NjQ2VForcGdGTzMvSVlVT0M2SEplMEIvdkVadzkzeTkNCitodGlWa0ZtZEorQ2JkeGZyaEZrWHU5ZGRWeWptTENRODFIOTRJV2xOdm5PdlFHTkNpNWNHenpIZTVGTldpVy9PRGZrNkRKN0tJUUINCmlieUovVVoxdnJlKzExTytPWGZ0bkwwcHFybThvTnE5dURneWZQUW1US1Rxb0pDaGl6ZEFFTVlyNDVsQ3UybUJJNmVtVFhJWWVJYlANCm9uenZpUUlEQVFBQg0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tDQo=\",\n};\n", "/**\n * FingerprintJS v4.6.1 - Copyright (c) FingerprintJS, Inc, 2025 (https://fingerprint.com)\n *\n * Licensed under Business Source License 1.1 https://mariadb.com/bsl11/\n * Licensor: FingerprintJS, Inc.\n * Licensed Work: FingerprintJS browser fingerprinting library\n * Additional Use Grant: None\n * Change Date: Four years from first release for the specific version.\n * Change License: MIT, text at https://opensource.org/license/mit/ with the following copyright notice:\n * Copyright 2015-present FingerprintJS, Inc.\n */\n\nimport { __awaiter, __generator, __assign, __spreadArray } from 'tslib';\n\nvar version = \"4.6.1\";\n\nfunction wait(durationMs, resolveWith) {\n    return new Promise(function (resolve) { return setTimeout(resolve, durationMs, resolveWith); });\n}\n/**\n * Allows asynchronous actions and microtasks to happen.\n */\nfunction releaseEventLoop() {\n    // Don't use setTimeout because Chrome throttles it in some cases causing very long agent execution:\n    // https://stackoverflow.com/a/6032591/1118709\n    // https://github.com/chromium/chromium/commit/0295dd09496330f3a9103ef7e543fa9b6050409b\n    // Reusing a MessageChannel object gives no noticeable benefits\n    return new Promise(function (resolve) {\n        var channel = new MessageChannel();\n        channel.port1.onmessage = function () { return resolve(); };\n        channel.port2.postMessage(null);\n    });\n}\nfunction requestIdleCallbackIfAvailable(fallbackTimeout, deadlineTimeout) {\n    if (deadlineTimeout === void 0) { deadlineTimeout = Infinity; }\n    var requestIdleCallback = window.requestIdleCallback;\n    if (requestIdleCallback) {\n        // The function `requestIdleCallback` loses the binding to `window` here.\n        // `globalThis` isn't always equal `window` (see https://github.com/fingerprintjs/fingerprintjs/issues/683).\n        // Therefore, an error can occur. `call(window,` prevents the error.\n        return new Promise(function (resolve) { return requestIdleCallback.call(window, function () { return resolve(); }, { timeout: deadlineTimeout }); });\n    }\n    else {\n        return wait(Math.min(fallbackTimeout, deadlineTimeout));\n    }\n}\nfunction isPromise(value) {\n    return !!value && typeof value.then === 'function';\n}\n/**\n * Calls a maybe asynchronous function without creating microtasks when the function is synchronous.\n * Catches errors in both cases.\n *\n * If just you run a code like this:\n * ```\n * console.time('Action duration')\n * await action()\n * console.timeEnd('Action duration')\n * ```\n * The synchronous function time can be measured incorrectly because another microtask may run before the `await`\n * returns the control back to the code.\n */\nfunction awaitIfAsync(action, callback) {\n    try {\n        var returnedValue = action();\n        if (isPromise(returnedValue)) {\n            returnedValue.then(function (result) { return callback(true, result); }, function (error) { return callback(false, error); });\n        }\n        else {\n            callback(true, returnedValue);\n        }\n    }\n    catch (error) {\n        callback(false, error);\n    }\n}\n/**\n * If you run many synchronous tasks without using this function, the JS main loop will be busy and asynchronous tasks\n * (e.g. completing a network request, rendering the page) won't be able to happen.\n * This function allows running many synchronous tasks such way that asynchronous tasks can run too in background.\n */\nfunction mapWithBreaks(items, callback, loopReleaseInterval) {\n    if (loopReleaseInterval === void 0) { loopReleaseInterval = 16; }\n    return __awaiter(this, void 0, void 0, function () {\n        var results, lastLoopReleaseTime, i, now;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    results = Array(items.length);\n                    lastLoopReleaseTime = Date.now();\n                    i = 0;\n                    _a.label = 1;\n                case 1:\n                    if (!(i < items.length)) return [3 /*break*/, 4];\n                    results[i] = callback(items[i], i);\n                    now = Date.now();\n                    if (!(now >= lastLoopReleaseTime + loopReleaseInterval)) return [3 /*break*/, 3];\n                    lastLoopReleaseTime = now;\n                    return [4 /*yield*/, releaseEventLoop()];\n                case 2:\n                    _a.sent();\n                    _a.label = 3;\n                case 3:\n                    ++i;\n                    return [3 /*break*/, 1];\n                case 4: return [2 /*return*/, results];\n            }\n        });\n    });\n}\n/**\n * Makes the given promise never emit an unhandled promise rejection console warning.\n * The promise will still pass errors to the next promises.\n * Returns the input promise for convenience.\n *\n * Otherwise, promise emits a console warning unless it has a `catch` listener.\n */\nfunction suppressUnhandledRejectionWarning(promise) {\n    promise.then(undefined, function () { return undefined; });\n    return promise;\n}\n\n/*\n * This file contains functions to work with pure data only (no browser features, DOM, side effects, etc).\n */\n/**\n * Does the same as Array.prototype.includes but has better typing\n */\nfunction includes(haystack, needle) {\n    for (var i = 0, l = haystack.length; i < l; ++i) {\n        if (haystack[i] === needle) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * Like `!includes()` but with proper typing\n */\nfunction excludes(haystack, needle) {\n    return !includes(haystack, needle);\n}\n/**\n * Be careful, NaN can return\n */\nfunction toInt(value) {\n    return parseInt(value);\n}\n/**\n * Be careful, NaN can return\n */\nfunction toFloat(value) {\n    return parseFloat(value);\n}\nfunction replaceNaN(value, replacement) {\n    return typeof value === 'number' && isNaN(value) ? replacement : value;\n}\nfunction countTruthy(values) {\n    return values.reduce(function (sum, value) { return sum + (value ? 1 : 0); }, 0);\n}\nfunction round(value, base) {\n    if (base === void 0) { base = 1; }\n    if (Math.abs(base) >= 1) {\n        return Math.round(value / base) * base;\n    }\n    else {\n        // Sometimes when a number is multiplied by a small number, precision is lost,\n        // for example 1234 * 0.0001 === 0.12340000000000001, and it's more precise divide: 1234 / (1 / 0.0001) === 0.1234.\n        var counterBase = 1 / base;\n        return Math.round(value * counterBase) / counterBase;\n    }\n}\n/**\n * Parses a CSS selector into tag name with HTML attributes.\n * Only single element selector are supported (without operators like space, +, >, etc).\n *\n * Multiple values can be returned for each attribute. You decide how to handle them.\n */\nfunction parseSimpleCssSelector(selector) {\n    var _a, _b;\n    var errorMessage = \"Unexpected syntax '\".concat(selector, \"'\");\n    var tagMatch = /^\\s*([a-z-]*)(.*)$/i.exec(selector);\n    var tag = tagMatch[1] || undefined;\n    var attributes = {};\n    var partsRegex = /([.:#][\\w-]+|\\[.+?\\])/gi;\n    var addAttribute = function (name, value) {\n        attributes[name] = attributes[name] || [];\n        attributes[name].push(value);\n    };\n    for (;;) {\n        var match = partsRegex.exec(tagMatch[2]);\n        if (!match) {\n            break;\n        }\n        var part = match[0];\n        switch (part[0]) {\n            case '.':\n                addAttribute('class', part.slice(1));\n                break;\n            case '#':\n                addAttribute('id', part.slice(1));\n                break;\n            case '[': {\n                var attributeMatch = /^\\[([\\w-]+)([~|^$*]?=(\"(.*?)\"|([\\w-]+)))?(\\s+[is])?\\]$/.exec(part);\n                if (attributeMatch) {\n                    addAttribute(attributeMatch[1], (_b = (_a = attributeMatch[4]) !== null && _a !== void 0 ? _a : attributeMatch[5]) !== null && _b !== void 0 ? _b : '');\n                }\n                else {\n                    throw new Error(errorMessage);\n                }\n                break;\n            }\n            default:\n                throw new Error(errorMessage);\n        }\n    }\n    return [tag, attributes];\n}\n/**\n * Converts a string to UTF8 bytes\n */\nfunction getUTF8Bytes(input) {\n    // Benchmark: https://jsbench.me/b6klaaxgwq/1\n    // If you want to just count bytes, see solutions at https://jsbench.me/ehklab415e/1\n    var result = new Uint8Array(input.length);\n    for (var i = 0; i < input.length; i++) {\n        // `charCode` is faster than encoding, so we prefer that when it's possible\n        var charCode = input.charCodeAt(i);\n        // In case of non-ASCII symbols we use proper encoding\n        if (charCode > 127) {\n            return new TextEncoder().encode(input);\n        }\n        result[i] = charCode;\n    }\n    return result;\n}\n\n/*\n * Based on https://github.com/karanlyons/murmurHash3.js/blob/a33d0723127e2e5415056c455f8aed2451ace208/murmurHash3.js\n */\n/**\n * Adds two 64-bit values (provided as tuples of 32-bit values)\n * and updates (mutates) first value to write the result\n */\nfunction x64Add(m, n) {\n    var m0 = m[0] >>> 16, m1 = m[0] & 0xffff, m2 = m[1] >>> 16, m3 = m[1] & 0xffff;\n    var n0 = n[0] >>> 16, n1 = n[0] & 0xffff, n2 = n[1] >>> 16, n3 = n[1] & 0xffff;\n    var o0 = 0, o1 = 0, o2 = 0, o3 = 0;\n    o3 += m3 + n3;\n    o2 += o3 >>> 16;\n    o3 &= 0xffff;\n    o2 += m2 + n2;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o1 += m1 + n1;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o0 += m0 + n0;\n    o0 &= 0xffff;\n    m[0] = (o0 << 16) | o1;\n    m[1] = (o2 << 16) | o3;\n}\n/**\n * Multiplies two 64-bit values (provided as tuples of 32-bit values)\n * and updates (mutates) first value to write the result\n */\nfunction x64Multiply(m, n) {\n    var m0 = m[0] >>> 16, m1 = m[0] & 0xffff, m2 = m[1] >>> 16, m3 = m[1] & 0xffff;\n    var n0 = n[0] >>> 16, n1 = n[0] & 0xffff, n2 = n[1] >>> 16, n3 = n[1] & 0xffff;\n    var o0 = 0, o1 = 0, o2 = 0, o3 = 0;\n    o3 += m3 * n3;\n    o2 += o3 >>> 16;\n    o3 &= 0xffff;\n    o2 += m2 * n3;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o2 += m3 * n2;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o1 += m1 * n3;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o1 += m2 * n2;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o1 += m3 * n1;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o0 += m0 * n3 + m1 * n2 + m2 * n1 + m3 * n0;\n    o0 &= 0xffff;\n    m[0] = (o0 << 16) | o1;\n    m[1] = (o2 << 16) | o3;\n}\n/**\n * Provides left rotation of the given int64 value (provided as tuple of two int32)\n * by given number of bits. Result is written back to the value\n */\nfunction x64Rotl(m, bits) {\n    var m0 = m[0];\n    bits %= 64;\n    if (bits === 32) {\n        m[0] = m[1];\n        m[1] = m0;\n    }\n    else if (bits < 32) {\n        m[0] = (m0 << bits) | (m[1] >>> (32 - bits));\n        m[1] = (m[1] << bits) | (m0 >>> (32 - bits));\n    }\n    else {\n        bits -= 32;\n        m[0] = (m[1] << bits) | (m0 >>> (32 - bits));\n        m[1] = (m0 << bits) | (m[1] >>> (32 - bits));\n    }\n}\n/**\n * Provides a left shift of the given int32 value (provided as tuple of [0, int32])\n * by given number of bits. Result is written back to the value\n */\nfunction x64LeftShift(m, bits) {\n    bits %= 64;\n    if (bits === 0) {\n        return;\n    }\n    else if (bits < 32) {\n        m[0] = m[1] >>> (32 - bits);\n        m[1] = m[1] << bits;\n    }\n    else {\n        m[0] = m[1] << (bits - 32);\n        m[1] = 0;\n    }\n}\n/**\n * Provides a XOR of the given int64 values(provided as tuple of two int32).\n * Result is written back to the first value\n */\nfunction x64Xor(m, n) {\n    m[0] ^= n[0];\n    m[1] ^= n[1];\n}\nvar F1 = [0xff51afd7, 0xed558ccd];\nvar F2 = [0xc4ceb9fe, 0x1a85ec53];\n/**\n * Calculates murmurHash3's final x64 mix of that block and writes result back to the input value.\n * (`[0, h[0] >>> 1]` is a 33 bit unsigned right shift. This is the\n * only place where we need to right shift 64bit ints.)\n */\nfunction x64Fmix(h) {\n    var shifted = [0, h[0] >>> 1];\n    x64Xor(h, shifted);\n    x64Multiply(h, F1);\n    shifted[1] = h[0] >>> 1;\n    x64Xor(h, shifted);\n    x64Multiply(h, F2);\n    shifted[1] = h[0] >>> 1;\n    x64Xor(h, shifted);\n}\nvar C1 = [0x87c37b91, 0x114253d5];\nvar C2 = [0x4cf5ad43, 0x2745937f];\nvar M$1 = [0, 5];\nvar N1 = [0, 0x52dce729];\nvar N2 = [0, 0x38495ab5];\n/**\n * Given a string and an optional seed as an int, returns a 128 bit\n * hash using the x64 flavor of MurmurHash3, as an unsigned hex.\n * All internal functions mutates passed value to achieve minimal memory allocations and GC load\n *\n * Benchmark https://jsbench.me/p4lkpaoabi/1\n */\nfunction x64hash128(input, seed) {\n    var key = getUTF8Bytes(input);\n    seed = seed || 0;\n    var length = [0, key.length];\n    var remainder = length[1] % 16;\n    var bytes = length[1] - remainder;\n    var h1 = [0, seed];\n    var h2 = [0, seed];\n    var k1 = [0, 0];\n    var k2 = [0, 0];\n    var i;\n    for (i = 0; i < bytes; i = i + 16) {\n        k1[0] = key[i + 4] | (key[i + 5] << 8) | (key[i + 6] << 16) | (key[i + 7] << 24);\n        k1[1] = key[i] | (key[i + 1] << 8) | (key[i + 2] << 16) | (key[i + 3] << 24);\n        k2[0] = key[i + 12] | (key[i + 13] << 8) | (key[i + 14] << 16) | (key[i + 15] << 24);\n        k2[1] = key[i + 8] | (key[i + 9] << 8) | (key[i + 10] << 16) | (key[i + 11] << 24);\n        x64Multiply(k1, C1);\n        x64Rotl(k1, 31);\n        x64Multiply(k1, C2);\n        x64Xor(h1, k1);\n        x64Rotl(h1, 27);\n        x64Add(h1, h2);\n        x64Multiply(h1, M$1);\n        x64Add(h1, N1);\n        x64Multiply(k2, C2);\n        x64Rotl(k2, 33);\n        x64Multiply(k2, C1);\n        x64Xor(h2, k2);\n        x64Rotl(h2, 31);\n        x64Add(h2, h1);\n        x64Multiply(h2, M$1);\n        x64Add(h2, N2);\n    }\n    k1[0] = 0;\n    k1[1] = 0;\n    k2[0] = 0;\n    k2[1] = 0;\n    var val = [0, 0];\n    switch (remainder) {\n        case 15:\n            val[1] = key[i + 14];\n            x64LeftShift(val, 48);\n            x64Xor(k2, val);\n        // fallthrough\n        case 14:\n            val[1] = key[i + 13];\n            x64LeftShift(val, 40);\n            x64Xor(k2, val);\n        // fallthrough\n        case 13:\n            val[1] = key[i + 12];\n            x64LeftShift(val, 32);\n            x64Xor(k2, val);\n        // fallthrough\n        case 12:\n            val[1] = key[i + 11];\n            x64LeftShift(val, 24);\n            x64Xor(k2, val);\n        // fallthrough\n        case 11:\n            val[1] = key[i + 10];\n            x64LeftShift(val, 16);\n            x64Xor(k2, val);\n        // fallthrough\n        case 10:\n            val[1] = key[i + 9];\n            x64LeftShift(val, 8);\n            x64Xor(k2, val);\n        // fallthrough\n        case 9:\n            val[1] = key[i + 8];\n            x64Xor(k2, val);\n            x64Multiply(k2, C2);\n            x64Rotl(k2, 33);\n            x64Multiply(k2, C1);\n            x64Xor(h2, k2);\n        // fallthrough\n        case 8:\n            val[1] = key[i + 7];\n            x64LeftShift(val, 56);\n            x64Xor(k1, val);\n        // fallthrough\n        case 7:\n            val[1] = key[i + 6];\n            x64LeftShift(val, 48);\n            x64Xor(k1, val);\n        // fallthrough\n        case 6:\n            val[1] = key[i + 5];\n            x64LeftShift(val, 40);\n            x64Xor(k1, val);\n        // fallthrough\n        case 5:\n            val[1] = key[i + 4];\n            x64LeftShift(val, 32);\n            x64Xor(k1, val);\n        // fallthrough\n        case 4:\n            val[1] = key[i + 3];\n            x64LeftShift(val, 24);\n            x64Xor(k1, val);\n        // fallthrough\n        case 3:\n            val[1] = key[i + 2];\n            x64LeftShift(val, 16);\n            x64Xor(k1, val);\n        // fallthrough\n        case 2:\n            val[1] = key[i + 1];\n            x64LeftShift(val, 8);\n            x64Xor(k1, val);\n        // fallthrough\n        case 1:\n            val[1] = key[i];\n            x64Xor(k1, val);\n            x64Multiply(k1, C1);\n            x64Rotl(k1, 31);\n            x64Multiply(k1, C2);\n            x64Xor(h1, k1);\n        // fallthrough\n    }\n    x64Xor(h1, length);\n    x64Xor(h2, length);\n    x64Add(h1, h2);\n    x64Add(h2, h1);\n    x64Fmix(h1);\n    x64Fmix(h2);\n    x64Add(h1, h2);\n    x64Add(h2, h1);\n    return (('00000000' + (h1[0] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h1[1] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h2[0] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h2[1] >>> 0).toString(16)).slice(-8));\n}\n\n/**\n * Converts an error object to a plain object that can be used with `JSON.stringify`.\n * If you just run `JSON.stringify(error)`, you'll get `'{}'`.\n */\nfunction errorToObject(error) {\n    var _a;\n    return __assign({ name: error.name, message: error.message, stack: (_a = error.stack) === null || _a === void 0 ? void 0 : _a.split('\\n') }, error);\n}\nfunction isFunctionNative(func) {\n    return /^function\\s.*?\\{\\s*\\[native code]\\s*}$/.test(String(func));\n}\n\nfunction isFinalResultLoaded(loadResult) {\n    return typeof loadResult !== 'function';\n}\n/**\n * Loads the given entropy source. Returns a function that gets an entropy component from the source.\n *\n * The result is returned synchronously to prevent `loadSources` from\n * waiting for one source to load before getting the components from the other sources.\n */\nfunction loadSource(source, sourceOptions) {\n    var sourceLoadPromise = suppressUnhandledRejectionWarning(new Promise(function (resolveLoad) {\n        var loadStartTime = Date.now();\n        // `awaitIfAsync` is used instead of just `await` in order to measure the duration of synchronous sources\n        // correctly (other microtasks won't affect the duration).\n        awaitIfAsync(source.bind(null, sourceOptions), function () {\n            var loadArgs = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                loadArgs[_i] = arguments[_i];\n            }\n            var loadDuration = Date.now() - loadStartTime;\n            // Source loading failed\n            if (!loadArgs[0]) {\n                return resolveLoad(function () { return ({ error: loadArgs[1], duration: loadDuration }); });\n            }\n            var loadResult = loadArgs[1];\n            // Source loaded with the final result\n            if (isFinalResultLoaded(loadResult)) {\n                return resolveLoad(function () { return ({ value: loadResult, duration: loadDuration }); });\n            }\n            // Source loaded with \"get\" stage\n            resolveLoad(function () {\n                return new Promise(function (resolveGet) {\n                    var getStartTime = Date.now();\n                    awaitIfAsync(loadResult, function () {\n                        var getArgs = [];\n                        for (var _i = 0; _i < arguments.length; _i++) {\n                            getArgs[_i] = arguments[_i];\n                        }\n                        var duration = loadDuration + Date.now() - getStartTime;\n                        // Source getting failed\n                        if (!getArgs[0]) {\n                            return resolveGet({ error: getArgs[1], duration: duration });\n                        }\n                        // Source getting succeeded\n                        resolveGet({ value: getArgs[1], duration: duration });\n                    });\n                });\n            });\n        });\n    }));\n    return function getComponent() {\n        return sourceLoadPromise.then(function (finalizeSource) { return finalizeSource(); });\n    };\n}\n/**\n * Loads the given entropy sources. Returns a function that collects the entropy components.\n *\n * The result is returned synchronously in order to allow start getting the components\n * before the sources are loaded completely.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction loadSources(sources, sourceOptions, excludeSources, loopReleaseInterval) {\n    var includedSources = Object.keys(sources).filter(function (sourceKey) { return excludes(excludeSources, sourceKey); });\n    // Using `mapWithBreaks` allows asynchronous sources to complete between synchronous sources\n    // and measure the duration correctly\n    var sourceGettersPromise = suppressUnhandledRejectionWarning(mapWithBreaks(includedSources, function (sourceKey) { return loadSource(sources[sourceKey], sourceOptions); }, loopReleaseInterval));\n    return function getComponents() {\n        return __awaiter(this, void 0, void 0, function () {\n            var sourceGetters, componentPromises, componentArray, components, index;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, sourceGettersPromise];\n                    case 1:\n                        sourceGetters = _a.sent();\n                        return [4 /*yield*/, mapWithBreaks(sourceGetters, function (sourceGetter) { return suppressUnhandledRejectionWarning(sourceGetter()); }, loopReleaseInterval)];\n                    case 2:\n                        componentPromises = _a.sent();\n                        return [4 /*yield*/, Promise.all(componentPromises)\n                            // Keeping the component keys order the same as the source keys order\n                        ];\n                    case 3:\n                        componentArray = _a.sent();\n                        components = {};\n                        for (index = 0; index < includedSources.length; ++index) {\n                            components[includedSources[index]] = componentArray[index];\n                        }\n                        return [2 /*return*/, components];\n                }\n            });\n        });\n    };\n}\n/**\n * Modifies an entropy source by transforming its returned value with the given function.\n * Keeps the source properties: sync/async, 1/2 stages.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction transformSource(source, transformValue) {\n    var transformLoadResult = function (loadResult) {\n        if (isFinalResultLoaded(loadResult)) {\n            return transformValue(loadResult);\n        }\n        return function () {\n            var getResult = loadResult();\n            if (isPromise(getResult)) {\n                return getResult.then(transformValue);\n            }\n            return transformValue(getResult);\n        };\n    };\n    return function (options) {\n        var loadResult = source(options);\n        if (isPromise(loadResult)) {\n            return loadResult.then(transformLoadResult);\n        }\n        return transformLoadResult(loadResult);\n    };\n}\n\n/*\n * Functions to help with features that vary through browsers\n */\n/**\n * Checks whether the browser is based on Trident (the Internet Explorer engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isTrident() {\n    var w = window;\n    var n = navigator;\n    // The properties are checked to be in IE 10, IE 11 and not to be in other browsers in October 2020\n    return (countTruthy([\n        'MSCSSMatrix' in w,\n        'msSetImmediate' in w,\n        'msIndexedDB' in w,\n        'msMaxTouchPoints' in n,\n        'msPointerEnabled' in n,\n    ]) >= 4);\n}\n/**\n * Checks whether the browser is based on EdgeHTML (the pre-Chromium Edge engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isEdgeHTML() {\n    // Based on research in October 2020\n    var w = window;\n    var n = navigator;\n    return (countTruthy(['msWriteProfilerMark' in w, 'MSStream' in w, 'msLaunchUri' in n, 'msSaveBlob' in n]) >= 3 &&\n        !isTrident());\n}\n/**\n * Checks whether the browser is based on Chromium without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isChromium() {\n    // Based on research in October 2020. Tested to detect Chromium 42-86.\n    var w = window;\n    var n = navigator;\n    return (countTruthy([\n        'webkitPersistentStorage' in n,\n        'webkitTemporaryStorage' in n,\n        (n.vendor || '').indexOf('Google') === 0,\n        'webkitResolveLocalFileSystemURL' in w,\n        'BatteryManager' in w,\n        'webkitMediaStream' in w,\n        'webkitSpeechGrammar' in w,\n    ]) >= 5);\n}\n/**\n * Checks whether the browser is based on mobile or desktop Safari without using user-agent.\n * All iOS browsers use WebKit (the Safari engine).\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isWebKit() {\n    // Based on research in August 2024\n    var w = window;\n    var n = navigator;\n    return (countTruthy([\n        'ApplePayError' in w,\n        'CSSPrimitiveValue' in w,\n        'Counter' in w,\n        n.vendor.indexOf('Apple') === 0,\n        'RGBColor' in w,\n        'WebKitMediaKeys' in w,\n    ]) >= 4);\n}\n/**\n * Checks whether this WebKit browser is a desktop browser.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isDesktopWebKit() {\n    // Checked in Safari and DuckDuckGo\n    var w = window;\n    var HTMLElement = w.HTMLElement, Document = w.Document;\n    return (countTruthy([\n        'safari' in w,\n        !('ongestureend' in w),\n        !('TouchEvent' in w),\n        !('orientation' in w),\n        HTMLElement && !('autocapitalize' in HTMLElement.prototype),\n        Document && 'pointerLockElement' in Document.prototype,\n    ]) >= 4);\n}\n/**\n * Checks whether this WebKit browser is Safari.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * Warning! The function works properly only for Safari version 15.4 and newer.\n */\nfunction isSafariWebKit() {\n    // Checked in Safari, Chrome, Firefox, Yandex, UC Browser, Opera, Edge and DuckDuckGo.\n    // iOS Safari and Chrome were checked on iOS 11-18. DuckDuckGo was checked on iOS 17-18 and macOS 14-15.\n    // Desktop Safari versions 12-18 were checked.\n    // The other browsers were checked on iOS 17 and 18; there was no chance to check them on the other OS versions.\n    var w = window;\n    return (\n    // Filters-out Chrome, Yandex, DuckDuckGo (macOS and iOS), Edge\n    isFunctionNative(w.print) &&\n        // Doesn't work in Safari < 15.4\n        String(w.browser) === '[object WebPageNamespace]');\n}\n/**\n * Checks whether the browser is based on Gecko (Firefox engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isGecko() {\n    var _a, _b;\n    var w = window;\n    // Based on research in September 2020\n    return (countTruthy([\n        'buildID' in navigator,\n        'MozAppearance' in ((_b = (_a = document.documentElement) === null || _a === void 0 ? void 0 : _a.style) !== null && _b !== void 0 ? _b : {}),\n        'onmozfullscreenchange' in w,\n        'mozInnerScreenX' in w,\n        'CSSMozDocumentRule' in w,\n        'CanvasCaptureMediaStream' in w,\n    ]) >= 4);\n}\n/**\n * Checks whether the browser is based on Chromium version ≥86 without using user-agent.\n * It doesn't check that the browser is based on Chromium, there is a separate function for this.\n */\nfunction isChromium86OrNewer() {\n    // Checked in Chrome 85 vs Chrome 86 both on desktop and Android. Checked in macOS Chrome 128, Android Chrome 127.\n    var w = window;\n    return (countTruthy([\n        !('MediaSettingsRange' in w),\n        'RTCEncodedAudioFrame' in w,\n        '' + w.Intl === '[object Intl]',\n        '' + w.Reflect === '[object Reflect]',\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on Chromium version ≥122 without using user-agent.\n * It doesn't check that the browser is based on Chromium, there is a separate function for this.\n */\nfunction isChromium122OrNewer() {\n    // Checked in Chrome 121 vs Chrome 122 and 129 both on desktop and Android\n    var w = window;\n    var URLPattern = w.URLPattern;\n    return (countTruthy([\n        'union' in Set.prototype,\n        'Iterator' in w,\n        URLPattern && 'hasRegExpGroups' in URLPattern.prototype,\n        'RGB8' in WebGLRenderingContext.prototype,\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on WebKit version ≥606 (Safari ≥12) without using user-agent.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * @see https://en.wikipedia.org/wiki/Safari_version_history#Release_history Safari-WebKit versions map\n */\nfunction isWebKit606OrNewer() {\n    // Checked in Safari 9–18\n    var w = window;\n    return (countTruthy([\n        'DOMRectList' in w,\n        'RTCPeerConnectionIceEvent' in w,\n        'SVGGeometryElement' in w,\n        'ontransitioncancel' in w,\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on WebKit version ≥616 (Safari ≥17) without using user-agent.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * @see https://developer.apple.com/documentation/safari-release-notes/safari-17-release-notes Safari 17 release notes\n * @see https://tauri.app/v1/references/webview-versions/#webkit-versions-in-safari Safari-WebKit versions map\n */\nfunction isWebKit616OrNewer() {\n    var w = window;\n    var n = navigator;\n    var CSS = w.CSS, HTMLButtonElement = w.HTMLButtonElement;\n    return (countTruthy([\n        !('getStorageUpdates' in n),\n        HTMLButtonElement && 'popover' in HTMLButtonElement.prototype,\n        'CSSCounterStyleRule' in w,\n        CSS.supports('font-size-adjust: ex-height 0.5'),\n        CSS.supports('text-transform: full-width'),\n    ]) >= 4);\n}\n/**\n * Checks whether the device is an iPad.\n * It doesn't check that the engine is WebKit and that the WebKit isn't desktop.\n */\nfunction isIPad() {\n    // Checked on:\n    // Safari on iPadOS (both mobile and desktop modes): 8, 11-18\n    // Chrome on iPadOS (both mobile and desktop modes): 11-18\n    // Safari on iOS (both mobile and desktop modes): 9-18\n    // Chrome on iOS (both mobile and desktop modes): 9-18\n    // Before iOS 13. Safari tampers the value in \"request desktop site\" mode since iOS 13.\n    if (navigator.platform === 'iPad') {\n        return true;\n    }\n    var s = screen;\n    var screenRatio = s.width / s.height;\n    return (countTruthy([\n        // Since iOS 13. Doesn't work in Chrome on iPadOS <15, but works in desktop mode.\n        'MediaSource' in window,\n        // Since iOS 12. Doesn't work in Chrome on iPadOS.\n        !!Element.prototype.webkitRequestFullscreen,\n        // iPhone 4S that runs iOS 9 matches this, but it is not supported\n        // Doesn't work in incognito mode of Safari ≥17 with split screen because of tracking prevention\n        screenRatio > 0.65 && screenRatio < 1.53,\n    ]) >= 2);\n}\n/**\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getFullscreenElement() {\n    var d = document;\n    return d.fullscreenElement || d.msFullscreenElement || d.mozFullScreenElement || d.webkitFullscreenElement || null;\n}\nfunction exitFullscreen() {\n    var d = document;\n    // `call` is required because the function throws an error without a proper \"this\" context\n    return (d.exitFullscreen || d.msExitFullscreen || d.mozCancelFullScreen || d.webkitExitFullscreen).call(d);\n}\n/**\n * Checks whether the device runs on Android without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isAndroid() {\n    var isItChromium = isChromium();\n    var isItGecko = isGecko();\n    var w = window;\n    var n = navigator;\n    var c = 'connection';\n    // Chrome removes all words \"Android\" from `navigator` when desktop version is requested\n    // Firefox keeps \"Android\" in `navigator.appVersion` when desktop version is requested\n    if (isItChromium) {\n        return (countTruthy([\n            !('SharedWorker' in w),\n            // `typechange` is deprecated, but it's still present on Android (tested on Chrome Mobile 117)\n            // Removal proposal https://bugs.chromium.org/p/chromium/issues/detail?id=699892\n            // Note: this expression returns true on ChromeOS, so additional detectors are required to avoid false-positives\n            n[c] && 'ontypechange' in n[c],\n            !('sinkId' in new Audio()),\n        ]) >= 2);\n    }\n    else if (isItGecko) {\n        return countTruthy(['onorientationchange' in w, 'orientation' in w, /android/i.test(n.appVersion)]) >= 2;\n    }\n    else {\n        // Only 2 browser engines are presented on Android.\n        // Actually, there is also Android 4.1 browser, but it's not worth detecting it at the moment.\n        return false;\n    }\n}\n/**\n * Checks whether the browser is Samsung Internet without using user-agent.\n * It doesn't check that the browser is based on Chromium, please use `isChromium` before using this function.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isSamsungInternet() {\n    // Checked in Samsung Internet 21, 25 and 27\n    var n = navigator;\n    var w = window;\n    var audioPrototype = Audio.prototype;\n    var visualViewport = w.visualViewport;\n    return (countTruthy([\n        'srLatency' in audioPrototype,\n        'srChannelCount' in audioPrototype,\n        'devicePosture' in n,\n        visualViewport && 'segments' in visualViewport,\n        'getTextInformation' in Image.prototype, // Not available in Samsung Internet 21\n    ]) >= 3);\n}\n\n/**\n * A deep description: https://fingerprint.com/blog/audio-fingerprinting/\n * Inspired by and based on https://github.com/cozylife/audio-fingerprint\n *\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * Audio signal is noised in private mode of Safari 17, so audio fingerprinting is skipped in Safari 17.\n */\nfunction getAudioFingerprint() {\n    if (doesBrowserPerformAntifingerprinting$1()) {\n        return -4 /* SpecialFingerprint.KnownForAntifingerprinting */;\n    }\n    return getUnstableAudioFingerprint();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableAudioFingerprint() {\n    var w = window;\n    var AudioContext = w.OfflineAudioContext || w.webkitOfflineAudioContext;\n    if (!AudioContext) {\n        return -2 /* SpecialFingerprint.NotSupported */;\n    }\n    // In some browsers, audio context always stays suspended unless the context is started in response to a user action\n    // (e.g. a click or a tap). It prevents audio fingerprint from being taken at an arbitrary moment of time.\n    // Such browsers are old and unpopular, so the audio fingerprinting is just skipped in them.\n    // See a similar case explanation at https://stackoverflow.com/questions/46363048/onaudioprocess-not-called-on-ios11#46534088\n    if (doesBrowserSuspendAudioContext()) {\n        return -1 /* SpecialFingerprint.KnownForSuspending */;\n    }\n    var hashFromIndex = 4500;\n    var hashToIndex = 5000;\n    var context = new AudioContext(1, hashToIndex, 44100);\n    var oscillator = context.createOscillator();\n    oscillator.type = 'triangle';\n    oscillator.frequency.value = 10000;\n    var compressor = context.createDynamicsCompressor();\n    compressor.threshold.value = -50;\n    compressor.knee.value = 40;\n    compressor.ratio.value = 12;\n    compressor.attack.value = 0;\n    compressor.release.value = 0.25;\n    oscillator.connect(compressor);\n    compressor.connect(context.destination);\n    oscillator.start(0);\n    var _a = startRenderingAudio(context), renderPromise = _a[0], finishRendering = _a[1];\n    // Suppresses the console error message in case when the fingerprint fails before requested\n    var fingerprintPromise = suppressUnhandledRejectionWarning(renderPromise.then(function (buffer) { return getHash(buffer.getChannelData(0).subarray(hashFromIndex)); }, function (error) {\n        if (error.name === \"timeout\" /* InnerErrorName.Timeout */ || error.name === \"suspended\" /* InnerErrorName.Suspended */) {\n            return -3 /* SpecialFingerprint.Timeout */;\n        }\n        throw error;\n    }));\n    return function () {\n        finishRendering();\n        return fingerprintPromise;\n    };\n}\n/**\n * Checks if the current browser is known for always suspending audio context\n */\nfunction doesBrowserSuspendAudioContext() {\n    // Mobile Safari 11 and older\n    return isWebKit() && !isDesktopWebKit() && !isWebKit606OrNewer();\n}\n/**\n * Checks if the current browser is known for applying anti-fingerprinting measures in all or some critical modes\n */\nfunction doesBrowserPerformAntifingerprinting$1() {\n    return (\n    // Safari ≥17\n    (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) ||\n        // Samsung Internet ≥26\n        (isChromium() && isSamsungInternet() && isChromium122OrNewer()));\n}\n/**\n * Starts rendering the audio context.\n * When the returned function is called, the render process starts finishing.\n */\nfunction startRenderingAudio(context) {\n    var renderTryMaxCount = 3;\n    var renderRetryDelay = 500;\n    var runningMaxAwaitTime = 500;\n    var runningSufficientTime = 5000;\n    var finalize = function () { return undefined; };\n    var resultPromise = new Promise(function (resolve, reject) {\n        var isFinalized = false;\n        var renderTryCount = 0;\n        var startedRunningAt = 0;\n        context.oncomplete = function (event) { return resolve(event.renderedBuffer); };\n        var startRunningTimeout = function () {\n            setTimeout(function () { return reject(makeInnerError(\"timeout\" /* InnerErrorName.Timeout */)); }, Math.min(runningMaxAwaitTime, startedRunningAt + runningSufficientTime - Date.now()));\n        };\n        var tryRender = function () {\n            try {\n                var renderingPromise = context.startRendering();\n                // `context.startRendering` has two APIs: Promise and callback, we check that it's really a promise just in case\n                if (isPromise(renderingPromise)) {\n                    // Suppresses all unhandled rejections in case of scheduled redundant retries after successful rendering\n                    suppressUnhandledRejectionWarning(renderingPromise);\n                }\n                switch (context.state) {\n                    case 'running':\n                        startedRunningAt = Date.now();\n                        if (isFinalized) {\n                            startRunningTimeout();\n                        }\n                        break;\n                    // Sometimes the audio context doesn't start after calling `startRendering` (in addition to the cases where\n                    // audio context doesn't start at all). A known case is starting an audio context when the browser tab is in\n                    // background on iPhone. Retries usually help in this case.\n                    case 'suspended':\n                        // The audio context can reject starting until the tab is in foreground. Long fingerprint duration\n                        // in background isn't a problem, therefore the retry attempts don't count in background. It can lead to\n                        // a situation when a fingerprint takes very long time and finishes successfully. FYI, the audio context\n                        // can be suspended when `document.hidden === false` and start running after a retry.\n                        if (!document.hidden) {\n                            renderTryCount++;\n                        }\n                        if (isFinalized && renderTryCount >= renderTryMaxCount) {\n                            reject(makeInnerError(\"suspended\" /* InnerErrorName.Suspended */));\n                        }\n                        else {\n                            setTimeout(tryRender, renderRetryDelay);\n                        }\n                        break;\n                }\n            }\n            catch (error) {\n                reject(error);\n            }\n        };\n        tryRender();\n        finalize = function () {\n            if (!isFinalized) {\n                isFinalized = true;\n                if (startedRunningAt > 0) {\n                    startRunningTimeout();\n                }\n            }\n        };\n    });\n    return [resultPromise, finalize];\n}\nfunction getHash(signal) {\n    var hash = 0;\n    for (var i = 0; i < signal.length; ++i) {\n        hash += Math.abs(signal[i]);\n    }\n    return hash;\n}\nfunction makeInnerError(name) {\n    var error = new Error(name);\n    error.name = name;\n    return error;\n}\n\n/**\n * Creates and keeps an invisible iframe while the given function runs.\n * The given function is called when the iframe is loaded and has a body.\n * The iframe allows to measure DOM sizes inside itself.\n *\n * Notice: passing an initial HTML code doesn't work in IE.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction withIframe(action, initialHtml, domPollInterval) {\n    var _a, _b, _c;\n    if (domPollInterval === void 0) { domPollInterval = 50; }\n    return __awaiter(this, void 0, void 0, function () {\n        var d, iframe;\n        return __generator(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    d = document;\n                    _d.label = 1;\n                case 1:\n                    if (!!d.body) return [3 /*break*/, 3];\n                    return [4 /*yield*/, wait(domPollInterval)];\n                case 2:\n                    _d.sent();\n                    return [3 /*break*/, 1];\n                case 3:\n                    iframe = d.createElement('iframe');\n                    _d.label = 4;\n                case 4:\n                    _d.trys.push([4, , 10, 11]);\n                    return [4 /*yield*/, new Promise(function (_resolve, _reject) {\n                            var isComplete = false;\n                            var resolve = function () {\n                                isComplete = true;\n                                _resolve();\n                            };\n                            var reject = function (error) {\n                                isComplete = true;\n                                _reject(error);\n                            };\n                            iframe.onload = resolve;\n                            iframe.onerror = reject;\n                            var style = iframe.style;\n                            style.setProperty('display', 'block', 'important'); // Required for browsers to calculate the layout\n                            style.position = 'absolute';\n                            style.top = '0';\n                            style.left = '0';\n                            style.visibility = 'hidden';\n                            if (initialHtml && 'srcdoc' in iframe) {\n                                iframe.srcdoc = initialHtml;\n                            }\n                            else {\n                                iframe.src = 'about:blank';\n                            }\n                            d.body.appendChild(iframe);\n                            // WebKit in WeChat doesn't fire the iframe's `onload` for some reason.\n                            // This code checks for the loading state manually.\n                            // See https://github.com/fingerprintjs/fingerprintjs/issues/645\n                            var checkReadyState = function () {\n                                var _a, _b;\n                                // The ready state may never become 'complete' in Firefox despite the 'load' event being fired.\n                                // So an infinite setTimeout loop can happen without this check.\n                                // See https://github.com/fingerprintjs/fingerprintjs/pull/716#issuecomment-986898796\n                                if (isComplete) {\n                                    return;\n                                }\n                                // Make sure iframe.contentWindow and iframe.contentWindow.document are both loaded\n                                // The contentWindow.document can miss in JSDOM (https://github.com/jsdom/jsdom).\n                                if (((_b = (_a = iframe.contentWindow) === null || _a === void 0 ? void 0 : _a.document) === null || _b === void 0 ? void 0 : _b.readyState) === 'complete') {\n                                    resolve();\n                                }\n                                else {\n                                    setTimeout(checkReadyState, 10);\n                                }\n                            };\n                            checkReadyState();\n                        })];\n                case 5:\n                    _d.sent();\n                    _d.label = 6;\n                case 6:\n                    if (!!((_b = (_a = iframe.contentWindow) === null || _a === void 0 ? void 0 : _a.document) === null || _b === void 0 ? void 0 : _b.body)) return [3 /*break*/, 8];\n                    return [4 /*yield*/, wait(domPollInterval)];\n                case 7:\n                    _d.sent();\n                    return [3 /*break*/, 6];\n                case 8: return [4 /*yield*/, action(iframe, iframe.contentWindow)];\n                case 9: return [2 /*return*/, _d.sent()];\n                case 10:\n                    (_c = iframe.parentNode) === null || _c === void 0 ? void 0 : _c.removeChild(iframe);\n                    return [7 /*endfinally*/];\n                case 11: return [2 /*return*/];\n            }\n        });\n    });\n}\n/**\n * Creates a DOM element that matches the given selector.\n * Only single element selector are supported (without operators like space, +, >, etc).\n */\nfunction selectorToElement(selector) {\n    var _a = parseSimpleCssSelector(selector), tag = _a[0], attributes = _a[1];\n    var element = document.createElement(tag !== null && tag !== void 0 ? tag : 'div');\n    for (var _i = 0, _b = Object.keys(attributes); _i < _b.length; _i++) {\n        var name_1 = _b[_i];\n        var value = attributes[name_1].join(' ');\n        // Changing the `style` attribute can cause a CSP error, therefore we change the `style.cssText` property.\n        // https://github.com/fingerprintjs/fingerprintjs/issues/733\n        if (name_1 === 'style') {\n            addStyleString(element.style, value);\n        }\n        else {\n            element.setAttribute(name_1, value);\n        }\n    }\n    return element;\n}\n/**\n * Adds CSS styles from a string in such a way that doesn't trigger a CSP warning (unsafe-inline or unsafe-eval)\n */\nfunction addStyleString(style, source) {\n    // We don't use `style.cssText` because browsers must block it when no `unsafe-eval` CSP is presented: https://csplite.com/csp145/#w3c_note\n    // Even though the browsers ignore this standard, we don't use `cssText` just in case.\n    for (var _i = 0, _a = source.split(';'); _i < _a.length; _i++) {\n        var property = _a[_i];\n        var match = /^\\s*([\\w-]+)\\s*:\\s*(.+?)(\\s*!([\\w-]+))?\\s*$/.exec(property);\n        if (match) {\n            var name_2 = match[1], value = match[2], priority = match[4];\n            style.setProperty(name_2, value, priority || ''); // The last argument can't be undefined in IE11\n        }\n    }\n}\n/**\n * Returns true if the code runs in an iframe, and any parent page's origin doesn't match the current origin\n */\nfunction isAnyParentCrossOrigin() {\n    var currentWindow = window;\n    for (;;) {\n        var parentWindow = currentWindow.parent;\n        if (!parentWindow || parentWindow === currentWindow) {\n            return false; // The top page is reached\n        }\n        try {\n            if (parentWindow.location.origin !== currentWindow.location.origin) {\n                return true;\n            }\n        }\n        catch (error) {\n            // The error is thrown when `origin` is accessed on `parentWindow.location` when the parent is cross-origin\n            if (error instanceof Error && error.name === 'SecurityError') {\n                return true;\n            }\n            throw error;\n        }\n        currentWindow = parentWindow;\n    }\n}\n\n// We use m or w because these two characters take up the maximum width.\n// And we use a LLi so that the same matching fonts can get separated.\nvar testString = 'mmMwWLliI0O&1';\n// We test using 48px font size, we may use any size. I guess larger the better.\nvar textSize = '48px';\n// A font will be compared against all the three default fonts.\n// And if for any default fonts it doesn't match, then that font is available.\nvar baseFonts = ['monospace', 'sans-serif', 'serif'];\nvar fontList = [\n    // This is android-specific font from \"Roboto\" family\n    'sans-serif-thin',\n    'ARNO PRO',\n    'Agency FB',\n    'Arabic Typesetting',\n    'Arial Unicode MS',\n    'AvantGarde Bk BT',\n    'BankGothic Md BT',\n    'Batang',\n    'Bitstream Vera Sans Mono',\n    'Calibri',\n    'Century',\n    'Century Gothic',\n    'Clarendon',\n    'EUROSTILE',\n    'Franklin Gothic',\n    'Futura Bk BT',\n    'Futura Md BT',\n    'GOTHAM',\n    'Gill Sans',\n    'HELV',\n    'Haettenschweiler',\n    'Helvetica Neue',\n    'Humanst521 BT',\n    'Leelawadee',\n    'Letter Gothic',\n    'Levenim MT',\n    'Lucida Bright',\n    'Lucida Sans',\n    'Menlo',\n    'MS Mincho',\n    'MS Outlook',\n    'MS Reference Specialty',\n    'MS UI Gothic',\n    'MT Extra',\n    'MYRIAD PRO',\n    'Marlett',\n    'Meiryo UI',\n    'Microsoft Uighur',\n    'Minion Pro',\n    'Monotype Corsiva',\n    'PMingLiU',\n    'Pristina',\n    'SCRIPTINA',\n    'Segoe UI Light',\n    'Serifa',\n    'SimHei',\n    'Small Fonts',\n    'Staccato222 BT',\n    'TRAJAN PRO',\n    'Univers CE 55 Medium',\n    'Vrinda',\n    'ZWAdobeF',\n];\n// kudos to http://www.lalit.org/lab/javascript-css-font-detect/\nfunction getFonts() {\n    var _this = this;\n    // Running the script in an iframe makes it not affect the page look and not be affected by the page CSS. See:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/592\n    // https://github.com/fingerprintjs/fingerprintjs/issues/628\n    return withIframe(function (_, _a) {\n        var document = _a.document;\n        return __awaiter(_this, void 0, void 0, function () {\n            var holder, spansContainer, defaultWidth, defaultHeight, createSpan, createSpanWithFonts, initializeBaseFontsSpans, initializeFontsSpans, isFontAvailable, baseFontsSpans, fontsSpans, index;\n            return __generator(this, function (_b) {\n                holder = document.body;\n                holder.style.fontSize = textSize;\n                spansContainer = document.createElement('div');\n                spansContainer.style.setProperty('visibility', 'hidden', 'important');\n                defaultWidth = {};\n                defaultHeight = {};\n                createSpan = function (fontFamily) {\n                    var span = document.createElement('span');\n                    var style = span.style;\n                    style.position = 'absolute';\n                    style.top = '0';\n                    style.left = '0';\n                    style.fontFamily = fontFamily;\n                    span.textContent = testString;\n                    spansContainer.appendChild(span);\n                    return span;\n                };\n                createSpanWithFonts = function (fontToDetect, baseFont) {\n                    return createSpan(\"'\".concat(fontToDetect, \"',\").concat(baseFont));\n                };\n                initializeBaseFontsSpans = function () {\n                    return baseFonts.map(createSpan);\n                };\n                initializeFontsSpans = function () {\n                    // Stores {fontName : [spans for that font]}\n                    var spans = {};\n                    var _loop_1 = function (font) {\n                        spans[font] = baseFonts.map(function (baseFont) { return createSpanWithFonts(font, baseFont); });\n                    };\n                    for (var _i = 0, fontList_1 = fontList; _i < fontList_1.length; _i++) {\n                        var font = fontList_1[_i];\n                        _loop_1(font);\n                    }\n                    return spans;\n                };\n                isFontAvailable = function (fontSpans) {\n                    return baseFonts.some(function (baseFont, baseFontIndex) {\n                        return fontSpans[baseFontIndex].offsetWidth !== defaultWidth[baseFont] ||\n                            fontSpans[baseFontIndex].offsetHeight !== defaultHeight[baseFont];\n                    });\n                };\n                baseFontsSpans = initializeBaseFontsSpans();\n                fontsSpans = initializeFontsSpans();\n                // add all the spans to the DOM\n                holder.appendChild(spansContainer);\n                // get the default width for the three base fonts\n                for (index = 0; index < baseFonts.length; index++) {\n                    defaultWidth[baseFonts[index]] = baseFontsSpans[index].offsetWidth; // width for the default font\n                    defaultHeight[baseFonts[index]] = baseFontsSpans[index].offsetHeight; // height for the default font\n                }\n                // check available fonts\n                return [2 /*return*/, fontList.filter(function (font) { return isFontAvailable(fontsSpans[font]); })];\n            });\n        });\n    });\n}\n\nfunction getPlugins() {\n    var rawPlugins = navigator.plugins;\n    if (!rawPlugins) {\n        return undefined;\n    }\n    var plugins = [];\n    // Safari 10 doesn't support iterating navigator.plugins with for...of\n    for (var i = 0; i < rawPlugins.length; ++i) {\n        var plugin = rawPlugins[i];\n        if (!plugin) {\n            continue;\n        }\n        var mimeTypes = [];\n        for (var j = 0; j < plugin.length; ++j) {\n            var mimeType = plugin[j];\n            mimeTypes.push({\n                type: mimeType.type,\n                suffixes: mimeType.suffixes,\n            });\n        }\n        plugins.push({\n            name: plugin.name,\n            description: plugin.description,\n            mimeTypes: mimeTypes,\n        });\n    }\n    return plugins;\n}\n\n/**\n * @see https://www.browserleaks.com/canvas#how-does-it-work\n *\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * Canvas image is noised in private mode of Safari 17, so image rendering is skipped in Safari 17.\n */\nfunction getCanvasFingerprint() {\n    return getUnstableCanvasFingerprint(doesBrowserPerformAntifingerprinting());\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableCanvasFingerprint(skipImages) {\n    var _a;\n    var winding = false;\n    var geometry;\n    var text;\n    var _b = makeCanvasContext(), canvas = _b[0], context = _b[1];\n    if (!isSupported(canvas, context)) {\n        geometry = text = \"unsupported\" /* ImageStatus.Unsupported */;\n    }\n    else {\n        winding = doesSupportWinding(context);\n        if (skipImages) {\n            geometry = text = \"skipped\" /* ImageStatus.Skipped */;\n        }\n        else {\n            _a = renderImages(canvas, context), geometry = _a[0], text = _a[1];\n        }\n    }\n    return { winding: winding, geometry: geometry, text: text };\n}\nfunction makeCanvasContext() {\n    var canvas = document.createElement('canvas');\n    canvas.width = 1;\n    canvas.height = 1;\n    return [canvas, canvas.getContext('2d')];\n}\nfunction isSupported(canvas, context) {\n    return !!(context && canvas.toDataURL);\n}\nfunction doesSupportWinding(context) {\n    // https://web.archive.org/web/20170825024655/http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // https://github.com/Modernizr/Modernizr/blob/master/feature-detects/canvas/winding.js\n    context.rect(0, 0, 10, 10);\n    context.rect(2, 2, 6, 6);\n    return !context.isPointInPath(5, 5, 'evenodd');\n}\nfunction renderImages(canvas, context) {\n    renderTextImage(canvas, context);\n    var textImage1 = canvasToString(canvas);\n    var textImage2 = canvasToString(canvas); // It's slightly faster to double-encode the text image\n    // Some browsers add a noise to the canvas: https://github.com/fingerprintjs/fingerprintjs/issues/791\n    // The canvas is excluded from the fingerprint in this case\n    if (textImage1 !== textImage2) {\n        return [\"unstable\" /* ImageStatus.Unstable */, \"unstable\" /* ImageStatus.Unstable */];\n    }\n    // Text is unstable:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/583\n    // https://github.com/fingerprintjs/fingerprintjs/issues/103\n    // Therefore it's extracted into a separate image.\n    renderGeometryImage(canvas, context);\n    var geometryImage = canvasToString(canvas);\n    return [geometryImage, textImage1];\n}\nfunction renderTextImage(canvas, context) {\n    // Resizing the canvas cleans it\n    canvas.width = 240;\n    canvas.height = 60;\n    context.textBaseline = 'alphabetic';\n    context.fillStyle = '#f60';\n    context.fillRect(100, 1, 62, 20);\n    context.fillStyle = '#069';\n    // It's important to use explicit built-in fonts in order to exclude the affect of font preferences\n    // (there is a separate entropy source for them).\n    context.font = '11pt \"Times New Roman\"';\n    // The choice of emojis has a gigantic impact on rendering performance (especially in FF).\n    // Some newer emojis cause it to slow down 50-200 times.\n    // There must be no text to the right of the emoji, see https://github.com/fingerprintjs/fingerprintjs/issues/574\n    // A bare emoji shouldn't be used because the canvas will change depending on the script encoding:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/66\n    // Escape sequence shouldn't be used too because Terser will turn it into a bare unicode.\n    var printedText = \"Cwm fjordbank gly \".concat(String.fromCharCode(55357, 56835) /* 😃 */);\n    context.fillText(printedText, 2, 15);\n    context.fillStyle = 'rgba(102, 204, 0, 0.2)';\n    context.font = '18pt Arial';\n    context.fillText(printedText, 4, 45);\n}\nfunction renderGeometryImage(canvas, context) {\n    // Resizing the canvas cleans it\n    canvas.width = 122;\n    canvas.height = 110;\n    // Canvas blending\n    // https://web.archive.org/web/**************/http://blogs.adobe.com/webplatform/2013/01/28/blending-features-in-canvas/\n    // http://jsfiddle.net/NDYV8/16/\n    context.globalCompositeOperation = 'multiply';\n    for (var _i = 0, _a = [\n        ['#f2f', 40, 40],\n        ['#2ff', 80, 40],\n        ['#ff2', 60, 80],\n    ]; _i < _a.length; _i++) {\n        var _b = _a[_i], color = _b[0], x = _b[1], y = _b[2];\n        context.fillStyle = color;\n        context.beginPath();\n        context.arc(x, y, 40, 0, Math.PI * 2, true);\n        context.closePath();\n        context.fill();\n    }\n    // Canvas winding\n    // https://web.archive.org/web/20130913061632/http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // http://jsfiddle.net/NDYV8/19/\n    context.fillStyle = '#f9c';\n    context.arc(60, 60, 60, 0, Math.PI * 2, true);\n    context.arc(60, 60, 20, 0, Math.PI * 2, true);\n    context.fill('evenodd');\n}\nfunction canvasToString(canvas) {\n    return canvas.toDataURL();\n}\n/**\n * Checks if the current browser is known for applying anti-fingerprinting measures in all or some critical modes\n */\nfunction doesBrowserPerformAntifingerprinting() {\n    // Safari 17\n    return isWebKit() && isWebKit616OrNewer() && isSafariWebKit();\n}\n\n/**\n * This is a crude and primitive touch screen detection. It's not possible to currently reliably detect the availability\n * of a touch screen with a JS, without actually subscribing to a touch event.\n *\n * @see http://www.stucox.com/blog/you-cant-detect-a-touchscreen/\n * @see https://github.com/Modernizr/Modernizr/issues/548\n */\nfunction getTouchSupport() {\n    var n = navigator;\n    var maxTouchPoints = 0;\n    var touchEvent;\n    if (n.maxTouchPoints !== undefined) {\n        maxTouchPoints = toInt(n.maxTouchPoints);\n    }\n    else if (n.msMaxTouchPoints !== undefined) {\n        maxTouchPoints = n.msMaxTouchPoints;\n    }\n    try {\n        document.createEvent('TouchEvent');\n        touchEvent = true;\n    }\n    catch (_a) {\n        touchEvent = false;\n    }\n    var touchStart = 'ontouchstart' in window;\n    return {\n        maxTouchPoints: maxTouchPoints,\n        touchEvent: touchEvent,\n        touchStart: touchStart,\n    };\n}\n\nfunction getOsCpu() {\n    return navigator.oscpu;\n}\n\nfunction getLanguages() {\n    var n = navigator;\n    var result = [];\n    var language = n.language || n.userLanguage || n.browserLanguage || n.systemLanguage;\n    if (language !== undefined) {\n        result.push([language]);\n    }\n    if (Array.isArray(n.languages)) {\n        // Starting from Chromium 86, there is only a single value in `navigator.language` in Incognito mode:\n        // the value of `navigator.language`. Therefore the value is ignored in this browser.\n        if (!(isChromium() && isChromium86OrNewer())) {\n            result.push(n.languages);\n        }\n    }\n    else if (typeof n.languages === 'string') {\n        var languages = n.languages;\n        if (languages) {\n            result.push(languages.split(','));\n        }\n    }\n    return result;\n}\n\nfunction getColorDepth() {\n    return window.screen.colorDepth;\n}\n\nfunction getDeviceMemory() {\n    // `navigator.deviceMemory` is a string containing a number in some unidentified cases\n    return replaceNaN(toFloat(navigator.deviceMemory), undefined);\n}\n\n/**\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * The window resolution is always the document size in private mode of Safari 17,\n * so the window resolution is not used in Safari 17.\n */\nfunction getScreenResolution() {\n    if (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) {\n        return undefined;\n    }\n    return getUnstableScreenResolution();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableScreenResolution() {\n    var s = screen;\n    // Some browsers return screen resolution as strings, e.g. \"1200\", instead of a number, e.g. 1200.\n    // I suspect it's done by certain plugins that randomize browser properties to prevent fingerprinting.\n    // Some browsers even return  screen resolution as not numbers.\n    var parseDimension = function (value) { return replaceNaN(toInt(value), null); };\n    var dimensions = [parseDimension(s.width), parseDimension(s.height)];\n    dimensions.sort().reverse();\n    return dimensions;\n}\n\nvar screenFrameCheckInterval = 2500;\nvar roundingPrecision = 10;\n// The type is readonly to protect from unwanted mutations\nvar screenFrameBackup;\nvar screenFrameSizeTimeoutId;\n/**\n * Starts watching the screen frame size. When a non-zero size appears, the size is saved and the watch is stopped.\n * Later, when `getScreenFrame` runs, it will return the saved non-zero size if the current size is null.\n *\n * This trick is required to mitigate the fact that the screen frame turns null in some cases.\n * See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n */\nfunction watchScreenFrame() {\n    if (screenFrameSizeTimeoutId !== undefined) {\n        return;\n    }\n    var checkScreenFrame = function () {\n        var frameSize = getCurrentScreenFrame();\n        if (isFrameSizeNull(frameSize)) {\n            screenFrameSizeTimeoutId = setTimeout(checkScreenFrame, screenFrameCheckInterval);\n        }\n        else {\n            screenFrameBackup = frameSize;\n            screenFrameSizeTimeoutId = undefined;\n        }\n    };\n    checkScreenFrame();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableScreenFrame() {\n    var _this = this;\n    watchScreenFrame();\n    return function () { return __awaiter(_this, void 0, void 0, function () {\n        var frameSize;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    frameSize = getCurrentScreenFrame();\n                    if (!isFrameSizeNull(frameSize)) return [3 /*break*/, 2];\n                    if (screenFrameBackup) {\n                        return [2 /*return*/, __spreadArray([], screenFrameBackup, true)];\n                    }\n                    if (!getFullscreenElement()) return [3 /*break*/, 2];\n                    // Some browsers set the screen frame to zero when programmatic fullscreen is on.\n                    // There is a chance of getting a non-zero frame after exiting the fullscreen.\n                    // See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n                    return [4 /*yield*/, exitFullscreen()];\n                case 1:\n                    // Some browsers set the screen frame to zero when programmatic fullscreen is on.\n                    // There is a chance of getting a non-zero frame after exiting the fullscreen.\n                    // See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n                    _a.sent();\n                    frameSize = getCurrentScreenFrame();\n                    _a.label = 2;\n                case 2:\n                    if (!isFrameSizeNull(frameSize)) {\n                        screenFrameBackup = frameSize;\n                    }\n                    return [2 /*return*/, frameSize];\n            }\n        });\n    }); };\n}\n/**\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n *\n * Sometimes the available screen resolution changes a bit, e.g. 1900x1440 → 1900x1439. A possible reason: macOS Dock\n * shrinks to fit more icons when there is too little space. The rounding is used to mitigate the difference.\n *\n * The frame width is always 0 in private mode of Safari 17, so the frame is not used in Safari 17.\n */\nfunction getScreenFrame() {\n    var _this = this;\n    if (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) {\n        return function () { return Promise.resolve(undefined); };\n    }\n    var screenFrameGetter = getUnstableScreenFrame();\n    return function () { return __awaiter(_this, void 0, void 0, function () {\n        var frameSize, processSize;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0: return [4 /*yield*/, screenFrameGetter()];\n                case 1:\n                    frameSize = _a.sent();\n                    processSize = function (sideSize) { return (sideSize === null ? null : round(sideSize, roundingPrecision)); };\n                    // It might look like I don't know about `for` and `map`.\n                    // In fact, such code is used to avoid TypeScript issues without using `as`.\n                    return [2 /*return*/, [processSize(frameSize[0]), processSize(frameSize[1]), processSize(frameSize[2]), processSize(frameSize[3])]];\n            }\n        });\n    }); };\n}\nfunction getCurrentScreenFrame() {\n    var s = screen;\n    // Some browsers return screen resolution as strings, e.g. \"1200\", instead of a number, e.g. 1200.\n    // I suspect it's done by certain plugins that randomize browser properties to prevent fingerprinting.\n    //\n    // Some browsers (IE, Edge ≤18) don't provide `screen.availLeft` and `screen.availTop`. The property values are\n    // replaced with 0 in such cases to not lose the entropy from `screen.availWidth` and `screen.availHeight`.\n    return [\n        replaceNaN(toFloat(s.availTop), null),\n        replaceNaN(toFloat(s.width) - toFloat(s.availWidth) - replaceNaN(toFloat(s.availLeft), 0), null),\n        replaceNaN(toFloat(s.height) - toFloat(s.availHeight) - replaceNaN(toFloat(s.availTop), 0), null),\n        replaceNaN(toFloat(s.availLeft), null),\n    ];\n}\nfunction isFrameSizeNull(frameSize) {\n    for (var i = 0; i < 4; ++i) {\n        if (frameSize[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction getHardwareConcurrency() {\n    // sometimes hardware concurrency is a string\n    return replaceNaN(toInt(navigator.hardwareConcurrency), undefined);\n}\n\nfunction getTimezone() {\n    var _a;\n    var DateTimeFormat = (_a = window.Intl) === null || _a === void 0 ? void 0 : _a.DateTimeFormat;\n    if (DateTimeFormat) {\n        var timezone = new DateTimeFormat().resolvedOptions().timeZone;\n        if (timezone) {\n            return timezone;\n        }\n    }\n    // For browsers that don't support timezone names\n    // The minus is intentional because the JS offset is opposite to the real offset\n    var offset = -getTimezoneOffset();\n    return \"UTC\".concat(offset >= 0 ? '+' : '').concat(offset);\n}\nfunction getTimezoneOffset() {\n    var currentYear = new Date().getFullYear();\n    // The timezone offset may change over time due to daylight saving time (DST) shifts.\n    // The non-DST timezone offset is used as the result timezone offset.\n    // Since the DST season differs in the northern and the southern hemispheres,\n    // both January and July timezones offsets are considered.\n    return Math.max(\n    // `getTimezoneOffset` returns a number as a string in some unidentified cases\n    toFloat(new Date(currentYear, 0, 1).getTimezoneOffset()), toFloat(new Date(currentYear, 6, 1).getTimezoneOffset()));\n}\n\nfunction getSessionStorage() {\n    try {\n        return !!window.sessionStorage;\n    }\n    catch (error) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\n// https://bugzilla.mozilla.org/show_bug.cgi?id=781447\nfunction getLocalStorage() {\n    try {\n        return !!window.localStorage;\n    }\n    catch (e) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\nfunction getIndexedDB() {\n    // IE and Edge don't allow accessing indexedDB in private mode, therefore IE and Edge will have different\n    // visitor identifier in normal and private modes.\n    if (isTrident() || isEdgeHTML()) {\n        return undefined;\n    }\n    try {\n        return !!window.indexedDB;\n    }\n    catch (e) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\nfunction getOpenDatabase() {\n    return !!window.openDatabase;\n}\n\nfunction getCpuClass() {\n    return navigator.cpuClass;\n}\n\nfunction getPlatform() {\n    // Android Chrome 86 and 87 and Android Firefox 80 and 84 don't mock the platform value when desktop mode is requested\n    var platform = navigator.platform;\n    // iOS mocks the platform value when desktop version is requested: https://github.com/fingerprintjs/fingerprintjs/issues/514\n    // iPad uses desktop mode by default since iOS 13\n    // The value is 'MacIntel' on M1 Macs\n    // The value is 'iPhone' on iPod Touch\n    if (platform === 'MacIntel') {\n        if (isWebKit() && !isDesktopWebKit()) {\n            return isIPad() ? 'iPad' : 'iPhone';\n        }\n    }\n    return platform;\n}\n\nfunction getVendor() {\n    return navigator.vendor || '';\n}\n\n/**\n * Checks for browser-specific (not engine specific) global variables to tell browsers with the same engine apart.\n * Only somewhat popular browsers are considered.\n */\nfunction getVendorFlavors() {\n    var flavors = [];\n    for (var _i = 0, _a = [\n        // Blink and some browsers on iOS\n        'chrome',\n        // Safari on macOS\n        'safari',\n        // Chrome on iOS (checked in 85 on 13 and 87 on 14)\n        '__crWeb',\n        '__gCrWeb',\n        // Yandex Browser on iOS, macOS and Android (checked in 21.2 on iOS 14, macOS and Android)\n        'yandex',\n        // Yandex Browser on iOS (checked in 21.2 on 14)\n        '__yb',\n        '__ybro',\n        // Firefox on iOS (checked in 32 on 14)\n        '__firefox__',\n        // Edge on iOS (checked in 46 on 14)\n        '__edgeTrackingPreventionStatistics',\n        'webkit',\n        // Opera Touch on iOS (checked in 2.6 on 14)\n        'oprt',\n        // Samsung Internet on Android (checked in 11.1)\n        'samsungAr',\n        // UC Browser on Android (checked in 12.10 and 13.0)\n        'ucweb',\n        'UCShellJava',\n        // Puffin on Android (checked in 9.0)\n        'puffinDevice',\n        // UC on iOS and Opera on Android have no specific global variables\n        // Edge for Android isn't checked\n    ]; _i < _a.length; _i++) {\n        var key = _a[_i];\n        var value = window[key];\n        if (value && typeof value === 'object') {\n            flavors.push(key);\n        }\n    }\n    return flavors.sort();\n}\n\n/**\n * navigator.cookieEnabled cannot detect custom or nuanced cookie blocking configurations. For example, when blocking\n * cookies via the Advanced Privacy Settings in IE9, it always returns true. And there have been issues in the past with\n * site-specific exceptions. Don't rely on it.\n *\n * @see https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cookies.js Taken from here\n */\nfunction areCookiesEnabled() {\n    var d = document;\n    // Taken from here: https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cookies.js\n    // navigator.cookieEnabled cannot detect custom or nuanced cookie blocking configurations. For example, when blocking\n    // cookies via the Advanced Privacy Settings in IE9, it always returns true. And there have been issues in the past\n    // with site-specific exceptions. Don't rely on it.\n    // try..catch because some in situations `document.cookie` is exposed but throws a\n    // SecurityError if you try to access it; e.g. documents created from data URIs\n    // or in sandboxed iframes (depending on flags/context)\n    try {\n        // Create cookie\n        d.cookie = 'cookietest=1; SameSite=Strict;';\n        var result = d.cookie.indexOf('cookietest=') !== -1;\n        // Delete cookie\n        d.cookie = 'cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT';\n        return result;\n    }\n    catch (e) {\n        return false;\n    }\n}\n\n/**\n * Only single element selector are supported (no operators like space, +, >, etc).\n * `embed` and `position: fixed;` will be considered as blocked anyway because it always has no offsetParent.\n * Avoid `iframe` and anything with `[src=]` because they produce excess HTTP requests.\n *\n * The \"inappropriate\" selectors are obfuscated. See https://github.com/fingerprintjs/fingerprintjs/issues/734.\n * A function is used instead of a plain object to help tree-shaking.\n *\n * The function code is generated automatically. See docs/content_blockers.md to learn how to make the list.\n */\nfunction getFilters() {\n    var fromB64 = atob; // Just for better minification\n    return {\n        abpIndo: [\n            '#Iklan-Melayang',\n            '#Kolom-Iklan-728',\n            '#SidebarIklan-wrapper',\n            '[title=\"ALIENBOLA\" i]',\n            fromB64('I0JveC1CYW5uZXItYWRz'),\n        ],\n        abpvn: ['.quangcao', '#mobileCatfish', fromB64('LmNsb3NlLWFkcw=='), '[id^=\"bn_bottom_fixed_\"]', '#pmadv'],\n        adBlockFinland: [\n            '.mainostila',\n            fromB64('LnNwb25zb3JpdA=='),\n            '.ylamainos',\n            fromB64('YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd'),\n        ],\n        adBlockPersian: [\n            '#navbar_notice_50',\n            '.kadr',\n            'TABLE[width=\"140px\"]',\n            '#divAgahi',\n            fromB64('YVtocmVmXj0iaHR0cDovL2cxLnYuZndtcm0ubmV0L2FkLyJd'),\n        ],\n        adBlockWarningRemoval: [\n            '#adblock-honeypot',\n            '.adblocker-root',\n            '.wp_adblock_detect',\n            fromB64('LmhlYWRlci1ibG9ja2VkLWFk'),\n            fromB64('I2FkX2Jsb2NrZXI='),\n        ],\n        adGuardAnnoyances: [\n            '.hs-sosyal',\n            '#cookieconsentdiv',\n            'div[class^=\"app_gdpr\"]',\n            '.as-oil',\n            '[data-cypress=\"soft-push-notification-modal\"]',\n        ],\n        adGuardBase: [\n            '.BetterJsPopOverlay',\n            fromB64('I2FkXzMwMFgyNTA='),\n            fromB64('I2Jhbm5lcmZsb2F0MjI='),\n            fromB64('I2NhbXBhaWduLWJhbm5lcg=='),\n            fromB64('I0FkLUNvbnRlbnQ='),\n        ],\n        adGuardChinese: [\n            fromB64('LlppX2FkX2FfSA=='),\n            fromB64('YVtocmVmKj0iLmh0aGJldDM0LmNvbSJd'),\n            '#widget-quan',\n            fromB64('YVtocmVmKj0iLzg0OTkyMDIwLnh5eiJd'),\n            fromB64('YVtocmVmKj0iLjE5NTZobC5jb20vIl0='),\n        ],\n        adGuardFrench: [\n            '#pavePub',\n            fromB64('LmFkLWRlc2t0b3AtcmVjdGFuZ2xl'),\n            '.mobile_adhesion',\n            '.widgetadv',\n            fromB64('LmFkc19iYW4='),\n        ],\n        adGuardGerman: ['aside[data-portal-id=\"leaderboard\"]'],\n        adGuardJapanese: [\n            '#kauli_yad_1',\n            fromB64('YVtocmVmXj0iaHR0cDovL2FkMi50cmFmZmljZ2F0ZS5uZXQvIl0='),\n            fromB64('Ll9wb3BJbl9pbmZpbml0ZV9hZA=='),\n            fromB64('LmFkZ29vZ2xl'),\n            fromB64('Ll9faXNib29zdFJldHVybkFk'),\n        ],\n        adGuardMobile: [\n            fromB64('YW1wLWF1dG8tYWRz'),\n            fromB64('LmFtcF9hZA=='),\n            'amp-embed[type=\"24smi\"]',\n            '#mgid_iframe1',\n            fromB64('I2FkX2ludmlld19hcmVh'),\n        ],\n        adGuardRussian: [\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZC5sZXRtZWFkcy5jb20vIl0='),\n            fromB64('LnJlY2xhbWE='),\n            'div[id^=\"smi2adblock\"]',\n            fromB64('ZGl2W2lkXj0iQWRGb3hfYmFubmVyXyJd'),\n            '#psyduckpockeball',\n        ],\n        adGuardSocial: [\n            fromB64('YVtocmVmXj0iLy93d3cuc3R1bWJsZXVwb24uY29tL3N1Ym1pdD91cmw9Il0='),\n            fromB64('YVtocmVmXj0iLy90ZWxlZ3JhbS5tZS9zaGFyZS91cmw/Il0='),\n            '.etsy-tweet',\n            '#inlineShare',\n            '.popup-social',\n        ],\n        adGuardSpanishPortuguese: ['#barraPublicidade', '#Publicidade', '#publiEspecial', '#queTooltip', '.cnt-publi'],\n        adGuardTrackingProtection: [\n            '#qoo-counter',\n            fromB64('YVtocmVmXj0iaHR0cDovL2NsaWNrLmhvdGxvZy5ydS8iXQ=='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2hpdGNvdW50ZXIucnUvdG9wL3N0YXQucGhwIl0='),\n            fromB64('YVtocmVmXj0iaHR0cDovL3RvcC5tYWlsLnJ1L2p1bXAiXQ=='),\n            '#top100counter',\n        ],\n        adGuardTurkish: [\n            '#backkapat',\n            fromB64('I3Jla2xhbWk='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2Fkc2Vydi5vbnRlay5jb20udHIvIl0='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2l6bGVuemkuY29tL2NhbXBhaWduLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cDovL3d3dy5pbnN0YWxsYWRzLm5ldC8iXQ=='),\n        ],\n        bulgarian: [fromB64('dGQjZnJlZW5ldF90YWJsZV9hZHM='), '#ea_intext_div', '.lapni-pop-over', '#xenium_hot_offers'],\n        easyList: [\n            '.yb-floorad',\n            fromB64('LndpZGdldF9wb19hZHNfd2lkZ2V0'),\n            fromB64('LnRyYWZmaWNqdW5reS1hZA=='),\n            '.textad_headline',\n            fromB64('LnNwb25zb3JlZC10ZXh0LWxpbmtz'),\n        ],\n        easyListChina: [\n            fromB64('LmFwcGd1aWRlLXdyYXBbb25jbGljayo9ImJjZWJvcy5jb20iXQ=='),\n            fromB64('LmZyb250cGFnZUFkdk0='),\n            '#taotaole',\n            '#aafoot.top_box',\n            '.cfa_popup',\n        ],\n        easyListCookie: [\n            '.ezmob-footer',\n            '.cc-CookieWarning',\n            '[data-cookie-number]',\n            fromB64('LmF3LWNvb2tpZS1iYW5uZXI='),\n            '.sygnal24-gdpr-modal-wrap',\n        ],\n        easyListCzechSlovak: [\n            '#onlajny-stickers',\n            fromB64('I3Jla2xhbW5pLWJveA=='),\n            fromB64('LnJla2xhbWEtbWVnYWJvYXJk'),\n            '.sklik',\n            fromB64('W2lkXj0ic2tsaWtSZWtsYW1hIl0='),\n        ],\n        easyListDutch: [\n            fromB64('I2FkdmVydGVudGll'),\n            fromB64('I3ZpcEFkbWFya3RCYW5uZXJCbG9jaw=='),\n            '.adstekst',\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly94bHR1YmUubmwvY2xpY2svIl0='),\n            '#semilo-lrectangle',\n        ],\n        easyListGermany: [\n            '#SSpotIMPopSlider',\n            fromB64('LnNwb25zb3JsaW5rZ3J1ZW4='),\n            fromB64('I3dlcmJ1bmdza3k='),\n            fromB64('I3Jla2xhbWUtcmVjaHRzLW1pdHRl'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9iZDc0Mi5jb20vIl0='),\n        ],\n        easyListItaly: [\n            fromB64('LmJveF9hZHZfYW5udW5jaQ=='),\n            '.sb-box-pubbliredazionale',\n            fromB64('YVtocmVmXj0iaHR0cDovL2FmZmlsaWF6aW9uaWFkcy5zbmFpLml0LyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZHNlcnZlci5odG1sLml0LyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZmZpbGlhemlvbmlhZHMuc25haS5pdC8iXQ=='),\n        ],\n        easyListLithuania: [\n            fromB64('LnJla2xhbW9zX3RhcnBhcw=='),\n            fromB64('LnJla2xhbW9zX251b3JvZG9z'),\n            fromB64('aW1nW2FsdD0iUmVrbGFtaW5pcyBza3lkZWxpcyJd'),\n            fromB64('aW1nW2FsdD0iRGVkaWt1b3RpLmx0IHNlcnZlcmlhaSJd'),\n            fromB64('aW1nW2FsdD0iSG9zdGluZ2FzIFNlcnZlcmlhaS5sdCJd'),\n        ],\n        estonian: [fromB64('QVtocmVmKj0iaHR0cDovL3BheTRyZXN1bHRzMjQuZXUiXQ==')],\n        fanboyAnnoyances: ['#ac-lre-player', '.navigate-to-top', '#subscribe_popup', '.newsletter_holder', '#back-top'],\n        fanboyAntiFacebook: ['.util-bar-module-firefly-visible'],\n        fanboyEnhancedTrackers: [\n            '.open.pushModal',\n            '#issuem-leaky-paywall-articles-zero-remaining-nag',\n            '#sovrn_container',\n            'div[class$=\"-hide\"][zoompage-fontsize][style=\"display: block;\"]',\n            '.BlockNag__Card',\n        ],\n        fanboySocial: ['#FollowUs', '#meteored_share', '#social_follow', '.article-sharer', '.community__social-desc'],\n        frellwitSwedish: [\n            fromB64('YVtocmVmKj0iY2FzaW5vcHJvLnNlIl1bdGFyZ2V0PSJfYmxhbmsiXQ=='),\n            fromB64('YVtocmVmKj0iZG9rdG9yLXNlLm9uZWxpbmsubWUiXQ=='),\n            'article.category-samarbete',\n            fromB64('ZGl2LmhvbGlkQWRz'),\n            'ul.adsmodern',\n        ],\n        greekAdBlock: [\n            fromB64('QVtocmVmKj0iYWRtYW4ub3RlbmV0LmdyL2NsaWNrPyJd'),\n            fromB64('QVtocmVmKj0iaHR0cDovL2F4aWFiYW5uZXJzLmV4b2R1cy5nci8iXQ=='),\n            fromB64('QVtocmVmKj0iaHR0cDovL2ludGVyYWN0aXZlLmZvcnRobmV0LmdyL2NsaWNrPyJd'),\n            'DIV.agores300',\n            'TABLE.advright',\n        ],\n        hungarian: [\n            '#cemp_doboz',\n            '.optimonk-iframe-container',\n            fromB64('LmFkX19tYWlu'),\n            fromB64('W2NsYXNzKj0iR29vZ2xlQWRzIl0='),\n            '#hirdetesek_box',\n        ],\n        iDontCareAboutCookies: [\n            '.alert-info[data-block-track*=\"CookieNotice\"]',\n            '.ModuleTemplateCookieIndicator',\n            '.o--cookies--container',\n            '#cookies-policy-sticky',\n            '#stickyCookieBar',\n        ],\n        icelandicAbp: [fromB64('QVtocmVmXj0iL2ZyYW1ld29yay9yZXNvdXJjZXMvZm9ybXMvYWRzLmFzcHgiXQ==')],\n        latvian: [\n            fromB64('YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiAxMjBweDsgaGVpZ2h0O' +\n                'iA0MHB4OyBvdmVyZmxvdzogaGlkZGVuOyBwb3NpdGlvbjogcmVsYXRpdmU7Il0='),\n            fromB64('YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6I' +\n                'DMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ=='),\n        ],\n        listKr: [\n            fromB64('YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0='),\n            fromB64('I2xpdmVyZUFkV3JhcHBlcg=='),\n            fromB64('YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=='),\n            fromB64('aW5zLmZhc3R2aWV3LWFk'),\n            '.revenue_unit_item.dable',\n        ],\n        listeAr: [\n            fromB64('LmdlbWluaUxCMUFk'),\n            '.right-and-left-sponsers',\n            fromB64('YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=='),\n            fromB64('YVtocmVmKj0iYm9vcmFxLm9yZyJd'),\n            fromB64('YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd'),\n        ],\n        listeFr: [\n            fromB64('YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=='),\n            fromB64('I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=='),\n            fromB64('YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0='),\n            '.site-pub-interstitiel',\n            'div[id^=\"crt-\"][data-criteo-id]',\n        ],\n        officialPolish: [\n            '#ceneo-placeholder-ceneo-12',\n            fromB64('W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=='),\n            fromB64('YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=='),\n            fromB64('ZGl2I3NrYXBpZWNfYWQ='),\n        ],\n        ro: [\n            fromB64('YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0='),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd'),\n            'a[href^=\"/url/\"]',\n        ],\n        ruAd: [\n            fromB64('YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd'),\n            fromB64('YVtocmVmKj0iLy91dGltZy5ydS8iXQ=='),\n            fromB64('YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0='),\n            '#pgeldiz',\n            '.yandex-rtb-block',\n        ],\n        thaiAds: [\n            'a[href*=macau-uta-popup]',\n            fromB64('I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=='),\n            fromB64('LmFkczMwMHM='),\n            '.bumq',\n            '.img-kosana',\n        ],\n        webAnnoyancesUltralist: [\n            '#mod-social-share-2',\n            '#social-tools',\n            fromB64('LmN0cGwtZnVsbGJhbm5lcg=='),\n            '.zergnet-recommend',\n            '.yt.btn-link.btn-md.btn',\n        ],\n    };\n}\n/**\n * The order of the returned array means nothing (it's always sorted alphabetically).\n *\n * Notice that the source is slightly unstable.\n * Safari provides a 2-taps way to disable all content blockers on a page temporarily.\n * Also content blockers can be disabled permanently for a domain, but it requires 4 taps.\n * So empty array shouldn't be treated as \"no blockers\", it should be treated as \"no signal\".\n * If you are a website owner, don't make your visitors want to disable content blockers.\n */\nfunction getDomBlockers(_a) {\n    var _b = _a === void 0 ? {} : _a, debug = _b.debug;\n    return __awaiter(this, void 0, void 0, function () {\n        var filters, filterNames, allSelectors, blockedSelectors, activeBlockers;\n        var _c;\n        return __generator(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    if (!isApplicable()) {\n                        return [2 /*return*/, undefined];\n                    }\n                    filters = getFilters();\n                    filterNames = Object.keys(filters);\n                    allSelectors = (_c = []).concat.apply(_c, filterNames.map(function (filterName) { return filters[filterName]; }));\n                    return [4 /*yield*/, getBlockedSelectors(allSelectors)];\n                case 1:\n                    blockedSelectors = _d.sent();\n                    if (debug) {\n                        printDebug(filters, blockedSelectors);\n                    }\n                    activeBlockers = filterNames.filter(function (filterName) {\n                        var selectors = filters[filterName];\n                        var blockedCount = countTruthy(selectors.map(function (selector) { return blockedSelectors[selector]; }));\n                        return blockedCount > selectors.length * 0.6;\n                    });\n                    activeBlockers.sort();\n                    return [2 /*return*/, activeBlockers];\n            }\n        });\n    });\n}\nfunction isApplicable() {\n    // Safari (desktop and mobile) and all Android browsers keep content blockers in both regular and private mode\n    return isWebKit() || isAndroid();\n}\nfunction getBlockedSelectors(selectors) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function () {\n        var d, root, elements, blockedSelectors, i, element, holder, i;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    d = document;\n                    root = d.createElement('div');\n                    elements = new Array(selectors.length);\n                    blockedSelectors = {} // Set() isn't used just in case somebody need older browser support\n                    ;\n                    forceShow(root);\n                    // First create all elements that can be blocked. If the DOM steps below are done in a single cycle,\n                    // browser will alternate tree modification and layout reading, that is very slow.\n                    for (i = 0; i < selectors.length; ++i) {\n                        element = selectorToElement(selectors[i]);\n                        if (element.tagName === 'DIALOG') {\n                            element.show();\n                        }\n                        holder = d.createElement('div') // Protects from unwanted effects of `+` and `~` selectors of filters\n                        ;\n                        forceShow(holder);\n                        holder.appendChild(element);\n                        root.appendChild(holder);\n                        elements[i] = element;\n                    }\n                    _b.label = 1;\n                case 1:\n                    if (!!d.body) return [3 /*break*/, 3];\n                    return [4 /*yield*/, wait(50)];\n                case 2:\n                    _b.sent();\n                    return [3 /*break*/, 1];\n                case 3:\n                    d.body.appendChild(root);\n                    try {\n                        // Then check which of the elements are blocked\n                        for (i = 0; i < selectors.length; ++i) {\n                            if (!elements[i].offsetParent) {\n                                blockedSelectors[selectors[i]] = true;\n                            }\n                        }\n                    }\n                    finally {\n                        // Then remove the elements\n                        (_a = root.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(root);\n                    }\n                    return [2 /*return*/, blockedSelectors];\n            }\n        });\n    });\n}\nfunction forceShow(element) {\n    element.style.setProperty('visibility', 'hidden', 'important');\n    element.style.setProperty('display', 'block', 'important');\n}\nfunction printDebug(filters, blockedSelectors) {\n    var message = 'DOM blockers debug:\\n```';\n    for (var _i = 0, _a = Object.keys(filters); _i < _a.length; _i++) {\n        var filterName = _a[_i];\n        message += \"\\n\".concat(filterName, \":\");\n        for (var _b = 0, _c = filters[filterName]; _b < _c.length; _b++) {\n            var selector = _c[_b];\n            message += \"\\n  \".concat(blockedSelectors[selector] ? '🚫' : '➡️', \" \").concat(selector);\n        }\n    }\n    // console.log is ok here because it's under a debug clause\n    // eslint-disable-next-line no-console\n    console.log(\"\".concat(message, \"\\n```\"));\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/color-gamut\n */\nfunction getColorGamut() {\n    // rec2020 includes p3 and p3 includes srgb\n    for (var _i = 0, _a = ['rec2020', 'p3', 'srgb']; _i < _a.length; _i++) {\n        var gamut = _a[_i];\n        if (matchMedia(\"(color-gamut: \".concat(gamut, \")\")).matches) {\n            return gamut;\n        }\n    }\n    return undefined;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/inverted-colors\n */\nfunction areColorsInverted() {\n    if (doesMatch$5('inverted')) {\n        return true;\n    }\n    if (doesMatch$5('none')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$5(value) {\n    return matchMedia(\"(inverted-colors: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/forced-colors\n */\nfunction areColorsForced() {\n    if (doesMatch$4('active')) {\n        return true;\n    }\n    if (doesMatch$4('none')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$4(value) {\n    return matchMedia(\"(forced-colors: \".concat(value, \")\")).matches;\n}\n\nvar maxValueToCheck = 100;\n/**\n * If the display is monochrome (e.g. black&white), the value will be ≥0 and will mean the number of bits per pixel.\n * If the display is not monochrome, the returned value will be 0.\n * If the browser doesn't support this feature, the returned value will be undefined.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/monochrome\n */\nfunction getMonochromeDepth() {\n    if (!matchMedia('(min-monochrome: 0)').matches) {\n        // The media feature isn't supported by the browser\n        return undefined;\n    }\n    // A variation of binary search algorithm can be used here.\n    // But since expected values are very small (≤10), there is no sense in adding the complexity.\n    for (var i = 0; i <= maxValueToCheck; ++i) {\n        if (matchMedia(\"(max-monochrome: \".concat(i, \")\")).matches) {\n            return i;\n        }\n    }\n    throw new Error('Too high value');\n}\n\n/**\n * @see https://www.w3.org/TR/mediaqueries-5/#prefers-contrast\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-contrast\n */\nfunction getContrastPreference() {\n    if (doesMatch$3('no-preference')) {\n        return 0 /* ContrastPreference.None */;\n    }\n    // The sources contradict on the keywords. Probably 'high' and 'low' will never be implemented.\n    // Need to check it when all browsers implement the feature.\n    if (doesMatch$3('high') || doesMatch$3('more')) {\n        return 1 /* ContrastPreference.More */;\n    }\n    if (doesMatch$3('low') || doesMatch$3('less')) {\n        return -1 /* ContrastPreference.Less */;\n    }\n    if (doesMatch$3('forced')) {\n        return 10 /* ContrastPreference.ForcedColors */;\n    }\n    return undefined;\n}\nfunction doesMatch$3(value) {\n    return matchMedia(\"(prefers-contrast: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-motion\n */\nfunction isMotionReduced() {\n    if (doesMatch$2('reduce')) {\n        return true;\n    }\n    if (doesMatch$2('no-preference')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$2(value) {\n    return matchMedia(\"(prefers-reduced-motion: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-transparency\n */\nfunction isTransparencyReduced() {\n    if (doesMatch$1('reduce')) {\n        return true;\n    }\n    if (doesMatch$1('no-preference')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$1(value) {\n    return matchMedia(\"(prefers-reduced-transparency: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://www.w3.org/TR/mediaqueries-5/#dynamic-range\n */\nfunction isHDR() {\n    if (doesMatch('high')) {\n        return true;\n    }\n    if (doesMatch('standard')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch(value) {\n    return matchMedia(\"(dynamic-range: \".concat(value, \")\")).matches;\n}\n\nvar M = Math; // To reduce the minified code size\nvar fallbackFn = function () { return 0; };\n/**\n * @see https://gitlab.torproject.org/legacy/trac/-/issues/13018\n * @see https://bugzilla.mozilla.org/show_bug.cgi?id=531915\n */\nfunction getMathFingerprint() {\n    // Native operations\n    var acos = M.acos || fallbackFn;\n    var acosh = M.acosh || fallbackFn;\n    var asin = M.asin || fallbackFn;\n    var asinh = M.asinh || fallbackFn;\n    var atanh = M.atanh || fallbackFn;\n    var atan = M.atan || fallbackFn;\n    var sin = M.sin || fallbackFn;\n    var sinh = M.sinh || fallbackFn;\n    var cos = M.cos || fallbackFn;\n    var cosh = M.cosh || fallbackFn;\n    var tan = M.tan || fallbackFn;\n    var tanh = M.tanh || fallbackFn;\n    var exp = M.exp || fallbackFn;\n    var expm1 = M.expm1 || fallbackFn;\n    var log1p = M.log1p || fallbackFn;\n    // Operation polyfills\n    var powPI = function (value) { return M.pow(M.PI, value); };\n    var acoshPf = function (value) { return M.log(value + M.sqrt(value * value - 1)); };\n    var asinhPf = function (value) { return M.log(value + M.sqrt(value * value + 1)); };\n    var atanhPf = function (value) { return M.log((1 + value) / (1 - value)) / 2; };\n    var sinhPf = function (value) { return M.exp(value) - 1 / M.exp(value) / 2; };\n    var coshPf = function (value) { return (M.exp(value) + 1 / M.exp(value)) / 2; };\n    var expm1Pf = function (value) { return M.exp(value) - 1; };\n    var tanhPf = function (value) { return (M.exp(2 * value) - 1) / (M.exp(2 * value) + 1); };\n    var log1pPf = function (value) { return M.log(1 + value); };\n    // Note: constant values are empirical\n    return {\n        acos: acos(0.123124234234234242),\n        acosh: acosh(1e308),\n        acoshPf: acoshPf(1e154),\n        asin: asin(0.123124234234234242),\n        asinh: asinh(1),\n        asinhPf: asinhPf(1),\n        atanh: atanh(0.5),\n        atanhPf: atanhPf(0.5),\n        atan: atan(0.5),\n        sin: sin(-1e300),\n        sinh: sinh(1),\n        sinhPf: sinhPf(1),\n        cos: cos(10.000000000123),\n        cosh: cosh(1),\n        coshPf: coshPf(1),\n        tan: tan(-1e300),\n        tanh: tanh(1),\n        tanhPf: tanhPf(1),\n        exp: exp(1),\n        expm1: expm1(1),\n        expm1Pf: expm1Pf(1),\n        log1p: log1p(10),\n        log1pPf: log1pPf(10),\n        powPI: powPI(-100),\n    };\n}\n\n/**\n * We use m or w because these two characters take up the maximum width.\n * Also there are a couple of ligatures.\n */\nvar defaultText = 'mmMwWLliI0fiflO&1';\n/**\n * Settings of text blocks to measure. The keys are random but persistent words.\n */\nvar presets = {\n    /**\n     * The default font. User can change it in desktop Chrome, desktop Firefox, IE 11,\n     * Android Chrome (but only when the size is ≥ than the default) and Android Firefox.\n     */\n    default: [],\n    /** OS font on macOS. User can change its size and weight. Applies after Safari restart. */\n    apple: [{ font: '-apple-system-body' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    serif: [{ fontFamily: 'serif' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    sans: [{ fontFamily: 'sans-serif' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    mono: [{ fontFamily: 'monospace' }],\n    /**\n     * Check the smallest allowed font size. User can change it in desktop Chrome, desktop Firefox and desktop Safari.\n     * The height can be 0 in Chrome on a retina display.\n     */\n    min: [{ fontSize: '1px' }],\n    /** Tells one OS from another in desktop Chrome. */\n    system: [{ fontFamily: 'system-ui' }],\n};\n/**\n * The result is a dictionary of the width of the text samples.\n * Heights aren't included because they give no extra entropy and are unstable.\n *\n * The result is very stable in IE 11, Edge 18 and Safari 14.\n * The result changes when the OS pixel density changes in Chromium 87. The real pixel density is required to solve,\n * but seems like it's impossible: https://stackoverflow.com/q/1713771/1118709.\n * The \"min\" and the \"mono\" (only on Windows) value may change when the page is zoomed in Firefox 87.\n */\nfunction getFontPreferences() {\n    return withNaturalFonts(function (document, container) {\n        var elements = {};\n        var sizes = {};\n        // First create all elements to measure. If the DOM steps below are done in a single cycle,\n        // browser will alternate tree modification and layout reading, that is very slow.\n        for (var _i = 0, _a = Object.keys(presets); _i < _a.length; _i++) {\n            var key = _a[_i];\n            var _b = presets[key], _c = _b[0], style = _c === void 0 ? {} : _c, _d = _b[1], text = _d === void 0 ? defaultText : _d;\n            var element = document.createElement('span');\n            element.textContent = text;\n            element.style.whiteSpace = 'nowrap';\n            for (var _e = 0, _f = Object.keys(style); _e < _f.length; _e++) {\n                var name_1 = _f[_e];\n                var value = style[name_1];\n                if (value !== undefined) {\n                    element.style[name_1] = value;\n                }\n            }\n            elements[key] = element;\n            container.append(document.createElement('br'), element);\n        }\n        // Then measure the created elements\n        for (var _g = 0, _h = Object.keys(presets); _g < _h.length; _g++) {\n            var key = _h[_g];\n            sizes[key] = elements[key].getBoundingClientRect().width;\n        }\n        return sizes;\n    });\n}\n/**\n * Creates a DOM environment that provides the most natural font available, including Android OS font.\n * Measurements of the elements are zoom-independent.\n * Don't put a content to measure inside an absolutely positioned element.\n */\nfunction withNaturalFonts(action, containerWidthPx) {\n    if (containerWidthPx === void 0) { containerWidthPx = 4000; }\n    /*\n     * Requirements for Android Chrome to apply the system font size to a text inside an iframe:\n     * - The iframe mustn't have a `display: none;` style;\n     * - The text mustn't be positioned absolutely;\n     * - The text block must be wide enough.\n     *   2560px on some devices in portrait orientation for the biggest font size option (32px);\n     * - There must be much enough text to form a few lines (I don't know the exact numbers);\n     * - The text must have the `text-size-adjust: none` style. Otherwise the text will scale in \"Desktop site\" mode;\n     *\n     * Requirements for Android Firefox to apply the system font size to a text inside an iframe:\n     * - The iframe document must have a header: `<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />`.\n     *   The only way to set it is to use the `srcdoc` attribute of the iframe;\n     * - The iframe content must get loaded before adding extra content with JavaScript;\n     *\n     * https://example.com as the iframe target always inherits Android font settings so it can be used as a reference.\n     *\n     * Observations on how page zoom affects the measurements:\n     * - macOS Safari 11.1, 12.1, 13.1, 14.0: zoom reset + offsetWidth = 100% reliable;\n     * - macOS Safari 11.1, 12.1, 13.1, 14.0: zoom reset + getBoundingClientRect = 100% reliable;\n     * - macOS Safari 14.0: offsetWidth = 5% fluctuation;\n     * - macOS Safari 14.0: getBoundingClientRect = 5% fluctuation;\n     * - iOS Safari 9, 10, 11.0, 12.0: haven't found a way to zoom a page (pinch doesn't change layout);\n     * - iOS Safari 13.1, 14.0: zoom reset + offsetWidth = 100% reliable;\n     * - iOS Safari 13.1, 14.0: zoom reset + getBoundingClientRect = 100% reliable;\n     * - iOS Safari 14.0: offsetWidth = 100% reliable;\n     * - iOS Safari 14.0: getBoundingClientRect = 100% reliable;\n     * - Chrome 42, 65, 80, 87: zoom 1/devicePixelRatio + offsetWidth = 1px fluctuation;\n     * - Chrome 42, 65, 80, 87: zoom 1/devicePixelRatio + getBoundingClientRect = 100% reliable;\n     * - Chrome 87: offsetWidth = 1px fluctuation;\n     * - Chrome 87: getBoundingClientRect = 0.7px fluctuation;\n     * - Firefox 48, 51: offsetWidth = 10% fluctuation;\n     * - Firefox 48, 51: getBoundingClientRect = 10% fluctuation;\n     * - Firefox 52, 53, 57, 62, 66, 67, 68, 71, 75, 80, 84: offsetWidth = width 100% reliable, height 10% fluctuation;\n     * - Firefox 52, 53, 57, 62, 66, 67, 68, 71, 75, 80, 84: getBoundingClientRect = width 100% reliable, height 10%\n     *   fluctuation;\n     * - Android Chrome 86: haven't found a way to zoom a page (pinch doesn't change layout);\n     * - Android Firefox 84: font size in accessibility settings changes all the CSS sizes, but offsetWidth and\n     *   getBoundingClientRect keep measuring with regular units, so the size reflects the font size setting and doesn't\n     *   fluctuate;\n     * - IE 11, Edge 18: zoom 1/devicePixelRatio + offsetWidth = 100% reliable;\n     * - IE 11, Edge 18: zoom 1/devicePixelRatio + getBoundingClientRect = reflects the zoom level;\n     * - IE 11, Edge 18: offsetWidth = 100% reliable;\n     * - IE 11, Edge 18: getBoundingClientRect = 100% reliable;\n     */\n    return withIframe(function (_, iframeWindow) {\n        var iframeDocument = iframeWindow.document;\n        var iframeBody = iframeDocument.body;\n        var bodyStyle = iframeBody.style;\n        bodyStyle.width = \"\".concat(containerWidthPx, \"px\");\n        bodyStyle.webkitTextSizeAdjust = bodyStyle.textSizeAdjust = 'none';\n        // See the big comment above\n        if (isChromium()) {\n            iframeBody.style.zoom = \"\".concat(1 / iframeWindow.devicePixelRatio);\n        }\n        else if (isWebKit()) {\n            iframeBody.style.zoom = 'reset';\n        }\n        // See the big comment above\n        var linesOfText = iframeDocument.createElement('div');\n        linesOfText.textContent = __spreadArray([], Array((containerWidthPx / 20) << 0), true).map(function () { return 'word'; }).join(' ');\n        iframeBody.appendChild(linesOfText);\n        return action(iframeDocument, iframeBody);\n    }, '<!doctype html><html><head><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">');\n}\n\nfunction isPdfViewerEnabled() {\n    return navigator.pdfViewerEnabled;\n}\n\n/**\n * Unlike most other architectures, on x86/x86-64 when floating-point instructions\n * have no NaN arguments, but produce NaN output, the output NaN has sign bit set.\n * We use it to distinguish x86/x86-64 from other architectures, by doing subtraction\n * of two infinities (must produce NaN per IEEE 754 standard).\n *\n * See https://codebrowser.bddppq.com/pytorch/pytorch/third_party/XNNPACK/src/init.c.html#79\n */\nfunction getArchitecture() {\n    var f = new Float32Array(1);\n    var u8 = new Uint8Array(f.buffer);\n    f[0] = Infinity;\n    f[0] = f[0] - f[0];\n    return u8[3];\n}\n\n/**\n * The return type is a union instead of the enum, because it's too challenging to embed the const enum into another\n * project. Turning it into a union is a simple and an elegant solution.\n */\nfunction getApplePayState() {\n    var ApplePaySession = window.ApplePaySession;\n    if (typeof (ApplePaySession === null || ApplePaySession === void 0 ? void 0 : ApplePaySession.canMakePayments) !== 'function') {\n        return -1 /* ApplePayState.NoAPI */;\n    }\n    if (willPrintConsoleError()) {\n        return -3 /* ApplePayState.NotAvailableInFrame */;\n    }\n    try {\n        return ApplePaySession.canMakePayments() ? 1 /* ApplePayState.Enabled */ : 0 /* ApplePayState.Disabled */;\n    }\n    catch (error) {\n        return getStateFromError(error);\n    }\n}\n/**\n * Starting from Safari 15 calling `ApplePaySession.canMakePayments()` produces this error message when FingerprintJS\n * runs in an iframe with a cross-origin parent page, and the iframe on that page has no allow=\"payment *\" attribute:\n *   Feature policy 'Payment' check failed for element with origin 'https://example.com' and allow attribute ''.\n * This function checks whether the error message is expected.\n *\n * We check for cross-origin parents, which is prone to false-positive results. Instead, we should check for allowed\n * feature/permission, but we can't because none of these API works in Safari yet:\n *   navigator.permissions.query({ name: ‘payment' })\n *   navigator.permissions.query({ name: ‘payment-handler' })\n *   document.featurePolicy\n */\nvar willPrintConsoleError = isAnyParentCrossOrigin;\nfunction getStateFromError(error) {\n    // See full expected error messages in the test\n    if (error instanceof Error && error.name === 'InvalidAccessError' && /\\bfrom\\b.*\\binsecure\\b/i.test(error.message)) {\n        return -2 /* ApplePayState.NotAvailableInInsecureContext */;\n    }\n    throw error;\n}\n\n/**\n * Checks whether the Safari's Privacy Preserving Ad Measurement setting is on.\n * The setting is on when the value is not undefined.\n * A.k.a. private click measurement, privacy-preserving ad attribution.\n *\n * Unfortunately, it doesn't work in mobile Safari.\n * Probably, it will start working in mobile Safari or stop working in desktop Safari later.\n * We've found no way to detect the setting state in mobile Safari. Help wanted.\n *\n * @see https://webkit.org/blog/11529/introducing-private-click-measurement-pcm/\n * @see https://developer.apple.com/videos/play/wwdc2021/10033\n */\nfunction getPrivateClickMeasurement() {\n    var _a;\n    var link = document.createElement('a');\n    var sourceId = (_a = link.attributionSourceId) !== null && _a !== void 0 ? _a : link.attributionsourceid;\n    return sourceId === undefined ? undefined : String(sourceId);\n}\n\n/** WebGl context is not available */\nvar STATUS_NO_GL_CONTEXT = -1;\n/** WebGL context `getParameter` method is not a function */\nvar STATUS_GET_PARAMETER_NOT_A_FUNCTION = -2;\nvar validContextParameters = new Set([\n    10752, 2849, 2884, 2885, 2886, 2928, 2929, 2930, 2931, 2932, 2960, 2961, 2962, 2963, 2964, 2965, 2966, 2967, 2968,\n    2978, 3024, 3042, 3088, 3089, 3106, 3107, 32773, 32777, 32777, 32823, 32824, 32936, 32937, 32938, 32939, 32968, 32969,\n    32970, 32971, 3317, 33170, 3333, 3379, 3386, 33901, 33902, 34016, 34024, 34076, 3408, 3410, 3411, 3412, 3413, 3414,\n    3415, 34467, 34816, 34817, 34818, 34819, 34877, 34921, 34930, 35660, 35661, 35724, 35738, 35739, 36003, 36004, 36005,\n    36347, 36348, 36349, 37440, 37441, 37443, 7936, 7937, 7938,\n    // SAMPLE_ALPHA_TO_COVERAGE (32926) and SAMPLE_COVERAGE (32928) are excluded because they trigger a console warning\n    // in IE, Chrome ≤ 59 and Safari ≤ 13 and give no entropy.\n]);\nvar validExtensionParams = new Set([\n    34047,\n    35723,\n    36063,\n    34852,\n    34853,\n    34854,\n    34229,\n    36392,\n    36795,\n    38449, // MAX_VIEWS_OVR\n]);\nvar shaderTypes = ['FRAGMENT_SHADER', 'VERTEX_SHADER'];\nvar precisionTypes = ['LOW_FLOAT', 'MEDIUM_FLOAT', 'HIGH_FLOAT', 'LOW_INT', 'MEDIUM_INT', 'HIGH_INT'];\nvar rendererInfoExtensionName = 'WEBGL_debug_renderer_info';\nvar polygonModeExtensionName = 'WEBGL_polygon_mode';\n/**\n * Gets the basic and simple WebGL parameters\n */\nfunction getWebGlBasics(_a) {\n    var _b, _c, _d, _e, _f, _g;\n    var cache = _a.cache;\n    var gl = getWebGLContext(cache);\n    if (!gl) {\n        return STATUS_NO_GL_CONTEXT;\n    }\n    if (!isValidParameterGetter(gl)) {\n        return STATUS_GET_PARAMETER_NOT_A_FUNCTION;\n    }\n    var debugExtension = shouldAvoidDebugRendererInfo() ? null : gl.getExtension(rendererInfoExtensionName);\n    return {\n        version: ((_b = gl.getParameter(gl.VERSION)) === null || _b === void 0 ? void 0 : _b.toString()) || '',\n        vendor: ((_c = gl.getParameter(gl.VENDOR)) === null || _c === void 0 ? void 0 : _c.toString()) || '',\n        vendorUnmasked: debugExtension ? (_d = gl.getParameter(debugExtension.UNMASKED_VENDOR_WEBGL)) === null || _d === void 0 ? void 0 : _d.toString() : '',\n        renderer: ((_e = gl.getParameter(gl.RENDERER)) === null || _e === void 0 ? void 0 : _e.toString()) || '',\n        rendererUnmasked: debugExtension ? (_f = gl.getParameter(debugExtension.UNMASKED_RENDERER_WEBGL)) === null || _f === void 0 ? void 0 : _f.toString() : '',\n        shadingLanguageVersion: ((_g = gl.getParameter(gl.SHADING_LANGUAGE_VERSION)) === null || _g === void 0 ? void 0 : _g.toString()) || '',\n    };\n}\n/**\n * Gets the advanced and massive WebGL parameters and extensions\n */\nfunction getWebGlExtensions(_a) {\n    var cache = _a.cache;\n    var gl = getWebGLContext(cache);\n    if (!gl) {\n        return STATUS_NO_GL_CONTEXT;\n    }\n    if (!isValidParameterGetter(gl)) {\n        return STATUS_GET_PARAMETER_NOT_A_FUNCTION;\n    }\n    var extensions = gl.getSupportedExtensions();\n    var contextAttributes = gl.getContextAttributes();\n    var unsupportedExtensions = [];\n    // Features\n    var attributes = [];\n    var parameters = [];\n    var extensionParameters = [];\n    var shaderPrecisions = [];\n    // Context attributes\n    if (contextAttributes) {\n        for (var _i = 0, _b = Object.keys(contextAttributes); _i < _b.length; _i++) {\n            var attributeName = _b[_i];\n            attributes.push(\"\".concat(attributeName, \"=\").concat(contextAttributes[attributeName]));\n        }\n    }\n    // Context parameters\n    var constants = getConstantsFromPrototype(gl);\n    for (var _c = 0, constants_1 = constants; _c < constants_1.length; _c++) {\n        var constant = constants_1[_c];\n        var code = gl[constant];\n        parameters.push(\"\".concat(constant, \"=\").concat(code).concat(validContextParameters.has(code) ? \"=\".concat(gl.getParameter(code)) : ''));\n    }\n    // Extension parameters\n    if (extensions) {\n        for (var _d = 0, extensions_1 = extensions; _d < extensions_1.length; _d++) {\n            var name_1 = extensions_1[_d];\n            if ((name_1 === rendererInfoExtensionName && shouldAvoidDebugRendererInfo()) ||\n                (name_1 === polygonModeExtensionName && shouldAvoidPolygonModeExtensions())) {\n                continue;\n            }\n            var extension = gl.getExtension(name_1);\n            if (!extension) {\n                unsupportedExtensions.push(name_1);\n                continue;\n            }\n            for (var _e = 0, _f = getConstantsFromPrototype(extension); _e < _f.length; _e++) {\n                var constant = _f[_e];\n                var code = extension[constant];\n                extensionParameters.push(\"\".concat(constant, \"=\").concat(code).concat(validExtensionParams.has(code) ? \"=\".concat(gl.getParameter(code)) : ''));\n            }\n        }\n    }\n    // Shader precision\n    for (var _g = 0, shaderTypes_1 = shaderTypes; _g < shaderTypes_1.length; _g++) {\n        var shaderType = shaderTypes_1[_g];\n        for (var _h = 0, precisionTypes_1 = precisionTypes; _h < precisionTypes_1.length; _h++) {\n            var precisionType = precisionTypes_1[_h];\n            var shaderPrecision = getShaderPrecision(gl, shaderType, precisionType);\n            shaderPrecisions.push(\"\".concat(shaderType, \".\").concat(precisionType, \"=\").concat(shaderPrecision.join(',')));\n        }\n    }\n    // Postprocess\n    extensionParameters.sort();\n    parameters.sort();\n    return {\n        contextAttributes: attributes,\n        parameters: parameters,\n        shaderPrecisions: shaderPrecisions,\n        extensions: extensions,\n        extensionParameters: extensionParameters,\n        unsupportedExtensions: unsupportedExtensions,\n    };\n}\n/**\n * This function usually takes the most time to execute in all the sources, therefore we cache its result.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getWebGLContext(cache) {\n    if (cache.webgl) {\n        return cache.webgl.context;\n    }\n    var canvas = document.createElement('canvas');\n    var context;\n    canvas.addEventListener('webglCreateContextError', function () { return (context = undefined); });\n    for (var _i = 0, _a = ['webgl', 'experimental-webgl']; _i < _a.length; _i++) {\n        var type = _a[_i];\n        try {\n            context = canvas.getContext(type);\n        }\n        catch (_b) {\n            // Ok, continue\n        }\n        if (context) {\n            break;\n        }\n    }\n    cache.webgl = { context: context };\n    return context;\n}\n/**\n * https://developer.mozilla.org/en-US/docs/Web/API/WebGLShaderPrecisionFormat\n * https://developer.mozilla.org/en-US/docs/Web/API/WebGLRenderingContext/getShaderPrecisionFormat\n * https://www.khronos.org/registry/webgl/specs/latest/1.0/#5.12\n */\nfunction getShaderPrecision(gl, shaderType, precisionType) {\n    var shaderPrecision = gl.getShaderPrecisionFormat(gl[shaderType], gl[precisionType]);\n    return shaderPrecision ? [shaderPrecision.rangeMin, shaderPrecision.rangeMax, shaderPrecision.precision] : [];\n}\nfunction getConstantsFromPrototype(obj) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    var keys = Object.keys(obj.__proto__);\n    return keys.filter(isConstantLike);\n}\nfunction isConstantLike(key) {\n    return typeof key === 'string' && !key.match(/[^A-Z0-9_x]/);\n}\n/**\n * Some browsers print a console warning when the WEBGL_debug_renderer_info extension is requested.\n * JS Agent aims to avoid printing messages to console, so we avoid this extension in that browsers.\n */\nfunction shouldAvoidDebugRendererInfo() {\n    return isGecko();\n}\n/**\n * Some browsers print a console warning when the WEBGL_polygon_mode extension is requested.\n * JS Agent aims to avoid printing messages to console, so we avoid this extension in that browsers.\n */\nfunction shouldAvoidPolygonModeExtensions() {\n    return isChromium() || isWebKit();\n}\n/**\n * Some unknown browsers have no `getParameter` method\n */\nfunction isValidParameterGetter(gl) {\n    return typeof gl.getParameter === 'function';\n}\n\nfunction getAudioContextBaseLatency() {\n    var _a;\n    // The signal emits warning in Chrome and Firefox, therefore it is enabled on Safari where it doesn't produce warning\n    // and on Android where it's less visible\n    var isAllowedPlatform = isAndroid() || isWebKit();\n    if (!isAllowedPlatform) {\n        return -2 /* SpecialFingerprint.Disabled */;\n    }\n    if (!window.AudioContext) {\n        return -1 /* SpecialFingerprint.NotSupported */;\n    }\n    return (_a = new AudioContext().baseLatency) !== null && _a !== void 0 ? _a : -1 /* SpecialFingerprint.NotSupported */;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/resolvedOptions\n *\n * The return type is a union instead of a const enum due to the difficulty of embedding const enums in other projects.\n * This makes integration simpler and more elegant.\n */\nfunction getDateTimeLocale() {\n    if (!window.Intl) {\n        return -1 /* Status.IntlAPINotSupported */;\n    }\n    var DateTimeFormat = window.Intl.DateTimeFormat;\n    if (!DateTimeFormat) {\n        return -2 /* Status.DateTimeFormatNotSupported */;\n    }\n    var locale = DateTimeFormat().resolvedOptions().locale;\n    if (!locale && locale !== '') {\n        return -3 /* Status.LocaleNotAvailable */;\n    }\n    return locale;\n}\n\n/**\n * The list of entropy sources used to make visitor identifiers.\n *\n * This value isn't restricted by Semantic Versioning, i.e. it may be changed without bumping minor or major version of\n * this package.\n *\n * Note: Rollup and Webpack are smart enough to remove unused properties of this object during tree-shaking, so there is\n * no need to export the sources individually.\n */\nvar sources = {\n    // READ FIRST:\n    // See https://github.com/fingerprintjs/fingerprintjs/blob/master/contributing.md#how-to-add-an-entropy-source\n    // to learn how entropy source works and how to make your own.\n    // The sources run in this exact order.\n    // The asynchronous sources are at the start to run in parallel with other sources.\n    fonts: getFonts,\n    domBlockers: getDomBlockers,\n    fontPreferences: getFontPreferences,\n    audio: getAudioFingerprint,\n    screenFrame: getScreenFrame,\n    canvas: getCanvasFingerprint,\n    osCpu: getOsCpu,\n    languages: getLanguages,\n    colorDepth: getColorDepth,\n    deviceMemory: getDeviceMemory,\n    screenResolution: getScreenResolution,\n    hardwareConcurrency: getHardwareConcurrency,\n    timezone: getTimezone,\n    sessionStorage: getSessionStorage,\n    localStorage: getLocalStorage,\n    indexedDB: getIndexedDB,\n    openDatabase: getOpenDatabase,\n    cpuClass: getCpuClass,\n    platform: getPlatform,\n    plugins: getPlugins,\n    touchSupport: getTouchSupport,\n    vendor: getVendor,\n    vendorFlavors: getVendorFlavors,\n    cookiesEnabled: areCookiesEnabled,\n    colorGamut: getColorGamut,\n    invertedColors: areColorsInverted,\n    forcedColors: areColorsForced,\n    monochrome: getMonochromeDepth,\n    contrast: getContrastPreference,\n    reducedMotion: isMotionReduced,\n    reducedTransparency: isTransparencyReduced,\n    hdr: isHDR,\n    math: getMathFingerprint,\n    pdfViewerEnabled: isPdfViewerEnabled,\n    architecture: getArchitecture,\n    applePay: getApplePayState,\n    privateClickMeasurement: getPrivateClickMeasurement,\n    audioBaseLatency: getAudioContextBaseLatency,\n    dateTimeLocale: getDateTimeLocale,\n    // Some sources can affect other sources (e.g. WebGL can affect canvas), so it's important to run these sources\n    // after other sources.\n    webGlBasics: getWebGlBasics,\n    webGlExtensions: getWebGlExtensions,\n};\n/**\n * Loads the built-in entropy sources.\n * Returns a function that collects the entropy components to make the visitor identifier.\n */\nfunction loadBuiltinSources(options) {\n    return loadSources(sources, options, []);\n}\n\nvar commentTemplate = '$ if upgrade to Pro: https://fpjs.dev/pro';\nfunction getConfidence(components) {\n    var openConfidenceScore = getOpenConfidenceScore(components);\n    var proConfidenceScore = deriveProConfidenceScore(openConfidenceScore);\n    return { score: openConfidenceScore, comment: commentTemplate.replace(/\\$/g, \"\".concat(proConfidenceScore)) };\n}\nfunction getOpenConfidenceScore(components) {\n    // In order to calculate the true probability of the visitor identifier being correct, we need to know the number of\n    // website visitors (the higher the number, the less the probability because the fingerprint entropy is limited).\n    // JS agent doesn't know the number of visitors, so we can only do an approximate assessment.\n    if (isAndroid()) {\n        return 0.4;\n    }\n    // Safari (mobile and desktop)\n    if (isWebKit()) {\n        return isDesktopWebKit() && !(isWebKit616OrNewer() && isSafariWebKit()) ? 0.5 : 0.3;\n    }\n    var platform = 'value' in components.platform ? components.platform.value : '';\n    // Windows\n    if (/^Win/.test(platform)) {\n        // The score is greater than on macOS because of the higher variety of devices running Windows.\n        // Chrome provides more entropy than Firefox according too\n        // https://netmarketshare.com/browser-market-share.aspx?options=%7B%22filter%22%3A%7B%22%24and%22%3A%5B%7B%22platform%22%3A%7B%22%24in%22%3A%5B%22Windows%22%5D%7D%7D%5D%7D%2C%22dateLabel%22%3A%22Trend%22%2C%22attributes%22%3A%22share%22%2C%22group%22%3A%22browser%22%2C%22sort%22%3A%7B%22share%22%3A-1%7D%2C%22id%22%3A%22browsersDesktop%22%2C%22dateInterval%22%3A%22Monthly%22%2C%22dateStart%22%3A%222019-11%22%2C%22dateEnd%22%3A%222020-10%22%2C%22segments%22%3A%22-1000%22%7D\n        // So we assign the same score to them.\n        return 0.6;\n    }\n    // macOS\n    if (/^Mac/.test(platform)) {\n        // Chrome provides more entropy than Safari and Safari provides more entropy than Firefox.\n        // Chrome is more popular than Safari and Safari is more popular than Firefox according to\n        // https://netmarketshare.com/browser-market-share.aspx?options=%7B%22filter%22%3A%7B%22%24and%22%3A%5B%7B%22platform%22%3A%7B%22%24in%22%3A%5B%22Mac%20OS%22%5D%7D%7D%5D%7D%2C%22dateLabel%22%3A%22Trend%22%2C%22attributes%22%3A%22share%22%2C%22group%22%3A%22browser%22%2C%22sort%22%3A%7B%22share%22%3A-1%7D%2C%22id%22%3A%22browsersDesktop%22%2C%22dateInterval%22%3A%22Monthly%22%2C%22dateStart%22%3A%222019-11%22%2C%22dateEnd%22%3A%222020-10%22%2C%22segments%22%3A%22-1000%22%7D\n        // So we assign the same score to them.\n        return 0.5;\n    }\n    // Another platform, e.g. a desktop Linux. It's rare, so it should be pretty unique.\n    return 0.7;\n}\nfunction deriveProConfidenceScore(openConfidenceScore) {\n    return round(0.99 + 0.01 * openConfidenceScore, 0.0001);\n}\n\nfunction componentsToCanonicalString(components) {\n    var result = '';\n    for (var _i = 0, _a = Object.keys(components).sort(); _i < _a.length; _i++) {\n        var componentKey = _a[_i];\n        var component = components[componentKey];\n        var value = 'error' in component ? 'error' : JSON.stringify(component.value);\n        result += \"\".concat(result ? '|' : '').concat(componentKey.replace(/([:|\\\\])/g, '\\\\$1'), \":\").concat(value);\n    }\n    return result;\n}\nfunction componentsToDebugString(components) {\n    return JSON.stringify(components, function (_key, value) {\n        if (value instanceof Error) {\n            return errorToObject(value);\n        }\n        return value;\n    }, 2);\n}\nfunction hashComponents(components) {\n    return x64hash128(componentsToCanonicalString(components));\n}\n/**\n * Makes a GetResult implementation that calculates the visitor id hash on demand.\n * Designed for optimisation.\n */\nfunction makeLazyGetResult(components) {\n    var visitorIdCache;\n    // This function runs very fast, so there is no need to make it lazy\n    var confidence = getConfidence(components);\n    // A plain class isn't used because its getters and setters aren't enumerable.\n    return {\n        get visitorId() {\n            if (visitorIdCache === undefined) {\n                visitorIdCache = hashComponents(this.components);\n            }\n            return visitorIdCache;\n        },\n        set visitorId(visitorId) {\n            visitorIdCache = visitorId;\n        },\n        confidence: confidence,\n        components: components,\n        version: version,\n    };\n}\n/**\n * A delay is required to ensure consistent entropy components.\n * See https://github.com/fingerprintjs/fingerprintjs/issues/254\n * and https://github.com/fingerprintjs/fingerprintjs/issues/307\n * and https://github.com/fingerprintjs/fingerprintjs/commit/945633e7c5f67ae38eb0fea37349712f0e669b18\n */\nfunction prepareForSources(delayFallback) {\n    if (delayFallback === void 0) { delayFallback = 50; }\n    // A proper deadline is unknown. Let it be twice the fallback timeout so that both cases have the same average time.\n    return requestIdleCallbackIfAvailable(delayFallback, delayFallback * 2);\n}\n/**\n * The function isn't exported from the index file to not allow to call it without `load()`.\n * The hiding gives more freedom for future non-breaking updates.\n *\n * A factory function is used instead of a class to shorten the attribute names in the minified code.\n * Native private class fields could've been used, but TypeScript doesn't allow them with `\"target\": \"es5\"`.\n */\nfunction makeAgent(getComponents, debug) {\n    var creationTime = Date.now();\n    return {\n        get: function (options) {\n            return __awaiter(this, void 0, void 0, function () {\n                var startTime, components, result;\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            startTime = Date.now();\n                            return [4 /*yield*/, getComponents()];\n                        case 1:\n                            components = _a.sent();\n                            result = makeLazyGetResult(components);\n                            if (debug || (options === null || options === void 0 ? void 0 : options.debug)) {\n                                // console.log is ok here because it's under a debug clause\n                                // eslint-disable-next-line no-console\n                                console.log(\"Copy the text below to get the debug data:\\n\\n```\\nversion: \".concat(result.version, \"\\nuserAgent: \").concat(navigator.userAgent, \"\\ntimeBetweenLoadAndGet: \").concat(startTime - creationTime, \"\\nvisitorId: \").concat(result.visitorId, \"\\ncomponents: \").concat(componentsToDebugString(components), \"\\n```\"));\n                            }\n                            return [2 /*return*/, result];\n                    }\n                });\n            });\n        },\n    };\n}\n/**\n * Sends an unpersonalized AJAX request to collect installation statistics\n */\nfunction monitor() {\n    // The FingerprintJS CDN (https://github.com/fingerprintjs/cdn) replaces `window.__fpjs_d_m` with `true`\n    if (window.__fpjs_d_m || Math.random() >= 0.001) {\n        return;\n    }\n    try {\n        var request = new XMLHttpRequest();\n        request.open('get', \"https://m1.openfpcdn.io/fingerprintjs/v\".concat(version, \"/npm-monitoring\"), true);\n        request.send();\n    }\n    catch (error) {\n        // console.error is ok here because it's an unexpected error handler\n        // eslint-disable-next-line no-console\n        console.error(error);\n    }\n}\n/**\n * Builds an instance of Agent and waits a delay required for a proper operation.\n */\nfunction load(options) {\n    var _a;\n    if (options === void 0) { options = {}; }\n    return __awaiter(this, void 0, void 0, function () {\n        var delayFallback, debug, getComponents;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    if ((_a = options.monitoring) !== null && _a !== void 0 ? _a : true) {\n                        monitor();\n                    }\n                    delayFallback = options.delayFallback, debug = options.debug;\n                    return [4 /*yield*/, prepareForSources(delayFallback)];\n                case 1:\n                    _b.sent();\n                    getComponents = loadBuiltinSources({ cache: {}, debug: debug });\n                    return [2 /*return*/, makeAgent(getComponents, debug)];\n            }\n        });\n    });\n}\n\n// The default export is a syntax sugar (`import * as FP from '...' → import FP from '...'`).\n// It should contain all the public exported values.\nvar index = { load: load, hashComponents: hashComponents, componentsToDebugString: componentsToDebugString };\n// The exports below are for private usage. They may change unexpectedly. Use them at your own risk.\n/** Not documented, out of Semantic Versioning, usage is at your own risk */\nvar murmurX64Hash128 = x64hash128;\n\nexport { componentsToDebugString, index as default, getFullscreenElement, getUnstableAudioFingerprint, getUnstableCanvasFingerprint, getUnstableScreenFrame, getUnstableScreenResolution, getWebGLContext, hashComponents, isAndroid, isChromium, isDesktopWebKit, isEdgeHTML, isGecko, isSamsungInternet, isTrident, isWebKit, load, loadSources, murmurX64Hash128, prepareForSources, sources, transformSource, withIframe };\n", "import FingerprintJS from '@fingerprintjs/fingerprintjs';\nexport async function getDeviceId() {\n    const fp = await FingerprintJS.load();\n    const result = await fp.get();\n    return result.visitorId; // Unique Device ID\n}\nexport async function fetchWithDeviceId(input, init) {\n    const deviceId = await getDeviceId();\n    // Convert Headers instance to a plain object if needed\n    let headers;\n    if (init?.headers instanceof Headers) {\n        headers = new Headers();\n        init.headers.forEach((value, key) => {\n            headers.set(key, value);\n        });\n    }\n    else {\n        headers = new Headers(init?.headers || {});\n    }\n    // Set the custom header\n    headers.set(\"X-Device-Id\", deviceId);\n    const modifiedInit = {\n        ...init,\n        headers,\n        credentials: \"include\", // Ensure credentials are sent if needed\n    };\n    return fetch(input, modifiedInit);\n}\nexport async function fetchWithDeviceIdandApiKey(input, init = {}, apiKey) {\n    const deviceId = await getDeviceId(); // Lấy deviceId\n    // Khởi tạo headers và đảm bảo giữ nguyên headers cũ nếu có\n    const headers = new Headers(init.headers);\n    headers.set(\"X-Device-Id\", deviceId);\n    headers.set(\"X-Api-Key\", apiKey); // Gửi API key trong request\n    // Tạo request mới với headers cập nhật\n    const modifiedInit = {\n        ...init,\n        headers,\n        credentials: \"include\", // Giữ cookies nếu cần\n    };\n    try {\n        const response = await fetch(input, modifiedInit);\n        return response;\n    }\n    catch (error) {\n        console.error('Fetch error:', error);\n        throw error;\n    }\n}\n", "import { environment } from \"../environments/environment\";\nimport { fetchWithDeviceId } from \"../utils/deviceUtils\";\nimport FingerprintJS from '@fingerprintjs/fingerprintjs';\nconst apiUrl = environment.apiUrl;\nconst publicKey = atob(environment.publicKey);\nexport class CryptoService {\n    constructor() {\n        this.keyPair = null;\n        this.encryptionKeyPair = null;\n    }\n    async gra() {\n        this.keyPair = await crypto.subtle.generateKey({\n            name: \"RSA-OAEP\",\n            modulusLength: 2048,\n            publicExponent: new Uint8Array([1, 0, 1]),\n            hash: \"SHA-256\"\n        }, true, [\"encrypt\", \"decrypt\"]);\n        const publicKey = await crypto.subtle.exportKey(\"spki\", this.keyPair.publicKey);\n        const privateKey = await crypto.subtle.exportKey(\"pkcs8\", this.keyPair.privateKey);\n        return {\n            publicKey: this.arrayBufferToPEM(publicKey, \"PUBLIC KEY\"),\n            privateKey: this.arrayBufferToPEM(privateKey, \"PRIVATE KEY\")\n        };\n    }\n    async ga() {\n        return await crypto.subtle.generateKey({ name: 'AES-GCM', length: 256 }, true, ['encrypt', 'decrypt']);\n    }\n    async ea(aesKey, data) {\n        const encoder = new TextEncoder();\n        const dataBuffer = encoder.encode(data);\n        const iv = crypto.getRandomValues(new Uint8Array(12));\n        const encryptedData = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, aesKey, dataBuffer);\n        return { encryptedData, iv };\n    }\n    async irpu(pem) {\n        const binaryDer = this.pemToArrayBuffer(pem);\n        return await crypto.subtle.importKey('spki', binaryDer, { name: 'RSA-OAEP', hash: 'SHA-256' }, true, ['encrypt']);\n    }\n    async irpr(pem) {\n        const binaryDer = this.pemToArrayBuffer(pem);\n        return await crypto.subtle.importKey('pkcs8', binaryDer, { name: 'RSA-OAEP', hash: 'SHA-256' }, true, ['decrypt']);\n    }\n    async era(publicKey, aesKey) {\n        const exportedAESKey = await crypto.subtle.exportKey('raw', aesKey);\n        return await crypto.subtle.encrypt({ name: 'RSA-OAEP' }, publicKey, exportedAESKey);\n    }\n    async dra(privateKey, encryptedData) {\n        return await crypto.subtle.decrypt({ name: 'RSA-OAEP' }, privateKey, encryptedData);\n    }\n    async he(publicKeyPem, data) {\n        const aesKey = await this.ga();\n        const { encryptedData, iv } = await this.ea(aesKey, data);\n        const publicKey = await this.irpu(publicKeyPem);\n        const encryptedAESKey = await this.era(publicKey, aesKey);\n        const combinedData = new Uint8Array(encryptedAESKey.byteLength + iv.byteLength + encryptedData.byteLength);\n        combinedData.set(new Uint8Array(encryptedAESKey), 0);\n        combinedData.set(iv, encryptedAESKey.byteLength);\n        combinedData.set(new Uint8Array(encryptedData), encryptedAESKey.byteLength + iv.byteLength);\n        return btoa(String.fromCharCode(...combinedData));\n    }\n    async hd(privateKeyBem, encryptedText) {\n        try {\n            const combinedData = Uint8Array.from(atob(encryptedText), c => c.charCodeAt(0));\n            const encryptedAesKey = combinedData.slice(0, 256);\n            const encryptedData = combinedData.slice(256, combinedData.length);\n            const privateKey = await this.irpr(privateKeyBem);\n            const aesKeyBuffer = await this.dra(privateKey, encryptedAesKey);\n            const decryptedData = await this.da(aesKeyBuffer, encryptedData);\n            return decryptedData;\n        }\n        catch (error) {\n            console.error(\"Error during decryption:\", error);\n            throw new Error(\"Decryption failed\");\n        }\n    }\n    bts(buffer) {\n        return btoa(String.fromCharCode(...new Uint8Array(buffer)));\n    }\n    async da(aesKeyBuffer, encryptedData) {\n        try {\n            const aesKey = await crypto.subtle.importKey(\"raw\", aesKeyBuffer, { name: \"AES-GCM\" }, false, [\"decrypt\"]);\n            const iv = encryptedData.slice(0, 12);\n            const tag = encryptedData.slice(12, 28);\n            const cipherText = encryptedData.slice(28);\n            const encryptedBuffer = new Uint8Array([...cipherText, ...tag]);\n            const decryptedBuffer = await crypto.subtle.decrypt({ name: \"AES-GCM\", iv: iv }, aesKey, encryptedBuffer);\n            return new TextDecoder().decode(decryptedBuffer);\n        }\n        catch (error) {\n            throw new Error(\"AES-GCM Decryption failed\");\n        }\n    }\n    async encrypt(publicKey, plainText) {\n        const key = await this.importPublicKey(publicKey);\n        const encryptedData = await crypto.subtle.encrypt({\n            name: \"RSA-OAEP\"\n        }, key, new TextEncoder().encode(plainText));\n        return this.arrayBufferToBase64(encryptedData);\n    }\n    async decrypt(privateKey, encryptedText) {\n        const key = await this.importPrivateKey(privateKey);\n        const decryptedData = await crypto.subtle.decrypt({\n            name: \"RSA-OAEP\"\n        }, key, this.base64ToArrayBuffer(encryptedText));\n        return new TextDecoder().decode(decryptedData);\n    }\n    async importPublicKey(pem) {\n        return crypto.subtle.importKey(\"spki\", this.pemToArrayBuffer(pem), {\n            name: \"RSA-OAEP\",\n            hash: \"SHA-256\"\n        }, true, [\"encrypt\"]);\n    }\n    async importPrivateKey(pem) {\n        return crypto.subtle.importKey(\"pkcs8\", this.pemToArrayBuffer(pem), {\n            name: \"RSA-OAEP\",\n            hash: \"SHA-256\"\n        }, true, [\"decrypt\"]);\n    }\n    arrayBufferToPEM(buffer, type) {\n        const base64 = this.arrayBufferToBase64(buffer);\n        const pem = `-----BEGIN ${type}-----\\n${base64.match(/.{1,64}/g)?.join('\\n')}\\n-----END ${type}-----`;\n        return pem;\n    }\n    arrayBufferToBase64(buffer) {\n        let binary = '';\n        const bytes = new Uint8Array(buffer);\n        const len = bytes.byteLength;\n        for (let i = 0; i < len; i++) {\n            binary += String.fromCharCode(bytes[i]);\n        }\n        return window.btoa(binary);\n    }\n    base64ToArrayBuffer(base64) {\n        const binaryString = window.atob(base64);\n        const len = binaryString.length;\n        const bytes = new Uint8Array(len);\n        for (let i = 0; i < len; i++) {\n            bytes[i] = binaryString.charCodeAt(i);\n        }\n        return bytes.buffer;\n    }\n    pemToArrayBuffer(pem) {\n        const base64 = pem.replace(/-----BEGIN [A-Z ]+-----|-----END [A-Z ]+-----|\\n/g, '');\n        return this.base64ToArrayBuffer(base64);\n    }\n    async gr() {\n        this.encryptionKeyPair = await this.gra();\n        const signingKeyPair = await crypto.subtle.generateKey({ name: \"RSASSA-PKCS1-v1_5\", modulusLength: 2048, publicExponent: new Uint8Array([0x01, 0x00, 0x01]), hash: \"SHA-256\" }, true, [\"sign\", \"verify\"]);\n        const ep = this.textToBase64(this.encryptionKeyPair.publicKey);\n        const spBuffer = await crypto.subtle.exportKey(\"spki\", signingKeyPair.publicKey);\n        const sp = btoa(String.fromCharCode(...new Uint8Array(spBuffer)));\n        const s = crypto.randomUUID();\n        const xdi = await this.gdi();\n        const encoder = new TextEncoder();\n        const sBuffer = encoder.encode(s + xdi);\n        const signatureBuffer = await crypto.subtle.sign({ name: \"RSASSA-PKCS1-v1_5\" }, signingKeyPair.privateKey, sBuffer);\n        const ss = btoa(String.fromCharCode(...new Uint8Array(signatureBuffer)));\n        return { ep, sp, ss, s };\n    }\n    textToBase64(text) {\n        return btoa(unescape(encodeURIComponent(text)));\n    }\n    sc(name, value, minutes) {\n        const date = new Date();\n        date.setTime(date.getTime() + (minutes * 60 * 1000));\n        const expires = \"expires=\" + date.toUTCString();\n        document.cookie = name + \"=\" + value + \";\" + expires + \";path=/\";\n    }\n    gc(name) {\n        const nameEQ = name + \"=\";\n        const ca = document.cookie.split(';');\n        for (let i = 0; i < ca.length; i++) {\n            let c = ca[i];\n            while (c.charAt(0) === ' ')\n                c = c.substring(1, c.length);\n            if (c.indexOf(nameEQ) === 0)\n                return c.substring(nameEQ.length, c.length);\n        }\n        return null;\n    }\n    //remove cookie\n    rc(name) {\n        document.cookie = name + '=; Max-Age=-99999999;';\n    }\n    //remove all cookies\n    ra() {\n        const cookies = document.cookie.split(\";\");\n        for (let i = 0; i < cookies.length; i++) {\n            const cookie = cookies[i];\n            const eqPos = cookie.indexOf(\"=\");\n            const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;\n            document.cookie = name + \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n        }\n    }\n    async spu() {\n        const { ep, sp, ss, s } = await this.gr();\n        const request = {\n            ep: ep,\n            sp: sp,\n            ss: ss,\n            s: s\n        };\n        const requestJson = JSON.stringify(request);\n        const encryptedData = await this.he(publicKey, requestJson);\n        const requestEncrypt = {\n            EncryptData: encryptedData\n        };\n        try {\n            const response = await fetchWithDeviceId(apiUrl + '/api/Crypto/dr', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestEncrypt)\n            });\n            if (!response.ok) {\n                throw new Error('Network response was not ok');\n            }\n            const responseData = await response.json();\n            if (responseData && responseData.resultObj && responseData.resultObj.encryptedData) {\n                this.sc('s', responseData.resultObj.encryptedData, 5);\n                const privateKeyBase64String = this.textToBase64(this.encryptionKeyPair.privateKey);\n                this.sc('c', privateKeyBase64String, 5);\n            }\n            else {\n                console.error('Invalid response from server:', responseData);\n            }\n        }\n        catch (error) {\n            console.error('Error in spu:', error);\n        }\n    }\n    async dsk() {\n        var c = this.gc('c');\n        var s = this.gc('s');\n        if (!c || !s) {\n            return \"\";\n        }\n        var cText = atob(c);\n        var serverKey = await this.hd(cText, s);\n        return serverKey;\n    }\n    async eda(data) {\n        var s = await this.dsk();\n        var sText = atob(s);\n        if (!s) {\n            return \"\";\n        }\n        var encryptedData = await this.he(sText, data);\n        return encryptedData;\n    }\n    async dda(encryptedData) {\n        var c = this.gc('c');\n        if (!c) {\n            return \"\";\n        }\n        var cText = atob(c);\n        var decryptedData = await this.hd(cText, encryptedData);\n        return decryptedData;\n    }\n    async csi() {\n        try {\n            const response = await fetchWithDeviceId(apiUrl + '/api/Crypto/check-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: null\n            });\n            if (!response.ok) {\n                throw new Error('Network response was not ok');\n            }\n            const responseData = await response.json();\n            console.log(responseData);\n        }\n        catch (error) {\n            console.error('Error in csi:', error);\n        }\n    }\n    async iih() {\n        if (!this.ch()) {\n            await this.spu();\n        }\n    }\n    ch() {\n        if (this.gc('s') && this.gc('c')) {\n            return true;\n        }\n        return false;\n    }\n    async wk() {\n        let retries = 10;\n        while (!this.gc('s') && retries > 0) {\n            await new Promise(resolve => setTimeout(resolve, 200));\n            retries--;\n        }\n    }\n    async gdi() {\n        const fp = await FingerprintJS.load();\n        const result = await fp.get();\n        return result.visitorId; // Device ID duy nhất\n    }\n}\n", "import { environment } from \"../environments/environment\";\nimport { fetchWithDeviceIdandApiKey } from \"../utils/deviceUtils\";\nconst apiUrl = environment.apiUrl;\nexport class FlightService {\n    async request(endpoint, request, useDeviceId = true, apiKey) {\n        try {\n            const fetchFn = useDeviceId ? fetchWithDeviceIdandApiKey : fetch;\n            const response = await fetchFn(`${apiUrl}/api/Library/${endpoint}`, {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' },\n                body: JSON.stringify(request)\n            }, apiKey);\n            if (!response.ok) {\n                throw response;\n            }\n            return await response.json();\n        }\n        catch (error) {\n            throw error;\n        }\n    }\n    SearchTrip(request, api) { return this.request('SearchTrip', request, true, api); }\n    PriceAncillary(request, api) { return this.request('PriceAncillary', request, true, api); }\n    FareRules(request, language) { return this.request('../FareRules/get-fare-rules/' + language, request, false, ''); }\n    AvailableTrip(request, api) { return this.request('AvailableTrip', request, true, api); }\n    RequestTrip(request, api) { return this.request('RequestTrip', request, true, api); }\n    RePayment(request, api) { return this.request('RePayment', request, true, api); }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t=t=>(e,o)=>{void 0!==o?o.addInitializer((()=>{customElements.define(t,e)})):customElements.define(t,e)};export{t as customElement};\n//# sourceMappingURL=custom-element.js.map\n", "import{defaultConverter as t,notEqual as e}from\"../reactive-element.js\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */const o={attribute:!0,type:String,converter:t,reflect:!1,hasChanged:e},r=(t=o,e,r)=>{const{kind:n,metadata:i}=r;let s=globalThis.litPropertyMetadata.get(i);if(void 0===s&&globalThis.litPropertyMetadata.set(i,s=new Map),s.set(r.name,t),\"accessor\"===n){const{name:o}=r;return{set(r){const n=e.get.call(this);e.set.call(this,r),this.requestUpdate(o,n,t)},init(e){return void 0!==e&&this.P(o,void 0,t),e}}}if(\"setter\"===n){const{name:o}=r;return function(r){const n=this[o];e.call(this,r),this.requestUpdate(o,n,t)}}throw Error(\"Unsupported decorator location: \"+n)};function n(t){return(e,o)=>\"object\"==typeof o?r(t,e,o):((t,e,o)=>{const r=e.hasOwnProperty(o);return e.constructor.createProperty(o,r?{...t,wrapped:!0}:t),r?Object.getOwnPropertyDescriptor(e,o):void 0})(t,e,o)}export{n as property,r as standardProperty};\n//# sourceMappingURL=property.js.map\n", "import{property as t}from\"./property.js\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */function r(r){return t({...r,state:!0,attribute:!1})}export{r as state};\n//# sourceMappingURL=state.js.map\n", "import { environment } from \"../environments/environment\";\nimport { fetchWithDeviceIdandApiKey } from \"../utils/deviceUtils\";\nconst apiUrl = environment.apiUrl;\nexport const getAirportInfoByCode = async (airportsCode, language, apiKey) => {\n    const requestBody = {\n        airportsCode: airportsCode.join(';'),\n        language: language\n    };\n    try {\n        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/airport-info`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(requestBody)\n        }, apiKey);\n        if (!response.ok) {\n            throw response;\n        }\n        return await response.json();\n    }\n    catch (error) {\n        throw error;\n    }\n};\nexport const phones = async () => {\n    const response = await fetch(`${apiUrl}/api/World/phones`, {\n        method: 'GET'\n    });\n    return response.json();\n};\nexport const getAirportsDefault = async (language, apiKey) => {\n    const requestBody = {\n        language: language\n    };\n    try {\n        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/airports-default`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(requestBody)\n        }, apiKey);\n        if (!response.ok) {\n            throw response;\n        }\n        return await response.json();\n    }\n    catch (error) {\n        throw error;\n    }\n};\nexport const getFeatures = async (features, apiKey) => {\n    try {\n        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/feature/${features}`, {\n            method: 'GET',\n            headers: { 'Content-Type': 'application/json' },\n        }, apiKey);\n        if (!response.ok) {\n            throw response;\n        }\n        return await response.json();\n    }\n    catch (error) {\n        throw error;\n    }\n};\nexport const searchAirport = async (request) => {\n    const requestBody = JSON.stringify(request);\n    const response = await fetch(`${apiUrl}/api/World/flight/airport-search`, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: requestBody\n    });\n    return response.json();\n};\n", "/**\n * Chuyển đổi đối tượng Date thành chuỗi định dạng yyyy-MM-dd\n * @param date - Đ<PERSON>i tượng Date cần chuyển đổi\n * @returns Chuỗi định dạng yyyy-MM-dd\n */\nexport const formatDate = (date) => {\n    const year = date.getFullYear();\n    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Thêm số 0 ở đầu nếu cần\n    const day = date.getDate().toString().padStart(2, '0'); // Thêm số 0 ở đầu nếu cần\n    return `${year}-${month}-${day}`; // Định dạng yyyy-MM-dd\n};\nexport function formatDateTo_ddMMyyyy(date, language) {\n    if (!date || date === undefined) {\n        return null;\n    }\n    var date1 = new Date(date);\n    if (language === 'vi') {\n        // Tr<PERSON> về dạng dd/MM/yyyy\n        return date1.toLocaleDateString('vi-VN', {\n            day: '2-digit',\n            month: '2-digit',\n            year: 'numeric'\n        });\n    }\n    // Trả về dạng 11 Jul, 2025\n    const day = date1.getDate().toString().padStart(2, '0');\n    const month = date1.toLocaleString('en-US', { month: 'short' }); // Jul\n    const year = date1.getFullYear();\n    return `${day} ${month}, ${year}`;\n}\nexport function getDurationByArray(legs) {\n    if (legs == null)\n        return '';\n    var duration = 0;\n    var departure = new Date(legs[0].DepartureDate);\n    var arrival = new Date(legs[legs.length - 1].ArrivalDate);\n    duration = arrival.getTime() - departure.getTime();\n    var hours = Math.floor(duration / 3600000);\n    var minutes = Math.floor((duration % 3600000) / 60000);\n    return hours.toString().padStart(2, '0') + 'h' + minutes.toString().padStart(2, '0');\n}\nexport function formatDateToString(date, language) {\n    if (!date)\n        return null;\n    let day, month, year;\n    // Trường hợp là đối tượng Date\n    if (date instanceof Date) {\n        day = date.getDate();\n        month = date.getMonth() + 1;\n        year = date.getFullYear();\n    }\n    // Trường hợp là object có day/month/year\n    else if (typeof date === 'object' && ('day' in date || 'month' in date || 'year' in date)) {\n        day = date.day || 1;\n        month = date.month || 1;\n        year = date.year || 2000;\n    }\n    // Trường hợp là string có thể parse được\n    else if (typeof date === 'string') {\n        const parsed = new Date(date);\n        if (isNaN(parsed.getTime()))\n            return null;\n        day = parsed.getDate();\n        month = parsed.getMonth() + 1;\n        year = parsed.getFullYear();\n    }\n    else {\n        return null;\n    }\n    const dd = day.toString().padStart(2, '0');\n    const mm = month.toString().padStart(2, '0');\n    const yyyy = year.toString();\n    // Trả về theo ngôn ngữ\n    return language === 'vi' ? `${dd}/${mm}/${yyyy}` : `${mm}/${dd}/${yyyy}`;\n}\nexport function validatePhone(phone) {\n    return phone.match(/^[0-9]{6,12}$/) ? true : false;\n}\nexport function validateEmail(email) {\n    return email.match(/^([\\w.%+-]+)@([\\w-]+\\.)+([\\w]{2,})$/i) ? true : false;\n}\nexport function getTimeFromDateTime(dateTime) {\n    const date = new Date(dateTime);\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n}\nexport function convertDurationToHour(duration) {\n    const hours = Math.floor(duration / 60).toString().padStart(2, '0');\n    const minutes = (duration % 60).toString().padStart(2, '0');\n    return `${hours}h${minutes}`;\n}\nexport function getDuration(leg) {\n    if (leg == null)\n        return '';\n    var departure = new Date(leg.DepartureDate);\n    var arrival = new Date(leg.ArrivalDate);\n    var duration = arrival.getTime() - departure.getTime();\n    var hours = Math.floor(duration / 3600000);\n    var minutes = Math.floor((duration % 3600000) / 60000);\n    return hours.toString().padStart(2, '0') + 'h' + minutes.toString().padStart(2, '0');\n}\nexport function formatddMMyyyy(date) {\n    if (date == null)\n        return '';\n    var dateObj = new Date(date);\n    return dateObj.getDate().toString().padStart(2, '0') + '/' + ((dateObj.getMonth() + 1).toString().padStart(2, '0')) + '/' + dateObj.getFullYear();\n}\nexport function getDayInWeek(date) {\n    const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];\n    const dateObj = new Date(date);\n    return days[dateObj.getDay()];\n}\nexport function getFlights(legs) {\n    return legs?.map(leg => leg.OperatingAirlines + leg.FlightNumber).join(' - ');\n}\nexport function getDirect(legs, language) {\n    return legs.length > 1 ? (language === 'vi' ? \"Nhiều chặng\" : \"Multiple stops\") : (language === 'vi' ? \"Bay thẳng\" : \"Direct flight\");\n}\nexport function getDurarionLeg(leg) {\n    var departure = new Date(leg.DepartureDate);\n    var arrival = new Date(leg.ArrivalDate);\n    var duration = (arrival.getTime() - departure.getTime()) / 60000;\n    return convertDurationToHour(duration);\n}\nexport function getPassengerDescription(searchTripRequest, paxType, language) {\n    if (!searchTripRequest) {\n        return '';\n    }\n    switch (paxType) {\n        case 'ADT':\n            return `${searchTripRequest?.Adult} x ${language === 'vi' ? 'Người lớn' : 'Adult'}`;\n        case 'CHD':\n            return `${searchTripRequest?.Child} x ${language === 'vi' ? 'Trẻ em' : 'Child'}`;\n        case 'INF':\n            return `${searchTripRequest?.Infant} x ${language === 'vi' ? 'Em bé' : 'Infant'}`;\n        default:\n            return '';\n    }\n}\nexport function getPassengerDescriptionV2(paxType, ADT = 0, CHD = 0, INF = 0, language) {\n    switch (paxType) {\n        case 'ADT':\n            return `${ADT} x ${language === 'vi' ? 'Người lớn' : 'Adult'}`;\n        case 'CHD':\n            return `${CHD} x ${language === 'vi' ? 'Trẻ em' : 'Child'}`;\n        case 'INF':\n            return `${INF} x ${language === 'vi' ? 'Em bé' : 'Infant'}`;\n        default:\n            return '';\n    }\n}\nexport function getTypePassenger(passenger, passengers, language) {\n    var type = '';\n    switch (passenger.type) {\n        case 'adult':\n            type = language === 'vi' ? 'Người lớn' : 'Adult';\n            break;\n        case 'child':\n            type = language === 'vi' ? 'Trẻ em' : 'Child';\n            break;\n        case 'infant':\n            type = language === 'vi' ? 'Em bé' : 'Infant';\n            break;\n    }\n    //get index of passenger in type\n    var indexPassenger = passengers.filter(p => p.type === passenger.type).indexOf(passenger);\n    var result = `${type} ${indexPassenger + 1}`;\n    return result;\n}\nexport function formatNumber(value, convertedVND, language) {\n    if (value === null || value === undefined)\n        return '';\n    const result = language === 'vi' ? value : value / convertedVND;\n    if (language === 'vi' || convertedVND === 1) {\n        return Math.round(result).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, '.');\n    }\n    else {\n        const [integerPart, decimalPart] = result.toFixed(2).split('.');\n        const formattedInteger = integerPart.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n        return `${formattedInteger}.${decimalPart}`;\n    }\n}\nexport function range(start, end) {\n    if (start > end) {\n        return [];\n    }\n    return Array(end - start + 1).fill(0).map((_, idx) => start + idx);\n}\nexport function getDayOfWeek(date, language) {\n    const dayOfWeek = new Date(date).getDay();\n    return language === 'vi' ? ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'][dayOfWeek] : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayOfWeek];\n}\nexport function formatDate_ddMM(date, language) {\n    const day = date.getDate().toString().padStart(2, '0');\n    if (language === 'vi') {\n        const monthStr = (date.getMonth() + 1).toString().padStart(2, '0');\n        return `${day}/${monthStr}`;\n    }\n    const monthStr = date.toLocaleString('en', { month: 'short' });\n    return `${day} ${monthStr}`;\n}\nexport function formatDateS(dateString, language) {\n    var date = new Date(dateString);\n    return formatDateTypeDate(date, language);\n}\nexport function formatDateTypeDate(date, language) {\n    const daysOfWeek = language === 'vi'\n        ? [\"Chủ nhật\", \"Thứ hai\", \"Thứ ba\", \"Thứ tư\", \"Thứ năm\", \"Thứ sáu\", \"Thứ bảy\"]\n        : [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"];\n    const dayOfWeek = daysOfWeek[date.getDay()];\n    if (!dayOfWeek) {\n        return '';\n    }\n    const day = date.getDate().toString().padStart(2, '0');\n    const year = date.getFullYear();\n    if (language === 'vi') {\n        const monthStr = (date.getMonth() + 1).toString().padStart(2, '0');\n        return `${dayOfWeek}, ${day}/${monthStr}/${year}`;\n    }\n    // Format for English: Thursday, 31 July 2023\n    const monthStr = date.toLocaleString('en', { month: 'long' });\n    return `${dayOfWeek}, ${day} ${monthStr} ${year}`;\n}\nexport function getFareType(bookingInfos) {\n    return bookingInfos.map(bookingInfo => bookingInfo.FareType || bookingInfo.CabinName).join(' - ');\n}\nexport function debounce(func, wait) {\n    let timeout;\n    return (...args) => {\n        clearTimeout(timeout);\n        timeout = window.setTimeout(() => func(...args), wait);\n    };\n}\n", "import { html, render } from \"lit\";\nexport const modalTemplate = (isOpen, title, content, isCountDown, countdown, close, reSearch) => {\n    if (!isOpen) {\n        // Clean up portal when modal is closed\n        const portalContainer = document.getElementById('modal-portal');\n        if (portalContainer) {\n            render('', portalContainer);\n        }\n        return;\n    }\n    // Create portal container if it doesn't exist\n    let portalContainer = document.getElementById('modal-portal');\n    if (!portalContainer) {\n        portalContainer = document.createElement('div');\n        portalContainer.id = 'modal-portal';\n        document.body.appendChild(portalContainer);\n    }\n    // Render modal content\n    const modalContent = html `\r\n    <div class=\"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-20 dark:bg-opacity-80\">\r\n        <div class=\"relative bg-white rounded-lg shadow dark:bg-gray-700\">\r\n            <!-- Modal header -->\r\n            <div class=\"flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600\">\r\n                <h3 class=\"text-xl font-bold text-nmt-600 dark:text-white\">\r\n                    ${title}\r\n                </h3>\r\n            </div>\r\n            <!-- Modal body -->\r\n            <div class=\"p-4 md:p-5 py-4 overflow-y-auto world-map\">\r\n                <div class=\"max-h-[60vh] h-full max-w-lg\">\r\n                    <!-- content notification -->\r\n                    ${content}\r\n                </div>\r\n            </div>\r\n            <!-- Modal footer -->\r\n            <div class=\"flex items-center justify-end pb-4 px-4 rounded-b dark:border-gray-600\">\r\n            ${isCountDown ? html `\r\n            <button @click=\"${reSearch}\"\r\n                    class=\"h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-400 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2\">\r\n                    Tải Lại (${countdown})\r\n                </button>\r\n            ` : html `\r\n            <button @click=\"${close}\"\r\n                    class=\"h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-400 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2\">\r\n                    Đóng\r\n                </button>\r\n            `}\r\n            </div>\r\n        </div>\r\n    </div>\r\n    `;\n    // Render the modal content into the portal container\n    render(modalContent, portalContainer);\n};\n", "import { __decorate, __metadata } from \"tslib\";\nimport { css, LitElement, unsafeCSS } from \"lit\";\nimport { modalTemplate } from \"./modal-template\";\nimport { customElement, property, state } from \"lit/decorators.js\";\nimport styles from '../../styles/styles.css';\nlet Modal = class Modal extends LitElement {\n    static { this.styles = [\n        unsafeCSS(styles),\n        css `\r\n        :host {\r\n        font-family: var(--nmt-font, 'Roboto', sans-serif);\r\n        }`\n    ]; }\n    constructor() {\n        super();\n        this.uri_searchBox = \"\";\n        this.isOpen = false;\n        this._title = \"\";\n        this.content = \"\";\n        this.isCountDown = false;\n        this.countdown = 0;\n    }\n    static get properties() {\n        return {\n            isOpen: { type: Boolean },\n            _title: { type: String },\n            content: { type: String },\n            isCountDown: { type: Boolean },\n            countdown: { type: Number },\n        };\n    }\n    firstUpdated(_changedProperties) {\n        super.firstUpdated(_changedProperties);\n        console.log('isOpen', this.isOpen);\n        console.log('title', this._title);\n        console.log('content', this.content);\n        console.log('isCountDown', this.isCountDown);\n        console.log('countdown', this.countdown);\n        if (this.isCountDown) {\n            this.startCountdown();\n        }\n    }\n    update(changedProperties) {\n        super.update(changedProperties);\n        if (changedProperties.has(\"isOpen\") && this.isOpen) {\n            this._title = this._title || \"Thông báo\";\n            this.content = this.content || \"Nội dung thông báo\";\n            this.isCountDown = this.isCountDown || false;\n            this.countdown = this.countdown || 0;\n        }\n    }\n    startCountdown() {\n        if (this.countdown > 0) {\n            setTimeout(() => {\n                this.countdown--;\n                this.startCountdown();\n            }, 1000);\n        }\n        else {\n            this.isCountDown = false;\n            this.reSearch();\n        }\n    }\n    start({ title = \"Thông báo\", content = \"Nội dung thông báo\", isCountDown = false, countdown = 0, } = {}) {\n        this._title = title;\n        this.content = content;\n        this.isCountDown = isCountDown;\n        this.countdown = countdown;\n        this.isOpen = true;\n        if (this.isCountDown) {\n            this.startCountdown();\n        }\n    }\n    // Phương thức để ẩn modal\n    close() {\n        this.isOpen = false;\n    }\n    reSearch() {\n        window.location.href = `/${this.uri_searchBox}`;\n    }\n    render() {\n        return modalTemplate(this.isOpen, this._title || \"Thông báo\", this.content, this.isCountDown || false, this.countdown || 0, this.close.bind(this), this.reSearch.bind(this));\n    }\n};\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], Modal.prototype, \"uri_searchBox\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], Modal.prototype, \"isOpen\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], Modal.prototype, \"_title\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], Modal.prototype, \"content\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], Modal.prototype, \"isCountDown\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Number)\n], Modal.prototype, \"countdown\", void 0);\nModal = __decorate([\n    customElement(\"modal-notification\"),\n    __metadata(\"design:paramtypes\", [])\n], Modal);\nexport { Modal };\n", "import { html } from \"lit\";\nimport { convertDurationToHour, formatDateTo_ddMMyyyy, formatNumber, getDurarionLeg, getFlights, getPassengerDescriptionV2, getTimeFromDateTime } from \"../../utils/dateUtils\";\nimport \"../modal/modal\";\nimport { environment } from \"../../environments/environment\";\nconst apiUrl = environment.apiUrl;\nexport const TripRePaymentTemplate = (autoFillOrderCode, request, uri_searchBox, language, _agent, _termsUrl, _isLoading, _agree, _isSubmit, _isShowDetailsTrip, _orderDetails, _inforAirports, _pricePaxInfor, _servicePrice, _sumPrice, _paymentMethod, _banks, _bankNote, _transferContent, _cashInfo, _currencySymbol, _convertedVND, showDetailsTrip, setPaymentMethod, selectBank, setAgree, onPayment, handleLanguageChange, showLanguageSelect) => html `\r\n    ${_isLoading ? html `\r\n    <div class=\"static\" *ngIf=\"isLoading\">\r\n        <div class=\"loader-container\">\r\n            <span class=\"loader\"></span>\r\n            <img src=\"${apiUrl}/assets/img/background/trip_loading2.gif\"/>\r\n            <span class=\"loadidng-vertical  bg-gradient-to-br from-nmt-600 to-nmt-400 shadow-lg shadow-white\">${language === 'vi' ? `Đang kiểm tra chuyến bay, vui lòng chờ trong giây lát...` : `Checking flight, please wait a moment...`}</span>\r\n        </div>\r\n    </div>\r\n    ` : \"\"}\r\n    <div class=\"w-full min-h-screen bg-gray-100 relative  max-md:pb-24\">\r\n    <div class=\"max-w-7xl mx-auto min-h-[70vh]  pb-8 relative\">\r\n        <div class=\"min-h-screen  py-8 \">\r\n            <div class=\"w-full md:px-4 \">\r\n                <div class=\"pt-4 pb-8\">\r\n                    <div\r\n                        class=\"w-full max-w-4xl mx-auto max-md:overflow-x-scroll max-md:overflow-y-hidden max-md:pb-[10px]\">\r\n                        <div class=\"flex items-center justify-center max-md:space-x-2\">\r\n                            <div class=\"flex items-center\">\r\n                                <div class=\"relative group\">\r\n                                    <div\r\n                                        class=\"flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white  cursor-pointer\">\r\n                                        <a href=\"/${uri_searchBox}\"\r\n                                            class=\"flex items-center gap-3 transition-transform duration-300 group-hover:scale-105\">\r\n                                            <div class=\"rounded-lg p-2 transition-colors duration-300 bg-nmt-500\">\r\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                    viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                    stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                    class=\"lucide lucide-search w-5 h-5 text-white\">\r\n                                                    <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\r\n                                                    <path d=\"m21 21-4.3-4.3\"></path>\r\n                                                </svg>\r\n                                            </div><span\r\n                                                class=\"font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden\">\r\n                                                ${language === 'vi' ? `Tìm kiếm` : `Search`}\r\n                                            </span>\r\n    </a>\r\n\r\n                                    </div>\r\n                                </div>\r\n                                <div\r\n                                    class=\"md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-white text-gray-400 max-md:hidden\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"\r\n                                        fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                        stroke-linejoin=\"round\" class=\"lucide lucide-chevron-right w-5 h-5\">\r\n                                        <path d=\"m9 18 6-6-6-6\"></path>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"flex items-center\">\r\n                                <div class=\"relative group\">\r\n                                    <div\r\n                                        class=\"flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-pointer\">\r\n                                        <div (click)=\"goToTripSelection()\"\r\n                                            class=\"flex items-center gap-3 transition-transform duration-300 group-hover:scale-105\">\r\n                                            <div class=\"rounded-lg p-2 transition-colors duration-300 bg-nmt-500\">\r\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                    viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                    stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                    class=\"lucide lucide-ticket w-5 h-5 text-white\">\r\n                                                    <path\r\n                                                        d=\"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z\">\r\n                                                    </path>\r\n                                                    <path d=\"M13 5v2\"></path>\r\n                                                    <path d=\"M13 17v2\"></path>\r\n                                                    <path d=\"M13 11v2\"></path>\r\n                                                </svg>\r\n                                            </div><span\r\n                                                class=\"font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden\">\r\n                                                ${language === 'vi' ? `Chọn vé` : `Select Ticket`}\r\n                                            </span>\r\n                                        </div>\r\n\r\n                                    </div>\r\n                                </div>\r\n                                <div\r\n                                    class=\"md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-white text-gray-400 max-md:hidden\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"\r\n                                        fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                        stroke-linejoin=\"round\" class=\"lucide lucide-chevron-right w-5 h-5\">\r\n                                        <path d=\"m9 18 6-6-6-6\"></path>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"flex items-center\">\r\n                                <div class=\"relative group\">\r\n                                    <div\r\n                                        class=\"flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed\">\r\n                                        <div\r\n                                            class=\"flex items-center gap-3 transition-transform duration-300 group-hover:scale-105\">\r\n                                            <div class=\"rounded-lg p-2 transition-colors duration-300 bg-nmt-500\">\r\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                    viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                    stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                    class=\"lucide lucide-user w-5 h-5 text-white\">\r\n                                                    <path d=\"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\"></path>\r\n                                                    <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\r\n                                                </svg>\r\n                                            </div><span\r\n                                                class=\"font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600  max-md:hidden\">\r\n                                                ${language === 'vi' ? `Thông tin` : `Information`}\r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                                <div\r\n                                    class=\"md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-white text-gray-400  max-md:hidden\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"\r\n                                        fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                        stroke-linejoin=\"round\" class=\"lucide lucide-chevron-right w-5 h-5\">\r\n                                        <path d=\"m9 18 6-6-6-6\"></path>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"flex items-center\">\r\n                                <div class=\"relative group\">\r\n                                    <div\r\n                                        class=\"flex items-center h-12 md:px-6 px-2 rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed\">\r\n                                        <div\r\n                                            class=\"flex items-center gap-3 transition-transform duration-300 group-hover:scale-105\">\r\n                                            <div class=\"rounded-lg p-2 transition-colors duration-300 bg-nmt-500\">\r\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                    viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                    stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                    class=\"lucide lucide-credit-card w-5 h-5 text-white\">\r\n                                                    <rect width=\"20\" height=\"14\" x=\"2\" y=\"5\" rx=\"2\"></rect>\r\n                                                    <line x1=\"2\" x2=\"22\" y1=\"10\" y2=\"10\"></line>\r\n                                                </svg>\r\n                                            </div>\r\n                                            <span\r\n                                                class=\"font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600\">\r\n                                                ${language === 'vi' ? `Thanh toán` : `Payment`}\r\n                                            </span>\r\n                                        </div>\r\n                                        <div class=\"absolute -bottom-[5px] left-1/2 transform -translate-x-1/2\">\r\n                                            <div class=\"w-2.5 h-2.5 rounded-full bg-nmt-500 ring-4 ring-white\"></div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                                <div\r\n                                    class=\"md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"\r\n                                        fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                        stroke-linejoin=\"round\" class=\"lucide lucide-chevron-right w-5 h-5\">\r\n                                        <path d=\"m9 18 6-6-6-6\"></path>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"flex items-center\">\r\n                                <div class=\"relative group\">\r\n                                    <div\r\n                                        class=\"flex items-center h-12 md:px-6 px-2 bg-white rounded-lg transition-all duration-300 ease-in-out border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 cursor-not-allowed\">\r\n                                        <div\r\n                                            class=\"flex items-center gap-3 transition-transform duration-300 group-hover:scale-105\">\r\n                                            <div\r\n                                                class=\"rounded-lg p-2 transition-colors duration-300 bg-gray-100 group-hover:bg-gray-200\">\r\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                    viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                    stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                    class=\"lucide lucide-check w-5 h-5 text-gray-600\">\r\n                                                    <path d=\"M20 6 9 17l-5-5\"></path>\r\n                                                </svg>\r\n                                            </div><span\r\n                                                class=\"font-medium tracking-wide whitespace-nowrap text-[15px] text-gray-600 max-md:hidden\">\r\n                                                ${language === 'vi' ? `Hoàn tất` : `Complete`}\r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid md:grid-cols-3 gap-6\">\r\n                    <!-- Payment Methods -->\r\n                    <div class=\"md:col-span-2\">\r\n                    \r\n                        <div\r\n                            class=\" border  rounded-lg shadow-sm space-y-1  bg-card text-card-foreground bg-white mb-8\">\r\n                            ${_orderDetails?.full?.InventoriesSelected.length > 0 ? html `\r\n                                                        <div class=\"rounded-lg bg-white shadow-lg\">\r\n                                                <div\r\n                                                    class=\"rounded-t-lg py-2  border-b border-gray-200 w-full px-4 flex justify-between items-center\">\r\n                                                    <h1 class=\"text-lg font-bold tracking-tight text-gray-900 dark:text-white\">\r\n                                                        ${language === 'vi' ? `Chi tiết chuyến bay` : `Flight Details`}\r\n                                                    </h1>\r\n                            <div class=\"flex justify-end items-center  \">\r\n                                    ${showLanguageSelect ? html `\r\n                                    <select id=\"language\" \r\n                                        class=\" text-sm bg-gray-200 rounded-lg px-2 py-1  border-none focus:outline-none text-gray-600 \"\r\n                                        .value=${language}\r\n                                        @change=${(e) => handleLanguageChange(e.target.value)}\r\n                                    >\r\n                                        <option style=\"background-color: #f0f0f0; color: black;\" value=\"en\">English</option>\r\n                                        <option style=\"background-color: #f0f0f0; color: black;\" value=\"vi\">Tiếng Việt</option>\r\n                                    </select>\r\n                                    ` : ''}\r\n                                </div>\r\n                                                </div>\r\n                            \r\n                                                <div class=\" bg-gray-100 border-gray-200 \">\r\n                                                    \r\n                                                    ${_orderDetails?.full?.InventoriesSelected.map((itinerarySelected, index) => html `\r\n                                                    <div class=\"w-full bg-gray-100 mt-4 border-b border-gray-200 shadow-md rounded-lg\">\r\n                            <!-- start flight infor -->\r\n                            <div class=\"bg-white rounded-e-lg rounded-bl-lg \">\r\n                                <div class=\"py-[2px]\">\r\n                                    <span\r\n                                        class=\" bg-gradient-to-br from-nmt-600 to-nmt-300 md:text-base text-sm text-white font-extrabold pl-2 py-1 pe-6 md:pe-12 rounded-tr-full\">\r\n                                        ${language === 'vi' ? `Chi tiết hành trình:` : `Itinerary Details:`}\r\n                                        ${_orderDetails?.full?.InventoriesSelected.length > 1 && index % 2 === 1 ?\n    (language === 'vi' ? `Chiều về` : `Return`) :\n    (language === 'vi' ? `Chiều đi` : `Departure`)}\r\n                                       \r\n                                    </span>\r\n                                </div>\r\n                                <div class=\"w-full  rounded-lg\">\r\n                                ${itinerarySelected.segment.Legs.map((leg, index) => html `\r\n                                ${index > 0 ? html `\r\n                                <div class=\"w-full bg-gray-600 p-1 text-white text-center text-sm\">\r\n                                    ${language === 'vi' ? `Trung chuyển tại` : `Transit at`} ${_inforAirports[leg.DepartureCode]?.name}\r\n                                    <strong>(${leg.DepartureCode})</strong> - ${language === 'vi' ? `Thời gian:` : `Time:`}\r\n                                    <strong>${convertDurationToHour(itinerarySelected.segment.Legs[index].StopTime)}</strong>\r\n                                </div>\r\n                                ` : \"\"}\r\n                               \r\n                                <div class=\"grid grid-cols-12 border-t border-gray-200  rounded-lg\">\r\n                                    <div\r\n                                        class=\"col-span-7 flex flex-col justify-center items-center text-black py-2 md:px-10  border-black rounded-lg\">\r\n                                        <div\r\n                                            class=\"w-full flex justify-between items-center px-4 md:text-sm  text-[10px]\">\r\n                                            <div class=\"text-left\">\r\n                                                <span class=\"font-extrabold\">\r\n                                                    (${leg?.DepartureCode})\r\n                                                </span>\r\n                                                ${_inforAirports[leg?.DepartureCode]?.cityName}\r\n                                                <div>\r\n                                                    <span class=\"text-gray-400\">\r\n                                                        ${_inforAirports[leg?.DepartureCode]?.name}</span>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div class=\"text-right\">\r\n                            \r\n                                                ${_inforAirports[leg?.ArrivalCode]?.cityName}\r\n                                                <span class=\"font-extrabold\">\r\n                                                    (${leg?.ArrivalCode})\r\n                                                </span>\r\n                                                <div>\r\n                                                    <span class=\"text-gray-400\">\r\n                                                        ${_inforAirports[leg?.ArrivalCode]?.name}</span>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div class=\"flex justify-center items-center w-full px-4 gap-2\">\r\n                                            <div class=\"flex flex-col justify-center items-center\">\r\n                                                <strong\r\n                                                    class=\"md:text-3xl text-base font-extrabold text-nmt-600\">\r\n                                                    ${getTimeFromDateTime(leg?.DepartureDate)}</strong>\r\n                                                <span class=\"md:text-sm text-[10px]\">\r\n                                                    ${formatDateTo_ddMMyyyy(leg?.DepartureDate, language)}\r\n                                                </span>\r\n                                                <strong class=\"md:text-base text-sm font-semibold text-gray-600\">\r\n                                                    ${language === 'vi' ? `Nhà ga:` : `Terminal:`} ${leg?.DepartureTerminal || '-'}\r\n                                                </strong>\r\n                                            </div>\r\n                                            <div\r\n                                                class=\"w-full flex-col justify-center items-center px-4 text-sm md:px-6\">\r\n                                                <div class=\"w-full text-center -mb-2\">\r\n                                                    ${leg?.Equipment}\r\n                                                </div>\r\n                                                <div class=\"w-full flex justify-center items-center\">\r\n                                                    <div class=\"w-full h-[2px] rounded-full bg-nmt-600\">\r\n                                                    </div>\r\n                                                    <svg xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                        class=\"w-5 h-5 fill-nmt-600 inline-block ml-[1px]\"\r\n                                                        viewBox=\"0 0 576 512\">\r\n                                                        <path\r\n                                                            d=\"M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z\" />\r\n                                                    </svg>\r\n                                                </div>\r\n                                                <div class=\"w-full text-center -mt-2\">\r\n                            \r\n                                                    ${convertDurationToHour(leg?.Duration)}\r\n                                                </div>\r\n                                            </div>\r\n                                            <div class=\"flex flex-col justify-center items-center\">\r\n                                                <strong\r\n                                                    class=\"md:text-2xl text-base font-extrabold text-nmt-500\">\r\n                                                    ${getTimeFromDateTime(leg?.ArrivalDate)}</strong>\r\n                                                <span class=\"md:text-sm text-[10px]\">\r\n                                                    ${formatDateTo_ddMMyyyy(leg?.ArrivalDate, language)}\r\n                                                </span>\r\n                                                <strong class=\"md:text-base text-sm font-semibold text-gray-600\">\r\n                                                    ${language === 'vi' ? `Nhà ga:` : `Terminal:`} ${leg?.ArrivalTerminal || '-'}\r\n                                                </strong>\r\n                                            </div>\r\n                                        </div>\r\n                            \r\n                                    </div>\r\n                                    <div class=\"col-span-5 flex flex-row  rounded-lg\">\r\n                                        <div\r\n                                            class=\"w-[1px] h-full bg-[100%_10px] bg-[linear-gradient(to_bottom,_transparent_50%,_#fb7740_50%)] bg-[length:100%_10px]\">\r\n                                        </div>\r\n                                        <div\r\n                                            class=\"px-4 py-2 flex flex-col items-center justify-center w-full md:text-sm text-[10px]\">\r\n                                            <span class=\"text-xs font-bold\">${language === 'vi' ? `Hãng vận chuyển` : `Airline`}</span>\r\n                                            <img src=\"${apiUrl}/assets/img/airlines/${leg?.OperatingAirlines}.png\"\r\n                                                class=\" w-auto h-12 mx-auto my-1\">\r\n                            \r\n                                            <span>${language === 'vi' ? `Chuyến bay:` : `Flight:`} <span\r\n                                                    class=\"text-nmt-500 font-extrabold tracking-wide\">${leg?.Airlines + leg?.FlightNumber}</span></span>\r\n                                            <span>${language === 'vi' ? `Loại vé:` : `Ticket Type:`}\r\n                                                <strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.CabinName}</strong>\r\n                            \r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div\r\n                                        class=\"col-span-12 border-t border-gray-200 p-2 flex justify-end items-center bg-gray-50/80  rounded-lg\">\r\n                                        <span class=\"text-end text-xs text-gray-800\">\r\n                                            ${language === 'vi' ? `Loại vé:` : `Ticket Type:`} <strong>\r\n                                                ${itinerarySelected.inventorySelected?.BookingInfos[index]?.FareType || itinerarySelected.inventorySelected?.BookingInfos[index]?.CabinName}\r\n                                            </strong>\r\n                                            | ${language === 'vi' ? `Hành lý xách tay:` : `Hand Baggage:`}\r\n                                            ${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandBaggage > 1 && itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag !== 0 ? html `<strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandBaggage}</strong>` : ``}\r\n                                            \r\n                                            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                class=\"lucide lucide-luggage w-4 h-4 text-blue-500 flex-shrink-0  inline-block\">\r\n                                                <path d=\"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\">\r\n                                                </path>\r\n                                                <rect width=\"20\" height=\"14\" x=\"2\" y=\"6\" rx=\"2\">\r\n                                                </rect>\r\n                                            </svg>\r\n                                            ${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag > 0 ? html `<strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag}</strong>` : html `<strong>7</strong>`}\r\n                            \r\n                                            kg\r\n                                            | ${language === 'vi' ? `Hành lý ký gửi:` : `Checked Baggage:`}\r\n                                            ${itinerarySelected.inventorySelected?.BookingInfos[index]?.BagPieces > 1 && itinerarySelected.inventorySelected?.BookingInfos[index]?.WeightBag !== 0 ? html `<strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.BagPieces}</strong>` : ``}\r\n                            \r\n                                         \r\n                                            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                class=\"lucide lucide-luggage w-4 h-4 text-blue-500 flex-shrink-0  inline-block\">\r\n                                                <path\r\n                                                    d=\"M6 20h0a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h0\">\r\n                                                </path>\r\n                                                <path d=\"M8 18V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v14\">\r\n                                                </path>\r\n                                                <path d=\"M10 20h4\"></path>\r\n                                                <circle cx=\"16\" cy=\"20\" r=\"2\"></circle>\r\n                                                <circle cx=\"8\" cy=\"20\" r=\"2\"></circle>\r\n                                            </svg>\r\n                            \r\n                                            <strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.WeightBag}</strong>\r\n                                            kg\r\n                                            | <svg class=\"w-4 h-4 inline-block text-gray-800 dark:text-white\"\r\n                                                aria-hidden=\"true\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\"\r\n                                                height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                                <path stroke=\"currentColor\" stroke-linecap=\"round\"\r\n                                                    stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                                                    d=\"M12 8v4l3 3m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\r\n                                            </svg>\r\n                                            ${language === 'vi' ? `Thời gian bay:` : `Flight Time:`}\r\n                                            <strong>${getDurarionLeg(leg)}</strong>\r\n                                            | ${language === 'vi' ? `Máy bay:` : `Aircraft:`} <strong>${leg.Equipment}</strong>\r\n                                        </span>\r\n                                    </div>\r\n                                </div>\r\n                                `)}\r\n                                </div>\r\n                            \r\n                            </div>\r\n                            <!-- end flight infor -->\r\n                            </div>\r\n                            `)}\r\n                                                </div>\r\n                            \r\n                                                <div\r\n                                                    class=\"col-span-12 border-t transition-all duration-700  ease-in-out    grid grid-cols-1 justify-end items-center bg-[#f8e2de] rounded-b-lg border-[1px] border-white shadow-[0px_0px_15px_#cccccc96,0px_0px_15px_#44444400_inset] ${_isShowDetailsTrip ? '!h-auto !w-full !opacity-100 p-2' : 'opacity-0 w-0 h-0 overflow-hidden'}\">\r\n                                                    <div class=\"grid grid-cols-4 border-b border-gray-600 md:text-sm text-xs\">\r\n                                                        <div class=\"text-start font-bold\">\r\n                                                            ${language === 'vi' ? `Hành khách` : `Passenger`}\r\n                                                        </div>\r\n                                                        <div class=\"text-end font-bold\">\r\n                                                            ${language === 'vi' ? `Giá vé` : `Ticket Price`}\r\n                                                        </div>\r\n                                                        <div class=\"text-end font-bold\">\r\n                                                            ${language === 'vi' ? `Thuế` : `Tax`}\r\n                                                        </div>\r\n                                                        <div class=\"text-end font-bold\">\r\n                                                            ${language === 'vi' ? `Giá Bán` : `Total Price`}\r\n                                                        </div>\r\n                                                    </div>\r\n                            \r\n                            \r\n                                                    ${_pricePaxInfor.map((fareInfo) => html `<div class=\"grid grid-cols-4 border-b border-gray-600 py-1 md:text-sm text-xs\">\r\n                                                    <div class=\"text-start\">\r\n                                                        ${getPassengerDescriptionV2(fareInfo?.PaxType, _orderDetails?.full?.adult, _orderDetails?.full?.child, _orderDetails?.full?.infant, language)}\r\n                                                    </div>\r\n                                                    <div class=\"text-end\">\r\n                                                        ${formatNumber(fareInfo.Fare, _convertedVND, language)}\r\n                            \r\n                                                    </div>\r\n                                                    <div class=\"text-end\">\r\n                                                        ${formatNumber(fareInfo.Tax, _convertedVND, language)}\r\n                            \r\n                                                    </div>\r\n                                                    <div class=\"text-end\">\r\n                                                        ${formatNumber(fareInfo.Fare + fareInfo.Tax, _convertedVND, language)}\r\n                                                    </div>\r\n                                                </div>`)}\r\n                                                 \r\n                                                    <div class=\" text-right md:text-sm text-xs\">\r\n                                                        ${language === 'vi' ? `Phí dịch vụ:` : `Service Fee:`} <strong class=\"md:text-xl text-base font-bold text-nmt-600\">\r\n                                                            <strong class=\"text-base text-nmt-600\">\r\n                                                                ${formatNumber(_servicePrice, _convertedVND, language)} </strong>\r\n                                                        </strong>${_currencySymbol}\r\n                                                    </div>\r\n                            \r\n                                                </div>\r\n                                                <div class=\"w-full text-right md:text-sm text-xs py-4 pe-2\">\r\n                                                    <span @click=\"${showDetailsTrip}\"\r\n                                                    class=\"text-nmt-600 cursor-pointer hover:underline text-sm underline ${_isShowDetailsTrip ? 'text-red-600' : ''}\">\r\n                                                         ${_isShowDetailsTrip ? (language === 'vi' ? `Ẩn chi tiết` : `Hide Details`) : (language === 'vi' ? `Chi tiết` : `Details`)}\r\n                                                    </span>\r\n                                                    ${language === 'vi' ? `Tổng cộng:` : `Total:`} <strong class=\"md:text-xl text-base font-bold text-nmt-600\">\r\n                            \r\n                                                        ${formatNumber(_sumPrice + _servicePrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>\r\n                                                </div>\r\n                                            </div>\r\n                                        ` : \"\"}\r\n                        </div>\r\n\r\n                        <div class=\" border  rounded-lg shadow-sm space-y-1  bg-card text-card-foreground bg-white\">\r\n                            <div class=\" rounded-t-lg px-4 pt-4 pb-2 border-b\">\r\n                                <h2 class=\"text-lg font-bold tracking-tight text-gray-900 dark:text-white\">\r\n                                    ${language === 'vi' ? `Phương Thức Thanh Toán` : `Payment Method`}\r\n                                </h2>\r\n                                <p class=\"text-sm text-gray-600\">${language === 'vi' ? `Chọn phương thức thanh toán của bạn` : `Choose your payment method`}</p>\r\n                            </div>\r\n                            <div class=\"md:p-6 p-2\">\r\n                                <div class=\"space-y-4\">\r\n                                    \r\n                                    <!-- Bank Transfer -->\r\n                                    <div class=\"flex items-start space-x-3 border rounded-lg p-4 transition-all border-gray-200 ${_paymentMethod === 'bank-transfer' ? 'border-nmt-500' : ''}\">\r\n                                        <div class=\"mt-1\">\r\n                                            <input type=\"radio\" id=\"bank-transfer\" name=\"paymentMethod\"\r\n                                                .checked=\"${_paymentMethod.includes('bank-transfer')}\"\r\n                                                @change=\"${() => setPaymentMethod('bank-transfer')}\"\r\n                                                class=\"h-4 w-4 text-nmt-600 focus:ring-nmt-500\" />\r\n                                        </div>\r\n                                        <div class=\"flex-1\">\r\n                                            <label for=\"bank-transfer\"\r\n                                                class=\"flex items-center text-lg font-medium cursor-pointer\">\r\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\"\r\n                                                    viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                    stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                    class=\"mr-2 h-5 w-5 text-nmt-600\">\r\n                                                    <rect width=\"20\" height=\"16\" x=\"2\" y=\"6\" rx=\"2\"></rect>\r\n                                                    <path d=\"M12 3v10\"></path>\r\n                                                    <path d=\"m5 9 7-4 7 4\"></path>\r\n                                                </svg>\r\n                                                ${language === 'vi' ? `Chuyển Khoản Ngân Hàng` : `Bank Transfer`}\r\n                                            </label>\r\n                                            <p class=\"text-sm text-gray-500 mt-1\">${language === 'vi' ? `Thanh toán trực tiếp từ tài khoản ngân hàng của bạn` : `Pay directly from your bank account`}</p>\r\n                                            <!-- banks list -->\r\n                                            ${_paymentMethod.includes('bank-transfer') ? html `\r\n                                                                                        <div class=\"mt-4 space-y-4 pt-3 border-t border-nmt-100\">\r\n                                                                                            <div class=\"grid grid-cols-3 gap-3\">\r\n                                                                                            ${_banks?.map((bank) => html `\r\n                                                                                             <button @click=\"${() => selectBank(bank)}\" type=\"button\"\r\n                                                                                                    class=\"border flex md:flex-row flex-col md:gap-1 justify-center items-center rounded-md py-2 md:px-4 px-1 text-center hover:bg-nmt-50 hover:text-nmt-800 transition-colors\r\n                                                                                                    ${bank.selected === true ? 'border-nmt-400 bg-nmt-400 text-white' : 'border-nmt-200 bg-white'}\">\r\n                                                                                                    <img src=\"${apiUrl}/${bank?.logoPath}\" alt=\"${bank?.bankName}\" class=\"md:h-8 h-6 rounded-md \" />\r\n                                                                                                    <span class=\"md:text-sm text-xs font-medium\">${bank?.bankName}</span>\r\n                                                                                                </button>\r\n                                                                                            `)}\r\n                                                                                            </div>\r\n                                                                                        </div>\r\n                                                                                        ` : ``}\r\n                                            \r\n                                            ${_paymentMethod.includes('bank-transfer') ? html `\r\n                                            <div class=\"mt-4 space-y-4 pt-3 border-t border-nmt-100\">\r\n                                                <div class=\"bg-nmt-100 p-3 rounded-md text-sm\">\r\n                                                    <p class=\"font-medium\">${language === 'vi' ? `Thông tin chuyển khoản` : `Transfer Information`}</p>\r\n                                                </div>\r\n\r\n                                                <div class=\"space-y-3\">\r\n                                                    ${_banks?.map((bank) => html `\r\n                                                         <div class=\"grid grid-cols-3 gap-2 text-sm ${bank.selected === true ? 'block' : 'hidden'}\">\r\n                                                        <div class=\"text-gray-600\">${language === 'vi' ? `Chủ Tài Khoản:` : `Account Holder:`}</div>\r\n                                                        <div class=\"col-span-2 font-medium\">${bank.accountHolder}</div>\r\n\r\n                                                        <div class=\"text-gray-600\">${language === 'vi' ? `Ngân hàng:` : `Bank:`}</div>\r\n                                                        <div class=\"col-span-2 font-medium\">${bank.bankName}</div>\r\n\r\n                                                        <div class=\"text-gray-600\">${language === 'vi' ? `Chi nhánh:` : `Branch:`}</div>\r\n                                                        <div class=\"col-span-2 font-medium\">${bank.branch}</div>\r\n\r\n                                                        <div class=\"text-gray-600\">${language === 'vi' ? `Số tài khoản:` : `Account Number:`}</div>\r\n                                                        <div class=\"col-span-2 font-medium\">${bank.accountNumber}</div>\r\n\r\n                                                        <div class=\"text-gray-600\">${language === 'vi' ? `Nội dung CK:` : `Transfer Content:`}</div>\r\n                                                        <div class=\"col-span-2 font-medium\">${_transferContent} ${autoFillOrderCode ? request.OrderCode : ''}</div>\r\n                                                        ${bank.qrImageUrl ? html `\r\n                                                            <div class=\"text-gray-600\">${language === 'vi' ? 'QR Code:' : 'QR Code:'}</div>\r\n                                                            <div class=\"col-span-2 font-medium\">\r\n                                                                <img src=\"${bank.qrImageUrl}\" alt=\"${bank?.bankName}\" class=\"h-36 rounded-md bg-gray-100 shadow-md\" />\r\n                                                            </div>\r\n                                                            ` : ''}\r\n                                                    </div>\r\n                                                    `)}\r\n                                                </div>\r\n\r\n                                                <div class=\"bg-nmt-50 border border-nmt-200 p-3 rounded-md mt-4\">\r\n                                                    <p class=\"text-sm font-medium text-nmt-800\">${language === 'vi' ? `Hướng dẫn xác nhận thanh toán:` : `Payment Confirmation Guide:`}</p>\r\n                                                    <p class=\"text-sm mt-2\">\r\n                                                        ${_bankNote}\r\n                                                    </p>\r\n                                                </div>\r\n\r\n                                            </div>\r\n                                            ` : ``}\r\n                                            \r\n                                        </div>\r\n                                    </div>\r\n\r\n\r\n\r\n                                    <!-- Cash Payment -->\r\n                                    <div class=\"flex items-start space-x-3 border rounded-lg p-4 transition-all ${_paymentMethod === 'cash' ? 'border-nmt-500' : ''}\">\r\n                                        <div class=\"mt-1\">\r\n                                            <input type=\"radio\" id=\"cash\" name=\"paymentMethod\"\r\n                                            .checked=\"${_paymentMethod === 'cash'}\" @change=\"${() => setPaymentMethod('cash')}\"\r\n                                                class=\"h-4 w-4 text-nmt-600 focus:ring-nmt-500\" />\r\n                                        </div>\r\n                                        <div class=\"flex-1\">\r\n                                            <label for=\"cash\"\r\n                                                class=\"flex items-center text-lg font-medium cursor-pointer\">\r\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\"\r\n                                                    viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                    stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                    class=\"mr-2 h-5 w-5 text-nmt-600\">\r\n                                                    <rect width=\"20\" height=\"12\" x=\"2\" y=\"6\" rx=\"2\"></rect>\r\n                                                    <circle cx=\"12\" cy=\"12\" r=\"2\"></circle>\r\n                                                    <path d=\"M6 12h.01M18 12h.01\"></path>\r\n                                                </svg>\r\n                                                ${language === 'vi' ? `Thanh Toán Tiền Mặt` : `Cash Payment`}\r\n                                            </label>\r\n                                            <p class=\"text-sm text-gray-500 mt-1\">${language === 'vi' ? `Thanh toán trực tiếp bằng tiền mặt tại quầy` : `Pay directly in cash at the counter`}</p>\r\n                                            ${_paymentMethod === 'cash' ? html `\r\n                                            <div class=\"mt-4 space-y-4 pt-3 border-t border-nmt-100\">\r\n                                                <div class=\"bg-nmt-100 p-3 rounded-md text-sm\">\r\n                                                    <p>${language === 'vi' ? `Thông tin thanh toán tiền mặt:` : `Cash Payment Information:`}</p>\r\n                                                </div>\r\n\r\n                                                <div class=\"space-y-3\">\r\n                                                    <div class=\"flex items-start gap-2\">\r\n                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\"\r\n                                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                            stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                            stroke-linejoin=\"round\"\r\n                                                            class=\"mt-0.5 text-nmt-600 min-w-5\">\r\n                                                            <path d=\"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z\">\r\n                                                            </path>\r\n                                                            <circle cx=\"12\" cy=\"10\" r=\"3\"></circle>\r\n                                                        </svg>\r\n                                                        <div>\r\n                                                            <p class=\"font-medium\">${language === 'vi' ? `Địa điểm thanh toán:` : `Payment Location:`}</p>\r\n                                                            <p class=\"text-sm text-gray-600\">${language === 'vi' ? `Quầy vé tại văn phòng đại lý của chúng tôi:` : `Ticket counter at our agency office:`} \r\n                                                            <span class=\"font-medium text-gray-800\">${_cashInfo.paymentAddress}</span>\r\n                                                            </p>\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    <div class=\"flex items-start gap-2\">\r\n                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\"\r\n                                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                            stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                            stroke-linejoin=\"round\" class=\"mt-0.5 text-nmt-600\">\r\n                                                            <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n                                                            <polyline points=\"12 6 12 12 16 14\"></polyline>\r\n                                                        </svg>\r\n                                                        <div>\r\n                                                            <p class=\"font-medium\">${language === 'vi' ? `Thời gian:` : `Time:`}</p>\r\n                                                            <ul class=\"list-disc pl-6 space-y-1\">\r\n                                                            <li>${_cashInfo.paymentDeadline}</li>\r\n                                                            <li>${_cashInfo.workingHours}</li>\r\n                                                            </ul>\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    <div class=\"flex items-start gap-2\">\r\n                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\"\r\n                                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                            stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                            stroke-linejoin=\"round\" class=\"mt-0.5 text-nmt-600\">\r\n                                                            <rect width=\"18\" height=\"18\" x=\"3\" y=\"3\" rx=\"2\" ry=\"2\">\r\n                                                            </rect>\r\n                                                            <line x1=\"9\" x2=\"15\" y1=\"9\" y2=\"9\"></line>\r\n                                                            <line x1=\"9\" x2=\"15\" y1=\"15\" y2=\"15\"></line>\r\n                                                        </svg>\r\n                                                        <div>\r\n                                                            <p class=\"font-medium\">${language === 'vi' ? `Ghi chú:` : `Note:`}</p>\r\n                                                            <p class=\"text-sm text-gray-600\">${_cashInfo.note}\r\n                                                            </p>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                            ` : ``}\r\n                                            \r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <!-- Flight Summary -->\r\n                    <div\r\n                        class=\"md:col-span-1 border  rounded-lg shadow-sm space-y-1  bg-card text-card-foreground bg-white max-md:hidden\">\r\n                        <div class=\" rounded-t-lg p-4 border-b\">\r\n                            <h2 class=\" text-lg font-bold tracking-tight text-gray-900 dark:text-white\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\"\r\n                                    fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                    stroke-linejoin=\"round\" class=\"h-5 w-5 inline-flex\">\r\n                                    <path\r\n                                        d=\"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z\">\r\n                                    </path>\r\n                                </svg>\r\n                                ${language === 'vi' ? `Thông Tin Chuyến Bay` : `Flight Information`}\r\n                            </h2>\r\n                        </div>\r\n                        <div>\r\n                            ${_orderDetails?.full?.InventoriesSelected.length > 0 ? html `\r\n                                            <div>\r\n                                            ${_orderDetails?.full?.InventoriesSelected.map((itinerarySelected, index) => html `\r\n                                                <div class=\"w-full relative \">\r\n                                                    <!-- Depart -->\r\n                                                    <h1 class=\"bg-[#c9efff] text-gray-800 border-b border-gray-200 w-full px-4 py-2 text-base font-bold ${index % 2 === 1 ? 'bg-[#fffbb3]' : ''} \">\r\n                                                        ${index % 2 === 0 ? (language === 'vi' ? `Chuyến đi` : `Departure`) : (language === 'vi' ? `Chuyến về` : `Return`)}\r\n                                                    </h1>\r\n                                                    <div\r\n                                                        class=\"flex flex-col justify-between items-center text-gray-800 py-2 border-r border-white \">\r\n                                                        <div class=\"w-full flex justify-between items-center px-4 text-xs font-bold\">\r\n                                                            <div class=\"text-left\">\r\n                                                                ${_inforAirports[itinerarySelected?.segment?.DepartureCode]?.cityName}\r\n                                                            </div>\r\n                                                            <div class=\"text-right\">\r\n                                                                ${_inforAirports[itinerarySelected?.segment?.ArrivalCode]?.cityName}\r\n                                                            </div>\r\n                            \r\n                                                        </div>\r\n                                                        <div class=\"flex justify-start items-center w-full px-4 gap-2 \">\r\n                                                            <div class=\"flex flex-col justify-start items-start\">\r\n                                                                <strong class=\"text-base font-bold rounded-full bg-white text-nmt-600\">\r\n                                                                    ${itinerarySelected?.segment?.DepartureCode}\r\n                                                                </strong>\r\n                                                                <strong class=\"text-xl font-bold \">\r\n                                                                    ${getTimeFromDateTime(itinerarySelected?.segment?.DepartureDate)}\r\n                                                                </strong>\r\n                            \r\n                                                            </div>\r\n                                                            <div class=\"w-full flex flex-col justify-center items-center\">\r\n                                                                <div\r\n                                                                    class=\"px-3  inline-flex justify-center items-center max-w-full text-nmt-600 bg-white box-border\">\r\n                                                                    <span\r\n                                                                        class=\"relative  text-nmt-600 font-normal h-[18px] tracking-[0px] leading-[18px] overflow-hidden text-ellipsis whitespace-nowrap\">\r\n                            \r\n                                                                        <img src=\"${apiUrl}/assets/img/airlines/${itinerarySelected?.segment?.Airlines}.png\" class=\"h-full w-auto\">\r\n                                                                    </span>\r\n                                                                </div>\r\n                                                                <div class=\"w-full flex justify-center items-center\">\r\n                                                                    <div class=\"w-full h-[2px] rounded-full bg-nmt-600\">\r\n                                                                    </div>\r\n                                                                    <svg xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                                        class=\"w-5 h-5 fill-nmt-600 inline-block ml-[1px]\"\r\n                                                                        viewBox=\"0 0 576 512\">\r\n                                                                        <path\r\n                                                                            d=\"M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z\" />\r\n                                                                        </svg>\r\n                                                                </div>\r\n                            \r\n                                                            </div>\r\n                                                            <div class=\"flex flex-col justify-end items-end\">\r\n                                                                <strong class=\"text-base font-bold rounded-full bg-white text-nmt-600\">\r\n                                                                    ${itinerarySelected?.segment?.ArrivalCode}\r\n                                                                </strong>\r\n                                                                <strong class=\"text-xl font-bold \">\r\n                                                                    ${getTimeFromDateTime(itinerarySelected?.segment?.ArrivalDate)}\r\n                                                                </strong>\r\n                                                            </div>\r\n                            \r\n                                                        </div>\r\n                                                        <div\r\n                                                            class=\"w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#d2d3d3_50%)] bg-[length:10px_100%]\">\r\n                                                        </div>\r\n                                                        <div class=\"flex justify-between items-center w-full px-4 mt-2 text-sm\">\r\n                                                            <div [ngClass]=\"{'flex flex-col': itinerarySelected?.segment?.Legs.length > 1}\">\r\n                                                                <span>\r\n                                                                    ${language === 'vi' ? `Ngày:` : `Date:`}\r\n                                                                </span>\r\n                                                                <strong class=\"text-xs\">\r\n                                                                    ${formatDateTo_ddMMyyyy(itinerarySelected?.segment?.DepartureDate, language)}\r\n                                                                </strong>\r\n                                                            </div>\r\n                                                            <div\r\n                                                                [ngClass]=\"{'flex flex-col items-end justify-end': itinerarySelected?.segment?.Legs.length > 1}\">\r\n                                                                <span>\r\n                                                                    ${language === 'vi' ? `Chuyến:` : `Flight:`}\r\n                                                                </span>\r\n                                                                <strong class=\"py-1 px-2 text-xs rounded-full bg-gray-200 text-right\">\r\n                                                                    ${getFlights(itinerarySelected?.segment?.Legs)}\r\n                                                                </strong>\r\n                                                            </div>\r\n                                                        </div>\r\n                            \r\n                                                    </div>\r\n                                                </div>\r\n                                            `)}\r\n                                                \r\n                                              \r\n                                            \r\n                                            ` : html `\r\n                                            <div class=\"py-4 text-center text-gray-600\">\r\n                                                    Chưa chọn chuyến bay\r\n                                                </div>\r\n                                            `}\r\n                                              <div\r\n                                                    class=\"w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#d2d3d3_50%)] bg-[length:10px_100%]\">\r\n                                                </div>\r\n\r\n                            <div class=\"w-full flex justify-between items-center px-4 mt-2 font-normal\">\r\n                    <div>\r\n                        <span>\r\n                            ${language === 'vi' ? `Giá vé:` : `Ticket Price:`}\r\n                        </span>\r\n                    </div>\r\n                    <div>\r\n                        <strong class=\"text-base text-nmt-600\">\r\n                            ${formatNumber(_sumPrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>\r\n                    </div>\r\n                </div>\r\n                <div class=\"w-full flex justify-between items-center px-4 mt-2 font-normal\">\r\n                    <div>\r\n                        <span>\r\n                            ${language === 'vi' ? `Giá dịch vụ:` : `Service Fee:`}\r\n                        </span>\r\n                    </div>\r\n                    <div>\r\n                        <strong class=\"text-base text-nmt-600\">\r\n                            ${formatNumber(_servicePrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>\r\n                    </div>\r\n                </div>\r\n                            <div\r\n                                class=\"w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#fb7740_50%)] bg-[length:10px_100%]\">\r\n                            </div>\r\n                            <div class=\"w-full flex justify-between items-center px-4 mt-2 font-bold\">\r\n                    <div>\r\n                        <span>\r\n                            ${language === 'vi' ? `Tổng giá:` : `Total Price:`}\r\n                        </span>\r\n                    </div>\r\n                    <div class=\" flex justify-end max-md:flex-col items-end\">\r\n                        <strong class=\"text-xl text-nmt-600 text-right w-full\">\r\n                            ${formatNumber(_sumPrice + _servicePrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>\r\n                    </div>\r\n                </div>\r\n                            <div\r\n                                class=\" flex flex-col items-center justify-center w-full bg-white p-4  mt-4 sticky bottom-0 \">\r\n                                <div class=\"mt-4 \">\r\n                                    <div>\r\n                                        <input type=\"checkbox\" id=\"agree\"\r\n                                        .checked=\"${_agree}\"\r\n                                        @change=\"${(e) => setAgree(e.target.checked)}\"\r\n                                            class=\"w-4 h-4 inline-block text-nmt-600 bg-gray-100 border-gray-300 rounded focus:ring-nmt-500 dark:focus:ring-nmt-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\">\r\n                                        <label for=\"agree\"\r\n                                            class=\"ms-2 text-sm cursor-pointer font-medium text-gray-900 dark:text-gray-300\">\r\n                                                ${language === 'vi' ? `Tôi đồng ý với` : `I agree with the`}\r\n                                                <a href=\"/${_termsUrl}\" target=\"_blank\" class=\"cursor-pointer hover:underline text-nmt-600 underline\">\r\n                                                    ${language === 'vi' ? `Quy Định và Điều Khoản` : `Terms and Conditions`}\r\n                                                </a>\r\n                                                ${language === 'vi' ? `của` : `of`} ${_agent}\r\n                                            </label>\r\n                                    </div>\r\n                                    ${!_agree && _isSubmit ? html `\r\n                                    <div class=\"flex items-center text-sm text-red-800 rounded-lg dark:text-red-400\"\r\n                                        role=\"alert\">\r\n                                        <svg class=\"flex-shrink-0 inline w-4 h-4 me-3\" aria-hidden=\"true\"\r\n                                            xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                            <path\r\n                                                d=\"M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z\" />\r\n                                        </svg>\r\n                                        <span class=\"sr-only\">Info</span>\r\n                                        <div>\r\n                                            <span class=\"font-medium\">${language === 'vi' ? `Bắt buộc!` : `Required!`}</span>\r\n                                            ${language === 'vi' ? `Bạn phải đồng ý trước khi thanh toán` : `You must agree before payment`}\r\n                                        </div>\r\n                                    </div>\r\n                                    ` : ``}\r\n                                    \r\n                                </div>\r\n                                <span class=\"relative group\">\r\n                                    <button @click=\"${() => onPayment()}\"\r\n                                        class=\"h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2\">\r\n                                        ${_paymentMethod === 'credit-card' || _paymentMethod === 'e-wallet' ?\n    (language === 'vi' ? `Thanh toán ngay` : `Pay Now`) :\n    (language === 'vi' ? `Đặt giữ chỗ` : `Hold Booking`)}\r\n                                    </button>\r\n                                </span>\r\n\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n<div class=\"z-50  w-full bg-white p-4 rounded-lg mt-4 shadow-2xl sticky bottom-0 border md:hidden\">\r\n    <div class=\"mt-4 w-full\">\r\n        <div>\r\n            <input type=\"checkbox\"\r\n                                        .checked=\"${_agree}\" .value=\"${_agree}\" @change=\"${(e) => setAgree(e.target.checked)}\"\r\n                class=\"w-4 h-4 inline-block text-nmt-600 bg-gray-100 border-gray-300 rounded focus:ring-nmt-500 dark:focus:ring-nmt-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\">\r\n            <label for=\"agree\" class=\"ms-2  cursor-pointer text-sm font-medium text-gray-900 dark:text-gray-300\">\r\n                ${language === 'vi' ? `Tôi đồng ý với` : `I agree with the`}\r\n                <a href=\"/${_termsUrl}\" target=\"_blank\"\r\n                    class=\"cursor-pointer hover:underline text-nmt-600 underline\">${language === 'vi' ? `Quy Định và Điều Khoản` : `Terms and Conditions`}</a>\r\n                ${language === 'vi' ? `của` : `of`} ${_agent}</label>\r\n        </div>\r\n        ${!_agree && _isSubmit ? html `\r\n                                    <div class=\"flex items-center text-sm text-red-800 rounded-lg dark:text-red-400\"\r\n                                        role=\"alert\">\r\n                                        <svg class=\"flex-shrink-0 inline w-4 h-4 me-3\" aria-hidden=\"true\"\r\n                                            xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                            <path\r\n                                                d=\"M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z\" />\r\n                                        </svg>\r\n                                        <span class=\"sr-only\">Info</span>\r\n                                        <div>\r\n                                            <span class=\"font-medium\">${language === 'vi' ? `Bắt buộc!` : `Required!`}</span> ${language === 'vi' ? `Bạn phải đồng ý trước khi thanh toán` : `You must agree before payment`}\r\n                                        </div>\r\n                                    </div>\r\n                                    ` : ``}\r\n                                    \r\n    </div>\r\n    <div class=\"flex items-center z-50 justify-between w-full bg-white rounded-lg   \">\r\n        <div class=\" flex justify-end  items-end\">\r\n            <strong class=\"text-xl text-nmt-600 text-right w-full\">\r\n                ${formatNumber(_sumPrice + _servicePrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>\r\n        </div>\r\n        <button @click=\"${() => onPayment()}\"\r\n            class=\"h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2\">\r\n            ${_paymentMethod === 'credit-card' || _paymentMethod === 'e-wallet' ?\n    (language === 'vi' ? 'Thanh toán ngay' : 'Pay Now') :\n    (language === 'vi' ? 'Đặt giữ chỗ' : 'Hold Booking')}\r\n            <div><svg aria-hidden=\"true\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\" class=\"w-6 h-6 text-white  animate-pulse\">\r\n                    <path fill-rule=\"evenodd\"\r\n                        d=\"M3 4a1 1 0 0 0-.822 1.57L6.632 12l-4.454 6.43A1 1 0 0 0 3 20h13.153a1 1 0 0 0 .822-.43l4.847-7a1 1 0 0 0 0-1.14l-4.847-7a1 1 0 0 0-.822-.43H3Z\"\r\n                        clip-rule=\"evenodd\"></path>\r\n                </svg></div>\r\n        </button>\r\n    </div>\r\n</div>\r\n\r\n<modal-notification  uri_searchBox=\"${uri_searchBox}\"></modal-notification>\r\n`;\n", "export const colors = {\n    inherit: 'inherit',\n    current: 'currentColor',\n    transparent: 'transparent',\n    black: '#000',\n    white: '#fff',\n    slate: {\n        '50': '#f8fafc',\n        '100': '#f1f5f9',\n        '200': '#e2e8f0',\n        '300': '#cbd5e1',\n        '400': '#94a3b8',\n        '500': '#64748b',\n        '600': '#475569',\n        '700': '#334155',\n        '800': '#1e293b',\n        '900': '#0f172a',\n        '950': '#020617'\n    },\n    gray: {\n        '50': '#f9fafb',\n        '100': '#f3f4f6',\n        '200': '#e5e7eb',\n        '300': '#d1d5db',\n        '400': '#9ca3af',\n        '500': '#6b7280',\n        '600': '#4b5563',\n        '700': '#374151',\n        '800': '#1f2937',\n        '900': '#111827',\n        '950': '#030712'\n    },\n    zinc: {\n        '50': '#fafafa',\n        '100': '#f4f4f5',\n        '200': '#e4e4e7',\n        '300': '#d4d4d8',\n        '400': '#a1a1aa',\n        '500': '#71717a',\n        '600': '#52525b',\n        '700': '#3f3f46',\n        '800': '#27272a',\n        '900': '#18181b',\n        '950': '#09090b'\n    },\n    neutral: {\n        '50': '#fafafa',\n        '100': '#f5f5f5',\n        '200': '#e5e5e5',\n        '300': '#d4d4d4',\n        '400': '#a3a3a3',\n        '500': '#737373',\n        '600': '#525252',\n        '700': '#404040',\n        '800': '#262626',\n        '900': '#171717',\n        '950': '#0a0a0a'\n    },\n    stone: {\n        '50': '#fafaf9',\n        '100': '#f5f5f4',\n        '200': '#e7e5e4',\n        '300': '#d6d3d1',\n        '400': '#a8a29e',\n        '500': '#78716c',\n        '600': '#57534e',\n        '700': '#44403c',\n        '800': '#292524',\n        '900': '#1c1917',\n        '950': '#0c0a09',\n    },\n    red: {\n        '50': '#fef2f2',\n        '100': '#fee2e2',\n        '200': '#fecaca',\n        '300': '#fca5a5',\n        '400': '#f87171',\n        '500': '#ef4444',\n        '600': '#dc2626',\n        '700': '#b91c1c',\n        '800': '#991b1b',\n        '900': '#7f1d1d',\n        '950': '#450a0a'\n    },\n    orange: {\n        '50': '#fff7ed',\n        '100': '#ffedd5',\n        '200': '#fed7aa',\n        '300': '#fdba74',\n        '400': '#fb923c',\n        '500': '#f97316',\n        '600': '#ea580c',\n        '700': '#c2410c',\n        '800': '#9a3412',\n        '900': '#7c2d12',\n        '950': '#431407'\n    },\n    amber: {\n        '50': '#fffbeb',\n        '100': '#fef3c7',\n        '200': '#fde68a',\n        '300': '#fcd34d',\n        '400': '#fbbf24',\n        '500': '#f59e0b',\n        '600': '#d97706',\n        '700': '#b45309',\n        '800': '#92400e',\n        '900': '#78350f',\n        '950': '#451a03'\n    },\n    yellow: {\n        '50': '#fefce8',\n        '100': '#fef9c3',\n        '200': '#fef08a',\n        '300': '#fde047',\n        '400': '#facc15',\n        '500': '#eab308',\n        '600': '#ca8a04',\n        '700': '#a16207',\n        '800': '#854d0e',\n        '900': '#713f12',\n        '950': '#422006'\n    },\n    lime: {\n        '50': '#f7fee7',\n        '100': '#ecfccb',\n        '200': '#d9f99d',\n        '300': '#bef264',\n        '400': '#a3e635',\n        '500': '#84cc16',\n        '600': '#65a30d',\n        '700': '#4d7c0f',\n        '800': '#3f6212',\n        '900': '#365314',\n        '950': '#1a2e05',\n    },\n    green: {\n        '50': '#f0fdf4',\n        '100': '#dcfce7',\n        '200': '#bbf7d0',\n        '300': '#86efac',\n        '400': '#4ade80',\n        '500': '#22c55e',\n        '600': '#16a34a',\n        '700': '#15803d',\n        '800': '#166534',\n        '900': '#14532d',\n        '950': '#052e16'\n    },\n    emerald: {\n        '50': '#ecfdf5',\n        '100': '#d1fae5',\n        '200': '#a7f3d0',\n        '300': '#6ee7b7',\n        '400': '#34d399',\n        '500': '#10b981',\n        '600': '#059669',\n        '700': '#047857',\n        '800': '#065f46',\n        '900': '#064e3b',\n        '950': '#022c22'\n    },\n    teal: {\n        '50': '#f0fdfa',\n        '100': '#ccfbf1',\n        '200': '#99f6e4',\n        '300': '#5eead4',\n        '400': '#2dd4bf',\n        '500': '#14b8a6',\n        '600': '#0d9488',\n        '700': '#0f766e',\n        '800': '#115e59',\n        '900': '#134e4a',\n        '950': '#042f2e'\n    },\n    cyan: {\n        '50': '#ecfeff',\n        '100': '#cffafe',\n        '200': '#a5f3fc',\n        '300': '#67e8f9',\n        '400': '#22d3ee',\n        '500': '#06b6d4',\n        '600': '#0891b2',\n        '700': '#0e7490',\n        '800': '#155e75',\n        '900': '#164e63',\n        '950': '#083344'\n    },\n    sky: {\n        '50': '#f0f9ff',\n        '100': '#e0f2fe',\n        '200': '#bae6fd',\n        '300': '#7dd3fc',\n        '400': '#38bdf8',\n        '500': '#0ea5e9',\n        '600': '#0284c7',\n        '700': '#0369a1',\n        '800': '#075985',\n        '900': '#0c4a6e',\n        '950': '#082f49'\n    },\n    blue: {\n        '50': '#eff6ff',\n        '100': '#dbeafe',\n        '200': '#bfdbfe',\n        '300': '#93c5fd',\n        '400': '#60a5fa',\n        '500': '#3b82f6',\n        '600': '#2563eb',\n        '700': '#1d4ed8',\n        '800': '#1e40af',\n        '900': '#1e3a8a',\n        '950': '#172554'\n    },\n    indigo: {\n        '50': '#eef2ff',\n        '100': '#e0e7ff',\n        '200': '#c7d2fe',\n        '300': '#a5b4fc',\n        '400': '#818cf8',\n        '500': '#6366f1',\n        '600': '#4f46e5',\n        '700': '#4338ca',\n        '800': '#3730a3',\n        '900': '#312e81',\n        '950': '#1e1b4b'\n    },\n    violet: {\n        '50': '#f5f3ff',\n        '100': '#ede9fe',\n        '200': '#ddd6fe',\n        '300': '#c4b5fd',\n        '400': '#a78bfa',\n        '500': '#8b5cf6',\n        '600': '#7c3aed',\n        '700': '#6d28d9',\n        '800': '#5b21b6',\n        '900': '#4c1d95',\n        '950': '#2e1065'\n    },\n    purple: {\n        '50': '#faf5ff',\n        '100': '#f3e8ff',\n        '200': '#e9d5ff',\n        '300': '#d8b4fe',\n        '400': '#c084fc',\n        '500': '#a855f7',\n        '600': '#9333ea',\n        '700': '#7e22ce',\n        '800': '#6b21a8',\n        '900': '#581c87',\n        '950': '#3b0764'\n    },\n    fuchsia: {\n        '50': '#fdf4ff',\n        '100': '#fae8ff',\n        '200': '#f5d0fe',\n        '300': '#f0abfc',\n        '400': '#e879f9',\n        '500': '#d946ef',\n        '600': '#c026d3',\n        '700': '#a21caf',\n        '800': '#86198f',\n        '900': '#701a75',\n        '950': '#4a044e',\n    },\n    pink: {\n        '50': '#fdf2f8',\n        '100': '#fce7f3',\n        '200': '#fbcfe8',\n        '300': '#f9a8d4',\n        '400': '#f472b6',\n        '500': '#ec4899',\n        '600': '#db2777',\n        '700': '#be185d',\n        '800': '#9d174d',\n        '900': '#831843',\n        '950': '#500724'\n    },\n    rose: {\n        '50': '#fff1f2',\n        '100': '#ffe4e6',\n        '200': '#fecdd3',\n        '300': '#fda4af',\n        '400': '#fb7185',\n        '500': '#f43f5e',\n        '600': '#e11d48',\n        '700': '#be123c',\n        '800': '#9f1239',\n        '900': '#881337',\n        '950': '#4c0519'\n    }\n};\n", "import { colors } from \"../interface/DefaultColors\";\n/**\n * Thi<PERSON><PERSON> lập các biến CSS đại diện cho màu \"nmt\"\n * @param baseColor Tên màu trong Tailwind (vd: 'blue', 'rose') hoặc mã màu hex (vd: '#3b82f6')\n */\nexport function setnmtColors(baseColor) {\n    console.log('baseColor', baseColor);\n    // Nếu baseColor là object chứa các giá trị màu đã được tính toán sẵn\n    try {\n        const parsed = JSON.parse(baseColor);\n        if (typeof parsed === 'object') {\n            const root = document.documentElement;\n            Object.entries(parsed).forEach(([key, value]) => {\n                root.style.setProperty(`--color-nmt-${key}`, value);\n            });\n            return;\n        }\n    }\n    catch (e) {\n    }\n    // Lấy màu hex từ tên màu Tailwind hoặc chuỗi hex\n    const getHexColor = (baseColor) => {\n        // Kiểm tra xem baseColor có phải là mã hex không\n        if (baseColor.startsWith(\"#\")) {\n            return baseColor;\n        }\n        // Kiểm tra xem baseColor có phải là tên màu trong Tailwind không\n        const color = colors[baseColor];\n        if (color) {\n            // Lấy màu chính (500) từ danh sách màu\n            return color[\"500\"];\n        }\n        // Nếu không phải tên màu hợp lệ, trả về màu mặc định (màu chính của Tailwind)\n        // return colors.blue[\"500\"]; // Màu xanh dương mặc định\n        return colors.orange[\"500\"]; // Màu cam mặc định\n    };\n    // Làm sáng màu\n    const lighten = (hex, percent) => {\n        const num = parseInt(hex.replace(\"#\", \"\"), 16);\n        const amt = Math.round(2.55 * percent);\n        const r = Math.min(255, Math.max(0, (num >> 16) + amt));\n        const g = Math.min(255, Math.max(0, ((num >> 8) & 0xff) + amt));\n        const b = Math.min(255, Math.max(0, (num & 0xff) + amt));\n        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;\n    };\n    // Làm tối màu\n    const darken = (hex, percent) => {\n        const num = parseInt(hex.replace(\"#\", \"\"), 16);\n        const amt = Math.round(2.55 * percent);\n        const r = Math.min(255, Math.max(0, (num >> 16) - amt));\n        const g = Math.min(255, Math.max(0, ((num >> 8) & 0xff) - amt));\n        const b = Math.min(255, Math.max(0, (num & 0xff) - amt));\n        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;\n    };\n    // Lấy màu gốc\n    const hexColor = getHexColor(baseColor);\n    // Các sắc độ màu\n    const shades = {\n        \"50\": lighten(hexColor, 50),\n        \"100\": lighten(hexColor, 40),\n        \"200\": lighten(hexColor, 30),\n        \"300\": lighten(hexColor, 20),\n        \"400\": lighten(hexColor, 10),\n        \"500\": hexColor,\n        \"600\": darken(hexColor, 10),\n        \"700\": darken(hexColor, 20),\n        \"800\": darken(hexColor, 30),\n        \"900\": darken(hexColor, 40),\n        \"950\": darken(hexColor, 50),\n    };\n    // Lấy root element\n    const root = document.documentElement;\n    // Gán vào biến CSS\n    Object.entries(shades).forEach(([key, value]) => {\n        root.style.setProperty(`--color-nmt-${key}`, value);\n    });\n}\n", "export const BANK_LOGOS = [\n    { name: 'VPBank', logoPath: '/assets/img/banks/logo-vpbank.jpg' },\n    { name: 'BIDV', logoPath: '/assets/img/banks/logo-bidv.jpg' },\n    { name: 'Vietcombank', logoPath: '/assets/img/banks/logo-vietcombank.jpg' },\n    { name: 'VietinBank', logoPath: '/assets/img/banks/logo-vietinbank.jpg' },\n    { name: 'MBBANK', logoPath: '/assets/img/banks/logo-mbbank.jpg' },\n    { name: 'ACB', logoPath: '/assets/img/banks/logo-acb.jpg' },\n    { name: 'SHB', logoPath: '/assets/img/banks/logo-shb.jpg' },\n    { name: 'Techcombank', logoPath: '/assets/img/banks/logo-techcombank.jpg' },\n    { name: 'Agribank', logoPath: '/assets/img/banks/logo-agribank.jpg' },\n    { name: 'HDBank', logoPath: '/assets/img/banks/logo-hdbank.jpg' },\n    { name: 'LienVietPostBank', logoPath: '/assets/img/banks/logo-lpbank.jpg' },\n    { name: 'VIB', logoPath: '/assets/img/banks/logo-vib.jpg' },\n    { name: 'SeABank', logoPath: '/assets/img/banks/logo-seabank.jpg' },\n    { name: 'VBSP', logoPath: '/assets/img/banks/logo-VBSP.webp' },\n    { name: 'TPBank', logoPath: '/assets/img/banks/logo-tpbank.jpg' },\n    { name: 'OCB', logoPath: '/assets/img/banks/logo-ocb.jpg' },\n    { name: 'MSB', logoPath: '/assets/img/banks/logo-msb.jpg' },\n    { name: 'Sacombank', logoPath: '/assets/img/banks/logo-sacombank.jpg' },\n    { name: 'Eximbank', logoPath: '/assets/img/banks/logo-eximbank.jpg' },\n    { name: 'SCB', logoPath: '/assets/img/banks/logo-scb.jpg' },\n    { name: 'VDB', logoPath: '/assets/img/banks/logo-vdb.jpg' },\n    { name: 'Nam A Bank', logoPath: '/assets/img/banks/logo-namabank.jpg' },\n    { name: 'ABBANK', logoPath: '/assets/img/banks/logo-abbank.jpg' },\n    { name: 'PVcomBank', logoPath: '/assets/img/banks/logo-pvcombank.jpg' },\n    { name: 'Bac A Bank', logoPath: '/assets/img/banks/logo-bacabank.jpg' },\n    { name: 'UOB', logoPath: '/assets/img/banks/logo-uob.jpg' },\n    { name: 'Woori', logoPath: '/assets/img/banks/logo-woori-bank.jpg' },\n    { name: 'HSBC', logoPath: '/assets/img/banks/logo-hsbc.jpg' },\n    { name: 'SCBVL', logoPath: '/assets/img/banks/logo-SCBVL.jpg' },\n    { name: 'PBVN', logoPath: '/assets/img/banks/logo-public-bank.jpg' },\n    { name: 'SHBVN', logoPath: '/assets/img/banks/logo-shinhan-bank.jpg' },\n    { name: 'NCB', logoPath: '/assets/img/banks/logo-ncb.jpg' },\n    { name: 'VietABank', logoPath: '/assets/img/banks/logo-vietabank.jpg' },\n    { name: 'BVBank', logoPath: '/assets/img/banks/logo-bvbank.jpg' },\n    { name: 'Vikki Bank', logoPath: '/assets/img/banks/logo-vikki.png' },\n    { name: 'Vietbank', logoPath: '/assets/img/banks/logo-vietbank.jpg' },\n    { name: 'ANZVL', logoPath: '/assets/img/banks/logo-anz-bank.jpg' },\n    { name: 'MBV', logoPath: '/assets/img/banks/logo-mbv.jpg' },\n    { name: 'CIMB', logoPath: '/assets/img/banks/logo-cimb.svg' },\n    { name: 'Kienlongbank', logoPath: '/assets/img/banks/logo-kienlongbank.jpg' },\n    { name: 'IVB', logoPath: '/assets/img/banks/logo-indovina.jpg' },\n    { name: 'BAOVIET Bank', logoPath: '/assets/img/banks/logo-baovietbank.jpg' },\n    { name: 'SAIGONBANK', logoPath: '/assets/img/banks/logo-saigonbank.jpg' },\n    { name: 'Co-opBank', logoPath: '/assets/img/banks/logo-co-opbank.jpg' },\n    { name: 'GPBank', logoPath: '/assets/img/banks/logo-gpbank.jpg' },\n    { name: 'VRB', logoPath: '/assets/img/banks/logo-VRB.png' },\n    { name: 'VCBNeo', logoPath: '/assets/img/banks/logo-vcbneo.png' },\n    { name: 'HLBVN', logoPath: '/assets/img/banks/logo-hong-leong-bank.jpg' },\n    { name: 'PGBank', logoPath: '/assets/img/banks/logo-PGBank.png' }\n];\n", "import { __decorate, __metadata } from \"tslib\";\nimport { css, LitElement, unsafeCSS } from \"lit\";\nimport { CryptoService } from \"../../services/CryptoService\";\nimport { FlightService } from \"../../services/FlightService\";\nimport { customElement, property, state } from \"lit/decorators.js\";\nimport { getAirportInfoByCode, getFeatures } from \"../../services/WorldServices\";\nimport { TripRePaymentTemplate } from \"./trip-repayment-template\";\nimport { setnmtColors } from \"../../services/ColorService\";\nimport { BANK_LOGOS } from \"../../share/data/bank-logos\";\nimport styles from '../../styles/styles.css';\nconst cryptoService = new CryptoService();\nconst flightService = new FlightService();\nlet TripRePayment = class TripRePayment extends LitElement {\n    static { this.styles = [\n        unsafeCSS(styles),\n        css `\r\n        :host {\r\n          font-family: var(--nmt-font, 'Roboto', sans-serif);\r\n        }`\n    ]; }\n    get language() {\n        return this._language;\n    }\n    set language(value) {\n        const oldValue = this._language;\n        // Chỉ kiểm tra URL nếu autoLanguageParam được bật\n        if (this.autoLanguageParam) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const languageParam = urlParams.get('language');\n            if (languageParam && languageParam !== this._language) {\n                // URL có language parameter - luôn ưu tiên URL\n                this._language = languageParam;\n                console.log('Language overridden from URL parameter:', this._language);\n            }\n            else {\n                // URL không có language parameter - sử dụng giá trị được set\n                this._language = value;\n                console.log('Language set from property:', this._language);\n                // Tự động thêm vào URL nếu chưa có\n                if (!this._hasCheckedURL) {\n                    this.updateURLWithLanguage();\n                    this._hasCheckedURL = true;\n                }\n            }\n        }\n        else {\n            // Không sử dụng URL parameter, chỉ set giá trị trực tiếp\n            this._language = value;\n            console.log('Language set from property (autoLanguageParam disabled):', this._language);\n        }\n        this.requestUpdate('language', oldValue);\n    }\n    get currencySymbolAv() {\n        return this.convertedVND === 1 || this.language === 'vi' ? '₫' : this.currencySymbol;\n    }\n    constructor(_cryptoService, _flightService) {\n        super();\n        this._cryptoService = _cryptoService;\n        this._flightService = _flightService;\n        this.autoFillOrderCode = false;\n        this.mode = \"online\";\n        this.googleFontsUrl = \"\";\n        this.font = \"\";\n        this.termsUrl = 'terms-and-policies';\n        this.ApiKey = '';\n        this.color = \"\";\n        this.uri_searchBox = \"\";\n        this.showLanguageSelect = false;\n        this.autoLanguageParam = false; // Bật/tắt tính năng tự động thêm language param\n        this._language = \"vi\";\n        this._hasCheckedURL = false; // Flag để tránh kiểm tra URL nhiều lần\n        this._ApiKey = '';\n        this._isLoading = false;\n        this._agree = true;\n        this._isSubmit = false;\n        this._isNotValid = false;\n        this._orderDetails = null;\n        this._orderAvailable = null;\n        this._isShowDetailsTrip = false;\n        this._passengers = [];\n        this._inforAirports = [];\n        this._pricePaxInfor = [];\n        this._servicePrice = 0;\n        this._sumPrice = 0;\n        this._totalPrice = 0;\n        this._paymentMethod = '';\n        this.titleModal = '';\n        this.contentModal = '';\n        this.isCountDown = false;\n        this.countdown = 0;\n        this.isShowModal = false;\n        this.request = {\n            OrderCode: '',\n            PhoneCustomer: '',\n            EmailCustomer: ''\n        };\n        this.banks = [];\n        this.bankNote = '';\n        this.transferContent = '';\n        this.cashInfo = null;\n        this.agent = '';\n        this.displayMode = 'total';\n        this.convertedVND = 1;\n        this.currencySymbol = '₫';\n        this._cryptoService = cryptoService;\n        this._flightService = flightService;\n    }\n    connectedCallback() {\n        super.connectedCallback();\n        this._ApiKey = this.ApiKey;\n        this.removeAttribute(\"ApiKey\");\n        // Kiểm tra URL ngay khi component được kết nối, trường hợp không có attribute language\n        this.checkLanguageFromURL();\n    }\n    checkLanguageFromURL() {\n        // Chỉ kiểm tra URL nếu autoLanguageParam được bật\n        if (!this.autoLanguageParam) {\n            console.log('autoLanguageParam disabled, skipping URL check');\n            return;\n        }\n        const urlParams = new URLSearchParams(window.location.search);\n        const languageParam = urlParams.get('language');\n        if (languageParam) {\n            // URL có language parameter - set giá trị từ URL\n            this._language = languageParam;\n            console.log('Language initialized from URL parameter:', this._language);\n            this.requestUpdate('language');\n        }\n        else if (!this._hasCheckedURL) {\n            // URL không có language parameter - tự động thêm vào URL với giá trị mặc định\n            this.updateURLWithLanguage();\n            this._hasCheckedURL = true;\n        }\n    }\n    updateURLWithLanguage() {\n        const currentUrl = new URL(window.location.href);\n        const params = new URLSearchParams(currentUrl.search);\n        // Thêm hoặc cập nhật parameter language\n        params.set('language', this._language);\n        // Cập nhật URL mà không reload trang\n        const newUrl = `${currentUrl.pathname}?${params.toString()}`;\n        window.history.replaceState({}, '', newUrl);\n        console.log('URL updated with language parameter:', newUrl);\n    }\n    async firstUpdated(_changedProperties) {\n        super.firstUpdated(_changedProperties);\n        // this.checkDevice();\n        await this.getRequest();\n        this.getPricePax();\n        this._sumPrice = this.getSumPrice();\n        this.loadPaymentValue();\n        if (this.color !== \"\") {\n            setnmtColors(this.color);\n            this.requestUpdate();\n        }\n        console.log(this.googleFontsUrl);\n        // Handle Google Fonts\n        if (this.googleFontsUrl) {\n            const googleFontsLink = document.createElement('link');\n            googleFontsLink.rel = 'stylesheet';\n            googleFontsLink.href = this.googleFontsUrl;\n            document.head.appendChild(googleFontsLink);\n        }\n        else {\n            // Default font if no Google Fonts URL provided\n            const googleFontsLink = document.createElement('link');\n            googleFontsLink.rel = 'stylesheet';\n            googleFontsLink.href = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap';\n            document.head.appendChild(googleFontsLink);\n        }\n        console.log('font', this.font);\n        if (this.font !== \"\") {\n            const root = document.documentElement;\n            root.style.setProperty('--nmt-font', this.font);\n        }\n    }\n    updated(_changedProperties) {\n        super.updated(_changedProperties);\n    }\n    loadPaymentValue() {\n        getFeatures('cash;credit', this._ApiKey).then((res) => {\n            if (res.isSuccessed) {\n                this.agent = res.resultObj?.agent || '';\n                const bankModel = JSON.parse(res.resultObj?.credit || '{}');\n                this.bankNote = bankModel?.note || '';\n                this.transferContent = bankModel?.transferContent;\n                this.banks = bankModel?.banksInfo?.map((bank) => {\n                    const matchedLogo = BANK_LOGOS.find(logo => logo.name.toLowerCase() === bank?.bankName.toLowerCase());\n                    return {\n                        ...bank,\n                        logoPath: matchedLogo?.logoPath || null,\n                        selected: false\n                    };\n                });\n                this.cashInfo = JSON.parse(res.resultObj?.cash || '{}');\n                this.setPaymentMethod('bank-transfer');\n            }\n        });\n    }\n    async getRequest() {\n        var params = new URLSearchParams(window.location.search);\n        this.request = {\n            OrderCode: params.get('OrderCode') || '',\n            PhoneCustomer: params.get('PhoneCustomer') || '',\n            EmailCustomer: params.get('EmailCustomer') || ''\n        };\n        if (this.request.OrderCode && this.request.PhoneCustomer && this.request.EmailCustomer) {\n            await this.AvailableTrip(this.request);\n        }\n    }\n    async AvailableTrip(request) {\n        if (!this._cryptoService.ch()) {\n            await this._cryptoService.spu();\n        }\n        this.CallAvailableTrip(request);\n    }\n    async CallAvailableTrip(request) {\n        this._isLoading = true;\n        var payloadsEncrypted = await this.RequestEncrypt(request);\n        try {\n            const res = await this._flightService.AvailableTrip(payloadsEncrypted, this._ApiKey);\n            const resDecrypted = await this._cryptoService.dda(res.resultObj);\n            var resJson = JSON.parse(resDecrypted);\n            if (resJson.IsSuccessed) {\n                var noteData = JSON.parse(resJson.ResultObj.Note);\n                this._orderDetails = noteData;\n                this._passengers = noteData.paxList;\n                this._isNotValid = true;\n                this._orderAvailable = resJson.ResultObj;\n                this.formatPassenger();\n                await this.getInforAirports();\n                if (this._orderAvailable?.PaymentMethod.includes('bank-transfer')) {\n                    var bankName = this._orderAvailable?.PaymentMethod.split('_')[1];\n                    this.banks.forEach((bank) => {\n                        bank.selected = bank.bankName === bankName;\n                    });\n                }\n                this.getPricePax();\n                this._servicePrice = this.getSumServicePrice();\n                this._sumPrice = this.getSumPrice();\n                this._totalPrice = this._sumPrice + this._servicePrice;\n                if (this._orderAvailable?.Status !== 0) {\n                    this.openModal('Thông báo', 'Đơn hàng đã được thanh toán hoặc hết hạn thanh toán', false);\n                }\n            }\n            this._isLoading = false;\n        }\n        catch (error) {\n            if (error.status === 403) {\n                this._cryptoService.ra();\n                await this._cryptoService.spu();\n                await this.CallAvailableTrip(request);\n            }\n            else {\n                this._isLoading = false;\n                this.openModal('Thông báo', 'Đơn hàng đã được thanh toán hoặc hết hạn thanh toán', false);\n            }\n        }\n    }\n    getSumServicePrice() {\n        var sum = 0;\n        this._passengers.forEach((passenger) => {\n            passenger.baggages.forEach((baggage) => {\n                sum += baggage.Price;\n            });\n        });\n        return sum;\n    }\n    formatPassenger() {\n        var indexInfant = 0;\n        this._orderDetails?.paxList.forEach((pax, index) => {\n            if (pax.type == 'infant') {\n                //get pax adult index same index infant\n                var paxAdult = this._orderDetails.paxList.find((pax) => pax.type == 'adult' && pax.index == indexInfant);\n                if (paxAdult) {\n                    paxAdult.withInfant = pax;\n                    //remove pax infant\n                    this._orderDetails.paxList.splice(index, 1);\n                }\n                indexInfant++;\n            }\n            else {\n                pax.index = index;\n            }\n        });\n    }\n    async getInforAirports() {\n        var airportsCode = [];\n        this._orderDetails?.full?.InventoriesSelected.forEach((inventory) => {\n            inventory.segment.Legs.forEach((leg) => {\n                if (!airportsCode.includes(leg.DepartureCode)) {\n                    airportsCode.push(leg.DepartureCode);\n                }\n                if (!airportsCode.includes(leg.ArrivalCode)) {\n                    airportsCode.push(leg.ArrivalCode);\n                }\n            });\n        });\n        try {\n            var res = await getAirportInfoByCode(airportsCode, this.language || 'vi', this._ApiKey);\n            if (res.isSuccessed) {\n                this._inforAirports = res.resultObj;\n                this.displayMode = res.feature.displayMode || 'total';\n                const currencyObj = typeof res.feature.currency === 'string' ? JSON.parse(res.feature.currency) : res.feature.currency;\n                this.currencySymbol = currencyObj.symbol || '₫';\n                this.convertedVND = currencyObj.convertedVND || 1;\n            }\n            if (this.mode === \"online\") {\n                if (res.feature?.color) {\n                    this.color = res.feature.color;\n                    if (this.color !== \"\") {\n                        setnmtColors(this.color);\n                        this.requestUpdate();\n                    }\n                }\n            }\n        }\n        catch (error) {\n            console.error(error);\n        }\n    }\n    getSumPrice() {\n        //check combine\n        if (this._orderDetails?.full?.InventoriesSelected.length === 1 && this._orderDetails?.full?.InventoriesSelected[0].combine) {\n            return this._orderDetails?.full?.InventoriesSelected[0].inventorySelected?.SumPrice || 0;\n        }\n        else if (this._orderDetails?.full?.InventoriesSelected.length > 1 && this._orderDetails?.full?.InventoriesSelected[0].combine) {\n            return this._orderDetails?.full?.InventoriesSelected[1].inventorySelected?.SumPrice || 0;\n        }\n        return this._orderDetails?.full?.InventoriesSelected.reduce((total, inventory) => {\n            return total + (inventory?.inventorySelected?.SumPrice || 0);\n        }, 0);\n    }\n    getPricePax() {\n        var data = [];\n        var typePax = ['ADT', 'CHD', 'INF'];\n        if (this._orderDetails?.full?.InventoriesSelected[0].combine && this._orderDetails?.full?.InventoriesSelected.length > 1) {\n            data = this._orderDetails?.full?.InventoriesSelected[1].inventorySelected?.FareInfos;\n        }\n        else {\n            var result = [];\n            typePax.forEach(paxType => {\n                result.push({ PaxType: paxType, Fare: 0, Tax: 0 });\n            });\n            this._orderDetails?.full?.InventoriesSelected.forEach((inventory) => {\n                inventory.inventorySelected.FareInfos.forEach((fareInfo) => {\n                    if (typePax.includes(fareInfo.PaxType)) {\n                        const paxResult = result.find(r => r.PaxType === fareInfo.PaxType);\n                        if (paxResult) {\n                            paxResult.Fare += fareInfo.Fare;\n                            paxResult.Tax += fareInfo.Tax;\n                        }\n                    }\n                });\n            });\n            // Remove entries with 0 count\n            if (this._orderDetails?.full?.adult === undefined) {\n                result = result.filter(r => r.PaxType !== 'ADT');\n            }\n            if (this._orderDetails?.full?.child === undefined) {\n                result = result.filter(r => r.PaxType !== 'CHD');\n            }\n            if (this._orderDetails?.full?.infant === undefined) {\n                result = result.filter(r => r.PaxType !== 'INF');\n            }\n            data = result;\n        }\n        this._pricePaxInfor = data;\n    }\n    showDetailsTrip() {\n        this._isShowDetailsTrip = !this._isShowDetailsTrip;\n    }\n    setPaymentMethod(method) {\n        this._paymentMethod = method;\n        if (method.includes('bank-transfer')) {\n            if (this.banks?.length > 0) {\n                this.banks.forEach(b => b.selected = false);\n                this.banks[0].selected = true;\n            }\n        }\n    }\n    selectBank(bank) {\n        this.banks.forEach(b => b.selected = false);\n        bank.selected = true;\n        this.requestUpdate();\n    }\n    setAgree(agree) {\n        this._agree = agree;\n    }\n    async RequestEncrypt(data) {\n        const encryptedData = await this._cryptoService.eda(JSON.stringify(data));\n        return {\n            EncryptData: encryptedData\n        };\n    }\n    reSearchTrip() {\n        // Chỉ thêm language parameter nếu autoLanguageParam được bật\n        if (this.autoLanguageParam) {\n            const params = new URLSearchParams();\n            params.append('language', this.language);\n            window.location.href = `${this.uri_searchBox}?${params.toString()}`;\n        }\n        else {\n            window.location.href = this.uri_searchBox;\n        }\n    }\n    openModal(title = 'Thông báo', content = `Thời gian đặt vé đã hết hạn.\\n\r\n        Vui lòng tải lại trang để xem kết quả mới nhất.`, isCountDown = true) {\n        const modal = this.renderRoot.querySelector(\"modal-notification\");\n        // Kiểm tra nếu modal tồn tại\n        if (modal) {\n            modal.start({\n                title,\n                content,\n                isCountDown,\n                countdown: 10,\n            });\n        }\n        if (this.isCountDown) {\n            const interval = setInterval(() => {\n                this.countdown--;\n                if (this.countdown === 0) {\n                    clearInterval(interval);\n                    this.reSearchTrip();\n                }\n            }, 1000);\n        }\n    }\n    async CallRequestTrip() {\n        this._isLoading = true;\n        var data = {\n            OrderCode: this._orderAvailable.OrderCode,\n            PaymentMethod: this._paymentMethod,\n            Amount: this._totalPrice\n        };\n        console.log(\"data\", data);\n        try {\n            var payloadsEncrypted = await this.RequestEncrypt(data);\n            const res = await this._flightService.RePayment(payloadsEncrypted, this._ApiKey);\n            const resDecrypted = await this._cryptoService.dda(res.resultObj);\n            var resJson = JSON.parse(resDecrypted);\n            console.log('resJson hold trip', resJson);\n            if (resJson.IsSuccessed) {\n                this._isLoading = false;\n                // this.router.navigateByUrl('/TripResult?' + params.toString());\n                const params = new URLSearchParams();\n                // Chỉ thêm language parameter nếu autoLanguageParam được bật\n                if (this.autoLanguageParam) {\n                    params.append('language', this.language);\n                }\n                const queryString = params.toString();\n                window.location.href = queryString ? `${resJson.ResultObj}&${queryString}` : resJson.ResultObj;\n            }\n            else {\n                console.log('show notification');\n                this.openModal('Thông báo', resJson.Message + \"\\nVui lòng tìm lại hành trình.\", true);\n                this._isLoading = false;\n            }\n        }\n        catch (error) {\n            if (error.status === 403 || error.status === 401) {\n                this._cryptoService.ra();\n                await this._cryptoService.spu();\n                await this.CallRequestTrip();\n            }\n        }\n    }\n    async onPayment() {\n        this.isShowModal = false;\n        this._isSubmit = true;\n        if (!this._agree) {\n            return;\n        }\n        if (this._paymentMethod === '') {\n            console.log(\"isShowModal\", this.isShowModal);\n            this.openModal('Thông báo', 'Vui lòng chọn phương thức thanh toán.', false);\n            console.log(\"isShowModal12123\", this.isShowModal);\n            return;\n        }\n        if (this._paymentMethod === 'credit-card') {\n            this.openModal('Thông báo', 'Phương thức thanh toán chưa được hỗ trợ. Vui lòng chọn phương thức thanh toán khác.', false);\n            return;\n        }\n        if (this._paymentMethod === 'bank-transfer') {\n            const selectedBank = this.banks?.find((bank) => bank.selected);\n            this._paymentMethod = 'bank-transfer_' + selectedBank?.bankName;\n        }\n        if (!this._cryptoService.ch()) {\n            await this._cryptoService.spu();\n        }\n        await this.CallRequestTrip();\n    }\n    handleLanguageChange(newLang) {\n        this.language = newLang;\n        this.getInforAirports();\n        // Tự động cập nhật URL với language mới\n        this.updateURLWithLanguage();\n        this.requestUpdate();\n    }\n    render() {\n        return TripRePaymentTemplate(this.autoFillOrderCode, this.request, this.uri_searchBox, this.language, this.agent, this.termsUrl, this._isLoading, this._agree, this._isSubmit, this._isShowDetailsTrip, this._orderDetails, this._inforAirports, this._pricePaxInfor, this._servicePrice, this._sumPrice, this._paymentMethod, this.banks, this.bankNote, this.transferContent, this.cashInfo, this.currencySymbolAv, this.convertedVND, this.showDetailsTrip.bind(this), this.setPaymentMethod.bind(this), this.selectBank.bind(this), this.setAgree.bind(this), this.onPayment.bind(this), this.handleLanguageChange.bind(this), this.showLanguageSelect // truyền thuộc tính mới\n        );\n    }\n};\n__decorate([\n    property({ type: Boolean }),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"autoFillOrderCode\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"mode\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"googleFontsUrl\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"font\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"termsUrl\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"ApiKey\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"color\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"uri_searchBox\", void 0);\n__decorate([\n    property({ type: Boolean }),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"showLanguageSelect\", void 0);\n__decorate([\n    property({ type: Boolean }),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"autoLanguageParam\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", String),\n    __metadata(\"design:paramtypes\", [String])\n], TripRePayment.prototype, \"language\", null);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripRePayment.prototype, \"_ApiKey\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripRePayment.prototype, \"_isLoading\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripRePayment.prototype, \"_agree\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripRePayment.prototype, \"_isSubmit\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripRePayment.prototype, \"_isNotValid\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"_orderDetails\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"_orderAvailable\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripRePayment.prototype, \"_isShowDetailsTrip\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Array)\n], TripRePayment.prototype, \"_passengers\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Array)\n], TripRePayment.prototype, \"_inforAirports\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Array)\n], TripRePayment.prototype, \"_pricePaxInfor\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Number)\n], TripRePayment.prototype, \"_servicePrice\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Number)\n], TripRePayment.prototype, \"_sumPrice\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Number)\n], TripRePayment.prototype, \"_totalPrice\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripRePayment.prototype, \"_paymentMethod\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripRePayment.prototype, \"titleModal\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripRePayment.prototype, \"contentModal\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripRePayment.prototype, \"isCountDown\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Number)\n], TripRePayment.prototype, \"countdown\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripRePayment.prototype, \"isShowModal\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"request\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Array)\n], TripRePayment.prototype, \"banks\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripRePayment.prototype, \"bankNote\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripRePayment.prototype, \"transferContent\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripRePayment.prototype, \"cashInfo\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripRePayment.prototype, \"agent\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripRePayment.prototype, \"displayMode\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Number)\n], TripRePayment.prototype, \"convertedVND\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripRePayment.prototype, \"currencySymbol\", void 0);\nTripRePayment = __decorate([\n    customElement(\"trip-repayment\"),\n    __metadata(\"design:paramtypes\", [CryptoService,\n        FlightService])\n], TripRePayment);\nexport { TripRePayment };\n"], "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "this", "__decorate", "decorators", "target", "key", "desc", "d", "c", "r", "getOwnPropertyDescriptor", "Reflect", "decorate", "defineProperty", "__metadata", "metadataKey", "metadataValue", "metadata", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "value", "step", "next", "e", "rejected", "throw", "result", "done", "then", "__generator", "body", "f", "y", "_", "label", "sent", "trys", "ops", "g", "create", "Iterator", "verb", "return", "Symbol", "iterator", "v", "op", "TypeError", "pop", "push", "__spread<PERSON><PERSON>y", "to", "from", "pack", "ar", "l", "Array", "slice", "concat", "globalThis", "ShadowRoot", "ShadyCSS", "nativeShadow", "Document", "CSSStyleSheet", "o", "WeakMap", "n$3", "constructor", "_$cssResult$", "Error", "cssText", "styleSheet", "let", "get", "replaceSync", "set", "toString", "reduce", "cssRules", "is", "getOwnPropertyNames", "h", "getOwnPropertySymbols", "getPrototypeOf", "a", "trustedTypes", "emptyScript", "reactiveElementPolyfillSupport", "u", "toAttribute", "Boolean", "JSON", "stringify", "fromAttribute", "Number", "parse", "attribute", "type", "String", "converter", "reflect", "has<PERSON><PERSON>ed", "litPropertyMetadata", "b", "HTMLElement", "addInitializer", "_$Ei", "observedAttributes", "finalize", "_$Eh", "keys", "createProperty", "state", "elementProperties", "noAccessor", "getPropertyDescriptor", "requestUpdate", "configurable", "enumerable", "getPropertyOptions", "Map", "finalized", "properties", "_$Eu", "elementStyles", "finalizeStyles", "styles", "isArray", "Set", "flat", "reverse", "unshift", "toLowerCase", "super", "_$Ep", "isUpdatePending", "hasUpdated", "_$Em", "_$Ev", "_$ES", "enableUpdating", "_$AL", "_$E_", "for<PERSON>ach", "addController", "_$EO", "add", "renderRoot", "isConnected", "hostConnected", "removeController", "delete", "size", "createRenderRoot", "shadowRoot", "attachShadow", "shadowRootOptions", "adoptedStyleSheets", "map", "document", "createElement", "litNonce", "setAttribute", "textContent", "append<PERSON><PERSON><PERSON>", "connectedCallback", "disconnectedCallback", "hostDisconnected", "attributeChangedCallback", "_$AK", "_$EC", "removeAttribute", "_$ET", "has", "_$Ej", "scheduleUpdate", "performUpdate", "wrapped", "shouldUpdate", "willUpdate", "hostUpdate", "update", "_$EU", "_$AE", "hostUpdated", "firstUpdated", "updated", "updateComplete", "getUpdateComplete", "mode", "ReactiveElement", "reactiveElementVersions", "createPolicy", "createHTML", "Math", "random", "toFixed", "createComment", "m", "RegExp", "$", "x", "_$litType$", "strings", "values", "T", "for", "E", "A", "C", "createTreeWalker", "N", "parts", "lastIndex", "exec", "test", "startsWith", "V", "el", "currentNode", "content", "<PERSON><PERSON><PERSON><PERSON>", "replaceWith", "childNodes", "nextNode", "nodeType", "hasAttributes", "getAttributeNames", "endsWith", "getAttribute", "split", "index", "name", "ctor", "H", "I", "L", "k", "tagName", "append", "data", "indexOf", "innerHTML", "S", "_$Co", "_$Cl", "_$litDirective$", "_$AO", "_$AT", "_$AS", "M$2", "_$AV", "_$AN", "_$AD", "_$AM", "parentNode", "_$AU", "creationScope", "importNode", "R", "nextS<PERSON>ling", "z", "_$AI", "_$Cv", "_$AH", "_$AA", "_$AB", "options", "startNode", "endNode", "_$AR", "O", "insertBefore", "createTextNode", "_$AC", "M", "_$AP", "remove", "setConnected", "element", "fill", "j", "toggleAttribute", "capture", "once", "passive", "removeEventListener", "addEventListener", "handleEvent", "host", "litHtmlPolyfillSupport", "B", "litHtmlVersions", "renderBefore", "_$litPart$", "r$2", "renderOptions", "_$Do", "render", "_$litElement$", "litElementHydrateSupport", "LitElement", "litElementPolyfillSupport", "litElementVersions", "_0x8dfdad", "_0x3516", "_0x68a1", "_0x2807bc", "_0x1c8ab9", "_0x4bc0ef", "_0x68a1be", "_0x351614", "_0x3ba872", "_0x3655a7", "_0x531800", "parseInt", "shift", "_0x597171", "environment", "wait", "durationMs", "resolveWith", "setTimeout", "isPromise", "awaitIfAsync", "action", "callback", "returnedValue", "error", "mapWithBreaks", "items", "loopReleaseInterval", "results", "lastLoopReleaseTime", "now", "_a", "Date", "channel", "MessageChannel", "port1", "onmessage", "port2", "postMessage", "suppressUnhandledRejectionWarning", "promise", "undefined", "toInt", "toFloat", "parseFloat", "replaceNaN", "replacement", "isNaN", "<PERSON><PERSON><PERSON><PERSON>", "sum", "round", "base", "abs", "counterBase", "x64Add", "m0", "n0", "o0", "o1", "o2", "o3", "x64Multiply", "m1", "m2", "m3", "n1", "n2", "n3", "x64Rotl", "bits", "x64LeftShift", "x64Xor", "F1", "F2", "x64Fmix", "shifted", "C1", "C2", "M$1", "N1", "N2", "x64hash128", "input", "seed", "getUTF8Bytes", "Uint8Array", "charCode", "charCodeAt", "TextEncoder", "encode", "remainder", "bytes", "h1", "h2", "k1", "k2", "val", "loadSources", "sources", "sourceOptions", "excludeSources", "includedSources", "filter", "sourceKey", "excludes", "haystack", "needle", "includes", "sourceGettersPromise", "loadSource", "source", "sourceLoadPromise", "resolveLoad", "loadStartTime", "bind", "loadArgs", "_i", "loadResult", "loadDuration", "isFinalResultLoaded", "duration", "resolveGet", "getStartTime", "getArgs", "finalizeSource", "componentPromises", "componentArray", "components", "sourceGetter", "all", "isTrident", "w", "window", "navigator", "isChromium", "vendor", "isWebKit", "isDesktopWebKit", "isSafariWebKit", "isFunctionNative", "func", "print", "browser", "isGecko", "_b", "documentElement", "style", "isWebKit616OrNewer", "CSS", "HTMLButtonElement", "supports", "exitFullscreen", "msExitFullscreen", "mozCancelFullScreen", "webkitExitFullscreen", "isAndroid", "isItChromium", "isItGecko", "Audio", "appVersion", "makeInnerError", "withIframe", "initialHtml", "domPollInterval", "_c", "iframe", "_d", "_resolve", "_reject", "isComplete", "onload", "onerror", "checkReadyState", "setProperty", "position", "top", "left", "visibility", "srcdoc", "src", "contentWindow", "readyState", "<PERSON><PERSON><PERSON><PERSON>", "selectorToElement", "selector", "parseSimpleCssSelector", "errorMessage", "tagMatch", "attributes", "tag", "partsRegex", "addAttribute", "match", "part", "attributeMatch", "name_1", "join", "addStyleString", "name_2", "property", "priority", "baseFonts", "fontList", "canvasToString", "canvas", "toDataURL", "screenFrameBackup", "screenFrameSizeTimeoutId", "getUnstableScreenFrame", "_this", "watchScreenFrame", "checkScreenFrame", "frameSize", "getCurrentScreenFrame", "isFrameSizeNull", "getFullscreenElement", "fullscreenElement", "msFullscreenElement", "mozFullScreenElement", "webkitFullscreenElement", "screen", "availTop", "width", "availWidth", "availLeft", "height", "availHeight", "getBlockedSelectors", "selectors", "root", "elements", "blockedSelectors", "holder", "forceShow", "show", "offsetParent", "doesMatch$5", "matchMedia", "matches", "doesMatch$4", "doesMatch$3", "doesMatch$2", "doesMatch$1", "doesMatch", "fallbackFn", "presets", "default", "apple", "font", "serif", "fontFamily", "sans", "mono", "min", "fontSize", "system", "willPrintConsoleError", "isAnyParentCrossOrigin", "currentWindow", "parentWindow", "parent", "location", "origin", "validContextParameters", "validExtensionParams", "shaderTypes", "precisionTypes", "getWebGLContext", "cache", "webgl", "context", "getContext", "getShaderPrecision", "gl", "shaderType", "precisionType", "shaderPrecision", "getShaderPrecisionFormat", "rangeMin", "rangeMax", "precision", "getConstantsFromPrototype", "obj", "__proto__", "isConstantLike", "shouldAvoidDebugRendererInfo", "isValidParameterGetter", "getParameter", "fonts", "getFonts", "spansContainer", "defaultWidth", "defaultHeight", "createSpan", "createSpanWithFonts", "initializeFontsSpans", "isFontAvailable", "baseFontsSpans", "fontsSpans", "span", "fontToDetect", "baseFont", "spans", "fontList_1", "fontSpans", "some", "baseFontIndex", "offsetWidth", "offsetHeight", "domBlockers", "getDomBlockers", "debug", "filters", "filterNames", "isApplicable", "getFilters", "fromB64", "atob", "abpIndo", "abpvn", "adBlockFinland", "ad<PERSON><PERSON><PERSON><PERSON><PERSON>", "adBlockWarningRemoval", "adGuardAnnoyances", "adGuardBase", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adGuardJapanese", "adGuardMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adGuard<PERSON><PERSON><PERSON>", "adGuardSpanishPortuguese", "adGuardTrackingProtection", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bulgarian", "easyList", "easyListChina", "easyList<PERSON><PERSON><PERSON>", "easyListCzechSlovak", "easyList<PERSON>ut<PERSON>", "easyListGermany", "easyListItaly", "easyListLithuania", "estonian", "fanboyAnnoyances", "fanboyAntiFacebook", "fanboyEnhancedTrackers", "fanboySocial", "frellwitSwedish", "greekAdBlock", "hungarian", "iDontCareAboutCookies", "icelandicAbp", "latvian", "listKr", "listeAr", "listeFr", "officialPolish", "ro", "ruAd", "thaiAds", "webAnnoyancesUltralist", "filterName", "printDebug", "activeBlockers", "sort", "fontPreferences", "getFontPreferences", "withNaturalFonts", "containerWidthPx", "iframeWindow", "linesOfText", "iframeDocument", "iframeBody", "bodyStyle", "webkitTextSizeAdjust", "textSizeAdjust", "zoom", "devicePixelRatio", "container", "sizes", "text", "whiteSpace", "_e", "_f", "_g", "_h", "getBoundingClientRect", "audio", "getAudioFingerprint", "doesBrowserPerformAntifingerprinting$1", "isSamsungInternet", "audioPrototype", "visualViewport", "Image", "isChromium122OrNewer", "URLPattern", "WebGLRenderingContext", "getUnstableAudioFingerprint", "renderPromise", "finishRendering", "fingerprintPromise", "AudioContext", "OfflineAudioContext", "webkitOfflineAudioContext", "doesBrowserSuspendAudioContext", "isWebKit606OrNewer", "oscillator", "createOscillator", "frequency", "compressor", "createDynamicsCompressor", "threshold", "knee", "ratio", "attack", "release", "connect", "destination", "start", "startRenderingAudio", "isFinalized", "renderTryCount", "startedRunningAt", "startRunningTimeout", "oncomplete", "event", "<PERSON><PERSON><PERSON><PERSON>", "tryRender", "renderingPromise", "startRendering", "hidden", "buffer", "getHash", "signal", "hash", "getChannelData", "subarray", "screenFrame", "getScreenFrame", "screenFrameGetter", "processSize", "sideSize", "getCanvasFingerprint", "getUnstableCanvasFingerprint", "skipImages", "geometry", "winding", "makeCanvasContext", "isSupported", "doesSupportWinding", "rect", "isPointInPath", "renderImages", "renderTextImage", "textBaseline", "fillStyle", "fillRect", "printedText", "fromCharCode", "fillText", "textImage1", "renderGeometryImage", "globalCompositeOperation", "color", "beginPath", "arc", "PI", "closePath", "doesBrowserPerformAntifingerprinting", "osCpu", "getOsCpu", "oscpu", "languages", "getLanguages", "language", "userLanguage", "browserLanguage", "systemLanguage", "isChromium86OrNewer", "Intl", "colorDepth", "getColorDepth", "deviceMemory", "getDeviceMemory", "screenResolution", "getScreenResolution", "getUnstableScreenResolution", "parseDimension", "dimensions", "hardwareConcurrency", "getHardwareConcurrency", "timezone", "getTimezone", "DateTimeFormat", "resolvedOptions", "timeZone", "offset", "getTimezoneOffset", "currentYear", "getFullYear", "max", "sessionStorage", "getSessionStorage", "localStorage", "getLocalStorage", "indexedDB", "getIndexedDB", "isEdgeHTML", "openDatabase", "getOpenDatabase", "cpuClass", "getCpuClass", "platform", "getPlatform", "isIPad", "screenRatio", "Element", "webkitRequestFullscreen", "plugins", "getPlugins", "rawPlugins", "plugin", "mimeTypes", "mimeType", "suffixes", "description", "touchSupport", "getTouchSupport", "touchEvent", "maxTouchPoints", "msMaxTouchPoints", "createEvent", "touchStart", "get<PERSON>endor", "vendorFlavors", "getVendorFlavors", "flavors", "cookiesEnabled", "areCookiesEnabled", "cookie", "colorGamut", "getColorGamut", "gamut", "invertedColors", "areColorsInverted", "forcedColors", "areColorsForced", "monochrome", "getMonochromeDepth", "contrast", "getContrastPreference", "reducedMotion", "isMotionReduced", "reducedTransparency", "isTransparencyReduced", "hdr", "isHDR", "math", "getMathFingerprint", "acos", "acosh", "asin", "asinh", "atanh", "atan", "sin", "sinh", "cos", "cosh", "tan", "tanh", "exp", "expm1", "log1p", "acoshPf", "log", "sqrt", "asinhPf", "atanhPf", "sinhPf", "coshPf", "tanhPf", "expm1Pf", "log1pPf", "powPI", "pow", "pdfViewerEnabled", "isPdfViewerEnabled", "architecture", "getArchitecture", "Float32Array", "u8", "Infinity", "applePay", "getApplePayState", "ApplePaySession", "canMakePayments", "getStateFromError", "message", "privateClickMeasurement", "getPrivateClickMeasurement", "link", "sourceId", "attributionSourceId", "attributionsourceid", "audioBaseLatency", "getAudioContextBaseLatency", "baseLatency", "dateTimeLocale", "getDateTimeLocale", "locale", "webGlBasics", "getWebGlBasics", "debugExtension", "getExtension", "version", "VERSION", "VENDOR", "vendorUnmasked", "UNMASKED_VENDOR_WEBGL", "renderer", "RENDERER", "rendererUnmasked", "UNMASKED_RENDERER_WEBGL", "shadingLanguageVersion", "SHADING_LANGUAGE_VERSION", "webGlExtensions", "getWebGlExtensions", "extensions", "getSupportedExtensions", "contextAttributes", "getContextAttributes", "unsupportedExtensions", "parameters", "extensionParameters", "shaderPrecisions", "attributeName", "constants_1", "code", "constant", "extensions_1", "extension", "shaderTypes_1", "precisionTypes_1", "loadBuiltinSources", "getConfidence", "proConfidenceScore", "deriveProConfidenceScore", "openConfidenceScore", "getOpenConfidenceScore", "score", "comment", "replace", "hashComponents", "componentsToCanonicalString", "componentKey", "component", "prepareForSources", "<PERSON><PERSON><PERSON><PERSON>", "requestIdleCallbackIfAvailable", "fallbackTimeout", "deadlineTimeout", "requestIdleCallback", "timeout", "makeAgent", "getComponents", "makeLazyGetResult", "visitorIdCache", "confidence", "visitorId", "load", "monitoring", "monitor", "__fpjs_d_m", "request", "XMLHttpRequest", "open", "send", "componentsToDebugString", "_key", "errorToObject", "stack", "getDeviceId", "_getDeviceId", "_asyncToGenerator", "_regeneratorRuntime", "_callee", "_context", "FingerprintJS", "fp", "fetchWithDeviceId", "_x", "_x2", "_fetchWithDeviceId", "_callee2", "init", "headers", "modifiedInit", "_context2", "_0x544682", "_0x527c75", "deviceId", "Headers", "_objectSpread", "fetch", "fetchWithDeviceIdandApiKey", "_x3", "_fetchWithDeviceIdandApiKey", "_callee3", "<PERSON><PERSON><PERSON><PERSON>", "response", "_args3", "_context3", "_0x6a675", "_0x1f78e2", "_0x1fe8", "_0x481595", "_0x4aa1fa", "_0x5763aa", "_0x5763", "_0x1fe895", "_0x833959", "apiUrl", "public<PERSON>ey", "CryptoService", "_gdi", "_wk", "_iih", "_csi", "_dda", "_eda", "_dsk", "_spu", "_gr", "_importPrivateKey", "_importPublicKey", "_decrypt", "_encrypt", "_da", "_hd", "_he", "_dra", "_era", "_irpr", "_irpu", "_ea", "_ga", "_gra", "_createClass", "_classCallCheck", "encryptionKeyPair", "privateKey", "_0x3cb1ca", "_0x10656d", "crypto", "_0x347d86", "aes<PERSON>ey", "iv", "encryptedData", "dataBuffer", "encoder", "ea", "_callee4", "pem", "binaryDer", "_context4", "_0x573b78", "_0x8c7442", "_callee5", "_context5", "_x4", "_callee6", "exportedAESKey", "_context6", "_0x32e057", "_x5", "_x6", "era", "_callee7", "_context7", "_x7", "_x8", "_callee8", "publicKeyPem", "encryptedAESKey", "combinedData", "_context8", "_0x1dba4e", "_0x3d0355", "ga", "_yield$this$ea", "btoa", "_toConsumableArray", "_x9", "_x10", "_callee9", "privateKeyBem", "encryptedText", "encryptedAesKey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decryptedData", "_context9", "da", "_x11", "_x12", "hd", "_callee10", "encryptedBuffer", "decryptedBuffer", "_context10", "cipherText", "TextDecoder", "decode", "_x13", "_x14", "_callee11", "plainText", "_context11", "_x15", "_x16", "_callee12", "_context12", "_x17", "_x18", "_callee13", "_context13", "pemToA<PERSON>y<PERSON><PERSON>er", "_x19", "_callee14", "_context14", "_x20", "base64", "_base64$match", "binary", "len", "binaryString", "_callee15", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ep", "sp", "signature<PERSON><PERSON>er", "ss", "_context15", "_0x464b88", "_0xccf870", "gra", "spB<PERSON>er", "xdi", "s<PERSON><PERSON><PERSON>", "unescape", "encodeURIComponent", "minutes", "date", "expires", "nameEQ", "ca", "cookies", "eqPos", "_callee16", "_yield$this$gr", "requestEncrypt", "responseData", "privateKeyBase64String", "_context16", "gr", "requestJson", "he", "sc", "textToBase64", "_callee17", "server<PERSON>ey", "_context17", "gc", "cText", "_callee18", "sText", "_context18", "_x21", "_callee19", "_context19", "_x22", "_callee20", "_context20", "_0x56555e", "_0x45dbb5", "_callee21", "_context21", "_0x54d978", "ch", "_callee22", "retries", "_context22", "_0x3658a0", "_callee23", "_context23", "_0x33bdcc", "_0xe25593", "_0x339d", "_0x3b8682", "_0x312e0e", "_0xdd0669", "_0xdd06", "_0x339d6f", "_0x12f0b8", "_0x4b11a9", "FlightService", "_request2", "_0x4e0f37", "_0x21ff9d", "endpoint", "fetchFn", "_args", "useDeviceId", "api", "_0x487510", "customElements", "define", "kind", "getAirportInfoByCode", "_ref", "airportsCode", "requestBody", "_0x4868c6", "_0x22d5", "_0x24d9d4", "_0xb493d0", "getFeatures", "_ref4", "features", "_0x1a39bb", "_0x4a7da7", "formatDateTo_ddMMyyyy", "month", "date1", "day", "year", "getTimeFromDateTime", "dateTime", "hours", "convertDurationToHour", "formatNumber", "convertedVND", "integerPart", "_result$toFixed$split2", "_slicedToArray", "decimalPart", "formattedInteger", "_0x3266", "_0x4c2a6a", "_ref5", "_0x8a33", "_0x4d3b89", "_0x1f73aa", "_0x2976a8", "_0x2976", "_0x8a33e6", "_0xe06a6b", "_Modal", "Modal", "_callSuper", "_inherits", "_changedProperties", "_superPropGet", "changedProperties", "_title", "_this2", "reSearch", "title", "_ref$title", "_ref$content", "isCountDown", "_ref$isCountDown", "countdown", "_ref$countdown", "isOpen", "close", "_0x1f967c", "_0x2ede", "portalContainer", "modalContent", "html", "_templateObject", "_taggedTemplateLiteral", "_templateObject2", "_templateObject3", "modalTemplate", "unsafeCSS", "css", "_0x30f889", "customElement", "TripRePaymentTemplate", "autoFillOrderCode", "uri_searchBox", "_agent", "_termsUrl", "_isLoading", "_agree", "_isSubmit", "_isShowDetailsTrip", "_orderDetails", "_inforAirports", "_pricePaxInfor", "_servicePrice", "_sumPrice", "_paymentMethod", "_banks", "_bankNote", "_transferContent", "_cashInfo", "_currencySymbol", "_convertedVND", "showDetailsTrip", "setPaymentMethod", "selectBank", "setAgree", "onPayment", "handleLanguageChange", "showLanguageSelect", "_orderDetails$full", "_templateObject4", "_orderDetails$full2", "itinerarySelected", "_orderDetails$full3", "_templateObject5", "leg", "_inforAirports$leg$De", "_itinerarySelected$in12", "_templateObject6", "_templateObject7", "_inforAirports$leg$De2", "_inforAirports$leg$De3", "_inforAirports$leg$Ar", "_inforAirports$leg$Ar2", "_itinerarySelected$in", "_itinerarySelected$in2", "_itinerarySelected$in3", "_itinerarySelected$in4", "_itinerarySelected$in5", "_templateObject8", "_itinerarySelected$in6", "_itinerarySelected$in7", "_templateObject9", "_itinerarySelected$in8", "_templateObject10", "_itinerarySelected$in9", "_itinerarySelected$in10", "_templateObject11", "_itinerarySelected$in11", "getDurarionLeg", "departure", "fareInfo", "_orderDetails$full6", "_templateObject12", "getPassengerDescriptionV2", "paxType", "ADT", "CHD", "INF", "_orderDetails$full4", "_orderDetails$full5", "_templateObject13", "bank", "_templateObject14", "_templateObject15", "_templateObject16", "_templateObject17", "_templateObject18", "_orderDetails$full7", "_templateObject19", "_orderDetails$full8", "_templateObject20", "_inforAirports$itiner", "_itinerarySelected$se", "_inforAirports$itiner2", "_itinerarySelected$se2", "_itinerarySelected$se3", "_itinerarySelected$se4", "_itinerarySelected$se5", "_itinerarySelected$se6", "_itinerarySelected$se7", "_itinerarySelected$se8", "getFlights", "legs", "_itinerarySelected$se9", "_templateObject21", "_templateObject22", "_templateObject23", "_0x13ac0a", "_0x282d", "_0x1b3e95", "_0x1a31d4", "_0x2a610d", "_0x2a61", "_0x282d59", "_0x498f30", "_0x191515", "_0x54c7f6", "_0x2c458", "colors", "setnmtColors", "baseColor", "console", "parsed", "_typeof", "_ref2", "lighten", "hex", "percent", "num", "amt", "darken", "shades", "hexColor", "_ref3", "BANK_LOGOS", "_0x1ed1", "_0x5120e5", "cryptoService", "flightService", "TripRePayment", "_TripRePayment", "_onPayment", "_CallRequestTrip", "_RequestEncrypt", "_getInforAirports", "_CallAvailableTrip", "_AvailableTrip", "_getRequest", "_firstUpdated", "_0x12f139", "_0x17b3", "_cryptoService", "_flightService", "oldValue", "languageParam", "_language", "URLSearchParams", "urlParams", "updateURLWithLanguage", "currencySymbol", "<PERSON><PERSON><PERSON><PERSON>", "currentUrl", "URL", "params", "newUrl", "_googleFontsLink", "_0xe02b1c", "_0x560db4", "googleFontsLink", "googleFontsUrl", "_<PERSON><PERSON><PERSON><PERSON>", "res", "bankModel", "_res$resultObj", "_res$resultObj2", "_bankModel$banksInfo", "matchedLogo", "logo", "_res$resultObj3", "_0x19e630", "_0x152b1f", "EmailCustomer", "getRequest", "_this$_orderAvailable3", "noteData", "_this$_orderAvailable2", "bankName", "_0x4168df", "payloadsEncrypted", "resDecrypted", "res<PERSON>son", "_isNotValid", "_orderAvailable", "_this$_orderAvailable", "ra", "CallAvailableTrip", "passenger", "baggage", "_this$_orderDetails", "_this3", "indexInfant", "pax", "paxAdult", "_res$feature", "_0x423496", "_0x322abb", "_this$_orderDetails2", "inventory", "currencyObj", "_this$_orderDetails8", "_this$_orderDetails3", "_this$_orderDetails4", "_this$_orderDetails5", "_this$_orderDetails6", "_this$_orderDetails7", "_this$_orderDetails9", "total", "_inventory$inventoryS", "_this$_orderDetails16", "typePax", "_this$_orderDetails10", "_this$_orderDetails11", "_this$_orderDetails12", "_this$_orderDetails13", "paxResult", "_this$_orderDetails14", "_this$_orderDetails15", "method", "_this$banks", "banks", "agree", "interval", "_this4", "modal", "setInterval", "clearInterval", "queryString", "OrderCode", "dda", "CallRequestTrip", "selectedBank", "isShowModal", "openModal", "_this$banks2", "newLang", "transferContent", "cashInfo", "currencySymbolAv"], "mappings": "y4XA+BO,IAAIA,SAAW,WAQlB,OAPAA,SAAWC,OAAOC,QAAU,SAAkBC,GAC1C,IAAK,IAAIC,EAAGC,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAE5C,IAAK,IAAII,KADTL,EAAIG,UAAUF,GACOJ,OAAOS,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,IAE9E,OAAON,CACV,GACeU,MAAMC,KAAMP,UAChC,EAcO,SAASQ,WAAWC,EAAYC,EAAQC,EAAKC,GAChD,IAA2HC,EAAvHC,EAAId,UAAUC,OAAQc,EAAID,EAAI,EAAIJ,EAAkB,OAATE,EAAgBA,EAAOlB,OAAOsB,yBAAyBN,EAAQC,GAAOC,EACrH,GAAuB,iBAAZK,SAAoD,mBAArBA,QAAQC,SAAyBH,EAAIE,QAAQC,SAAST,EAAYC,EAAQC,EAAKC,QACpH,IAAK,IAAId,EAAIW,EAAWR,OAAS,EAAQ,GAALH,EAAQA,KAASe,EAAIJ,EAAWX,MAAIiB,GAAKD,EAAI,EAAID,EAAEE,GAAS,EAAJD,EAAQD,EAAEH,EAAQC,EAAKI,GAAKF,EAAEH,EAAQC,KAASI,GAChJ,OAAW,EAAJD,GAASC,GAAKrB,OAAOyB,eAAeT,EAAQC,EAAKI,GAAIA,CAChE,CAmDO,SAASK,WAAWC,EAAaC,GACpC,GAAuB,iBAAZL,SAAoD,mBAArBA,QAAQM,SAAyB,OAAON,QAAQM,SAASF,EAAaC,EACpH,CAEO,SAASE,UAAUC,EAASC,EAAYC,EAAGC,GAE9C,OAAO,IAAKD,EAAAA,GAAUE,UAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUC,GAAS,IAAMC,EAAKN,EAAUO,KAAKF,IAAW,MAAOG,GAAKL,EAAOK,GAAO,CAC3F,SAASC,EAASJ,GAAS,IAAMC,EAAKN,EAAiBU,MAAEL,IAAW,MAAOG,GAAKL,EAAOK,GAAO,CAC9F,SAASF,EAAKK,GAJlB,IAAeN,EAIaM,EAAOC,KAAOV,EAAQS,EAAON,SAJ1CA,EAIyDM,EAAON,iBAJ/BN,EAAIM,EAAQ,IAAIN,GAAE,SAAUG,GAAWA,EAAQG,EAAO,KAIhBQ,KAAKT,EAAWK,EAAY,CAC9GH,GAAMN,EAAYA,EAAUtB,MAAMmB,EAASC,GAAc,KAAKS,OACtE,GACA,CAEO,SAASO,YAAYjB,EAASkB,GACjC,IAAsGC,EAAGC,EAAGjD,EAAxGkD,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPpD,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAG,EAAIqD,KAAM,GAAIC,IAAK,IAAeC,EAAIzD,OAAO0D,QAA4B,mBAAbC,SAA0BA,SAAW3D,QAAQS,WACtL,OAAOgD,EAAEhB,KAAOmB,EAAK,GAAIH,EAASb,MAAIgB,EAAK,GAAIH,EAAUI,OAAID,EAAK,GAAsB,mBAAXE,SAA0BL,EAAEK,OAAOC,UAAY,WAAa,OAAOlD,IAAO,GAAG4C,EAC1J,SAASG,EAAKvD,GAAK,OAAO,SAAU2D,GAAYxB,IAClCyB,EADuC,CAAC5D,EAAG2D,GAErD,GAAId,EAAG,MAAM,IAAIgB,UAAU,mCAC3B,KAA8Bd,EAAvBK,GAAaQ,EAAPR,EAAI,GAAiB,EAAKL,GAAG,IACtC,GAAIF,EAAI,EAAGC,IAAMjD,EAAY,EAAR+D,EAAG,GAASd,EAAUU,OAAII,EAAG,GAAKd,EAASP,SAAO1C,EAAIiD,EAAUU,SAAM3D,EAAES,KAAKwC,GAAI,GAAKA,EAAEV,SAAWvC,EAAIA,EAAES,KAAKwC,EAAGc,EAAG,KAAKnB,KAAM,OAAO5C,EAE3J,OADIiD,EAAI,GAAMc,EAAH/D,EAAQ,CAAS,EAAR+D,EAAG,GAAQ/D,EAAEqC,OACzB0B,GAAG,IACP,KAAK,EAAG,KAAK,EAAG/D,EAAI+D,EAAI,MACxB,KAAK,EAAc,OAAXb,EAAEC,QAAgB,CAAEd,MAAO0B,EAAG,GAAInB,MAAM,GAChD,KAAK,EAAGM,EAAEC,QAASF,EAAIc,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKb,EAAEI,IAAIW,MAAOf,EAAEG,KAAKY,MAAO,SACxC,QACI,KAAkBjE,EAAe,GAA3BA,EAAIkD,EAAEG,MAAYhD,QAAcL,EAAEA,EAAEK,OAAS,MAAkB,IAAV0D,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEb,EAAI,EAAG,QAAW,CAC5G,GAAc,IAAVa,EAAG,MAAc/D,GAAM+D,EAAG,GAAK/D,EAAE,IAAM+D,EAAG,GAAK/D,EAAE,IAAQkD,EAAEC,MAAQY,EAAG,QAC1E,GAAc,IAAVA,EAAG,IAAYb,EAAEC,MAAQnD,EAAE,GAAMkD,EAAEC,MAAQnD,EAAE,GAAIA,EAAI+D,MAAzD,CACA,KAAI/D,GAAKkD,EAAEC,MAAQnD,EAAE,IAArB,CACIA,EAAE,IAAIkD,EAAEI,IAAIW,MAChBf,EAAEG,KAAKY,MAAO,QAFqD,CAAxCf,EAAEC,MAAQnD,EAAE,GAAIkD,EAAEI,IAAIY,KAAKH,EADe,EAK7EA,EAAKhB,EAAKtC,KAAKoB,EAASqB,EAC8B,CAAxD,MAAOV,GAAKuB,EAAK,CAAC,EAAGvB,GAAIS,EAAI,CAAE,CAAW,QAAED,EAAIhD,EAAI,CAAI,CAC1D,GAAY,EAAR+D,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE1B,MAAO0B,EAAG,GAAKA,EAAG,QAAK,EAAQnB,MAAM,EArBf,CAAG,CAuBtE,CA+DO,SAASuB,cAAcC,EAAIC,EAAMC,GACpC,GAAIA,GAA6B,IAArBlE,UAAUC,OAAc,IAAK,IAA4BkE,EAAxBrE,EAAI,EAAGsE,EAAIH,EAAKhE,OAAYH,EAAIsE,EAAGtE,KACxEqE,GAAQrE,KAAKmE,KACRE,EAAAA,GAASE,MAAMlE,UAAUmE,MAAMjE,KAAK4D,EAAM,EAAGnE,IAC/CA,GAAKmE,EAAKnE,IAGrB,OAAOkE,EAAGO,OAAOJ,GAAME,MAAMlE,UAAUmE,MAAMjE,KAAK4D,GACtD,CCxNA,IAAMrE,IAAE4E,WAAWpC,IAAExC,IAAE6E,kBAAa,IAAS7E,IAAE8E,UAAU9E,IAAE8E,SAASC,eAAe,uBAAuBC,SAASzE,WAAW,YAAY0E,cAAc1E,UAAUN,IAAE2D,SAASsB,IAAE,IAAIC,QAAQC,IAAAA,MAAQC,WAAAA,CAAYrF,EAAEwC,EAAE0C,GAAG,GAAGvE,KAAK2E,cAAa,EAAGJ,IAAIjF,IAAE,MAAMsF,MAAM,qEAAqE5E,KAAK6E,QAAQxF,EAAEW,KAAKX,EAAEwC,CAAC,CAACiD,iBAAiBC,IAAI1F,EAAEW,KAAKuE,EAAE,IAAuC1C,EAAjCvC,EAAEU,KAAKX,EAAwJ,OAAnJwC,UAAG,IAASxC,QAAoD,KAAZA,GAA/BwC,OAAE,IAASvC,GAAG,IAAIA,EAAEI,QAAa6E,IAAES,IAAI1F,GAAaD,MAAKW,KAAKuE,EAAElF,EAAE,IAAIiF,eAAeW,YAAYjF,KAAK6E,SAAShD,IAAG0C,IAAEW,IAAI5F,EAAED,GAAWA,CAAC,CAAC8F,QAAAA,GAAW,OAAOnF,KAAK6E,OAAO,GAAQrE,IAAEnB,GAAG,IAAIG,IAAE,iBAAiBH,EAAEA,EAAEA,EAAE,QAAG,EAAOC,KAAGC,IAAE,CAACF,KAAKwC,KAAW0C,EAAE,IAAIlF,EAAEK,OAAOL,EAAE,GAAGwC,EAAEuD,SAASvD,EAAEvC,EAAEiF,IAAI1C,EAAE,MAAK,IAAG,IAA+OvC,EAAxOqF,aAAa,OAA2NrF,EAAlNuF,QAAQ,GAAG,iBAAuMvF,EAApL,OAAoLA,EAA3K,MAAMsF,MAAM,mEAA+JtF,EAA1F,uFAAwF,EAArP,GAA0PD,EAAEkF,EAAE,IAAIlF,EAAE,IAAW,IAAIG,IAAE+E,EAAElF,EAAEC,MAA2PiB,IAAEsB,IAAExC,GAAGA,EAAEA,IAAGA,GAAAA,aAAaiF,cAAc,CAAKS,IAAIlD,EAAE,GAAG,IAAI,IAAMvC,KAA2CD,EAApCgG,SAASxD,GAAGvC,EAAEuF,QAAQ,OAAOrE,IAAEqB,EAAM,CAAExC,OAAAA,ICAlzCiG,GAAG/F,IAAEqB,eAAeiB,IAAEpB,yBAAyBD,IAAE+E,oBAAoBC,IAAEC,sBAAsBlB,IAAEmB,eAAelG,KAAGL,OAAOwG,IAAE1B,WAAW1D,IAAEoF,IAAEC,aAAa/B,IAAEtD,IAAEA,IAAEsF,YAAY,GAAGlG,IAAEgG,IAAEG,+BAA+BxF,IAAE,CAACjB,EAAEC,IAAID,EAAE0G,IAAE,CAACC,WAAAA,CAAY3G,EAAEC,GAAG,OAAOA,GAAG,KAAK2G,QAAQ5G,EAAEA,EAAEwE,IAAE,KAAK,MAAM,KAAK1E,OAAO,KAAK2E,MAAMzE,EAAE,MAAMA,EAAEA,EAAE6G,KAAKC,UAAU9G,GAAG,OAAOA,CAAC,EAAE+G,aAAAA,CAAc/G,EAAEC,GAAGyF,IAAIxF,EAAEF,EAAE,OAAOC,GAAG,KAAK2G,QAAQ1G,EAAE,OAAOF,EAAE,MAAM,KAAKgH,OAAO9G,EAAE,OAAOF,EAAE,KAAKgH,OAAOhH,GAAG,MAAM,KAAKF,OAAO,KAAK2E,MAAM,IAAIvE,EAAE2G,KAAKI,MAAMjH,EAAkB,CAAf,MAAMA,GAAGE,EAAE,IAAI,EAAE,OAAOA,CAAC,GAAG8C,IAAE,CAAChD,EAAEC,KAAKC,IAAEF,EAAEC,GAAGgD,IAAE,CAACiE,WAAU,EAAGC,KAAKC,OAAOC,UAAUX,IAAEY,SAAQ,EAAGC,WAAWvE,KAAGY,OAAOjC,WAAWiC,OAAO,YAAY0C,IAAEkB,sBAAsB,IAAIrC,QAAcsC,MAAAA,UAAUC,YAAYC,qBAAAA,CAAsB3H,GAAGW,KAAKiH,QAAQjH,KAAK6D,IAAI,IAAIN,KAAKlE,EAAE,CAAC6H,gCAAgC,OAAOlH,KAAKmH,WAAWnH,KAAKoH,MAAM,IAAIpH,KAAKoH,KAAKC,OAAO,CAACC,qBAAsBjI,CAAAA,EAAEC,EAAEgD,KAAG,IAAyG9B,EAAtGlB,EAAEiI,QAAQjI,EAAEiH,WAAU,GAAIvG,KAAKiH,OAAOjH,KAAKwH,kBAAkBtC,IAAI7F,EAAEC,GAAIA,EAAEmI,aAAkBlI,EAAE0D,cAA6C,KAApCzC,EAAER,KAAK0H,sBAAsBrI,EAAEE,EAAED,KAAeuC,IAAE7B,KAAKJ,UAAUP,EAAEmB,GAAG,CAACkH,4BAA6BrI,CAAAA,EAAEC,EAAEC,GAAG,IAAMyF,IAAInD,EAAEqD,IAAIM,GAAGhF,IAAER,KAAKJ,UAAUP,IAAI,CAAC2F,GAAAA,GAAM,OAAOhF,KAAKV,EAAE,EAAE4F,GAAAA,CAAI7F,GAAGW,KAAKV,GAAGD,CAAC,GAAG,MAAM,CAAC2F,GAAAA,GAAM,OAAOnD,GAAG/B,KAAKE,KAAK,EAAEkF,GAAAA,CAAI5F,GAAG,IAAMkB,EAAEqB,GAAG/B,KAAKE,MAAMwF,EAAE1F,KAAKE,KAAKV,GAAGU,KAAK2H,cAActI,EAAEmB,EAAEjB,EAAE,EAAEqI,cAAa,EAAGC,YAAW,EAAG,CAACC,yBAA0BzI,CAAAA,GAAG,OAAOW,KAAKwH,kBAAkBxC,IAAI3F,IAAIiD,GAAC,CAAC2E,WAAAA,GAAc,IAA4D5H,EAAzDW,KAAKH,eAAeS,IAAE,yBAAmCjB,EAAEG,IAAEQ,OAAQmH,gBAAW,IAAS9H,EAAEwE,IAAI7D,KAAK6D,EAAE,IAAIxE,EAAEwE,IAAI7D,KAAKwH,kBAAkB,IAAIO,IAAI1I,EAAEmI,mBAAkB,CAACL,eAAkBA,GAAA,IAAGnH,KAAKH,eAAeS,IAAE,cAAzB,CAA8C,GAAGN,KAAKgI,WAAU,EAAGhI,KAAKiH,OAAOjH,KAAKH,eAAeS,IAAE,eAAe,CAAC,IAAMjB,EAAEW,KAAKiI,WAAW3I,EAAE,IAAIkG,IAAEnG,MAAMkF,IAAElF,IAAI,IAAI,IAAME,KAAKD,EAAEU,KAAKsH,eAAe/H,EAAEF,EAAEE,GAAG,CAAC,IAAMF,EAAEW,KAAKiD,OAAOjC,UAAU,GAAG,OAAO3B,EAAE,CAAC,IAAMC,EAAEuH,oBAAoB7B,IAAI3F,GAAG,QAAG,IAASC,EAAE,IAAI,IAAMD,EAAEE,KAAKD,EAAEU,KAAKwH,kBAAkBtC,IAAI7F,EAAEE,EAAE,CAACS,KAAKoH,KAAK,IAAIW,IAAI,IAAI,IAAM1I,EAAEC,KAAKU,KAAKwH,kBAAkB,CAAC,IAAMjI,EAAES,KAAKkI,KAAK7I,EAAEC,QAAG,IAASC,GAAGS,KAAKoH,KAAKlC,IAAI3F,EAAEF,EAAE,CAACW,KAAKmI,cAAcnI,KAAKoI,eAAepI,KAAKqI,OAA5c,CAAmd,CAACD,qBAAsB9I,CAAAA,GAAG,IAAMC,EAAE,GAAG,GAAGuE,MAAMwE,QAAQhJ,GAAG,CAAC,IAAMuC,EAAE,IAAI0G,IAAIjJ,EAAEkJ,KAAK,KAAKC,WAAW,IAAI,IAAMnJ,KAAKuC,EAAEtC,EAAEmJ,QAAQrJ,IAAEC,GAAG,WAAM,IAASA,GAAGC,EAAEgE,KAAKlE,IAAEC,IAAI,OAAOC,CAAC,CAAC2I,WAAAA,CAAY7I,EAAEC,GAAuB,OAAM,KAApBC,EAAED,EAAEiH,gBAAuB,EAAO,iBAAiBhH,EAAEA,EAAE,iBAAiBF,EAAEA,EAAEsJ,mBAAc,CAAM,CAACjE,WAAAA,GAAckE,QAAQ5I,KAAK6I,UAAK,EAAO7I,KAAK8I,iBAAgB,EAAG9I,KAAK+I,YAAW,EAAG/I,KAAKgJ,KAAK,KAAKhJ,KAAKiJ,MAAM,CAACA,IAAAA,GAAOjJ,KAAKkJ,KAAK,IAAI5H,SAASjC,GAAGW,KAAKmJ,eAAe9J,IAAIW,KAAKoJ,KAAK,IAAIrB,IAAI/H,KAAKqJ,OAAOrJ,KAAK2H,gBAAgB3H,KAAK0E,YAAYb,GAAGyF,SAASjK,GAAGA,EAAEW,OAAO,CAACuJ,aAAAA,CAAclK,IAAIW,KAAKwJ,OAAO,IAAIjB,KAAKkB,IAAIpK,QAAG,IAASW,KAAK0J,YAAY1J,KAAK2J,aAAatK,EAAEuK,iBAAiB,CAACC,gBAAAA,CAAiBxK,GAAGW,KAAKwJ,MAAMM,OAAOzK,EAAE,CAACgK,IAAAA,GAAO,IAA+D9J,EAAzDF,EAAE,IAAI0I,IAAyC,IAAUxI,KAA7CS,KAAK0E,YAAY8C,kBAAmCH,OAAOrH,KAAKH,eAAeN,KAAKF,EAAE6F,IAAI3F,EAAES,KAAKT,WAAWS,KAAKT,IAAW,EAAPF,EAAE0K,OAAS/J,KAAK6I,KAAKxJ,EAAE,CAAC2K,gBAAAA,GAAmB,IAAM3K,EAAEW,KAAKiK,YAAYjK,KAAKkK,aAAalK,KAAK0E,YAAYyF,mBAAmB,MDAhiE,EAAC7K,EAAEiF,KAAK,GAAG1C,IAAEvC,EAAE8K,mBAAmB7F,EAAE8F,KAAKhL,GAAGA,aAAaiF,cAAcjF,EAAEA,EAAEyF,kBAAkB,IAAI,IAAMjD,KAAK0C,EAAE,CAAC,IAAMA,EAAE+F,SAASC,cAAc,SAAS/K,EAAEH,IAAEmL,cAAS,IAAShL,GAAG+E,EAAEkG,aAAa,QAAQjL,GAAG+E,EAAEmG,YAAY7I,EAAEgD,QAAQvF,EAAEqL,YAAYpG,EAAE,GCAqzDjF,CAAED,EAAEW,KAAK0E,YAAYyD,eAAe9I,CAAC,CAACuL,iBAAAA,GAAoB5K,KAAK0J,aAAa1J,KAAKgK,mBAAmBhK,KAAKmJ,gBAAe,GAAInJ,KAAKwJ,MAAMF,SAASjK,GAAGA,EAAEuK,mBAAmB,CAACT,cAAAA,CAAe9J,GAAAA,CAAIwL,oBAAAA,GAAuB7K,KAAKwJ,MAAMF,SAASjK,GAAGA,EAAEyL,sBAAsB,CAACC,wBAAAA,CAAyB1L,EAAEC,EAAEC,GAAGS,KAAKgL,KAAK3L,EAAEE,EAAE,CAAC0L,IAAAA,CAAK5L,EAAEC,GAAG,IAAMC,EAAES,KAAK0E,YAAY8C,kBAAkBxC,IAAI3F,GAAGwC,EAAE7B,KAAK0E,YAAYwD,KAAK7I,EAAEE,QAAM,IAASsC,IAAG,IAAKtC,EAAEoH,UAAenG,QAAG,IAASjB,EAAEmH,WAAWV,YAAYzG,EAAEmH,UAAUX,KAAGC,YAAY1G,EAAEC,EAAEiH,MAAMxG,KAAKgJ,KAAK3J,EAAE,MAAMmB,EAAER,KAAKkL,gBAAgBrJ,GAAG7B,KAAKyK,aAAa5I,EAAErB,GAAGR,KAAKgJ,KAAK,KAAK,CAACgC,IAAAA,CAAK3L,EAAEC,GAAG,IAAMC,EAAES,KAAK0E,YAAY7C,EAAEtC,EAAE6H,KAAKpC,IAAI3F,GAAG,QAAG,IAASwC,GAAG7B,KAAKgJ,OAAOnH,EAAE,CAAC,IAAMxC,EAAEE,EAAEuI,mBAAmBjG,GAAGrB,EAAE,mBAAmBnB,EAAEqH,UAAU,CAACN,cAAc/G,EAAEqH,gBAAW,IAASrH,EAAEqH,WAAWN,cAAc/G,EAAEqH,UAAUX,IAAE/F,KAAKgJ,KAAKnH,EAAE7B,KAAK6B,GAAGrB,EAAE4F,cAAc9G,EAAED,EAAEmH,MAAMxG,KAAKgJ,KAAK,IAAI,CAAC,CAACrB,aAAAA,CAActI,EAAEC,EAAEC,GAAG,QAAG,IAASF,EAAE,CAAC,MAAGE,IAAIS,KAAK0E,YAAYoD,mBAAmBzI,IAAOuH,YAAYvE,KAAGrC,KAAKX,GAAGC,GAAG,OAAOU,KAAKoB,EAAE/B,EAAEC,EAAEC,EAAE,EAAC,IAAKS,KAAK8I,kBAAkB9I,KAAKkJ,KAAKlJ,KAAKmL,OAAO,CAAC/J,CAAAA,CAAE/B,EAAEC,EAAEC,GAAGS,KAAKoJ,KAAKgC,IAAI/L,IAAIW,KAAKoJ,KAAKlE,IAAI7F,EAAEC,IAAG,IAAKC,EAAEoH,SAAS3G,KAAKgJ,OAAO3J,IAAIW,KAAKqL,OAAO,IAAI9C,KAAKkB,IAAIpK,EAAE,CAAC8L,aAAanL,KAAK8I,iBAAgB,EAAG,UAAU9I,KAAKkJ,IAA+B,CAA1B,MAAM7J,GAAGiC,QAAQE,OAAOnC,EAAE,CAAC,IAAMA,EAAEW,KAAKsL,iBAAiB,OAAO,MAAMjM,SAASA,GAAGW,KAAK8I,eAAe,CAACwC,cAAAA,GAAiB,OAAOtL,KAAKuL,eAAe,CAACA,aAAAA,GAAgB,GAAIvL,KAAK8I,gBAAT,CAAgC,IAAI9I,KAAK+I,WAAW,CAAC,GAAG/I,KAAK0J,aAAa1J,KAAKgK,mBAAmBhK,KAAK6I,KAAK,CAAC,IAAI,IAAMxJ,EAAEC,KAAKU,KAAK6I,KAAK7I,KAAKX,GAAGC,EAAEU,KAAK6I,UAAK,CAAM,CAAC,IAAMxJ,EAAEW,KAAK0E,YAAY8C,kBAAkB,GAAU,EAAPnI,EAAE0K,KAAO,IAAI,IAAMzK,EAAEC,KAAKF,GAAI,IAAGE,EAAEiM,SAASxL,KAAKoJ,KAAKgC,IAAI9L,SAAI,IAASU,KAAKV,IAAIU,KAAKoB,EAAE9B,EAAEU,KAAKV,GAAGC,EAAE,CAACwF,IAAI1F,GAAE,EAASC,EAAEU,KAAKoJ,KAAK,KAAI/J,EAAEW,KAAKyL,aAAanM,KAAMU,KAAK0L,WAAWpM,GAAGU,KAAKwJ,MAAMF,SAASjK,GAAGA,EAAEsM,iBAAiB3L,KAAK4L,OAAOtM,IAAIU,KAAK6L,MAAwC,CAAjC,MAAMvM,GAAG,MAAMD,GAAE,EAAGW,KAAK6L,OAAOvM,CAAC,CAACD,GAAGW,KAAK8L,KAAKxM,EAAtd,CAAwd,CAACoM,UAAAA,CAAWrM,GAAIyM,CAAAA,IAAAA,CAAKzM,GAAGW,KAAKwJ,MAAMF,SAASjK,GAAGA,EAAE0M,kBAAkB/L,KAAK+I,aAAa/I,KAAK+I,YAAW,EAAG/I,KAAKgM,aAAa3M,IAAIW,KAAKiM,QAAQ5M,EAAE,CAACwM,IAAAA,GAAO7L,KAAKoJ,KAAK,IAAIrB,IAAI/H,KAAK8I,iBAAgB,CAAE,CAACoD,qBAAqB,OAAOlM,KAAKmM,mBAAmB,CAACA,iBAAAA,GAAoB,OAAOnM,KAAKkJ,IAAI,CAACuC,YAAAA,CAAapM,GAAG,OAAQ,CAAA,CAACuM,MAAAA,CAAOvM,GAAGW,KAAKqL,OAAOrL,KAAKqL,KAAK/B,SAASjK,GAAGW,KAAKiL,KAAK5L,EAAEW,KAAKX,MAAMW,KAAK6L,MAAM,CAACI,OAAAA,CAAQ5M,GAAI2M,CAAAA,YAAAA,CAAa3M,GAAI,EAACyH,EAAEqB,cAAc,GAAGrB,EAAEqD,kBAAkB,CAACiC,KAAK,QAAQtF,EAAExG,IAAE,sBAAsB,IAAIyH,IAAIjB,EAAExG,IAAE,cAAc,IAAIyH,IAAIpI,MAAI,CAAC0M,gBAAgBvF,KAAKnB,IAAE2G,0BAA0B,IAAI/I,KAAK,SCA56K,IAAClE,IAAE4E,WAAW1E,IAAEF,IAAEuG,aAAatG,EAAEC,IAAEA,IAAEgN,aAAa,WAAW,CAACC,WAAWnN,GAAGA,SAAI,EAAOwC,EAAE,QAAQ2D,EAAAA,OAASiH,KAAKC,SAASC,QAAQ,GAAG5I,MAAM,MAAMQ,IAAE,IAAIiB,EAAEhG,IAAM+E,IAAAA,OAAK/D,IAAE8J,SAASzG,EAAE,IAAIrD,IAAEoM,cAAc,IAAIrM,EAAElB,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,mBAAmBA,EAAEsG,EAAE7B,MAAMwE,QAA2DhI,EAAE,cAAc+B,EAAE,sDAAsDc,EAAE,OAAOZ,EAAE,KAAKsK,EAAEC,OAAAA,KAAYxM,sBAAsBA,MAAMA,uCAAuC,KAAKX,EAAE,KAAKiD,EAAE,KAAKmK,EAAE,qCAAwFC,EAAjD3N,IAAG,CAACE,KAAKD,MAAM2N,WAAW5N,EAAE6N,QAAQ3N,EAAE4N,OAAO7N,IAAMgD,CAAE,GAAiB8K,EAAEnK,OAAOoK,IAAI,gBAAgBC,EAAErK,OAAOoK,IAAI,eAAeE,EAAE,IAAI/I,QAAQgJ,EAAEhN,IAAEiN,iBAAiBjN,IAAE,KAAK,SAASY,EAAE/B,EAAEE,GAAG,GAAIoG,EAAEtG,IAAKA,EAAEQ,eAAe,OAAqD,YAAO,IAASP,EAAEA,EAAEkN,WAAWjN,GAAGA,EAAhF,MAAMqF,MAAM,iCAAqE,CAAmrB8I,MAAAA,EAAEhJ,WAAAA,EAAawI,QAAQ7N,EAAE4N,WAAW3N,GAAGE,GAAGuF,IAAIvE,EAAER,KAAK2N,MAAM,GAAG5I,IAAIxE,EAAE,EAAEoF,EAAE,EAAE,IAAMI,EAAE1G,EAAEK,OAAO,EAAEY,EAAEN,KAAK2N,OAAOtL,EAAEc,GAAvxB,EAAC9D,EAAEE,KAAK,IAA4BiB,EAAtBlB,EAAED,EAAEK,OAAO,EAAE6E,EAAE,GAASV,EAAE,IAAItE,EAAE,QAAQ,IAAIA,EAAE,SAAS,GAAGgB,EAAE8B,EAAE,IAAI0C,IAAIxF,EAAE,EAAEA,EAAED,EAAEC,IAAI,CAAC,IAAiBoG,EAAEI,EAAbzG,EAAED,EAAEE,GAAWe,KAAKgC,EAAE,EAAE,KAAKA,EAAEhD,EAAEI,SAASa,EAAEqN,UAAUtL,EAAc,QAAZyD,EAAExF,EAAEsN,KAAKvO,MAAcgD,EAAE/B,EAAEqN,UAAUrN,IAAI8B,EAAE,QAAQ0D,EAAE,GAAGxF,EAAE4C,OAAE,IAAS4C,EAAE,GAAGxF,EAAEgC,OAAE,IAASwD,EAAE,IAAIgH,EAAEe,KAAK/H,EAAE,MAAMvF,EAAEsM,OAAO,KAAK/G,EAAE,GAAG,MAAMxF,EAAEsM,QAAG,IAAS9G,EAAE,KAAKxF,EAAEsM,GAAGtM,IAAIsM,EAAE,MAAM9G,EAAE,IAAIxF,EAAEC,GAAG6B,EAAE/B,GAAE,QAAI,IAASyF,EAAE,GAAGzF,MAAMA,EAAEC,EAAEqN,UAAU7H,EAAE,GAAGrG,OAAOiG,EAAEI,EAAE,GAAGxF,OAAE,IAASwF,EAAE,GAAG8G,EAAE,MAAM9G,EAAE,GAAGnD,EAAEjD,GAAGY,IAAIqC,GAAGrC,IAAIZ,EAAEY,EAAEsM,EAAEtM,IAAI4C,GAAG5C,IAAIgC,EAAEhC,EAAE8B,GAAG9B,EAAEsM,EAAErM,OAAE,GAAQ,IAAMwM,EAAEzM,IAAIsM,GAAGxN,EAAEE,EAAE,GAAGwO,WAAW,MAAM,IAAI,GAAGlK,GAAGtD,IAAI8B,EAAE/C,EAAEE,IAAK,GAAHc,GAAMiE,EAAEhB,KAAKoC,GAAGrG,EAAEyE,MAAM,EAAEzD,GAAGuB,EAAEvC,EAAEyE,MAAMzD,GAAGkF,EAAEwH,GAAG1N,EAAEkG,QAAQlF,EAAEf,EAAEyN,EAAE,CAAC,MAAM,CAAC5L,EAAE/B,EAAEwE,GAAGxE,EAAEC,IAAI,QAAQ,IAAIC,EAAE,SAAS,IAAIA,EAAE,UAAU,KAAKgF,IAA0HyJ,CAAE3O,EAAEC,GAAG,GAAGU,KAAKiO,GAAGP,EAAEnD,cAAclI,EAAE7C,GAAGgO,EAAEU,YAAYlO,KAAKiO,GAAGE,QAAQ,IAAI7O,GAAG,IAAIA,EAAE,CAAC,IAAMD,EAAEW,KAAKiO,GAAGE,QAAQC,WAAW/O,EAAEgP,eAAehP,EAAEiP,WAAW,CAAC,KAAK,QAAQ9N,EAAEgN,EAAEe,aAAajO,EAAEZ,OAAOqG,GAAG,CAAC,GAAG,IAAIvF,EAAEgO,SAAS,CAAC,GAAGhO,EAAEiO,gBAAgB,IAAI,IAAMpP,KAAKmB,EAAEkO,oBAAoB,GAAGrP,EAAEsP,SAAS9M,GAAG,CAAC,IAAMtC,EAAE4D,EAAEwC,KAAKrG,EAAEkB,EAAEoO,aAAavP,GAAGwP,MAAMrJ,GAAG3D,EAAE,eAAegM,KAAKtO,GAAGe,EAAEiD,KAAK,CAACiD,KAAK,EAAEsI,MAAMvO,EAAEwO,KAAKlN,EAAE,GAAGqL,QAAQ5N,EAAE0P,KAAK,MAAMnN,EAAE,GAAGoN,EAAE,MAAMpN,EAAE,GAAGqN,EAAE,MAAMrN,EAAE,GAAGsN,EAAEC,IAAI5O,EAAE0K,gBAAgB7L,EAAE,MAAMA,EAAE0O,WAAWvI,KAAKlF,EAAEiD,KAAK,CAACiD,KAAK,EAAEsI,MAAMvO,IAAIC,EAAE0K,gBAAgB7L,IAAI,GAAG0N,EAAEe,KAAKtN,EAAE6O,SAAS,CAAC,IAAMhQ,EAAEmB,EAAEkK,YAAYmE,MAAMrJ,GAAGlG,EAAED,EAAEK,OAAO,EAAE,GAAK,EAAFJ,EAAI,CAACkB,EAAEkK,YAAYnL,IAAEA,IAAEsG,YAAY,GAAG,IAAId,IAAIxF,EAAE,EAAEA,EAAED,EAAEC,IAAIiB,EAAE8O,OAAOjQ,EAAEE,GAAGsE,KAAK2J,EAAEe,WAAWjO,EAAEiD,KAAK,CAACiD,KAAK,EAAEsI,QAAQvO,IAAIC,EAAE8O,OAAOjQ,EAAEC,GAAGuE,IAAI,CAAC,CAAC,MAAM,GAAG,IAAIrD,EAAEgO,SAAS,GAAGhO,EAAE+O,OAAOhL,IAAEjE,EAAEiD,KAAK,CAACiD,KAAK,EAAEsI,MAAMvO,QAAQ,CAACwE,IAAI1F,GAAE,EAAG,MAAK,KAAMA,EAAEmB,EAAE+O,KAAKC,QAAQhK,EAAEnG,EAAE,KAAKiB,EAAEiD,KAAK,CAACiD,KAAK,EAAEsI,MAAMvO,IAAIlB,GAAGmG,EAAE9F,OAAO,CAAC,CAACa,GAAG,CAAC,CAACgK,oBAAqBlL,CAAAA,EAAEE,GAAG,IAAMD,EAAEkB,IAAE+J,cAAc,YAAY,OAAOjL,EAAEmQ,UAAUpQ,EAAEC,CAAC,EAAE,SAASoQ,EAAErQ,EAAEE,EAAED,EAAED,EAAEwC,GAAG,GAAGtC,IAAI6N,EAAP,CAAkBrI,IAAIS,OAAE,IAAS3D,EAAEvC,EAAEqQ,OAAO9N,GAAGvC,EAAEsQ,KAAK,IAAMrL,EAAEhE,EAAEhB,QAAG,EAAOA,EAAEsQ,gBAAuBrK,GAAGd,cAAcH,IAAIiB,GAAGsK,QAAO,QAAI,IAASvL,EAAEiB,OAAE,GAAQA,EAAE,IAAIjB,EAAElF,IAAK0Q,KAAK1Q,EAAEC,EAAEuC,QAAI,IAASA,GAAGvC,EAAEqQ,OAAO,IAAI9N,GAAG2D,EAAElG,EAAEsQ,KAAKpK,QAAG,IAASA,IAAIjG,EAAEmQ,EAAErQ,EAAEmG,EAAEwK,KAAK3Q,EAAEE,EAAE4N,QAAQ3H,EAAE3D,GAApP,CAA2E,OAA6KtC,CAAC,CAAAwF,IAAAkL,IAAAA,MAASvL,WAAAA,CAAYrF,EAAEE,GAAGS,KAAKkQ,KAAK,GAAGlQ,KAAKmQ,UAAK,EAAOnQ,KAAKoQ,KAAK/Q,EAAEW,KAAKqQ,KAAK9Q,CAAC,CAAC+Q,cAAiBA,GAAA,OAAOtQ,KAAKqQ,KAAKC,UAAU,CAACC,QAAAA,GAAW,OAAOvQ,KAAKqQ,KAAKE,IAAI,CAACxK,CAAAA,CAAE1G,GAAG,IAAM4O,IAAIE,QAAQ5O,GAAGoO,MAAMrO,GAAGU,KAAKoQ,KAAKvO,GAAGxC,GAAGmR,eAAehQ,KAAGiQ,WAAWlR,GAAE,GAAIiO,EAAEU,YAAYrM,EAAEkD,IAAIS,EAAEgI,EAAEe,WAAWhK,EAAE,EAAE/E,EAAE,EAAEqE,EAAEvE,EAAE,GAAG,UAAK,IAASuE,GAAG,CAAC,GAAGU,IAAIV,EAAEiL,MAAM,CAAC/J,IAAIxF,EAAE,IAAIsE,EAAE2C,KAAKjH,EAAE,IAAImR,EAAElL,EAAEA,EAAEmL,YAAY3Q,KAAKX,GAAG,IAAIwE,EAAE2C,KAAKjH,EAAE,IAAIsE,EAAEmL,KAAKxJ,EAAE3B,EAAEkL,KAAKlL,EAAEqJ,QAAQlN,KAAKX,GAAG,IAAIwE,EAAE2C,OAAOjH,EAAE,IAAIqR,EAAEpL,EAAExF,KAAKX,IAAIW,KAAKkQ,KAAK3M,KAAKhE,GAAGsE,EAAEvE,IAAIE,EAAE,CAAC+E,IAAIV,GAAGiL,QAAQtJ,EAAEgI,EAAEe,WAAWhK,IAAI,CAAC,OAAOiJ,EAAEU,YAAY1N,IAAEqB,CAAC,CAAClC,CAAAA,CAAEN,GAAG0F,IAAIxF,EAAE,EAAE,IAAI,IAAMD,KAAKU,KAAKkQ,cAAc5Q,SAAI,IAASA,EAAE4N,SAAS5N,EAAEuR,KAAKxR,EAAEC,EAAEC,GAAGA,GAAGD,EAAE4N,QAAQxN,OAAO,GAAGJ,EAAEuR,KAAKxR,EAAEE,KAAKA,GAAG,GAAQmR,MAAAA,EAAEH,WAAW,OAAOvQ,KAAKqQ,MAAME,MAAMvQ,KAAK8Q,IAAI,CAACpM,WAAAA,CAAYrF,EAAEE,EAAED,EAAEuC,GAAG7B,KAAKwG,KAAK,EAAExG,KAAK+Q,KAAKzD,EAAEtN,KAAKmQ,UAAK,EAAOnQ,KAAKgR,KAAK3R,EAAEW,KAAKiR,KAAK1R,EAAES,KAAKqQ,KAAK/Q,EAAEU,KAAKkR,QAAQrP,EAAE7B,KAAK8Q,KAAKjP,GAAG8H,cAAa,CAAE,CAAC2G,cAAiBvL,GAAAA,IAAI1F,EAAEW,KAAKgR,KAAKV,WAAW,IAAM/Q,EAAES,KAAKqQ,KAAK,YAAO,IAAS9Q,GAAG,KAAKF,GAAGmP,SAAajP,EAAE+Q,WAAYjR,CAAC,CAAC8R,aAAAA,GAAgB,OAAOnR,KAAKgR,IAAI,CAACI,cAAc,OAAOpR,KAAKiR,IAAI,CAACJ,IAAAA,CAAKxR,EAAEE,EAAES,MAAMX,EAAEqQ,EAAE1P,KAAKX,EAAEE,GAAGgB,EAAElB,GAAGA,IAAIiO,GAAG,MAAMjO,GAAG,KAAKA,GAAGW,KAAK+Q,OAAOzD,GAAGtN,KAAKqR,OAAOrR,KAAK+Q,KAAKzD,GAAGjO,IAAIW,KAAK+Q,MAAM1R,IAAI+N,GAAGpN,KAAKuC,EAAElD,QAAG,IAASA,EAAE4N,WAAWjN,KAAK+M,EAAE1N,QAAG,IAASA,EAAEmP,SAASxO,KAAKoN,EAAE/N,GAA1zHA,IAAGsG,EAAEtG,IAAI,mBAAmBA,IAAI4D,OAAOC,UAAsxH6C,CAAE1G,GAAGW,KAAKoP,EAAE/P,GAAGW,KAAKuC,EAAElD,EAAE,CAACiS,CAAAA,CAAEjS,GAAG,OAAOW,KAAKgR,KAAKV,WAAWiB,aAAalS,EAAEW,KAAKiR,KAAK,CAAC7D,CAAAA,CAAE/N,GAAGW,KAAK+Q,OAAO1R,IAAIW,KAAKqR,OAAOrR,KAAK+Q,KAAK/Q,KAAKsR,EAAEjS,GAAG,CAACkD,CAAAA,CAAElD,GAAGW,KAAK+Q,OAAOzD,GAAG/M,EAAEP,KAAK+Q,MAAM/Q,KAAKgR,KAAKL,YAAYpB,KAAKlQ,EAAEW,KAAKoN,EAAE5M,IAAEgR,eAAenS,IAAIW,KAAK+Q,KAAK1R,CAAC,CAAC0N,CAAAA,CAAE1N,GAAG,IAAM8N,OAAO5N,EAAE0N,WAAW3N,GAAGD,EAAEwC,EAAE,iBAAiBvC,EAAEU,KAAKyR,KAAKpS,SAAI,IAASC,EAAE2O,KAAK3O,EAAE2O,GAAGP,EAAEnD,cAAcnJ,EAAE9B,EAAEkG,EAAElG,EAAEkG,EAAE,IAAIxF,KAAKkR,UAAU5R,GAAG,GAAGU,KAAK+Q,MAAMX,OAAOvO,EAAE7B,KAAK+Q,KAAKpR,EAAEJ,OAAO,CAAC,IAAMF,EAAE,IAAIqS,IAAE7P,EAAE7B,MAAMV,EAAED,EAAE0G,EAAE/F,KAAKkR,SAAS7R,EAAEM,EAAEJ,GAAGS,KAAKoN,EAAE9N,GAAGU,KAAK+Q,KAAK1R,CAAC,CAAC,CAACoS,IAAAA,CAAKpS,GAAG0F,IAAIxF,EAAEgO,EAAEvI,IAAI3F,EAAE6N,SAAS,YAAO,IAAS3N,GAAGgO,EAAErI,IAAI7F,EAAE6N,QAAQ3N,EAAE,IAAImO,EAAErO,IAAIE,CAAC,CAAC6P,CAAAA,CAAE/P,GAAGsG,EAAE3F,KAAK+Q,QAAQ/Q,KAAK+Q,KAAK,GAAG/Q,KAAKqR,QAAQ,IAAsC7L,EAAhCjG,EAAES,KAAK+Q,KAAKhM,IAAIzF,EAAEuC,EAAE,EAAE,IAAU2D,KAAKnG,EAAEwC,IAAItC,EAAEG,OAAOH,EAAEgE,KAAKjE,EAAE,IAAIoR,EAAE1Q,KAAKsR,EAAEzN,KAAK7D,KAAKsR,EAAEzN,KAAK7D,KAAKA,KAAKkR,UAAU5R,EAAEC,EAAEsC,GAAGvC,EAAEuR,KAAKrL,GAAG3D,IAAIA,EAAEtC,EAAEG,SAASM,KAAKqR,KAAK/R,GAAGA,EAAE2R,KAAKN,YAAY9O,GAAGtC,EAAEG,OAAOmC,EAAE,CAACwP,IAAAA,CAAKhS,EAAEW,KAAKgR,KAAKL,YAAYpR,GAAG,IAAIS,KAAK2R,QAAO,GAAG,EAAGpS,GAAGF,GAAGA,IAAIW,KAAKiR,MAAM,CAAC,IAAM1R,EAAEF,EAAEsR,YAAYtR,EAAEuS,SAASvS,EAAEE,CAAC,CAAC,CAACsS,YAAAA,CAAaxS,QAAG,IAASW,KAAKqQ,OAAOrQ,KAAK8Q,KAAKzR,EAAEW,KAAK2R,OAAOtS,GAAG,QAAQ+P,EAAEC,WAAAA,GAAc,OAAOrP,KAAK8R,QAAQzC,OAAO,CAACkB,QAAWA,GAAA,OAAOvQ,KAAKqQ,KAAKE,IAAI,CAAC7L,WAAAA,CAAYrF,EAAEE,EAAED,EAAEuC,EAAE2D,GAAGxF,KAAKwG,KAAK,EAAExG,KAAK+Q,KAAKzD,EAAEtN,KAAKmQ,UAAK,EAAOnQ,KAAK8R,QAAQzS,EAAEW,KAAK+O,KAAKxP,EAAES,KAAKqQ,KAAKxO,EAAE7B,KAAKkR,QAAQ1L,EAAW,EAATlG,EAAEI,QAAU,KAAKJ,EAAE,IAAI,KAAKA,EAAE,IAAIU,KAAK+Q,KAAKjN,MAAMxE,EAAEI,OAAO,GAAGqS,KAAK,IAAItL,QAAQzG,KAAKkN,QAAQ5N,GAAGU,KAAK+Q,KAAKzD,CAAC,CAACuD,IAAAA,CAAKxR,EAAEE,EAAES,KAAKV,EAAEuC,GAAG,IAAM2D,EAAExF,KAAKkN,QAAQnI,IAAIR,GAAE,EAAG,QAAG,IAASiB,EAAEnG,EAAEqQ,EAAE1P,KAAKX,EAAEE,EAAE,IAAGgF,GAAGhE,EAAElB,IAAIA,IAAIW,KAAK+Q,MAAM1R,IAAI+N,KAAMpN,KAAK+Q,KAAK1R,OAAO,CAAC,IAAcG,EAAEgB,EAAVqB,EAAExC,EAAU,IAAIA,EAAEmG,EAAE,GAAGhG,EAAE,EAAEA,EAAEgG,EAAE9F,OAAO,EAAEF,KAAIgB,EAAEkP,EAAE1P,KAAK6B,EAAEvC,EAAEE,GAAGD,EAAEC,MAAO4N,IAAI5M,EAAER,KAAK+Q,KAAKvR,IAAI+E,KAAKhE,EAAEC,IAAIA,IAAIR,KAAK+Q,KAAKvR,GAAGgB,IAAI8M,EAAEjO,EAAEiO,EAAEjO,IAAIiO,IAAIjO,IAAImB,GAAG,IAAIgF,EAAEhG,EAAE,IAAIQ,KAAK+Q,KAAKvR,GAAGgB,CAAC,CAAC+D,IAAI1C,GAAG7B,KAAKgS,EAAE3S,EAAE,CAAC2S,CAAAA,CAAE3S,GAAGA,IAAIiO,EAAEtN,KAAK8R,QAAQ5G,gBAAgBlL,KAAK+O,MAAM/O,KAAK8R,QAAQrH,aAAazK,KAAK+O,KAAK1P,GAAG,GAAG,EAAC,MAAO4P,UAAUG,EAAE1K,WAAAA,GAAckE,SAASnJ,WAAWO,KAAKwG,KAAK,CAAC,CAACwL,CAAAA,CAAE3S,GAAGW,KAAK8R,QAAQ9R,KAAK+O,MAAM1P,IAAIiO,OAAE,EAAOjO,CAAC,EAAC,MAAO6P,UAAUE,EAAE1K,WAAAA,GAAckE,SAASnJ,WAAWO,KAAKwG,KAAK,CAAC,CAACwL,CAAAA,CAAE3S,GAAGW,KAAK8R,QAAQG,gBAAgBjS,KAAK+O,OAAO1P,GAAGA,IAAIiO,EAAE,EAAQ6B,MAAAA,UAAUC,EAAE1K,WAAAA,CAAYrF,EAAEE,EAAED,EAAEuC,EAAE2D,GAAGoD,MAAMvJ,EAAEE,EAAED,EAAEuC,EAAE2D,GAAGxF,KAAKwG,KAAK,CAAC,CAACqK,IAAAA,CAAKxR,EAAEE,EAAES,MAAM,IAAqD6B,EAA8E2D,GAA/HnG,EAAEqQ,EAAE1P,KAAKX,EAAEE,EAAE,IAAI+N,KAAKF,IAAe9N,EAAEU,KAAK+Q,KAAKlP,EAAExC,IAAIiO,GAAGhO,IAAIgO,GAAGjO,EAAE6S,UAAU5S,EAAE4S,SAAS7S,EAAE8S,OAAO7S,EAAE6S,MAAM9S,EAAE+S,UAAU9S,EAAE8S,QAAQ5M,EAAEnG,IAAIiO,IAAIhO,IAAIgO,GAAGzL,GAAGA,GAAG7B,KAAK8R,QAAQO,oBAAoBrS,KAAK+O,KAAK/O,KAAKV,GAAGkG,GAAGxF,KAAK8R,QAAQQ,iBAAiBtS,KAAK+O,KAAK/O,KAAKX,GAAGW,KAAK+Q,KAAK1R,EAAC,CAACkT,WAAAA,CAAYlT,GAAG,mBAAmBW,KAAK+Q,KAAK/Q,KAAK+Q,KAAKjR,KAAKE,KAAKkR,SAASsB,MAAMxS,KAAK8R,QAAQzS,GAAGW,KAAK+Q,KAAKwB,YAAYlT,EAAE,QAAQuR,EAAElM,WAAAA,CAAYrF,EAAEE,EAAED,GAAGU,KAAK8R,QAAQzS,EAAEW,KAAKwG,KAAK,EAAExG,KAAKmQ,UAAK,EAAOnQ,KAAKqQ,KAAK9Q,EAAES,KAAKkR,QAAQ5R,CAAC,CAACiR,QAAAA,GAAW,OAAOvQ,KAAKqQ,KAAKE,IAAI,CAACM,IAAAA,CAAKxR,GAAGqQ,EAAE1P,KAAKX,EAAE,EAAO,IAA6D2S,EAAE3S,IAAEoT,uBAA6EC,GAAtDV,IAAItE,EAAEgD,IAAIrR,IAAEsT,kBAAkB,IAAIpP,KAAK,SAAiB,CAAClE,EAAEE,EAAED,KAAK,IAAMuC,EAAEvC,GAAGsT,cAAcrT,EAAEwF,IAAIS,EAAE3D,EAAEgR,WAAW,QAAG,IAASrN,EAAE,CAAC,IAAMnG,EAAEC,GAAGsT,cAAc,KAAK/Q,EAAEgR,WAAWrN,EAAE,IAAIkL,EAAEnR,EAAEgS,aAAa1N,IAAIxE,GAAGA,OAAE,EAAOC,GAAG,CAAA,EAAG,CAAC,OAAOkG,EAAEqL,KAAKxR,GAAGmG,ICAz6NsN,kBAAgBzT,EAAEqF,WAAAA,GAAckE,SAASnJ,WAAWO,KAAK+S,cAAc,CAACP,KAAKxS,MAAMA,KAAKgT,UAAK,CAAM,CAAChJ,gBAAAA,GAAmB,IAAM3K,EAAEuJ,MAAMoB,mBAAmB,OAAOhK,KAAK+S,cAAcH,eAAevT,EAAE+O,WAAW/O,CAAC,CAACuM,MAAAA,CAAOvM,GAAG,IAAMC,EAAEU,KAAKiT,SAASjT,KAAK+I,aAAa/I,KAAK+S,cAAcpJ,YAAY3J,KAAK2J,aAAaf,MAAMgD,OAAOvM,GAAGW,KAAKgT,KAAKnR,EAAEvC,EAAEU,KAAK0J,WAAW1J,KAAK+S,cAAc,CAACnI,iBAAAA,GAAoBhC,MAAMgC,oBAAoB5K,KAAKgT,MAAMnB,cAAa,EAAG,CAAChH,oBAAAA,GAAuBjC,MAAMiC,uBAAuB7K,KAAKgT,MAAMnB,cAAa,EAAG,CAACoB,MAAAA,GAAS,OAAO3T,CAAC,GAAmGC,GAAjGiB,IAAE0S,eAAc,EAAG1S,IAAawH,WAAE,EAAG/D,WAAWkP,2BAA2B,CAACC,WAAW5S,MAAYyD,WAAWoP,2BAA0B9T,IAAI,CAAC6T,WAAW5S,OAA0DyD,WAAWqP,qBAAqB,IAAI/P,KAAK,SCLhyB,IAAAgQ,UAAAC,QAAA,SAAAC,UAAA,IAAAC,EAAA,CAAA,gBAAA,gBAAA,UAAA,umBAAA,aAAA,gBAAA,+BAAA,WAAA,YAAA,eAAA,aAAA,kBAAA,OAAAD,QAAA,WAAA,OAAAC,CAAA,IAAA,CAAA,SAAAF,QAAAG,EAAAC,GAAA,IAAAC,EAAAJ,UAAA,OAAAD,QAAA,SAAAM,EAAAC,GAAA,OAAAF,EAAAC,GAAA,IAAA,GAAAH,EAAAC,EAAA,OAAA,IAAA,IAAAI,EAAAR,QAAAS,EAAAR,YAAA,IAAA,GAAA,SAAAS,SAAAF,EAAA,QAAAE,SAAAF,EAAA,MAAA,IAAAE,SAAAF,EAAA,MAAA,GAAAE,SAAAF,EAAA,MAAA,GAAAE,SAAAF,EAAA,MAAA,IAAAE,SAAAF,EAAA,MAAA,GAAAE,SAAAF,EAAA,MAAA,IAAAE,SAAAF,EAAA,MAAA,IAAAE,SAAAF,EAAA,MAAA,EAAAE,SAAAF,EAAA,MAAA,GAAA,MAAAC,EAAA1Q,KAAA0Q,EAAAE,QAAA,CAAA,MAAAC,GAAAH,EAAA1Q,KAAA0Q,EAAAE,QAAA,MAAO,IAAME,YAAc,gCAAA,+BCgB3B,SAASC,KAAKC,EAAYC,GACtB,OAAO,IAAIlT,SAAQ,SAAUC,GAAW,OAAOkT,WAAWlT,EAASgT,EAAYC,KACnF,CA4BA,SAASE,UAAUhT,GACf,QAASA,GAA+B,mBAAfA,EAAMQ,IACnC,CAcA,SAASyS,aAAaC,EAAQC,GAC1B,IACI,IAAIC,EAAgBF,IAChBF,UAAUI,GACVA,EAAc5S,MAAK,SAAUF,GAAU,OAAO6S,GAAS,EAAM7S,EAAQ,IAAI,SAAU+S,GAAS,OAAOF,GAAS,EAAOE,MAGnHF,GAAS,EAAMC,EAK3B,CAFI,MAAOC,GACHF,GAAS,EAAOE,EACxB,CACA,CAMA,SAASC,cAAcC,EAAOJ,EAAUK,GAEpC,YAD4B,IAAxBA,IAAkCA,EAAsB,IACrDjU,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAImV,EAASC,EAAqB7V,EAAG8V,EACrC,OAAOlT,YAAYnC,MAAM,SAAUsV,GAC/B,OAAQA,EAAG9S,OACP,KAAK,EACD2S,EAAUrR,MAAMmR,EAAMvV,QACtB0V,EAAsBG,KAAKF,MAC3B9V,EAAI,EACJ+V,EAAG9S,MAAQ,EACf,KAAK,EACD,OAAMjD,EAAI0V,EAAMvV,QAChByV,EAAQ5V,GAAKsV,EAASI,EAAM1V,GAAIA,GAChC8V,EAAME,KAAKF,MACED,EAAsBF,GAA7BG,GACND,EAAsBC,EACf,CAAC,EAvEjB,IAAI/T,SAAQ,SAAUC,GACzB,IAAIiU,EAAU,IAAIC,eAClBD,EAAQE,MAAMC,UAAY,WAAc,OAAOpU,GAAY,EAC3DiU,EAAQI,MAAMC,YAAY,KAClC,MAiEoF,CAAC,EAAa,IAH9C,CAAC,EAAa,GAMlD,KAAK,EACDP,EAAG7S,OACH6S,EAAG9S,MAAQ,EACf,KAAK,EAED,QADEjD,EACK,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,EAAc4V,GAE9C,GACA,GACA,CAQA,SAASW,kCAAkCC,GAEvC,OADAA,EAAQ7T,UAAK8T,GAAW,WAAiC,IAClDD,CACX,CAyBA,SAASE,MAAMvU,GACX,OAAOwS,SAASxS,EACpB,CAIA,SAASwU,QAAQxU,GACb,OAAOyU,WAAWzU,EACtB,CACA,SAAS0U,WAAW1U,EAAO2U,GACvB,MAAwB,iBAAV3U,GAAsB4U,MAAM5U,GAAS2U,EAAc3U,CACrE,CACA,SAAS6U,YAAYpJ,GACjB,OAAOA,EAAO/H,QAAO,SAAUoR,EAAK9U,GAAS,OAAO8U,GAAO9U,EAAQ,EAAI,EAAG,GAAI,EAClF,CACA,SAAS+U,MAAM/U,EAAOgV,GAElB,YADa,IAATA,IAAmBA,EAAO,GACR,GAAlBjK,KAAKkK,IAAID,GACFjK,KAAKgK,MAAM/U,EAAQgV,GAAQA,GAK9BE,EAAc,EAAIF,EACfjK,KAAKgK,MAAM/U,EAAQkV,GAAeA,EAEjD,CAyEA,SAASC,OAAOhK,EAAGrN,GACf,IAAIsX,EAAKjK,EAAE,KAAO,GACdkK,EAAKvX,EAAE,KAAO,GACdwX,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAEjCD,IADAC,IAHwE,MAAPtK,EAAE,KACK,MAAPrN,EAAE,OAGtD,GACb2X,GAAM,MAENF,IADAC,IAN+CrK,EAAE,KAAO,KACTrN,EAAE,KAAO,OAM3C,GACb0X,GAAM,MAMNrK,EAAE,KAJFmK,IADAC,IATkC,MAAPpK,EAAE,KACK,MAAPrN,EAAE,OAShB,KAEPsX,EAAKC,GACL,QACQ,IAHdE,GAAM,OAINpK,EAAE,GAAMqK,GAAM,GAAMC,CACxB,CAKA,SAASC,YAAYvK,EAAGrN,GACpB,IAAIsX,EAAKjK,EAAE,KAAO,GAAIwK,EAAY,MAAPxK,EAAE,GAAayK,EAAKzK,EAAE,KAAO,GAAI0K,EAAY,MAAP1K,EAAE,GAC/DkK,EAAKvX,EAAE,KAAO,GAAIgY,EAAY,MAAPhY,EAAE,GAAaiY,EAAKjY,EAAE,KAAO,GACpDwX,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAEjCD,IADAC,GAAMI,GAFsDG,EAAY,MAAPlY,EAAE,OAGtD,GACb2X,GAAM,MAKNF,IAJAC,GAAMI,EAAKI,KACE,MAEbR,GADM,MAANA,GACMK,EAAKE,KACE,IACbP,GAAM,MAYNrK,EAAE,KAVFmK,IADAC,GAAMI,EAAKK,KACE,MAEbT,GADM,MAANA,GACMK,EAAKG,KACE,MAEbR,GADM,MAANA,GACMM,EAAKC,KACE,KAEPV,EAAKY,EAAKL,EAAKI,EAAKH,EAAKE,EAAKD,EAAKR,GACnC,QACQ,IAHdE,GAAM,OAINpK,EAAE,GAAMqK,GAAM,GAAMC,CACxB,CAKA,SAASQ,QAAQ9K,EAAG+K,GAChB,IAAId,EAAKjK,EAAE,GAEE,KADb+K,GAAQ,KAEJ/K,EAAE,GAAKA,EAAE,GACTA,EAAE,GAAKiK,GAEFc,EAAO,IACZ/K,EAAE,GAAMiK,GAAMc,EAAS/K,EAAE,KAAQ,GAAK+K,EACtC/K,EAAE,GAAMA,EAAE,IAAM+K,EAASd,IAAQ,GAAKc,IAItC/K,EAAE,GAAMA,EAAE,KADV+K,GAAQ,IACiBd,IAAQ,GAAKc,EACtC/K,EAAE,GAAMiK,GAAMc,EAAS/K,EAAE,KAAQ,GAAK+K,EAE9C,CAKA,SAASC,aAAahL,EAAG+K,GAER,IADbA,GAAQ,MAICA,EAAO,IACZ/K,EAAE,GAAKA,EAAE,KAAQ,GAAK+K,EACtB/K,EAAE,GAAKA,EAAE,IAAM+K,IAGf/K,EAAE,GAAKA,EAAE,IAAO+K,EAAO,GACvB/K,EAAE,GAAK,GAEf,CAKA,SAASiL,OAAOjL,EAAGrN,GACfqN,EAAE,IAAMrN,EAAE,GACVqN,EAAE,IAAMrN,EAAE,EACd,CACA,IAAIuY,GAAK,CAAC,WAAY,YAClBC,GAAK,CAAC,WAAY,WAMtB,SAASC,QAAQzS,GACb,IAAI0S,EAAU,CAAC,EAAG1S,EAAE,KAAO,GAC3BsS,OAAOtS,EAAG0S,GACVd,YAAY5R,EAAGuS,IACfG,EAAQ,GAAK1S,EAAE,KAAO,EACtBsS,OAAOtS,EAAG0S,GACVd,YAAY5R,EAAGwS,IACfE,EAAQ,GAAK1S,EAAE,KAAO,EACtBsS,OAAOtS,EAAG0S,EACd,CACA,IAAIC,GAAK,CAAC,WAAY,WAClBC,GAAK,CAAC,WAAY,WAClBC,IAAM,CAAC,EAAG,GACVC,GAAK,CAAC,EAAG,YACTC,GAAK,CAAC,EAAG,WAQb,SAASC,WAAWC,EAAOC,GAWvB,IAVA,IAAItY,EArJR,SAASuY,aAAaF,GAIlB,IADA,IAAIzW,EAAS,IAAI4W,WAAWH,EAAM/Y,QACzBH,EAAI,EAAGA,EAAIkZ,EAAM/Y,OAAQH,IAAK,CAEnC,IAAIsZ,EAAWJ,EAAMK,WAAWvZ,GAEhC,GAAe,IAAXsZ,EACA,OAAA,IAAWE,aAAcC,OAAOP,GAEpCzW,EAAOzC,GAAKsZ,CACpB,CACI,OAAO7W,CACX,CAuIc2W,CAAaF,GAGnBQ,GADAvZ,EAAS,CAAC,EAAGU,EAAIV,SACE,GAAK,GACxBwZ,EAAQxZ,EAAO,GAAKuZ,EACpBE,EAAK,CAAC,EAJVT,EAAOA,GAAQ,GAKXU,EAAK,CAAC,EAAGV,GACTW,EAAK,CAAC,EAAG,GACTC,EAAK,CAAC,EAAG,GAER/Z,EAAI,EAAGA,EAAI2Z,EAAO3Z,GAAQ,GAC3B8Z,EAAG,GAAKjZ,EAAIb,EAAI,GAAMa,EAAIb,EAAI,IAAM,EAAMa,EAAIb,EAAI,IAAM,GAAOa,EAAIb,EAAI,IAAM,GAC7E8Z,EAAG,GAAKjZ,EAAIb,GAAMa,EAAIb,EAAI,IAAM,EAAMa,EAAIb,EAAI,IAAM,GAAOa,EAAIb,EAAI,IAAM,GACzE+Z,EAAG,GAAKlZ,EAAIb,EAAI,IAAOa,EAAIb,EAAI,KAAO,EAAMa,EAAIb,EAAI,KAAO,GAAOa,EAAIb,EAAI,KAAO,GACjF+Z,EAAG,GAAKlZ,EAAIb,EAAI,GAAMa,EAAIb,EAAI,IAAM,EAAMa,EAAIb,EAAI,KAAO,GAAOa,EAAIb,EAAI,KAAO,GAC/E6X,YAAYiC,EAAIlB,IAChBR,QAAQ0B,EAAI,IACZjC,YAAYiC,EAAIjB,IAChBN,OAAOqB,EAAIE,GACX1B,QAAQwB,EAAI,IACZtC,OAAOsC,EAAIC,GACXhC,YAAY+B,EAAId,KAChBxB,OAAOsC,EAAIb,IACXlB,YAAYkC,EAAIlB,IAChBT,QAAQ2B,EAAI,IACZlC,YAAYkC,EAAInB,IAChBL,OAAOsB,EAAIE,GACX3B,QAAQyB,EAAI,IACZvC,OAAOuC,EAAID,GACX/B,YAAYgC,EAAIf,KAChBxB,OAAOuC,EAAIb,IAEfc,EAAG,GAAK,EAERC,EADAD,EAAG,GAAK,GACA,EAER,IAAIE,EAAM,CADVD,EAAG,GAAK,EACM,GACd,OAAQL,GACJ,KAAK,GACDM,EAAI,GAAKnZ,EAAIb,EAAI,IACjBsY,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAKnZ,EAAIb,EAAI,IACjBsY,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAKnZ,EAAIb,EAAI,IACjBsY,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAKnZ,EAAIb,EAAI,IACjBsY,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAKnZ,EAAIb,EAAI,IACjBsY,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,GAClBzB,OAAOwB,EAAIC,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBuY,OAAOwB,EAAIC,GACXnC,YAAYkC,EAAIlB,IAChBT,QAAQ2B,EAAI,IACZlC,YAAYkC,EAAInB,IAChBL,OAAOsB,EAAIE,GAEf,KAAK,EACDC,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,GAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,GACbuY,OAAOuB,EAAIE,GACXnC,YAAYiC,EAAIlB,IAChBR,QAAQ0B,EAAI,IACZjC,YAAYiC,EAAIjB,IAChBN,OAAOqB,EAAIE,GAWnB,OARAvB,OAAOqB,EAAIzZ,GACXoY,OAAOsB,EAAI1Z,GACXmX,OAAOsC,EAAIC,GACXvC,OAAOuC,EAAID,GACXlB,QAAQkB,GACRlB,QAAQmB,GACRvC,OAAOsC,EAAIC,GACXvC,OAAOuC,EAAID,IACF,YAAcA,EAAG,KAAO,GAAGhU,SAAS,KAAKpB,WAC7C,YAAcoV,EAAG,KAAO,GAAGhU,SAAS,KAAKpB,WACzC,YAAcqV,EAAG,KAAO,GAAGjU,SAAS,KAAKpB,WACzC,YAAcqV,EAAG,KAAO,GAAGjU,SAAS,KAAKpB,SAClD,CA6EA,SAASyV,YAAYC,EAASC,EAAeC,EAAgBzE,GACzD,IAAI0E,EAAkBza,OAAOkI,KAAKoS,GAASI,QAAO,SAAUC,GAAa,OAzb7E,SAASC,SAASC,EAAUC,GACxB,OAZJ,SAASC,SAASF,EAAUC,GACxB,IAAK,IAAI1a,EAAI,EAAGsE,EAAImW,EAASta,OAAQH,EAAIsE,IAAKtE,EAC1C,GAAIya,EAASza,KAAO0a,EAChB,OAAO,EAGf,OAAO,CACX,CAKYC,CAASF,EAAUC,EAC/B,CAuboFF,CAASJ,EAAgBG,EAAW,IAGhHK,EAAuBrE,kCAAkCd,cAAc4E,GAAiB,SAAUE,GAAa,OA1DvH,SAASM,WAAWC,EAAQX,GACxB,IAAIY,EAAoBxE,kCAAkC,IAAIxU,SAAQ,SAAUiZ,GAC5E,IAAIC,EAAgBjF,KAAKF,MAGzBV,aAAa0F,EAAOI,KAAK,KAAMf,IAAgB,WAE3C,IADA,IAAIgB,EAAW,GACNC,EAAK,EAAGA,EAAKlb,UAAUC,OAAQib,IACpCD,EAASC,GAAMlb,UAAUkb,GAE7B,IAKIC,EALAC,EAAetF,KAAKF,MAAQmF,EAEhC,OAAKE,EAAS,GArB1B,SAASI,oBAAoBF,GACzB,MAA6B,mBAAfA,CAClB,CAwBgBE,CAFAF,EAAaF,EAAS,IAGfH,GAAY,WAAc,OAAU7Y,MAAOkZ,EAAYG,SAAUF,WAG5EN,GAAY,WACR,OAAO,IAAIjZ,SAAQ,SAAU0Z,GACzB,IAAIC,EAAe1F,KAAKF,MACxBV,aAAaiG,GAAY,WAErB,IADA,IAAIM,EAAU,GACLP,EAAK,EAAGA,EAAKlb,UAAUC,OAAQib,IACpCO,EAAQP,GAAMlb,UAAUkb,GAE5B,IAAII,EAAWF,EAAetF,KAAKF,MAAQ4F,EAE3C,IAAKC,EAAQ,GACT,OAAOF,EAAW,CAAEjG,MAAOmG,EAAQ,GAAIH,SAAUA,IAGrDC,EAAW,CAAEtZ,MAAOwZ,EAAQ,GAAIH,SAAUA,GAClE,GACA,GACA,IAzBuBR,GAAY,WAAc,MAAQ,CAAExF,MAAO2F,EAAS,GAAIK,SAAUF,KA0BzF,GACA,KACI,OAAO,WACH,OAAOP,EAAkBpY,MAAK,SAAUiZ,GAAkB,OAAOA,MACpE,CACL,CAc8Hf,CAAWX,EAAQK,GAAYJ,EAAiB,GAAExE,IAC5K,OAAO,WACH,OAAOjU,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAmBob,EAAmBC,EAAgBC,EAAYxM,EAClE,OAAO3M,YAAYnC,MAAM,SAAUsV,GAC/B,OAAQA,EAAG9S,OACP,KAAK,EAAG,MAAO,CAAC,EAAa2X,GAC7B,KAAK,EAED,MAAO,CAAC,EAAanF,cADLM,EAAG7S,QAC+B,SAAU8Y,GAAgB,OAAOzF,kCAAkCyF,IAAkB,GAAErG,IAC7I,KAAK,EAED,OADAkG,EAAoB9F,EAAG7S,OAChB,CAAC,EAAanB,QAAQka,IAAIJ,IAGrC,KAAK,EAGD,IAFAC,EAAiB/F,EAAG7S,OACpB6Y,EAAa,GACRxM,EAAQ,EAAGA,EAAQ8K,EAAgBla,SAAUoP,EAC9CwM,EAAW1B,EAAgB9K,IAAUuM,EAAevM,GAExD,MAAO,CAAC,EAAcwM,GAE9C,GACA,GACK,CACL,CAuCA,SAASG,YACL,IAAIC,EAAIC,OACJnc,EAAIoc,UAER,OAMM,GANErF,YAAY,CAChB,gBAAiBmF,EACjB,mBAAoBA,EACpB,gBAAiBA,EACjB,qBAAsBlc,EACtB,qBAAsBA,GAE9B,CAoBA,SAASqc,aAEL,IAAIH,EAAIC,OACJnc,EAAIoc,UACR,OAQM,GARErF,YAAY,CAChB,4BAA6B/W,EAC7B,2BAA4BA,EACW,KAAtCA,EAAEsc,QAAU,IAAItM,QAAQ,UACzB,oCAAqCkM,EACrC,mBAAoBA,EACpB,sBAAuBA,EACvB,wBAAyBA,GAEjC,CAQA,SAASK,WAEL,IAAIL,EAAIC,OAER,OAOM,GAPEpF,YAAY,CAChB,kBAAmBmF,EACnB,sBAAuBA,EACvB,YAAaA,EACiB,IAL1BE,UAKFE,OAAOtM,QAAQ,SACjB,aAAckM,EACd,oBAAqBA,GAE7B,CAQA,SAASM,kBAEL,IAAIN,EAAIC,OACJ5U,EAAc2U,EAAE3U,YAAa1C,EAAWqX,EAAErX,SAC9C,OAOM,GAPEkS,YAAY,CAChB,WAAYmF,IACV,iBAAkBA,KAClB,eAAgBA,KAChB,gBAAiBA,GACnB3U,KAAiB,mBAAoBA,EAAYnH,WACjDyE,GAAY,uBAAwBA,EAASzE,WAErD,CAOA,SAASqc,iBAKL,IAAIP,EAAIC,OACR,OAzOJ,SAASO,iBAAiBC,GACtB,MAAO,yCAAyCrO,KAAKrH,OAAO0V,GAChE,CAyOID,CAAiBR,EAAEU,QAEO,8BAAtB3V,OAAOiV,EAAEW,QACjB,CAOA,SAASC,UACL,IAAQC,EACJb,EAAIC,OAER,OAOM,GAPEpF,YAAY,CAChB,YAAaqF,UACb,kBAAoB,OAACW,EAAK,OAACjH,EAAKhL,SAASkS,sBAA6C,EAASlH,EAAGmH,OAAmCF,EAAK,CAAA,GAC1I,0BAA2Bb,EAC3B,oBAAqBA,EACrB,uBAAwBA,EACxB,6BAA8BA,GAEtC,CAqDA,SAASgB,qBACL,IAAIhB,EAAIC,OACJnc,EAAIoc,UACJe,EAAMjB,EAAEiB,IAAKC,EAAoBlB,EAAEkB,kBACvC,OAMM,GANErG,YAAY,GACd,sBAAuB/W,GACzBod,GAAqB,YAAaA,EAAkBhd,UACpD,wBAAyB8b,EACzBiB,EAAIE,SAAS,mCACbF,EAAIE,SAAS,+BAErB,CAmCA,SAASC,iBACL,IAAIxc,EAAIgK,SAER,OAAQhK,EAAEwc,gBAAkBxc,EAAEyc,kBAAoBzc,EAAE0c,qBAAuB1c,EAAE2c,sBAAsBnd,KAAKQ,EAC5G,CAOA,SAAS4c,YACL,IAAIC,EAAetB,aACfuB,EAAYd,UACZZ,EAAIC,OACJnc,EAAIoc,UACJrb,EAAI,aAGR,OAAI4c,EAQM,GAPE5G,YAAY,GACd,iBAAkBmF,GAIpBlc,EAAEe,IAAM,iBAAkBf,EAAEe,KAC1B,WAAY,IAAI8c,WAGjBD,GACkG,GAAhG7G,YAAY,CAAC,wBAAyBmF,EAAG,gBAAiBA,EAAG,WAAW5N,KAAKtO,EAAE8d,aAO9F,CAgLA,SAASC,eAAexO,GACpB,IAAIgG,EAAQ,IAAInQ,MAAMmK,GAEtB,OADAgG,EAAMhG,KAAOA,EACNgG,CACX,CAYA,SAASyI,WAAW5I,EAAQ6I,EAAaC,GACrC,IAAYC,EAEZ,YADwB,IAApBD,IAA8BA,EAAkB,IAC7Czc,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAIM,EAAGsd,EACP,OAAOzb,YAAYnC,MAAM,SAAU6d,GAC/B,OAAQA,EAAGrb,OACP,KAAK,EACDlC,EAAIgK,SACJuT,EAAGrb,MAAQ,EACf,KAAK,EACD,OAAMlC,EAAE8B,KAAa,CAAC,EAAa,GAC5B,CAAC,EAAakS,KAAKoJ,IAC9B,KAAK,EAED,OADAG,EAAGpb,OACI,CAAC,EAAa,GACzB,KAAK,EACDmb,EAAStd,EAAEiK,cAAc,UACzBsT,EAAGrb,MAAQ,EACf,KAAK,EAED,OADAqb,EAAGnb,KAAKa,KAAK,CAAC,EAAK,CAAA,GAAI,KAChB,CAAC,EAAa,IAAIjC,SAAQ,SAAUwc,EAAUC,GAC7C,IAAIC,GAAa,EACbzc,EAAU,WACVyc,GAAa,EACbF,GACH,EAOGrB,GAFJmB,EAAOK,OAAS1c,EAChBqc,EAAOM,QALM,SAAUnJ,GACnBiJ,GAAa,EACbD,EAAQhJ,EACX,EAGW6I,EAAOnB,OAgBf0B,GAfJ1B,EAAM2B,YAAY,UAAW,QAAS,aACtC3B,EAAM4B,SAAW,WACjB5B,EAAM6B,IAAM,IACZ7B,EAAM8B,KAAO,IACb9B,EAAM+B,WAAa,SACff,GAAe,WAAYG,EAC3BA,EAAOa,OAAShB,EAGhBG,EAAOc,IAAM,cAEjBpe,EAAE8B,KAAKuI,YAAYiT,GAIG,WAClB,IAAQrB,EAIJyB,IAK6I,cAA5I,OAACzB,EAAK,OAACjH,EAAKsI,EAAOe,oBAA2C,EAASrJ,EAAGhL,eAAsC,EAASiS,EAAGqC,YAC7Hrd,IAGAkT,WAAW0J,EAAiB,IAEnC,GACDA,GAC5B,KACgB,KAAK,EACDN,EAAGpb,OACHob,EAAGrb,MAAQ,EACf,KAAK,EACD,OAAO,OAAC+Z,EAAK,OAACjH,EAAKsI,EAAOe,oBAA2C,EAASrJ,EAAGhL,WAA+CiS,EAAGna,KAAc,CAAC,EAAa,GACxJ,CAAC,EAAakS,KAAKoJ,IAC9B,KAAK,EAED,OADAG,EAAGpb,OACI,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,EAAamS,EAAOgJ,EAAQA,EAAOe,gBACnD,KAAK,EAAG,MAAO,CAAC,EAAcd,EAAGpb,QACjC,KAAK,GAED,OADA,OAACkb,EAAKC,EAAOtN,aAAiDqN,EAAGkB,YAAYjB,GACtE,CAAC,GACZ,KAAK,GAAI,MAAO,CAAC,GAEjC,GACA,GACA,CAKA,SAASkB,kBAAkBC,GACnBzJ,EAn/BR,SAAS0J,uBAAuBD,GAW5B,IAVA,IAAQxC,EACJ0C,EAAe,sBAAsBjb,OAAO+a,EAAU,KACtDG,EAAW,sBAAsBrR,KAAKkR,GAEtCI,GADAC,EAAMF,EAAS,SAAMlJ,EACR,IACbqJ,EAAa,0BACbC,EAAe,SAAUvQ,EAAMrN,GAC/Byd,EAAWpQ,GAAQoQ,EAAWpQ,IAAS,GACvCoQ,EAAWpQ,GAAMxL,KAAK7B,EACzB,IACQ,CACL,IAAI6d,EAAQF,EAAWxR,KAAKqR,EAAS,IACrC,IAAKK,EACD,MAEJ,IAAIC,EAAOD,EAAM,GACjB,OAAQC,EAAK,IACT,IAAK,IACDF,EAAa,QAASE,EAAKzb,MAAM,IACjC,MACJ,IAAK,IACDub,EAAa,KAAME,EAAKzb,MAAM,IAC9B,MACJ,IAAK,IACD,IAAI0b,EAAiB,yDAAyD5R,KAAK2R,GACnF,IAAIC,EAIA,MAAM,IAAI7a,MAAMqa,GAHhBK,EAAaG,EAAe,GAAI,OAAClD,EAAK,OAACjH,EAAKmK,EAAe,IAAgCnK,EAAKmK,EAAe,IAAgClD,EAAK,IAKxJ,MAEJ,QACI,MAAM,IAAI3X,MAAMqa,GAEhC,CACI,MAAO,CAACG,EAAKD,EACjB,CA48BaH,CAAuBD,GAEhC,IAFA,IAA2CK,EAAM9J,EAAG,GAAI6J,EAAa7J,EAAG,GACpExD,EAAUxH,SAASC,cAAc6U,MAAAA,EAAiCA,EAAM,OACnEzE,EAAK,EAAG4B,EAAKpd,OAAOkI,KAAK8X,GAAaxE,EAAK4B,EAAG7c,OAAQib,IAAM,CACjE,IAAI+E,EAASnD,EAAG5B,GACZjZ,EAAQyd,EAAWO,GAAQC,KAAK,KAGrB,UAAXD,EACAE,eAAe9N,EAAQ2K,MAAO/a,GAG9BoQ,EAAQrH,aAAaiV,EAAQhe,EAEzC,CACI,OAAOoQ,CACX,CAIA,SAAS8N,eAAenD,EAAOpC,GAG3B,IAAK,IAAIM,EAAK,EAAGrF,EAAK+E,EAAOxL,MAAM,KAAM8L,EAAKrF,EAAG5V,OAAQib,IAAM,CAC3D,IAGQkF,EAAmBne,EAHvBoe,EAAWxK,EAAGqF,IACd4E,EAAQ,8CAA8C1R,KAAKiS,MAEvDD,EAASN,EAAM,GAAI7d,EAAQ6d,EAAM,GAAIQ,EAAWR,EAAM,GAC1D9C,EAAM2B,YAAYyB,EAAQne,EAAOqe,GAAY,IAEzD,CACA,CA6BA,IAKIC,UAAY,CAAC,YAAa,aAAc,SACxCC,SAAW,CAEX,kBACA,WACA,YACA,qBACA,mBACA,mBACA,mBACA,SACA,2BACA,UACA,UACA,iBACA,YACA,YACA,kBACA,eACA,eACA,SACA,YACA,OACA,mBACA,iBACA,gBACA,aACA,gBACA,aACA,gBACA,cACA,QACA,YACA,aACA,yBACA,eACA,WACA,aACA,UACA,YACA,mBACA,aACA,mBACA,WACA,WACA,YACA,iBACA,SACA,SACA,cACA,iBACA,aACA,uBACA,SACA,YA0NJ,SAASC,eAAeC,GACpB,OAAOA,EAAOC,WAClB,CAyGA,IAGIC,kBACAC,yBA8BJ,SAASC,yBACL,IAAIC,EAAQxgB,KAEZ,OAzBJ,SAASygB,mBACL,IAGIC,OAH6B1K,IAA7BsK,2BAGAI,EAAmB,WACnB,IAAIC,EAAYC,wBAEZN,yBADAO,gBAAgBF,GACWlM,WAAWiM,EAnBnB,WAsBnBL,kBAAoBM,EAG3B,IAEL,CASIF,GACO,WAAc,OAAOxf,UAAUuf,OAAO,OAAQ,GAAQ,WACzD,IAAIG,EACJ,OAAOxe,YAAYnC,MAAM,SAAUsV,GAC/B,OAAQA,EAAG9S,OACP,KAAK,EAED,OAAKqe,gBADLF,EAAYC,yBAERP,kBACO,CAAC,EAAc7c,cAAc,GAAI6c,mBAAmB,IA7yBnF,SAASS,uBACL,IAAIxgB,EAAIgK,SACR,OAAOhK,EAAEygB,mBAAqBzgB,EAAE0gB,qBAAuB1gB,EAAE2gB,sBAAwB3gB,EAAE4gB,yBAA2B,IAClH,CA4yByBJ,GAIE,CAAC,EAAahE,kBAJe,CAAC,EAAa,GAJV,CAAC,EAAa,GAS1D,KAAK,EAIDxH,EAAG7S,OACHke,EAAYC,wBACZtL,EAAG9S,MAAQ,EACf,KAAK,EAID,OAHKqe,gBAAgBF,KACjBN,kBAAoBM,GAEjB,CAAC,EAAcA,GAE1C,GACK,GAAI,CACT,CA8BA,SAASC,wBACL,IAAIthB,EAAI6hB,OAMR,MAAO,CACH/K,WAAWF,QAAQ5W,EAAE8hB,UAAW,MAChChL,WAAWF,QAAQ5W,EAAE+hB,OAASnL,QAAQ5W,EAAEgiB,YAAclL,WAAWF,QAAQ5W,EAAEiiB,WAAY,GAAI,MAC3FnL,WAAWF,QAAQ5W,EAAEkiB,QAAUtL,QAAQ5W,EAAEmiB,aAAerL,WAAWF,QAAQ5W,EAAE8hB,UAAW,GAAI,MAC5FhL,WAAWF,QAAQ5W,EAAEiiB,WAAY,MAEzC,CACA,SAASV,gBAAgBF,GACrB,IAAK,IAAIphB,EAAI,EAAGA,EAAI,IAAKA,EACrB,GAAIohB,EAAUphB,GACV,OAAO,EAGf,OAAO,CACX,CAweA,SAASmiB,oBAAoBC,GACzB,IAAIrM,EACJ,OAAOrU,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAIM,EAAGshB,EAAMC,EAAUC,EAAqBhQ,EAASiQ,EAAQxiB,EAC7D,OAAO4C,YAAYnC,MAAM,SAAUuc,GAC/B,OAAQA,EAAG/Z,OACP,KAAK,EASD,IARAlC,EAAIgK,SACJsX,EAAOthB,EAAEiK,cAAc,OACvBsX,EAAW,IAAI/d,MAAM6d,EAAUjiB,QAC/BoiB,EAAmB,GAEnBE,UAAUJ,GAGLriB,EAAI,EAAGA,EAAIoiB,EAAUjiB,SAAUH,EAER,YADxBuS,EAAUgN,kBAAkB6C,EAAUpiB,KAC1B8P,SACRyC,EAAQmQ,OAIZD,UAFAD,EAASzhB,EAAEiK,cAAc,QAGzBwX,EAAOpX,YAAYmH,GACnB8P,EAAKjX,YAAYoX,GACjBF,EAAStiB,GAAKuS,EAElByK,EAAG/Z,MAAQ,EACf,KAAK,EACD,OAAMlC,EAAE8B,KAAa,CAAC,EAAa,GAC5B,CAAC,EAAakS,KAAK,KAC9B,KAAK,EAED,OADAiI,EAAG9Z,OACI,CAAC,EAAa,GACzB,KAAK,EACDnC,EAAE8B,KAAKuI,YAAYiX,GACnB,IAEI,IAAKriB,EAAI,EAAGA,EAAIoiB,EAAUjiB,SAAUH,EAC3BsiB,EAAStiB,GAAG2iB,eACbJ,EAAiBH,EAAUpiB,KAAM,EAOjE,CAH4B,QAEJ,OAAC+V,EAAKsM,EAAKtR,aAAiDgF,EAAGuJ,YAAY+C,EACnG,CACoB,MAAO,CAAC,EAAcE,GAE1C,GACA,GACA,CACA,SAASE,UAAUlQ,GACfA,EAAQ2K,MAAM2B,YAAY,aAAc,SAAU,aAClDtM,EAAQ2K,MAAM2B,YAAY,UAAW,QAAS,YAClD,CA0CA,SAAS+D,YAAYzgB,GACjB,OAAO0gB,WAAW,qBAAqBpe,OAAOtC,EAAO,MAAM2gB,OAC/D,CAcA,SAASC,YAAY5gB,GACjB,OAAO0gB,WAAW,mBAAmBpe,OAAOtC,EAAO,MAAM2gB,OAC7D,CA8CA,SAASE,YAAY7gB,GACjB,OAAO0gB,WAAW,sBAAsBpe,OAAOtC,EAAO,MAAM2gB,OAChE,CAcA,SAASG,YAAY9gB,GACjB,OAAO0gB,WAAW,4BAA4Bpe,OAAOtC,EAAO,MAAM2gB,OACtE,CAcA,SAASI,YAAY/gB,GACjB,OAAO0gB,WAAW,kCAAkCpe,OAAOtC,EAAO,MAAM2gB,OAC5E,CAcA,SAASK,UAAUhhB,GACf,OAAO0gB,WAAW,mBAAmBpe,OAAOtC,EAAO,MAAM2gB,OAC7D,CAEA,IAAI3Q,EAAIjF,KACJkW,WAAa,WAAc,OAAO,CAAI,EAiE1C,IAIIC,QAAU,CAKVC,QAAS,GAETC,MAAO,CAAC,CAAEC,KAAM,uBAEhBC,MAAO,CAAC,CAAEC,WAAY,UAEtBC,KAAM,CAAC,CAAED,WAAY,eAErBE,KAAM,CAAC,CAAEF,WAAY,cAKrBG,IAAK,CAAC,CAAEC,SAAU,QAElBC,OAAQ,CAAC,CAAEL,WAAY,eAoK3B,IAAIM,sBA38CJ,SAASC,yBAEL,IADA,IAAIC,EAAgB9H,SACX,CACL,IAAI+H,EAAeD,EAAcE,OACjC,IAAKD,GAAgBA,IAAiBD,EAClC,OAAO,EAEX,IACI,GAAIC,EAAaE,SAASC,SAAWJ,EAAcG,SAASC,OACxD,OAAO,CASvB,CANQ,MAAO9O,GAEH,GAAIA,aAAiBnQ,OAAwB,kBAAfmQ,EAAMhG,KAChC,OAAO,EAEX,MAAMgG,CAClB,CACQ0O,EAAgBC,CACxB,CACA,EAm9CA,IAGII,uBAAyB,IAAIvb,IAAI,CACjC,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAC7G,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAChH,MAAO,MAAO,KAAM,MAAO,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAC9G,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAC/G,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,OAItDwb,qBAAuB,IAAIxb,IAAI,CAC/B,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,QAEAyb,YAAc,CAAC,kBAAmB,iBAClCC,eAAiB,CAAC,YAAa,eAAgB,aAAc,UAAW,aAAc,YA2G1F,SAASC,gBAAgBC,GACrB,GAAIA,EAAMC,MACN,OAAOD,EAAMC,MAAMC,QAEvB,IACIA,EADAlE,EAAS7V,SAASC,cAAc,UAEpC4V,EAAO7N,iBAAiB,2BAA2B,WAAc,OAAQ+R,OAAUrO,KACnF,IAAK,IAAI2E,EAAK,EAAGrF,EAAK,CAAC,QAAS,sBAAuBqF,EAAKrF,EAAG5V,OAAQib,IAAM,CACzE,IAAInU,EAAO8O,EAAGqF,GACd,IACI0J,EAAUlE,EAAOmE,WAAW9d,EAIxC,CAFQ,MAAO+V,GAAAA,CAGP,GAAI8H,EACA,KAEZ,CAEI,OADAF,EAAMC,MAAQ,CAAEC,QAASA,GAClBA,CACX,CAMA,SAASE,mBAAmBC,EAAIC,EAAYC,GAExC,OADIC,EAAkBH,EAAGI,yBAAyBJ,EAAGC,GAAaD,EAAGE,KAC5C,CAACC,EAAgBE,SAAUF,EAAgBG,SAAUH,EAAgBI,WAAa,EAC/G,CACA,SAASC,0BAA0BC,GAG/B,OADW9lB,OAAOkI,KAAK4d,EAAIC,WACfrL,OAAOsL,eACvB,CACA,SAASA,eAAe/kB,GACpB,MAAsB,iBAARA,IAAqBA,EAAImf,MAAM,cACjD,CAKA,SAAS6F,+BACL,OAAO9I,SACX,CAWA,SAAS+I,uBAAuBb,GAC5B,MAAkC,mBAApBA,EAAGc,YACrB,CA8CA,IAAI7L,QAAU,CAMV8L,MAnoDJ,SAASC,WACL,IAAIhF,EAAQxgB,KAIZ,OAAOwd,YAAW,SAAUjb,EAAG+S,GAC3B,IAAIhL,EAAWgL,EAAGhL,SAClB,OAAOrJ,UAAUuf,OAAO,OAAQ,GAAQ,WACpC,IAAIuB,EAAQ0D,EAAgBC,EAAcC,EAAeC,EAAYC,EAA+CC,EAAsBC,EAAiBC,EAAgBC,EAAYnX,EACvL,OAAO3M,YAAYnC,MAAM,SAAUuc,GA+C/B,KA9CAwF,EAASzX,EAASlI,MACXqa,MAAM4G,SAvEd,QAwECoC,EAAiBnb,EAASC,cAAc,QACzBkS,MAAM2B,YAAY,aAAc,SAAU,aACzDsH,EAAe,CAAA,EACfC,EAAgB,CAAA,EAChBC,EAAa,SAAU3C,GACnB,IAAIiD,EAAO5b,EAASC,cAAc,QAC9BkS,EAAQyJ,EAAKzJ,MAOjB,OANAA,EAAM4B,SAAW,WACjB5B,EAAM6B,IAAM,IACZ7B,EAAM8B,KAAO,IACb9B,EAAMwG,WAAaA,EACnBiD,EAAKxb,YArFR,gBAsFG+a,EAAe9a,YAAYub,GACpBA,CACV,EACDL,EAAsB,SAAUM,EAAcC,GAC1C,OAAOR,EAAW,IAAI5hB,OAAOmiB,EAAc,MAAMniB,OAAOoiB,GAC3D,EAIDN,EAAuB,WAMnB,IAJA,IAAIO,EAAQ,GAIH1L,EAAK,EAAG2L,EAAarG,SAAUtF,EAAK2L,EAAW5mB,OAAQib,IAAE,CAH1CoI,IACpBsD,EAAMtD,GAAQ/C,UAAU3V,KAAI,SAAU+b,GAAY,OAAOP,EAAoB9C,EAAMqD,EAAU,KAE/B,CACnDE,EAAW3L,IAG1B,OAAO0L,CACV,EACDN,EAAkB,SAAUQ,GACxB,OAAOvG,UAAUwG,MAAK,SAAUJ,EAAUK,GACtC,OAAOF,EAAUE,GAAeC,cAAgBhB,EAAaU,IACzDG,EAAUE,GAAeE,eAAiBhB,EAAcS,EACpF,GACiB,EACDJ,EApBWhG,UAAU3V,IAAIub,GAqBzBK,EAAaH,IAEb/D,EAAOpX,YAAY8a,GAEd3W,EAAQ,EAAGA,EAAQkR,UAAUtgB,OAAQoP,IACtC4W,EAAa1F,UAAUlR,IAAUkX,EAAelX,GAAO4X,YACvDf,EAAc3F,UAAUlR,IAAUkX,EAAelX,GAAO6X,aAG5D,MAAO,CAAC,EAAc1G,SAASpG,QAAO,SAAUkJ,GAAQ,OAAOgD,EAAgBE,EAAWlD,GAAS,IACnH,GACA,GACA,GACA,EAmkDI6D,YAvwBJ,SAASC,eAAevR,GACpB,IAAkCwR,QAAlB,IAAPxR,EAAgB,CAAA,EAAKA,GAAewR,MAC7C,OAAO7lB,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAI+mB,EAASC,EAA2BlF,EACpCnE,EACJ,OAAOxb,YAAYnC,MAAM,SAAU6d,GAC/B,OAAQA,EAAGrb,OACP,KAAK,EACD,OAuBpB,SAASykB,eAEL,OAAOlL,YAAcmB,WACzB,CA1ByB+J,IAGLF,EA7RpB,SAASG,aACL,IAAIC,EAAUC,KACd,MAAO,CACHC,QAAS,CACL,kBACA,mBACA,wBACA,wBACAF,EAAQ,yBAEZG,MAAO,CAAC,YAAa,iBAAkBH,EAAQ,oBAAqB,2BAA4B,UAChGI,eAAgB,CACZ,cACAJ,EAAQ,oBACR,aACAA,EAAQ,wCACRA,EAAQ,yDAEZK,eAAgB,CACZ,oBACA,QACA,uBACA,YACAL,EAAQ,qDAEZM,sBAAuB,CACnB,oBACA,kBACA,qBACAN,EAAQ,4BACRA,EAAQ,qBAEZO,kBAAmB,CACf,aACA,oBACA,yBACA,UACA,iDAEJC,YAAa,CACT,sBACAR,EAAQ,oBACRA,EAAQ,wBACRA,EAAQ,4BACRA,EAAQ,qBAEZS,eAAgB,CACZT,EAAQ,oBACRA,EAAQ,oCACR,eACAA,EAAQ,oCACRA,EAAQ,qCAEZU,cAAe,CACX,WACAV,EAAQ,gCACR,mBACA,aACAA,EAAQ,iBAEZW,cAAe,CAAC,uCAChBC,gBAAiB,CACb,eACAZ,EAAQ,wDACRA,EAAQ,gCACRA,EAAQ,gBACRA,EAAQ,6BAEZa,cAAe,CACXb,EAAQ,oBACRA,EAAQ,gBACR,0BACA,gBACAA,EAAQ,yBAEZc,eAAgB,CACZd,EAAQ,oDACRA,EAAQ,gBACR,yBACAA,EAAQ,oCACR,qBAEJe,cAAe,CACXf,EAAQ,gEACRA,EAAQ,oDACR,cACA,eACA,iBAEJgB,yBAA0B,CAAC,oBAAqB,eAAgB,iBAAkB,cAAe,cACjGC,0BAA2B,CACvB,eACAjB,EAAQ,oDACRA,EAAQ,gEACRA,EAAQ,oDACR,kBAEJkB,eAAgB,CACZ,aACAlB,EAAQ,gBACRA,EAAQ,wDACRA,EAAQ,wDACRA,EAAQ,yDAEZmB,UAAW,CAACnB,EAAQ,gCAAiC,iBAAkB,kBAAmB,sBAC1FoB,SAAU,CACN,cACApB,EAAQ,gCACRA,EAAQ,4BACR,mBACAA,EAAQ,iCAEZqB,cAAe,CACXrB,EAAQ,wDACRA,EAAQ,wBACR,YACA,kBACA,cAEJsB,eAAgB,CACZ,gBACA,oBACA,uBACAtB,EAAQ,4BACR,6BAEJuB,oBAAqB,CACjB,oBACAvB,EAAQ,wBACRA,EAAQ,4BACR,SACAA,EAAQ,iCAEZwB,cAAe,CACXxB,EAAQ,oBACRA,EAAQ,oCACR,YACAA,EAAQ,oDACR,sBAEJyB,gBAAiB,CACb,oBACAzB,EAAQ,4BACRA,EAAQ,oBACRA,EAAQ,gCACRA,EAAQ,6CAEZ0B,cAAe,CACX1B,EAAQ,4BACR,4BACAA,EAAQ,4DACRA,EAAQ,oDACRA,EAAQ,iEAEZ2B,kBAAmB,CACf3B,EAAQ,4BACRA,EAAQ,4BACRA,EAAQ,4CACRA,EAAQ,gDACRA,EAAQ,iDAEZ4B,SAAU,CAAC5B,EAAQ,qDACnB6B,iBAAkB,CAAC,iBAAkB,mBAAoB,mBAAoB,qBAAsB,aACnGC,mBAAoB,CAAC,oCACrBC,uBAAwB,CACpB,kBACA,oDACA,mBACA,kEACA,mBAEJC,aAAc,CAAC,YAAa,kBAAmB,iBAAkB,kBAAmB,2BACpFC,gBAAiB,CACbjC,EAAQ,4DACRA,EAAQ,gDACR,6BACAA,EAAQ,oBACR,gBAEJkC,aAAc,CACVlC,EAAQ,gDACRA,EAAQ,4DACRA,EAAQ,oEACR,gBACA,kBAEJmC,UAAW,CACP,cACA,6BACAnC,EAAQ,gBACRA,EAAQ,gCACR,mBAEJoC,sBAAuB,CACnB,gDACA,iCACA,yBACA,yBACA,oBAEJC,aAAc,CAACrC,EAAQ,qEACvBsC,QAAS,CACLtC,EAAQ,4KAERA,EAAQ,6KAGZuC,OAAQ,CACJvC,EAAQ,gDACRA,EAAQ,4BACRA,EAAQ,gDACRA,EAAQ,wBACR,4BAEJwC,QAAS,CACLxC,EAAQ,oBACR,2BACAA,EAAQ,oCACRA,EAAQ,gCACRA,EAAQ,yDAEZyC,QAAS,CACLzC,EAAQ,oDACRA,EAAQ,oCACRA,EAAQ,gDACR,yBACA,mCAEJ0C,eAAgB,CACZ,8BACA1C,EAAQ,gDACRA,EAAQ,wEACRA,EAAQ,gEACRA,EAAQ,yBAEZ2C,GAAI,CACA3C,EAAQ,4DACRA,EAAQ,oEACRA,EAAQ,4EACRA,EAAQ,oDACR,oBAEJ4C,KAAM,CACF5C,EAAQ,oCACRA,EAAQ,oCACRA,EAAQ,wCACR,WACA,qBAEJ6C,QAAS,CACL,2BACA7C,EAAQ,oDACRA,EAAQ,gBACR,QACA,eAEJ8C,uBAAwB,CACpB,sBACA,gBACA9C,EAAQ,4BACR,qBACA,2BAGZ,CAqB8BD,GACVF,EAAc7nB,OAAOkI,KAAK0f,GAEnB,CAAC,EAAarF,qBADL/D,EAAK,IAAI3Z,OAAOjE,MAAM4d,EAAIqJ,EAAY3c,KAAI,SAAU6f,GAAc,OAAOnD,EAAQmD,EAAY,QAJlG,CAAC,OAAclU,GAM9B,KAAK,EAWD,OAVA8L,EAAmBjE,EAAGpb,OAClBqkB,GA2ExB,SAASqD,WAAWpD,EAASjF,GAEzB,IADA,IACSnH,EAAK,EAAGrF,EAAKnW,OAAOkI,KAAK0f,GAAUpM,EAAKrF,EAAG5V,OAAQib,IAAM,CAC9D,IAAIuP,EAAa5U,EAAGqF,GACT,KAAK3W,OAAOkmB,EAAY,KACnC,IAAK,IAAI3N,EAAK,EAAGoB,EAAKoJ,EAAQmD,GAAa3N,EAAKoB,EAAGje,OAAQ6c,IAAM,CAC7D,IAAIwC,EAAWpB,EAAGpB,GACP,OAAOvY,OAAO8d,EAAiB/C,GAAY,KAAO,KAAM,KAAK/a,OAAO+a,EAC3F,CACA,CAIA,CAvFwBoL,CAAWpD,EAASjF,IAExBsI,EAAiBpD,EAAYnN,QAAO,SAAUqQ,GAG1C,OADmB3T,aADfoL,EAAYoF,EAAQmD,IACiB7f,KAAI,SAAU0U,GAAY,OAAO+C,EAAiB/C,EAAY,KAC9D,GAAnB4C,EAAUjiB,MACxD,KACmC2qB,OACR,CAAC,EAAcD,GAE1C,GACA,GACA,EA0uBIE,gBA1aJ,SAASC,qBACL,OAkCJ,SAASC,iBAAiB5V,EAAQ6V,GA8C9B,YA7CyB,IAArBA,IAA+BA,EAAmB,KA6C/CjN,YAAW,SAAUjb,EAAGmoB,GAC3B,IAaIC,EAbAC,EAAiBF,EAAapgB,SAC9BugB,EAAaD,EAAexoB,KAehC,OAHIuoB,IAXAG,EAAYD,EAAWpO,OACjB4E,MAAQ,GAAGrd,OAAOymB,EAAkB,MAC9CK,EAAUC,qBAAuBD,EAAUE,eAAiB,OAExDnP,aACAgP,EAAWpO,MAAMwO,KAAO,GAAGjnB,OAAO,EAAI0mB,EAAaQ,kBAE9CnP,aACL8O,EAAWpO,MAAMwO,KAAO,SAGVL,EAAergB,cAAc,SACnCG,YAAclH,cAAc,GAAIM,MAAO2mB,EAAmB,GAAO,IAAI,GAAMpgB,KAAI,WAAc,MAAO,UAAWsV,KAAK,KAChIkL,EAAWlgB,YAAYggB,GAChB/V,EAAOgW,EAAgBC,EACjC,GAAE,kGACP,CAnGWL,EAAiB,SAAUlgB,EAAU6gB,GAKxC,IAJA,IAAItJ,EAAW,GACXuJ,EAAQ,CAAA,EAGHzQ,EAAK,EAAGrF,EAAKnW,OAAOkI,KAAKub,SAAUjI,EAAKrF,EAAG5V,OAAQib,IAAM,CAC9D,IACoEkD,EADhEzd,EAAMkV,EAAGqF,GACsB8B,OAAe,KAA3BkB,GAAnBpB,EAAKqG,QAAQxiB,IAAc,IAA4B,CAAA,EAAKud,EAAgB0N,OAAc,KAA1BxN,EAAKtB,EAAG,IA3CtE,oBA2C+GsB,EACjH/L,EAAUxH,EAASC,cAAc,QACrCuH,EAAQpH,YAAc2gB,EACtBvZ,EAAQ2K,MAAM6O,WAAa,SAC3B,IAAK,IAAIC,EAAK,EAAGC,EAAKrsB,OAAOkI,KAAKoV,GAAQ8O,EAAKC,EAAG9rB,OAAQ6rB,IAAM,CAC5D,IAAI7L,EAAS8L,EAAGD,GACZ7pB,EAAQ+a,EAAMiD,QACJ1J,IAAVtU,IACAoQ,EAAQ2K,MAAMiD,GAAUhe,EAE5C,CACYmgB,EAASzhB,GAAO0R,EAChBqZ,EAAU7b,OAAOhF,EAASC,cAAc,MAAOuH,EAC3D,CAEQ,IAAK,IAAI2Z,EAAK,EAAGC,EAAKvsB,OAAOkI,KAAKub,SAAU6I,EAAKC,EAAGhsB,OAAQ+rB,IAExDL,EAAMhrB,EADIsrB,EAAGD,IACA5J,EAASzhB,GAAKurB,wBAAwBtK,MAEvD,OAAO+J,CACf,GACA,EA8YIQ,MA7/DJ,SAASC,sBACL,OA8DJ,SAASC,yCACL,OAEC/P,YAAcW,sBAAwBT,kBAElCJ,cA1FT,SAASkQ,oBAEL,IAAIvsB,EAAIoc,UACJF,EAAIC,OACJqQ,EAAiB3O,MAAMzd,UAE3B,OAMM,GANE2W,YAAY,CAChB,cAAeyV,EACf,mBAAoBA,EACpB,kBAAmBxsB,GAJnBysB,EAAiBvQ,EAAEuQ,iBAKD,aAAcA,EAChC,uBAAwBC,MAAMtsB,WAEtC,CA6EyBmsB,IAvNzB,SAASI,uBAEL,IAAIzQ,EAAIC,OACJyQ,EAAa1Q,EAAE0Q,WACnB,OAKM,GALE7V,YAAY,CAChB,UAAWhO,IAAI3I,UACf,aAAc8b,EACd0Q,GAAc,oBAAqBA,EAAWxsB,UAC9C,SAAUysB,sBAAsBzsB,WAExC,CA6MgDusB,EAChD,CApEQL,IACS,EAUjB,SAASQ,8BACL,IA2BIhX,EAAmCiX,EAAuBC,EAE1DC,EA7BA/Q,EAAIC,OACJ+Q,EAAehR,EAAEiR,qBAAuBjR,EAAEkR,0BAC9C,OAAKF,EAyCT,SAASG,iCAEL,OAAO9Q,aAAeC,oBA5L1B,SAAS8Q,qBAEL,IAAIpR,EAAIC,OACR,OAKM,GALEpF,YAAY,CAChB,gBAAiBmF,EACjB,8BAA+BA,EAC/B,uBAAwBA,EACxB,uBAAwBA,GAEhC,CAmLgDoR,EAChD,CArCQD,IACS,IAKTE,GADA1I,EAAU,IAAIqI,EAAa,EADb,IAC6B,QACtBM,oBACdxmB,KAAO,WAClBumB,EAAWE,UAAUvrB,MAAQ,KACzBwrB,EAAa7I,EAAQ8I,4BACdC,UAAU1rB,OAAW,GAChCwrB,EAAWG,KAAK3rB,MAAQ,GACxBwrB,EAAWI,MAAM5rB,MAAQ,GACzBwrB,EAAWK,OAAO7rB,MAAQ,EAC1BwrB,EAAWM,QAAQ9rB,MAAQ,IAC3BqrB,EAAWU,QAAQP,GACnBA,EAAWO,QAAQpJ,EAAQqJ,aAC3BX,EAAWY,MAAM,GACsBpB,GAAnCjX,EAkCR,SAASsY,oBAAoBvJ,GACzB,IAIIld,EAAW,WA0Df,EAAA,MAAO,CAzDa,IAAI7F,SAAQ,SAAUC,EAASC,GAC/C,IAAIqsB,GAAc,EACdC,EAAiB,EACjBC,EAAmB,EAEnBC,GADJ3J,EAAQ4J,WAAa,SAAUC,GAAS,OAAO3sB,EAAQ2sB,EAAMC,eAAkB,EACrD,WACtB1Z,YAAW,WAAc,OAAOjT,EAAO+b,eAAe,WAA2C,GAAE9Q,KAAK2W,IATtF,IAS+G2K,EAR7G,IAQwJxY,KAAKF,OACpL,GACG+Y,EAAY,WACZ,IACI,IAAIC,EAAmBhK,EAAQiK,iBAM/B,OAJI5Z,UAAU2Z,IAEVvY,kCAAkCuY,GAE9BhK,EAAQ9c,OACZ,IAAK,UACDwmB,EAAmBxY,KAAKF,MACpBwY,GACAG,IAEJ,MAIJ,IAAK,YAKI1jB,SAASikB,QACVT,IAEAD,GAvCA,GAuCeC,EACftsB,EAAO+b,eAAe,cAGtB9I,WAAW2Z,EA1CZ,KAiD3B,CAFY,MAAOrZ,GACHvT,EAAOuT,EACvB,CACS,EACDqZ,IACAjnB,EAAW,WACF0mB,IACDA,GAAc,EACS,EAAnBE,GACAC,IAGX,CACT,IAC2B7mB,EAC3B,CAlGaymB,CAAoBvJ,IAA6B,GAAImI,EAAkBlX,EAAG,GAE/EmX,EAAqB3W,kCAAkCyW,EAAcrqB,MAAK,SAAUssB,GAAU,OAiGtG,SAASC,QAAQC,GAEb,IADA,IAAIC,EAAO,EACFpvB,EAAI,EAAGA,EAAImvB,EAAOhvB,SAAUH,EACjCovB,GAAQliB,KAAKkK,IAAI+X,EAAOnvB,IAE5B,OAAOovB,CACX,CAvG6GF,CAAQD,EAAOI,eAAe,GAAGC,SAjBtH,MAiB+I,IAAI,SAAU9Z,GAC7K,GAAmB,YAAfA,EAAMhG,MAAkE,cAAfgG,EAAMhG,KAC/D,OAAS,EAEb,MAAMgG,CACd,KACW,WAEH,OADAyX,IACOC,CACV,IAnCY,CAoCjB,CAhDWH,EACX,EAy/DIwC,YAzvCJ,SAASC,iBACL,IAIIC,EAJAxO,EAAQxgB,KACZ,OAAI+b,YAAcW,sBAAwBT,iBAC/B,WAAc,OAAO3a,QAAQC,aAAQyU,EAAa,GAEzDgZ,EAAoBzO,yBACjB,WAAc,OAAOtf,UAAUuf,OAAO,OAAQ,GAAQ,WACzD,IAAIG,EAAWsO,EACf,OAAO9sB,YAAYnC,MAAM,SAAUsV,GAC/B,OAAQA,EAAG9S,OACP,KAAK,EAAG,MAAO,CAAC,EAAawsB,KAC7B,KAAK,EAKD,OAJArO,EAAYrL,EAAG7S,OAIR,CAAC,EAAc,EAHtBwsB,EAAc,SAAUC,GAAY,OAAqB,OAAbA,EAAoB,KAAOzY,MAAMyY,EAxFzE,GAwFyG,GAG1EvO,EAAU,IAAKsO,EAAYtO,EAAU,IAAKsO,EAAYtO,EAAU,IAAKsO,EAAYtO,EAAU,MAElJ,GACK,GAAI,EACT,EAsuCIR,OAliDJ,SAASgP,uBACL,OAQJ,SAASC,6BAA6BC,GAClC,IAEIC,EACAjE,EAFAkE,GAAU,EAGVhT,EAeR,SAASiT,oBACL,IAAIrP,EAAS7V,SAASC,cAAc,UAGpC,OAFA4V,EAAOkB,MAAQ,EACflB,EAAOqB,OAAS,EACT,CAACrB,EAAQA,EAAOmE,WAAW,MACtC,CApBakL,GAAqBrP,EAAS5D,EAAG,GAAI8H,EAAU9H,EAAG,GAa3D,OAQJ,SAASkT,YAAYtP,EAAQkE,GACzB,SAAUA,IAAWlE,EAAOC,UAChC,CAtBSqP,CAAYtP,EAAQkE,IAIrBkL,EAmBR,SAASG,mBAAmBrL,GAKxB,OAFAA,EAAQsL,KAAK,EAAG,EAAG,GAAI,IACvBtL,EAAQsL,KAAK,EAAG,EAAG,EAAG,IACdtL,EAAQuL,cAAc,EAAG,EAAG,UACxC,CAzBkBF,CAAmBrL,GACzBgL,EACAC,EAAWjE,EAAO,WAGkBiE,GAApCha,EAqBZ,SAASua,aAAa1P,EAAQkE,IAiB9B,SAASyL,gBAAgB3P,EAAQkE,GAE7BlE,EAAOkB,MAAQ,IACflB,EAAOqB,OAAS,GAChB6C,EAAQ0L,aAAe,aACvB1L,EAAQ2L,UAAY,OACpB3L,EAAQ4L,SAAS,IAAK,EAAG,GAAI,IAC7B5L,EAAQ2L,UAAY,OAGpB3L,EAAQtB,KAAO,yBAOXmN,EAAc,qBAAqBlsB,OAAOyC,OAAO0pB,aAAa,MAAO,QACzE9L,EAAQ+L,SAASF,EAAa,EAAG,IACjC7L,EAAQ2L,UAAY,yBACpB3L,EAAQtB,KAAO,aACfsB,EAAQ+L,SAASF,EAAa,EAAG,GACrC,CAtCIJ,CAAgB3P,EAAQkE,GACxB,IAAIgM,EAAanQ,eAAeC,GAIhC,OAAIkQ,IAHanQ,eAAeC,GAIrB,CAAC,WAAuC,aAiCvD,SAASmQ,oBAAoBnQ,EAAQkE,GAEjClE,EAAOkB,MAAQ,IACflB,EAAOqB,OAAS,IAIhB6C,EAAQkM,yBAA2B,WACnC,IAAK,IAAI5V,EAAK,EAAGrF,EAAK,CAClB,CAAC,OAAQ,GAAI,IACb,CAAC,OAAQ,GAAI,IACb,CAAC,OAAQ,GAAI,KACdqF,EAAKrF,EAAG5V,OAAQib,IAAM,CACrB,IAAiB6V,GAAbjU,EAAKjH,EAAGqF,IAAgB,GAAI3N,EAAIuP,EAAG,GAAIja,EAAIia,EAAG,GAClD8H,EAAQ2L,UAAYQ,EACpBnM,EAAQoM,YACRpM,EAAQqM,IAAI1jB,EAAG1K,EAAG,GAAI,EAAa,EAAVmK,KAAKkkB,IAAQ,GACtCtM,EAAQuM,YACRvM,EAAQtS,MAChB,CAIIsS,EAAQ2L,UAAY,OACpB3L,EAAQqM,IAAI,GAAI,GAAI,GAAI,EAAa,EAAVjkB,KAAKkkB,IAAQ,GACxCtM,EAAQqM,IAAI,GAAI,GAAI,GAAI,EAAa,EAAVjkB,KAAKkkB,IAAQ,GACxCtM,EAAQtS,KAAK,UACjB,CAtDIue,CAAoBnQ,EAAQkE,GAErB,CADanE,eAAeC,GACZkQ,GAC3B,CArCiBR,CAAa1P,EAAQkE,IAAwB,GAAIgH,EAAO/V,EAAG,KARpEga,EAAWjE,EAAO,cAWf,CAAEkE,QAASA,EAASD,SAAUA,EAAUjE,KAAMA,EACzD,CA3BW+D,CAsHX,SAASyB,uCAEL,OAAO9U,YAAcW,sBAAwBT,gBACjD,CAzHwC4U,GACxC,EAiiDIC,MAv4CJ,SAASC,WACL,OAAOnV,UAAUoV,KACrB,EAs4CIC,UAp4CJ,SAASC,eACL,IAAI1xB,EAAIoc,UACJ5Z,EAAS,GACTmvB,EAAW3xB,EAAE2xB,UAAY3xB,EAAE4xB,cAAgB5xB,EAAE6xB,iBAAmB7xB,EAAE8xB,eAiBtE,YAhBiBtb,IAAbmb,GACAnvB,EAAOuB,KAAK,CAAC4tB,IAEbrtB,MAAMwE,QAAQ9I,EAAEyxB,WAGVpV,cAxyBd,SAAS0V,sBAEL,IAAI7V,EAAIC,OACR,OAKM,GALEpF,YAAY,GACd,uBAAwBmF,GAC1B,yBAA0BA,EAC1B,GAAKA,EAAE8V,MAAS,gBAChB,GAAK9V,EAAEhb,SAAY,oBAE3B,CA+xB8B6wB,IAClBvvB,EAAOuB,KAAK/D,EAAEyxB,WAGU,iBAAhBzxB,EAAEyxB,YACVA,EAAYzxB,EAAEyxB,YAEdjvB,EAAOuB,KAAK0tB,EAAUpiB,MAAM,MAG7B7M,CACX,EAg3CIyvB,WA92CJ,SAASC,gBACL,OAAO/V,OAAOwF,OAAOsQ,UACzB,EA62CIE,aA32CJ,SAASC,kBAEL,OAAOxb,WAAWF,QAAQ0F,UAAU+V,mBAAe3b,EACvD,EAy2CI6b,iBAl2CJ,SAASC,sBACL,KAAI/V,YAAcW,sBAAwBT,kBAG1C,OAQJ,SAAS8V,8BAKgB,SAAjBC,EAA2BtwB,GAAS,OAAO0U,WAAWH,MAAMvU,GAAQ,KAAQ,CAJhF,IAAIpC,EAAI6hB,OAKJ8Q,EAAa,CAACD,EAAe1yB,EAAE+hB,OAAQ2Q,EAAe1yB,EAAEkiB,SAE5D,OADAyQ,EAAW5H,OAAO5hB,UACXwpB,CACX,CAjBWF,EACX,EA81CIG,oBAptCJ,SAASC,yBAEL,OAAO/b,WAAWH,MAAM2F,UAAUsW,0BAAsBlc,EAC5D,EAktCIoc,SAhtCJ,SAASC,cACL,IACIC,EAAiB,OAAChd,EAAKqG,OAAO6V,WAAkC,EAASlc,EAAGgd,eAChF,OAAIA,IACIF,GAAAA,IAAeE,GAAiBC,kBAAkBC,UAE3CJ,GAKXK,GAGR,SAASC,oBACL,IAAIC,GAAAA,IAAkBpd,MAAOqd,cAK7B,OAAOnmB,KAAKomB,IAEZ3c,QAAQ,IAAIX,KAAKod,EAAa,EAAG,GAAGD,qBAAsBxc,QAAQ,IAAIX,KAAKod,EAAa,EAAG,GAAGD,qBAClG,CAZkBA,GACP,MAAM1uB,OAAiB,GAAVyuB,EAAc,IAAM,IAAIzuB,OAAOyuB,GACvD,EAosCIK,eAxrCJ,SAASC,oBACL,IACI,QAASpX,OAAOmX,cAKxB,CAHI,MAAO/d,GAEH,OAAO,CACf,CACA,EAirCIie,aA9qCJ,SAASC,kBACL,IACI,QAAStX,OAAOqX,YAKxB,CAHI,MAAOnxB,GAEH,OAAO,CACf,CACA,EAuqCIqxB,UArqCJ,SAASC,eAGL,IAAI1X,cApnCR,SAAS2X,aAEL,IAAI1X,EAAIC,OACJnc,EAAIoc,UACR,OAA6G,GAArGrF,YAAY,CAAC,wBAAyBmF,EAAG,aAAcA,EAAG,gBAAiBlc,EAAG,eAAgBA,MACjGic,WACT,CA8mCuB2X,GAGnB,IACI,QAASzX,OAAOuX,SAKxB,CAHI,MAAOrxB,GAEH,OAAO,CACf,CACA,EAypCIwxB,aAvpCJ,SAASC,kBACL,QAAS3X,OAAO0X,YACpB,EAspCIE,SAppCJ,SAASC,cACL,OAAO5X,UAAU2X,QACrB,EAmpCIE,SAjpCJ,SAASC,cAEL,IAAID,EAAW7X,UAAU6X,SAKzB,MAAiB,aAAbA,GACI1X,aAAeC,kBAp+B3B,SAAS2X,SAOL,IAIIC,EAJJ,MAA2B,SAAvBhY,UAAU6X,WAIVG,GADAt0B,EAAI6hB,QACYE,MAAQ/hB,EAAEkiB,OASxB,GAREjL,YAAY,CAEhB,gBAAiBoF,SAEfkY,QAAQj0B,UAAUk0B,wBAGN,IAAdF,GAAsBA,EAAc,OAE5C,CAg9BmBD,GAAW,OAAS,SAG5BF,CACX,EAqoCIM,QAnlDJ,SAASC,aACL,IAAIC,EAAarY,UAAUmY,QAC3B,GAAKE,EAAL,CAKA,IAFA,IAAIF,EAAU,GAELx0B,EAAI,EAAGA,EAAI00B,EAAWv0B,SAAUH,EAAG,CACxC,IAAI20B,EAASD,EAAW10B,GACxB,GAAK20B,EAAL,CAIA,IADA,IAAIC,EAAY,GACPniB,EAAI,EAAGA,EAAIkiB,EAAOx0B,SAAUsS,EAAG,CACpC,IAAIoiB,EAAWF,EAAOliB,GACtBmiB,EAAU5wB,KAAK,CACXiD,KAAM4tB,EAAS5tB,KACf6tB,SAAUD,EAASC,UAEnC,CACQN,EAAQxwB,KAAK,CACTwL,KAAMmlB,EAAOnlB,KACbulB,YAAaJ,EAAOI,YACpBH,UAAWA,GAZvB,CAcA,CACI,OAAOJ,CAtBX,CAuBA,EAyjDIQ,aA96CJ,SAASC,kBACL,IAEIC,EAFAj1B,EAAIoc,UACJ8Y,EAAiB,OAEI1e,IAArBxW,EAAEk1B,eACFA,EAAiBze,MAAMzW,EAAEk1B,qBAEG1e,IAAvBxW,EAAEm1B,mBACPD,EAAiBl1B,EAAEm1B,kBAEvB,IACIrqB,SAASsqB,YAAY,cACrBH,GAAa,CAIrB,CAFI,MAAOnf,GACHmf,GAAa,CACrB,CAEI,MAAO,CACHC,eAAgBA,EAChBD,WAAYA,EACZI,WAJa,iBAAkBlZ,OAMvC,EAw5CIG,OAroCJ,SAASgZ,YACL,OAAOlZ,UAAUE,QAAU,EAC/B,EAooCIiZ,cA9nCJ,SAASC,mBAEL,IADA,IAAIC,EAAU,GACLta,EAAK,EAAGrF,EAAK,CAElB,SAEA,SAEA,UACA,WAEA,SAEA,OACA,SAEA,cAEA,qCACA,SAEA,OAEA,YAEA,QACA,cAEA,gBAGDqF,EAAKrF,EAAG5V,OAAQib,IAAM,CACrB,IAAIva,EAAMkV,EAAGqF,GACTjZ,EAAQia,OAAOvb,GACfsB,GAA0B,iBAAVA,GAChBuzB,EAAQ1xB,KAAKnD,EAEzB,CACI,OAAO60B,EAAQ5K,MACnB,EAwlCI6K,eA/kCJ,SAASC,oBACL,IAAI70B,EAAIgK,SAQR,IAEIhK,EAAE80B,OAAS,iCACX,IAAIpzB,GAA8C,IAArC1B,EAAE80B,OAAO5lB,QAAQ,eAG9B,OADAlP,EAAE80B,OAAS,uEACJpzB,CAIf,CAFI,MAAOH,GACH,OAAO,CACf,CACA,EA4jCIwzB,WAhrBJ,SAASC,gBAEL,IAAK,IAAI3a,EAAK,EAAGrF,EAAK,CAAC,UAAW,KAAM,QAASqF,EAAKrF,EAAG5V,OAAQib,IAAM,CACnE,IAAI4a,EAAQjgB,EAAGqF,GACf,GAAIyH,WAAW,iBAAiBpe,OAAOuxB,EAAO,MAAMlT,QAChD,OAAOkT,CAEnB,CAEA,EAwqBIC,eAnqBJ,SAASC,oBACL,QAAItT,YAAY,cAGZA,YAAY,cAAhB,CAIJ,EA4pBIuT,aAppBJ,SAASC,kBACL,QAAIrT,YAAY,YAGZA,YAAY,cAAhB,CAIJ,EA6oBIsT,WAhoBJ,SAASC,qBACL,GAAKzT,WAAW,uBAAuBC,QAAvC,CAMA,IAAK,IAAI9iB,EAAI,EAAGA,GAfE,MAesBA,EACpC,GAAI6iB,WAAW,oBAAoBpe,OAAOzE,EAAG,MAAM8iB,QAC/C,OAAO9iB,EAGf,MAAM,IAAIqF,MAAM,iBARpB,CASA,EAonBIkxB,SA9mBJ,SAASC,wBACL,OAAIxT,YAAY,iBACL,EAIPA,YAAY,SAAWA,YAAY,QAC5B,EAEPA,YAAY,QAAUA,YAAY,SACzB,EAETA,YAAY,UACL,QADX,CAIJ,EA+lBIyT,cAvlBJ,SAASC,kBACL,QAAIzT,YAAY,YAGZA,YAAY,uBAAhB,CAIJ,EAglBI0T,oBAxkBJ,SAASC,wBACL,QAAI1T,YAAY,YAGZA,YAAY,uBAAhB,CAIJ,EAikBI2T,IAzjBJ,SAASC,QACL,QAAI3T,UAAU,UAGVA,UAAU,kBAAd,CAIJ,EAkjBI4T,KAviBJ,SAASC,qBAEL,IAAIC,EAAO9kB,EAAE8kB,MAAQ7T,WACjB8T,EAAQ/kB,EAAE+kB,OAAS9T,WACnB+T,EAAOhlB,EAAEglB,MAAQ/T,WACjBgU,EAAQjlB,EAAEilB,OAAShU,WACnBiU,EAAQllB,EAAEklB,OAASjU,WACnBkU,EAAOnlB,EAAEmlB,MAAQlU,WACjBmU,EAAMplB,EAAEolB,KAAOnU,WACfoU,EAAOrlB,EAAEqlB,MAAQpU,WACjBqU,EAAMtlB,EAAEslB,KAAOrU,WACfsU,EAAOvlB,EAAEulB,MAAQtU,WACjBuU,EAAMxlB,EAAEwlB,KAAOvU,WACfwU,EAAOzlB,EAAEylB,MAAQxU,WACjByU,EAAM1lB,EAAE0lB,KAAOzU,WACf0U,EAAQ3lB,EAAE2lB,OAAS1U,WACnB2U,EAAQ5lB,EAAE4lB,OAAS3U,WAYvB,MAAO,CACH6T,KAAMA,EAAK,oBACXC,MAAOA,EAAM,OACbc,SAZoB71B,EAYH,MAZmBgQ,EAAE8lB,IAAI91B,EAAQgQ,EAAE+lB,KAAK/1B,EAAQA,EAAQ,KAazEg1B,KAAMA,EAAK,oBACXC,MAAOA,EAAM,GACbe,SAdoBh2B,EAcH,EAdmBgQ,EAAE8lB,IAAI91B,EAAQgQ,EAAE+lB,KAAK/1B,EAAQA,EAAQ,KAezEk1B,MAAOA,EAAM,IACbe,SAfoBj2B,EAeH,GAfmBgQ,EAAE8lB,KAAK,EAAI91B,IAAU,EAAIA,IAAU,GAgBvEm1B,KAAMA,EAAK,IACXC,IAAKA,UACLC,KAAMA,EAAK,GACXa,QAlBmBl2B,EAkBJ,EAlBoBgQ,EAAE0lB,IAAI11B,GAAS,EAAIgQ,EAAE0lB,IAAI11B,GAAS,GAmBrEs1B,IAAKA,EAAI,iBACTC,KAAMA,EAAK,GACXY,QApBmBn2B,EAoBJ,GApBqBgQ,EAAE0lB,IAAI11B,GAAS,EAAIgQ,EAAE0lB,IAAI11B,IAAU,GAqBvEw1B,IAAKA,UACLC,KAAMA,EAAK,GACXW,QArBmBp2B,EAqBJ,GArBqBgQ,EAAE0lB,IAAI,EAAI11B,GAAS,IAAMgQ,EAAE0lB,IAAI,EAAI11B,GAAS,IAsBhF01B,IAAKA,EAAI,GACTC,MAAOA,EAAM,GACbU,QAzBoCrmB,EAAE0lB,IAyBrB,GAzBkC,EA0BnDE,MAAOA,EAAM,IACbU,QAzBoCtmB,EAAE8lB,IAAI,IA0B1CS,MAlCkCvmB,EAAEwmB,IAAIxmB,EAAEif,SAoClD,EAkfIwH,iBAnWJ,SAASC,qBACL,OAAOxc,UAAUuc,gBACrB,EAkWIE,aAxVJ,SAASC,kBACL,IAAIj2B,EAAI,IAAIk2B,aAAa,GACrBC,EAAK,IAAI5f,WAAWvW,EAAEmsB,QAG1B,OAFAnsB,EAAE,GAAKo2B,IACPp2B,EAAE,GAAKA,EAAE,GAAKA,EAAE,GACTm2B,EAAG,EACd,EAmVIE,SA7UJ,SAASC,mBACL,IAAIC,EAAkBjd,OAAOid,gBAC7B,GAAmH,mBAAvGA,MAAAA,OAAyD,EAASA,EAAgBC,iBAC1F,OAAS,EAEb,GAAItV,wBACA,OAAS,EAEb,IACI,OAAOqV,EAAgBC,kBAAoB,EAAgC,CAInF,CAFI,MAAO9jB,GACH,OAgBR,SAAS+jB,kBAAkB/jB,GAEvB,GAAIA,aAAiBnQ,OAAwB,uBAAfmQ,EAAMhG,MAAiC,0BAA0BjB,KAAKiH,EAAMgkB,SACtG,OAAS,EAEb,MAAMhkB,CACV,CAtBe+jB,CAAkB/jB,EACjC,CACA,EAgUIikB,wBA9RJ,SAASC,6BACL,IACIC,EAAO5uB,SAASC,cAAc,KAC9B4uB,EAAW,OAAC7jB,EAAK4jB,EAAKE,qBAAiD9jB,EAAK4jB,EAAKG,oBACrF,YAAoBrjB,IAAbmjB,OAAyBnjB,EAAYvP,OAAO0yB,EACvD,EA0RIG,iBAvFJ,SAASC,6BACL,IAAIjkB,EAIJ,OADwB4H,aAAenB,WAIlCJ,OAAO+Q,cAGL,OAACpX,GAAAA,IAASoX,cAAe8M,aAAyClkB,GAF5D,GAHA,CAMjB,EA4EImkB,eApEJ,SAASC,oBACL,IAOIC,EAPJ,OAAKhe,OAAO6V,MAGRc,EAAiB3W,OAAO6V,KAAKc,iBAI7BqH,EAASrH,IAAiBC,kBAAkBoH,SACtB,KAAXA,EAGRA,GAFM,GAJA,GAJA,CAWjB,EA0DIC,YA5PJ,SAASC,eAAevkB,GACpB,IAAoBkW,EAShBsO,EANJ,OADItV,EAAKN,gBADG5O,EAAG6O,QAKVkB,uBAAuBb,IAGxBsV,EAAiB1U,+BAAiC,KAAOZ,EAAGuV,aAfpC,6BAgBrB,CACHC,SAAU,OAACzd,EAAKiI,EAAGc,aAAad,EAAGyV,eAAsC,EAAS1d,EAAGpX,aAAe,GACpG2W,QAAS,OAAC6B,EAAK6G,EAAGc,aAAad,EAAG0V,cAAqC,EAASvc,EAAGxY,aAAe,GAClGg1B,eAAgBL,EAAiB,OAACjc,EAAK2G,EAAGc,aAAawU,EAAeM,6BAAoD,EAASvc,EAAG1Y,WAAa,GACnJk1B,UAAW,OAAC9O,EAAK/G,EAAGc,aAAad,EAAG8V,gBAAuC,EAAS/O,EAAGpmB,aAAe,GACtGo1B,iBAAkBT,EAAiB,OAACtO,EAAKhH,EAAGc,aAAawU,EAAeU,+BAAsD,EAAShP,EAAGrmB,WAAa,GACvJs1B,wBAAyB,OAAChP,EAAKjH,EAAGc,aAAad,EAAGkW,gCAAuD,EAASjP,EAAGtmB,aAAe,MA9ClG,GAFf,CAkD3B,EA0OIw1B,gBAtOJ,SAASC,mBAAmBtlB,GACxB,IACIkP,EAAKN,gBADG5O,EAAG6O,OAEf,IAAKK,EACD,OA1DmB,EA4DvB,IAAKa,uBAAuBb,GACxB,OA3DkC,EA6DlCqW,EAAarW,EAAGsW,yBAApB,IACIC,EAAoBvW,EAAGwW,uBACvBC,EAAwB,GAExB9b,EAAa,GACb+b,EAAa,GACbC,EAAsB,GACtBC,EAAmB,GAEvB,GAAIL,EACA,IAAK,IAAIpgB,EAAK,EAAG4B,EAAKpd,OAAOkI,KAAK0zB,GAAoBpgB,EAAK4B,EAAG7c,OAAQib,IAAM,CACxE,IAAI0gB,EAAgB9e,EAAG5B,GACvBwE,EAAW5b,KAAK,GAAGS,OAAOq3B,EAAe,KAAKr3B,OAAO+2B,EAAkBM,IACnF,CAII,IADA,IACS1d,EAAK,EAAG2d,EADDtW,0BAA0BR,GACA7G,EAAK2d,EAAY57B,OAAQie,IAAM,CACrE,IACI4d,EAAO/W,EAAGgX,EADCF,EAAY3d,IAE3Bud,EAAW33B,KAAK,GAAGS,OAAOw3B,EAAU,KAAKx3B,OAAOu3B,GAAMv3B,OAAO8f,uBAAuB1Y,IAAImwB,GAAQ,IAAIv3B,OAAOwgB,EAAGc,aAAaiW,IAAS,IAC5I,CAEI,GAAIV,EACA,IAAK,IAAIhd,EAAK,EAAG4d,EAAeZ,EAAYhd,EAAK4d,EAAa/7B,OAAQme,IAAM,CACxE,IAAI6B,EAAS+b,EAAa5d,GAC1B,KA/DoB,8BA+Df6B,GAAwC0F,gCA9D1B,uBA+Dd1F,IA6FN7D,cAAgBE,aA9Ff,CAIA,IAAI2f,EAAYlX,EAAGuV,aAAara,GAChC,GAAKgc,EAIL,IAAK,IAAInQ,EAAK,EAAGC,EAAKxG,0BAA0B0W,GAAYnQ,EAAKC,EAAG9rB,OAAQ6rB,IAAM,CAC9E,IAAIiQ,EACAD,EAAOG,EAAUF,EADNhQ,EAAGD,IAElB4P,EAAoB53B,KAAK,GAAGS,OAAOw3B,EAAU,KAAKx3B,OAAOu3B,GAAMv3B,OAAO+f,qBAAqB3Y,IAAImwB,GAAQ,IAAIv3B,OAAOwgB,EAAGc,aAAaiW,IAAS,IAC3J,MAPgBN,EAAsB13B,KAAKmc,EAH3C,CAWA,CAGI,IAAK,IAAI+L,EAAK,EAAGkQ,EAAgB3X,YAAayH,EAAKkQ,EAAcj8B,OAAQ+rB,IAErE,IADA,IAAIhH,EAAakX,EAAclQ,GACtBC,EAAK,EAAGkQ,EAAmB3X,eAAgByH,EAAKkQ,EAAiBl8B,OAAQgsB,IAAM,CACpF,IAAIhH,EAAgBkX,EAAiBlQ,GACjC/G,EAAkBJ,mBAAmBC,EAAIC,EAAYC,GACzD0W,EAAiB73B,KAAK,GAAGS,OAAOygB,EAAY,KAAKzgB,OAAO0gB,EAAe,KAAK1gB,OAAO2gB,EAAgBhF,KAAK,MACpH,CAKI,OAFAwb,EAAoB9Q,OACpB6Q,EAAW7Q,OACJ,CACH0Q,kBAAmB5b,EACnB+b,WAAYA,EACZE,iBAAkBA,EAClBP,WAAYA,EACZM,oBAAqBA,EACrBF,sBAAuBA,EAE/B,GAqKA,SAASY,mBAAmB3qB,GACxB,OAAOsI,YAAYC,QAASvI,EAAS,GACzC,CAGA,SAAS4qB,cAAcxgB,GACnB,IACIygB,EAkCR,SAASC,yBAAyBC,GAC9B,OAAOxlB,MAAM,IAAO,IAAOwlB,EAAqB,KACpD,CApC6BD,CADrBC,EAIR,SAASC,uBAAuB5gB,GAI5B,OAAI4B,YACO,GAGPnB,YACOC,mBAAuBU,sBAAwBT,iBAA0B,GAAN,IAE1EwX,EAAW,UAAWnY,EAAWmY,SAAWnY,EAAWmY,SAAS/xB,MAAQ,GAExE,OAAOoM,KAAK2lB,GAKL,GAGP,OAAO3lB,KAAK2lB,GAKL,GAGJ,GACX,CAlC8ByI,CAAuB5gB,IAEjD,MAAO,CAAE6gB,MAAOF,EAAqBG,QAJnB,4CAI4CC,QAAQ,MAAO,GAAGr4B,OAAO+3B,IAC3F,CAsDA,SAASO,eAAehhB,GACpB,OAAO9C,WAnBX,SAAS+jB,4BAA4BjhB,GAEjC,IADA,IAAItZ,EAAS,GACJ2Y,EAAK,EAAGrF,EAAKnW,OAAOkI,KAAKiU,GAAY+O,OAAQ1P,EAAKrF,EAAG5V,OAAQib,IAAM,CACxE,IAAI6hB,EAAelnB,EAAGqF,GAElBjZ,EAAQ,UADR+6B,EAAYnhB,EAAWkhB,IACQ,QAAUt2B,KAAKC,UAAUs2B,EAAU/6B,OACtEM,GAAU,GAAGgC,OAAOhC,EAAS,IAAM,IAAIgC,OAAOw4B,EAAaH,QAAQ,YAAa,QAAS,KAAKr4B,OAAOtC,EAC7G,CACI,OAAOM,CACX,CAUsBu6B,CAA4BjhB,GAClD,CA+BA,SAASohB,kBAAkBC,GAGvB,OAnhGJ,SAASC,+BAA+BC,EAAiBC,QAC7B,IAApBA,IAA8BA,EAAkBrE,KACpD,IAAIsE,EAAsBphB,OAAOohB,oBACjC,OAAIA,EAIO,IAAIz7B,SAAQ,SAAUC,GAAW,OAAOw7B,EAAoBj9B,KAAK6b,QAAQ,WAAc,OAAOpa,MAAc,CAAEy7B,QAASF,OAGvHxoB,KAAK7H,KAAK2W,IAAIyZ,EAAiBC,GAE9C,CAugGWF,CAFyBD,OAAV,IAAlBA,EAA4C,GAEVA,EAA+B,EAAhBA,EACzD,CAQA,SAASM,UAAUC,EAAepW,GACXvR,KAAKF,MACxB,MAAO,CACHrQ,IAAK,SAAUkM,GACX,OAAOjQ,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAesb,EAAYtZ,EAC3B,OAAOG,YAAYnC,MAAM,SAAUsV,GAC/B,OAAQA,EAAG9S,OACP,KAAK,EAED,OADY+S,KAAKF,MACV,CAAC,EAAa6nB,KACzB,KAAK,EAQD,OAPA5hB,EAAahG,EAAG7S,OAChBT,EAnD5B,SAASm7B,kBAAkB7hB,GACvB,IAAI8hB,EAEAC,EAAavB,cAAcxgB,GAE/B,MAAO,CACHgiB,aAAAA,GAII,OAFIF,OADmBpnB,IAAnBonB,EACiBd,eAAet8B,KAAKsb,YAElC8hB,CACV,EACDE,aAAAA,CAAcA,GACVF,EAAiBE,CACpB,EACDD,WAAYA,EACZ/hB,WAAYA,EACZ0e,QA1hGM,QA4hGd,CAgCqCmD,CAAkB7hB,GACvBwL,GAAU5V,MAAAA,GAAkDA,EAAQ4V,MAKjE,CAAC,EAAc9kB,GAElD,GACA,GACS,EAET,CA+CA,IAAI8M,MAAQ,CAAEyuB,KAxBd,SAASA,KAAKrsB,GACV,IAAIoE,EAEJ,YADgB,IAAZpE,IAAsBA,EAAU,IAC7BjQ,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAI28B,EAAe7V,EACnB,OAAO3kB,YAAYnC,MAAM,SAAUuc,GAC/B,OAAQA,EAAG/Z,OACP,KAAK,EAKD,OAJI,OAAC8S,EAAKpE,EAAQssB,cAAwCloB,GA3B9E,SAASmoB,UAEL,KAAI9hB,OAAO+hB,YAA+B,MAAjBjxB,KAAKC,UAG9B,IACI,IAAIixB,EAAU,IAAIC,eAClBD,EAAQE,KAAK,MAAO,0CAA0C75B,OAnlGxD,QAmlGwE,oBAAoB,GAClG25B,EAAQG,MAMhB,CAJI,MAAO/oB,GAIX,CACA,CAawB0oB,GAEJd,EAAgBzrB,EAAQyrB,cAAe7V,EAAQ5V,EAAQ4V,MAChD,CAAC,EAAa4V,kBAAkBC,IAC3C,KAAK,EAGD,OAFApgB,EAAG9Z,OAEI,CAAC,EAAcw6B,UADNpB,mBAAmB,CAAE1X,MAAO,CAAI2C,EAAAA,MAAOA,IACRA,IAEnE,GACA,GACA,EAI0BwV,eAAgBA,eAAgByB,wBA7H1D,SAASA,wBAAwBziB,GAC7B,OAAOpV,KAAKC,UAAUmV,GAAY,SAAU0iB,EAAMt8B,GAC9C,OAAIA,aAAiBkD,MA9gF7B,SAASq5B,cAAclpB,GACnB,IAAIO,EACJ,OAAOpW,SAAS,CAAE6P,KAAMgG,EAAMhG,KAAMgqB,QAAShkB,EAAMgkB,QAASmF,MAAO,OAAC5oB,EAAKP,EAAMmpB,YAAmC,EAAS5oB,EAAGzG,MAAM,OAASkG,EACjJ,CA4gFmBkpB,CAAcv8B,GAElBA,CACV,GAAE,EACP,ggBC9gGA,SAAsBy8B,cAAWC,OAAAA,aAAAA,cAAAp+B,KAAAP,WAIhC,SAAA2+B,eAAAA,IAAAA,EAAAA,QAAAA,OAAAA,aAAAC,kBAAAC,+BAJM,SAAAC,QAAAv8B,aAAAs8B,+BAAA,SAAAE,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,OAAA,OAAAA,EAAAA,OACcC,gBAAoB,OAA7B,OAAFC,EAAEF,UAAAA,YACaE,YAAQ,OAAjB,OAAN18B,EAAMw8B,EAAAA,KAAAA,mBACLx8B,WAAgB,OAAA,YAAA,OAAAw8B,eAAAD,QAC1BH,MAAAp+B,KAAAP,WACD,SAAsBk/B,kBAAiBC,EAAAC,UAAAC,iCAAA9+B,KAAAP,UAqBtC,CAAA,SAAAq/B,0CAAAA,mBAAAT,kBAAAC,wBAAAA,OArBM,SAAAS,EAAiCtmB,EAAOumB,OAAIC,EAAAC,aAAAZ,+BAAA,SAAAa,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACxBhB,cAAa,OAiBR,OAjBtBmB,EAAQH,WAGVH,MAAAA,OAAI,EAAJA,EAAAA,mBAAyBO,SACzBN,EAAU,IAAIM,QACdP,IAAAA,eAAqB,SAACt9B,EAAOtB,GACzB6+B,UAAY7+B,EAAKsB,OAIrBu9B,EAAU,IAAIM,SAAQP,MAAAA,OAAI,EAAJA,IAAAA,QAAiB,IAG3CC,wBAA2BK,GACrBJ,EAAYM,eAAAA,gBACXR,EAAAA,GAAI,CAAA,EAAA,SACPC,uBACwBE,YAAAA,KAErBM,MAAMhnB,EAAOymB,IAAa,OAAA,UAAA,OAAAC,IAAAA,WAAAJ,UACpCD,MAAA9+B,KAAAP,UACD,CAAsBigC,SAAAA,2BAA0BC,UAAAC,0CAAA5/B,KAAAP,UAoB/C,CAAA,SAAAmgC,qCAAAA,4BAAAvB,kBAAAC,sBAAAA,MApBM,SAAAuB,EAA0CpnB,OAAKumB,EAAAc,EAAAZ,EAAAa,YAAAC,EAAAvgC,iBAAA6+B,+BAAA,SAAA2B,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,IAAAA,OAAA,OAAmB,OAAjBjB,IAAIgB,EAAAA,aAAAhqB,IAAAgqB,KAAAA,KAAG,CAAIF,EAAAA,IAAME,UAAAA,UAAAhqB,EAAAiqB,YAC9C9B,cAAa,OASR,OATtBmB,EAAQW,IAAAA,OAERhB,EAAU,IAAIM,QAAQP,EAAAA,YAC5BC,aAA2BK,GAC3BL,IAAAA,QAAAA,KAAyBa,GAEnBZ,EAAYM,eAAAA,kBACXR,GAAI,CAAA,EAAA,SACPC,gBADO,OAEiBgB,IAAAA,QAAAA,EAAAA,QAGDR,MAAMhnB,EAAOymB,GAAa,QAAnC,OAARa,EAAQE,UAAAA,iBACPF,GAAQ,QAGsB,MAHtBE,IAAAA,SAAAA,EAAAA,GAAAA,EAAAA,SAGsBA,EAAAA,GAAA,QAAA,OAAA,KAAA,OAAAA,EAAAA,UAAAJ,EAAA,KAAA,CAAA,cAG5CD,MAAA5/B,KAAAP,oaC7CD2gC,QAAAC,EAAAC,GAAA,IAAAC,EAAAC,UAAA,OAAAJ,QAAA,SAAAK,EAAAC,GAAA,OAAAH,EAAAE,GAAA,IAAA,GAAAJ,EAAAC,EAAA,kSAAA,IAAMK,SAAStsB,4BACTusB,UAAYxZ,KAAK/S,utCACvB,IAAawsB,wBA6RDC,EAAAA,EADPC,EAAAA,IA7BQC,EATAC,EAAAA,EATAC,IAVAC,EAtCAC,IADRC,EAjDAC,EAAAA,IAtCoBC,IAPRC,IAPAC,EAdLC,EAAAA,IADPC,EA5BOC,EAHCC,IAJAC,IAJCC,EAJAC,EAAAA,EAPFC,EAAAA,IAHAC,IAdCC,EADRC,EAAAA,qBAAAC,cAHD,SAAAxB,IAAcyB,IAAAA,EAAAA,QAAAA,qBAAAzB,GACV7gC,aAAe,KACfA,KAAAuiC,kBAAyB,OAC5B,CAAA,OAAA,YAAAH,EAAAA,EAAAA,EAAA/D,kBAAAC,+BACD,SAAAC,QAAAqC,EAAA4B,MAAAlE,OAAAA,wBAAAA,OAAA,SAAAE,GAAA,IAAA,IAAAiE,EAAAC,IAAA,OAAAlE,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACyBmE,uBAA0B,mDAG3B,IAAI/pB,WAAW,yBAE1B,GAAA,OAAA,OAAuB,OALpB,OAAZ5Y,aAAYw+B,UAAAA,YAMYmE,SAAAA,qBAAgC3iC,sBAAuB,OAAhE,OAAT4gC,EAASpC,EAAAA,KAAAA,YACUmE,SAAAA,QAAAA,aAAiC3iC,OAAA,eAAwB,OAAlE,OAAVwiC,EAAUhE,UAAAA,IAAAA,QAAAA,KACT,WACQx+B,OAAA,MAAsB4gC,qBACrB5gC,OAAA,MAAsBwiC,mBACrC,QAAA,YAAA,OAAAhE,eAAAD,EAAAv+B,UACJ,WAbQoiC,OAAAA,EAAAA,MAAApiC,KAAAP,cAAA,qBAAA0iC,EAAA9D,kBAAAC,+BAcT,SAAAS,WAAAT,sBAAAA,MAAA,SAAAa,GAAA,IAAA,IAAAyD,EAAAxC,UAAA,OAAAjB,EAAAA,KAAAA,WAAA,OAAA,OAAAA,IAAAA,QACiBwD,OAAAA,OAAAA,YAA0B,+BAAwC,aAAA,OAAuB,OAAA,OAAAxD,IAAAA,aAAAA,EAAAA,MAAA,OAAA,YAAA,OAAAA,EAAAA,UAAAJ,EAfjGT,KAgBR,WAFO6D,OAAAA,EAAAA,EAAAA,MAAAniC,KAAAP,cAAA,qBAAAyiC,EAAA7D,kBAAAC,+BAGR,SAAAuB,EAASgD,EAAQtzB,GAAI,IAAAuzB,EAAAC,SAAAzE,sBAAAA,MAAA,SAAA2B,GAAA,oBAAA,OAAAA,IAAAA,MAAAA,WAAA,OAGoC,OAD/C+C,GADAC,EAAU,IAAIlqB,qBACcxJ,GAC5BuzB,EAAKH,OAAAA,gBAAuB,IAAI/pB,gBAAeqnB,YACzB0C,SAAAA,cAAsB,mBAAmBG,GAAMD,EAAQG,GAAW,OAA3E,OAAbD,EAAa9C,UAAAA,IAAAA,QAAAA,KACZ,eAAE8C,KAAeD,IAAI,OAAA,YAAA,OAAA7C,eAAAJ,OAC/B,SANOjB,EAAAC,UAAAqD,UAAAliC,KAAAP,UAAFyjC,IAAE,mBAAAjB,EAAAA,EAAAA,EAAA5D,kBAAAC,+BAOR,SAAA6E,EAAWC,GAAGC,IAAAA,EAAAA,EAAAA,EAAA/E,OAAAA,wBAAAA,OAAA,SAAAgF,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,EAAAA,KAAAA,IAAAA,OAAA,OACkC,OAAtCD,EAAYrjC,aAAsBojC,GAAIE,IAAAA,QAC/BX,8BAAgCU,EAAW,QAAA,wBAA6C,UAAY,OAAA,OAAAC,EAAAA,cAAAA,WAAA,OAAA,OAAA,KAAA,OAAAA,IAAAA,WAAAH,EAAAnjC,UACpH,SAHS2/B,GAAAsC,OAAAA,EAAAA,QAAAjiC,KAAAP,cAAA,OAAA,YAAAuiC,EAAAA,EAAAA,EAAA3D,kBAAAC,+BAIV,SAAAmF,EAAWL,GAAGC,IAAAA,EAAAA,EAAAA,EAAA/E,OAAAA,wBAAAA,OAAA,SAAAoF,GAAA,cAAA,OAAAA,UAAAA,IAAAA,OAAA,OACkC,OAAtCL,EAAYrjC,aAAsBojC,GAAIM,IAAAA,QAC/Bf,8BAAiCU,EAAW,6BAA6C,UAAY,OAAA,OAAAK,EAAAA,SAAAA,KAAAA,WAAA,OAAA,OAAA,KAAA,OAAAA,eAAAD,EAAAzjC,UACrH,SAHS2jC,GAAA3B,OAAAA,EAAAA,EAAAA,MAAAhiC,KAAAP,cAAA,OAAA,gBAAAsiC,EAAA1D,kBAAAC,+BAIV,SAAAsF,EAAUhD,EAAWiC,GAAM,IAAAgB,SAAAvF,sBAAAA,MAAA,SAAAwF,GAAA,IAAA,IAAAC,EAAA3D,UAAA,OAAA0D,EAAAA,KAAAA,EAAAA,MAAA,OAAA,OAAAA,IAAAA,QACMnB,6BAA+BE,GAAO,OAA/C,OAAdgB,EAAcC,EAAAA,KAAAA,YACPnB,iBAAAA,MAAsB,QAAA,MAAsB/B,EAAWiD,GAAe,OAAA,OAAAC,mBAAAA,WAAA,OAAA,YAAA,OAAAA,eAAAF,OACtF,SAHQI,EAAAC,GAAAlC,OAAAA,EAAAA,EAAAA,MAAA/hC,KAAAP,UAAHykC,IAAG,uBAAApC,EAAAzD,kBAAAC,wBAAAA,OAIT,SAAA6F,EAAU3B,EAAYO,UAAazE,sBAAAA,MAAA,SAAA8F,GAAA,oBAAA,OAAAA,IAAAA,MAAAA,WAAA,OAAA,OAAAA,EAAAA,OAClBzB,iBAAAA,MAAsB,QAAA,MAAsBH,EAAYO,GAAc,OAAA,OAAAqB,YAAAA,KAAAA,EAAAA,MAAA,OAAA,YAAA,OAAAA,eAAAD,OACtF,SAFQE,EAAAC,UAAAxC,IAAAA,MAAA9hC,KAAAP,cAAA,iBAAAoiC,EAAAxD,kBAAAC,sBAAAA,MAGT,SAAAiG,EAASC,EAAcj1B,GAAIszB,IAAAA,EAAAE,EAAAD,EAAA2B,EAAAC,EAAA7B,EAAAA,eAAAvE,+BAAA,SAAAqG,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,IAAAA,MAAAA,EAAAA,MAAA,OAAA,OAAAA,YACF3kC,KAAA8kC,KAAS,OAAlB,OAANjC,EAAM8B,EAAAA,KAAAA,EAAAA,OACwB3kC,KAAAkjC,GAAQL,EAAQtzB,GAAK,OAAhC,OAAgCw1B,EAAAJ,UAAjD5B,EAAagC,EAAAA,cAAEjC,EAAEiC,EAAAA,GAAAJ,IAAAA,SACD3kC,OAAA,MAAUwkC,GAAa,QAAhC,OAAT5D,EAAS+D,IAAAA,MAAAA,aACe3kC,OAAA,MAAS4gC,EAAWiC,GAAO,QAImC,OAJtF4B,EAAeE,EAAAA,MACfD,EAAe,IAAI9rB,WAAW6rB,IAAAA,MAA6B3B,UAAgBC,YACjF2B,IAAiB,IAAI9rB,WAAW6rB,MAChCC,EAAAA,IAAiB5B,EAAI2B,EAAAA,YACrBC,UAAiB,IAAI9rB,WAAWmqB,GAAgB0B,EAAAA,WAA6B3B,WAAe6B,YAAAA,KACrFK,KAAKv+B,uBAAAA,OAAMw+B,mBAAiBP,MAAc,QAAA,UAAA,OAAAC,EAAAA,UAAAJ,EAAAvkC,KAZ5Cs+B,KAaR,SAVO4G,EAAAC,UAAAtD,gBAAA7hC,KAAAP,cAAA,iBAAAmiC,EAAAvD,kBAAAC,sBAAAA,MAWR,SAAA8G,EAASC,EAAeC,GAAa,IAAAC,EAAAxC,EAAAyC,EAAAC,SAAAnH,sBAAAA,MAAA,SAAAoH,GAAA,oBAAA,OAAAA,IAAAA,MAAAA,WAAA,OAIqC,OAJrCA,EAAAA,OAEvBhB,EAAe9rB,WAAAA,KAAgBwO,KAAKke,IAAgB,SAAA/kC,UAAKA,YAA1CqY,IACf2sB,EAAkBb,iBAClB3B,EAAgB2B,EAAAA,UAAwBA,WAAoBgB,IAAAA,QACzC1lC,aAAUqlC,GAAc,OAAjC,OAAV7C,EAAUkD,UAAAA,YACW1lC,OAAA,MAASwiC,EAAY+C,GAAgB,OAA9C,OAAZC,EAAYE,UAAAA,EAAAA,QACU1lC,KAAA2lC,GAAQH,EAAczC,GAAc,QAA7C,OAAb0C,EAAaC,IAAAA,MAAAA,YAAAA,KACZD,GAAa,QAG6B,MAH7BC,aAAAA,EAAAA,GAAAA,aAId,IAAI9gC,2BAA0B,QAAA,YAAA,OAAA8gC,eAAAN,EAAAplC,KAAA,CAAA,aAE3C,SAdO4lC,EAAAC,GAAAjE,OAAAA,EAAAA,MAAA5hC,KAAAP,UAAFqmC,IAAE,kBAeR,SAAItX,GACOwW,IAAAA,EAAAA,EAAAA,OAAAA,KAAKv+B,SAAAA,cAAAA,OAAMw+B,mBAAiB,IAAIrsB,WAAW4V,KAhB9C,GAiBP,qBAAAmT,EAAAtD,kBAAAC,wBAAAA,OACD,SAAAyH,EAASP,EAAczC,OAAaF,EAAAC,EAAAkD,EAAAC,MAAA3H,OAAAA,wBAAAA,OAAA,SAAA4H,GAAA,cAAA,OAAAA,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QAAAA,IAAAA,QAEPvD,OAAAA,iBAAAA,KAA+B6C,EAAc,oBAA4B,aAAY,OAI3C,OAJzD3C,EAAMqD,IAAAA,MACNpD,EAAKC,gBACL3jB,EAAM2jB,iBACNoD,EAAapD,IAAAA,UACbiD,EAAkB,IAAIptB,WAAU,KAAA,MAAAqsB,mBAAKkB,GAAUlB,mBAAK7lB,KAAK8mB,aACjCvD,uBAAsB,gBAAuBG,GAAMD,EAAQmD,GAAgB,QAApF,OAAfC,EAAeC,UAAAA,EAAAA,SAAAA,MAAAA,IACVE,aAAJC,OAAyBJ,IAAgB,QAAA,MAAAC,IAAAA,SAAAA,EAAAA,GAAAA,aAG1C,IAAIthC,mCAAkC,QAAA,YAAA,OAAAshC,EAAAA,UAAAH,EAAA,KAAA,CAAA,aAEnD,SAbOO,EAAAC,UAAA5E,UAAA3hC,KAAAP,UAAFkmC,IAAE,OAAA,YAAAjE,EAAAA,EAAAA,EAAArD,kBAAAC,wBAAAA,OAcR,SAAAkI,EAAc5F,EAAW6F,GAASrmC,IAAA2iC,EAAA3iC,EAAAA,SAAAk+B,wBAAAA,OAAA,SAAAoI,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,OAAA,OAAAA,EAAAA,OACZ1mC,OAAA,MAAqB4gC,GAAU,OAAxC,OAAHxgC,EAAGsmC,UAAAA,YACmB/D,SAAAA,QAAAA,MAAsB,QAAA,MAE/CviC,GAAAA,IAAS2Y,eAAJ,MAAyB0tB,IAAW,OAFzB,OAAb1D,EAAa2D,EAAAA,KAAAA,EAAAA,SAAAA,KAGZ1mC,OAAA,MAAyB+iC,IAAc,OAAA,OAAA,KAAA,OAAA2D,IAAAA,WAAAF,EAAAxmC,UACjD,SANY2mC,EAAAC,UAAAlF,EAAAA,MAAA1hC,KAAAP,cAAA,uBAAAgiC,EAAApD,kBAAAC,+BAOb,SAAAuI,EAAcrE,EAAY8C,OAAaG,MAAAnH,OAAAA,wBAAAA,OAAA,SAAAwI,GAAA,cAAA,OAAAA,EAAAA,KAAAA,IAAAA,OAAA,OAAA,OAAAA,YACjB9mC,OAAA,MAAsBwiC,GAAW,OAA1C,OAAHpiC,EAAG0mC,EAAAA,KAAAA,EAAAA,OACmBnE,iBAAAA,MAAsB,QAAA,MAE/CviC,EAAKJ,OAAA,MAAyBslC,IAAe,OAF7B,OAAbG,EAAaqB,EAAAA,KAAAA,YAAAA,UAGRV,eAAJ,MAAyBX,IAAc,OAAA,OAAA,KAAA,OAAAqB,eAAAD,EAAA7mC,UACjD,SANY+mC,EAAAC,UAAAvF,IAAAA,MAAAzhC,KAAAP,cAAA,kCAAA+hC,EAAAnD,kBAAAC,+BAOb,SAAA2I,EAAsB7D,GAAG9E,IAAAA,EAAAA,EAAAA,OAAAA,wBAAAA,OAAA,SAAA4I,GAAA,cAAA,OAAAA,UAAAA,IAAAA,OAAA,OAAA,OAAAA,EAAAA,gBACdvE,8BAAgC3iC,KAAAmnC,iBAAsB/D,GAAM,oBAAA,SAG1D,WAAY,OAAA,YAAA,OAAA8D,eAAAD,EAAAjnC,UACxB,SALoBonC,UAAA5F,IAAAA,MAAAxhC,KAAAP,cAAA,uBAAA8hC,EAAAlD,kBAAAC,wBAAAA,OAMrB,SAAA+I,EAAuBjE,kBAAG9E,+BAAA,SAAAgJ,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,OAAA,OAAAA,EAAAA,cACf3E,eAAAA,kBAAiC3iC,aAAsBojC,GAAM,QAAA,qBAG3D,WAAY,OAAA,OAAA,KAAA,OAAAkE,EAAAA,UAAAD,EAAArnC,UACxB,SALqBunC,UAAAhG,EAAAA,MAAAvhC,KAAAP,cAAA,8BAMtB,SAAiB+uB,EAAQhoB,WACfghC,EAASxnC,aAAyBwuB,GAEjC4U,sBADE,MAAiB58B,aAAjBxC,OAAqByjC,OAAAA,EAAUD,EAAAA,MAAa,kBAAbC,EAAAA,gCAAkDjhC,aAE7F,kBACD,SAAoBgoB,GAIhB,YAHIkZ,KACExuB,EAAQ,IAAIN,WAAW4V,GACvBmZ,EAAMzuB,UACH3Z,IAAOA,EAAIooC,EAAKpoC,IACrBmoC,GAAUjhC,eAAoByS,EAAM3Z,IAEjCoc,OAAAA,eAAY+rB,KACtB,OAAA,WACD,SAAoBF,GAIhB,YAHMI,EAAejsB,OAAAA,KAAY6rB,GAC3BG,EAAMC,UACN1uB,EAAQ,IAAIN,WAAW+uB,GACpBpoC,IAAOA,EAAIooC,EAAKpoC,IACrB2Z,EAAM3Z,GAAKqoC,UAAwBroC,GAEhC2Z,OAAAA,IAAAA,KARV,GASA,8BACD,SAAiBkqB,WACPoE,EAASpE,IAAAA,MAAY,+DACpBpjC,aAAyBwnC,EAHnC,GAIA,iBAAAlG,EAAAA,EAAAA,EAAAjD,kBAAAC,+BACD,SAAAuJ,QAAAC,EAAAC,EAAAC,EAAA1oC,EAAA2oC,EAAAC,aAAA5J,+BAAA,SAAA6J,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,WAAA,OAAA,OAAAA,IAAAA,QACmCnoC,KAAAsoC,MAAU,OAAnB,OAAtBtoC,aAAsBmoC,UAAAA,YACOxF,eAAAA,YAA0B,4DAAkE,IAAI/pB,WAAW,yBAA8C,iBAAmB,OAC3I,OADxDkvB,EAAcK,UACdJ,EAAK/nC,aAAkBA,OAAA,eAAiCmoC,IAAAA,QACvCxF,OAAAA,OAAAA,YAAAA,KAAgCmF,WAAyB,OAEnD,OAFvBS,EAAQJ,IAAAA,MACRH,EAAKhD,KAAKv+B,iBAAAA,MAAAA,OAAMw+B,mBAAiB,IAAIrsB,WAAW2vB,MAChDjpC,EAAIqjC,iBAAmBwF,EAAAA,QACXnoC,eAAU,QAEW,OAFjCwoC,EAAGL,UAEHM,GADAxF,EAAU,IAAIlqB,qBACWzZ,EAAIkpC,GAAIL,aACTxF,eAAAA,KAAmB,QAAA,MAA+BmF,EAAAA,WAA2BW,GAAQ,QAC3C,OADlER,EAAeE,IAAAA,MACfD,EAAKlD,KAAKv+B,iBAAAA,MAAAA,OAAMw+B,mBAAiB,IAAIrsB,WAAWqvB,MAAkBE,EAAAA,gBACjE,IAAEJ,KAAIC,KAAIE,IAAI5oC,IAAG,QAAA,UAAA,OAAA6oC,IAAAA,WAAAN,EAAA7nC,UAC3B,kBAbOshC,EAAAA,MAAAthC,KAAAP,cAAA,OAAA,WAcR,SAAa4rB,UACF2Z,KAAK0D,SAASC,mBAAmBtd,OAC3C,gBACD,SAAGtc,EAAMrN,EAAOknC,GACNC,IAAAA,EAAAA,EAAAA,EAAO,IAAItzB,KACjBszB,UAAaA,IAAAA,WAAkBD,OACzBE,IAAU,KAAaD,EAAAA,cAC7Bv+B,iBAAkByE,MAAarN,MAAconC,WALhD,GAMA,gBACD,SAAG/5B,GAGC,YAFMg6B,EAASh6B,MACTi6B,EAAK1+B,iBAAAA,WACF/K,IAAOA,EAAIypC,IAAAA,MAAWzpC,KAE3B,IADA,IAAIgB,EAAIyoC,EAAGzpC,SACJgB,IAAAA,UACHA,EAAIA,IAAAA,QAAeA,EAAAA,eACnBA,EAAAA,QAAUwoC,GACV,OAAOxoC,IAAAA,MAAYwoC,UAAexoC,IAAAA,cAEnC,OAEX,gBACA,SAAGwO,GACCzE,IAAAA,EAAAA,EAAAA,iBAAkByE,IAFtB,IAAA,GAIA,gBACA,WAEI,YADMk6B,EAAU3+B,iBAAAA,WACP/K,IAAOA,EAAI0pC,IAAAA,MAAgB1pC,KAChC,IAAM61B,EAAS6T,EAAQ1pC,GAEjBwP,GAAOm6B,GADPA,EAAQ9T,IAAAA,YACYA,EAAAA,SAAiB8T,GAAS9T,EACpD9qB,iBAAkByE,2CAP1B,CAAA,GASC,uBAAAsyB,EAAAhD,kBAAAC,wBAAAA,OACD,SAAA6K,IAAAC,IAAArG,EAAAsG,EAAAtJ,EAAAuJ,EAAAC,EAAAH,EAAAA,EAAA9K,OAAAA,+BAAA,SAAAkL,GAAA,cAAA,OAAAA,EAAAA,KAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACoCxpC,KAAAypC,KAAS,OAOE,OAPFL,EAAAI,UACnC7L,EAAU,IADRoK,EAAEqB,EAAAA,MAAIA,EAAAA,MAAIA,EAAAA,KAAGA,EAAAA,GAOfM,EAAcxjC,KAAAA,UAAey3B,GAAQ6L,IAAAA,SACfxpC,KAAA2pC,GAAQ/I,UAAW8I,GAAY,QAG1D,OAHK3G,EAAayG,UACbH,EAAiB,aACNtG,GAChByG,aAAAA,aAE0B7K,kBAAkBgC,0BAA2B,uBAEvD,6BAGHz6B,OAAAA,MAAemjC,KACvB,YANItJ,EAAQyJ,WAOTzJ,IAAWyJ,EAAAA,QAAA,MAAA,MACN,IAAI5kC,cAAoC,QAAA,OAAA4kC,aAEvBzJ,EAAAA,OAAe,SAApCuJ,EAAYE,IAAAA,QACEF,WAA0BA,YAAAA,QAC1CtpC,KAAA4pC,OAAaN,qBACPC,EAAyBvpC,KAAA6pC,aAAkB7pC,KAAAuiC,2BACjDviC,KAAA4pC,OAAaL,MAIhBC,aAAA,MAAA,QAAAA,IAAAA,SAAAA,EAAAA,GAAAA,cAGqC,QAAA,YAAA,OAAAA,eAAAL,EAAAnpC,KAAA,CAAA,cAE7C,kBArCQqhC,IAAAA,MAAArhC,KAAAP,cAAA,mBAAA2hC,EAAA/C,kBAAAC,sBAAAA,MAsCT,SAAAwL,IAAA,IAAAvpC,EAAAjB,EAAAyqC,SAAAzL,sBAAAA,MAAA,SAAA0L,GAAA,oBAAA,OAAAA,UAAAA,EAAAA,MAAA,UACQzpC,EAAIP,KAAAiqC,QACJ3qC,EAAIU,KAAAiqC,QACH1pC,GAAMjB,EAAC0qC,CAAAA,IAAAA,QAAA,MAAA,OAAAA,EAAAA,SAAAA,SACC,OAEM,OAAfE,EAAQ9iB,KAAK7mB,GAAEypC,YACGhqC,KAAA8lC,GAAQoE,EAAO5qC,GAAE,OAA1B,OAATyqC,EAASC,IAAAA,MAAAA,iBACND,GAAS,OAAA,YAAA,OAAAC,eAAAF,EAAA9pC,UACnB,WATQohC,OAAAA,EAAAA,QAAAA,MAAAphC,KAAAP,cAAA,sBAAA0hC,EAAA9C,kBAAAC,+BAUT,SAAA6L,EAAU56B,GAAI,IAAA66B,EAAArH,SAAAzE,sBAAAA,MAAA,SAAA+L,GAAA,oBAAA,OAAAA,UAAAA,EAAAA,MAAA,OAAA,OAAAA,IAAAA,QACIrqC,eAAU,UAApBV,EAAC+qC,IAAAA,MACDD,EAAQhjB,KAAK9nB,GACZA,GAAC+qC,YAAA,MAAA,OAAAA,IAAAA,QAAAA,SACO,OAAA,OAAAA,EAAAA,OAEarqC,KAAA2pC,GAAQS,EAAO76B,GAAK,OAA7B,OAAbwzB,EAAasH,UAAAA,mBACVtH,GAAa,QAAA,OAAA,KAAA,OAAAsH,eAAAF,EAAAnqC,UACvB,SARQsqC,GAAAnJ,OAAAA,EAAAA,EAAAA,MAAAnhC,KAAAP,cAAA,OAAA,YAAAyhC,EAAA7C,kBAAAC,sBAAAA,MAST,SAAAiM,EAAUxH,GAAa,IAAAxiC,EAAAklC,SAAAnH,sBAAAA,MAAA,SAAAkM,GAAA,oBAAA,OAAAA,IAAAA,MAAAA,WAAA,UACfjqC,EAAIP,KAAAiqC,SACFO,YAAA,MAAA,OAAAA,IAAAA,QAAAA,SACO,OAEM,OAAfN,EAAQ9iB,KAAK7mB,GAAEiqC,YACOxqC,KAAA8lC,GAAQoE,EAAOnH,GAAc,OAAtC,OAAb0C,EAAa+E,IAAAA,MAAAA,YAAAA,KACV/E,GAAa,OAAA,YAAA,OAAA+E,eAAAD,EAAAvqC,UACvB,SARQyqC,UAAAvJ,gBAAAlhC,KAAAP,cAAA,kBAAAwhC,EAAAA,EAAAA,EAAA5C,kBAAAC,+BAST,SAAAoM,QAAA3K,MAAAzB,OAAAA,wBAAAA,OAAA,SAAAqM,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,EAAAA,KAAAA,IAAAA,OAAA,OAAA,OAAAA,EAAAA,OAAAA,IAAAA,QAE+BhM,kBAAkBgC,gBAAsC,uBAElE,yCAGH,OACR,WANIZ,EAAQ4K,IAAAA,OAOT5K,IAAW4K,EAAAA,OAAA,MAAA,MACN,IAAI/lC,QAAJ,MAAwC,OAAA,OAAA+lC,YAEvB5K,YAAe,OAAxB4K,UACQA,IAAAA,SAAA,MAAA,QAAAA,IAAAA,SAAAA,EAAAA,GAAAA,aAGY,QAAA,YAAA,OAAAA,eAAAD,EAAA,KAAA,CAAA,QAzBrCpM,KA2BR,WAlBQ2C,OAAAA,EAAAA,EAAAA,MAAAjhC,KAAAP,cAAA,uBAAAuhC,EAAA3C,kBAAAC,+BAmBT,SAAAwM,IAAAxM,OAAAA,sBAAAA,MAAA,SAAAyM,GAAA,IAAA,IAAAC,EAAA5K,UAAA,OAAA2K,UAAAA,IAAAA,OAAA,UACS/qC,KAAAirC,KAASF,CAAAA,YAAA,MAAA,OAAAA,IAAAA,QACJ/qC,eAAU,OAAA,YAAA,OAAA+qC,IAAAA,WAAAD,EAAA9qC,UAEvB,kBAJQghC,UAAAhhC,KAAAP,cAAA,gBAKT,oBACQO,KAAAiqC,UAAgBjqC,KAAAiqC,QANf,GAUR,iBAAAlJ,EAAAA,EAAAA,EAAA1C,kBAAAC,+BACD,SAAA4M,IAAA,IAAAC,SAAA7M,sBAAAA,MAAA,SAAA8M,GAAA,IAAA,IAAAC,EAAAjL,UAAA,OAAAgL,EAAAA,KAAAA,EAAAA,MAAA,OACQD,KAAY,WACRnrC,KAAAiqC,WAAgBkB,EAAW,OAAAC,EAAAA,OACzB,IAAI9pC,SAAQ,SAAAC,UAAWkT,WAAWlT,MAAlC,IADyB6pC,IAAAA,QAAA,MACuB,OACtDD,IAAUC,EAAAA,OAAA,MAAA,OAAA,YAAA,OAAAA,EAAAA,UAAAF,EAAAlrC,UAEjB,WANO+gC,OAAAA,EAAAA,QAAA/gC,KAAAP,cAAA,OAAA,YAAAqhC,EAAAA,EAAAA,EAAAzC,kBAAAC,+BAOR,SAAAgN,QAAAtpC,MAAAs8B,OAAAA,wBAAAA,OAAA,SAAAiN,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACqB9M,gBAAoB,OAA7B,OAAFC,EAAE6M,EAAAA,KAAAA,EAAAA,OACa7M,IAAAA,QAAQ,OAAjB,OAAN18B,EAAMupC,EAAAA,KAAAA,YAAAA,KACLvpC,WAAgB,OAAA,YAAA,OAAAupC,eAAAD,OAC1B,WAJQxK,OAAAA,EAAAA,EAAAA,MAAA9gC,KAAAP,eApSb,gCCLAisC,QAAAC,EAAAC,GAAA,IAAAC,EAAAC,UAAA,OAAAJ,QAAA,SAAAK,EAAAC,GAAA,OAAAH,EAAAE,GAAA,IAAA,GAAAJ,EAAAC,EAAA,UACAE,UAAA,IAAAG,EAAA,CAAA,iBAAA,cAAA,aAAA,gBAAA,QAAA,SAAA,SAAA,OAAA,iBAAA,OAAA,gBAAA,UAAA,SAAA,OAAA,iBAAA,OAAA,cAAA,+BAAA,OAAA,QAAA,YAAA,YAAA,gBAAA,eAAA,SAAA,MAAA,mBAAA,SAAA,WAAA,OAAAH,QAAA,WAAA,OAAAG,CAAA,IAAA,iSACA,IAAMtL,SAAStsB,4BACF63B,cAAa,MAAA,MAAAC,EAAAC,EAAAC,iBAAAhK,cAAA,SAAA6J,IAAA5J,qBAAA4J,KAAA,CAAA,OAAA,gBAAAC,EAAA9N,kBAAAC,+BACtB,SAAAC,EAAc+N,EAAU3O,GAAO,IAAAmC,EAAAyM,EAAAxM,EAAAyM,EAAA/sC,iBAAA6+B,sBAAAA,MAAA,SAAAE,GAAA,oBAAA,OAAAA,IAAAA,MAAAA,EAAAA,MAAA,OAEyC,OAFvCiO,MAAWD,IAAAA,YAAAx2B,IAAAw2B,OAAAA,KAAS1M,IAAM0M,EAAAA,OAAAA,UAAAx2B,EAAAwoB,IAAAA,QAE7C+N,EAAUE,EAAc/M,2BAA6BD,MAAKjB,IAAAA,QACzC+N,aAAW5L,kCAAsB2L,GAAY,uBAEvD,6BACHpmC,KAAAA,UAAey3B,IACtBmC,GAAO,WAJJC,EAAQvB,EAAAA,MAKTuB,IAAWvB,IAAAA,QAAA,MAAA,MACNuB,EAAQ,OAAA,OAAAvB,aAELuB,EAAAA,OAAe,QAAA,OAAAvB,IAAAA,QAAAA,KAAAA,EAAAA,MAAA,QAAA,MAAAA,aAAAA,EAAAA,GAAAA,IAAAA,SAAAA,EAAAA,GAAA,QAAA,OAAA,KAAA,OAAAA,eAAAD,EAAA,KAAA,CAAA,aAKnC,SAhBYK,EAAAC,UAAAsN,IAAAA,MAAAnsC,KAAAP,UAAPk+B,IAAO,kBAiBb,SAAWA,EAAS+O,kBAAc1sC,oBAA2B29B,KAAe+O,KAAO,OAAA,WACnF,SAAe/O,EAAS+O,kBAAc1sC,OAAA,QAAA,KAA+B29B,KAAe+O,EADD,GACQ,uBAC3F,SAAU/O,EAASxM,kBAAmBnxB,oBAA8CmxB,EAAUwM,QADH,GACyB,2BACpH,SAAcA,EAAS+O,UAAc1sC,6BAA8B29B,KAAe+O,EADkC,GAC3B,yBACzF,SAAY/O,EAAS+O,kBAAc1sC,KAAA29B,eAA4BA,KAAe+O,EADW,GACJ,OAAA,WACrF,SAAU/O,EAAS+O,GAAc,IAAAC,EAAAP,EAAA,OAAApsC,OAAA,QAAA,KAA0B29B,KAAe+O,EADW,MAtB/D,GCE1B,IAAMrtC,EAAEA,GAAG,CAACwC,EAAE0C,cAAcA,EAAEA,EAAEyC,gBAAgB,KAAK4lC,eAAeC,OAAOxtC,EAAEwC,EAAC,IAAK+qC,eAAeC,OAAOxtC,EAAEwC,EAAC,ECAnG0C,EAAE,CAACgC,WAAU,EAAGC,KAAKC,OAAOC,UAAUrH,IAAEsH,SAAQ,EAAGC,WAAW/E,KAAGrB,IAAE,CAACnB,EAAEkF,EAAE1C,EAAErB,KAAK,IAAMssC,KAAKttC,EAAEwB,SAASzB,GAAGiB,EAAEuE,IAAIzF,EAAE2E,WAAW4C,oBAAoB7B,IAAIzF,GAAG,QAAG,IAASD,GAAG2E,WAAW4C,oBAAoB3B,IAAI3F,EAAED,EAAE,IAAIyI,KAAKzI,EAAE4F,IAAI1E,EAAEuO,KAAK1P,GAAG,aAAaG,EAAE,CAAC,IAAW+E,EAAG/D,EAAFuO,KAAI,MAAM,CAAC7J,GAAAA,CAAI1E,GAAG,IAAMhB,EAAEqC,EAAEmD,IAAIlF,KAAKE,MAAM6B,EAAEqD,IAAIpF,KAAKE,KAAKQ,GAAGR,KAAK2H,cAAcpD,EAAE/E,EAAEH,EAAE,EAAE2/B,IAAAA,CAAKn9B,GAAG,YAAO,IAASA,GAAG7B,KAAKoB,EAAEmD,OAAE,EAAOlF,GAAGwC,CAAC,EAAE,CAAC,GAAG,WAAWrC,EAAgG,MAAMoF,MAAM,mCAAmCpF,GAA7I,CAAC,IAAW+E,EAAG/D,EAAFuO,KAAI,OAAO,SAASvO,GAAG,IAAMhB,EAAEQ,KAAKuE,GAAG1C,EAAE/B,KAAKE,KAAKQ,GAAGR,KAAK2H,cAAcpD,EAAE/E,EAAEH,EAAE,CAAC,GAAoD,SAASG,EAAEH,GAAG,MAAM,CAACwC,EAAE0C,KAAI,MAAA,iBAAiBA,EAAE/D,IAAEnB,EAAEwC,EAAE0C,IAAKlF,EAAkJA,EAAnImB,EAAqIqB,EAAjIhC,eAAe0E,GAAU1C,EAAE6C,YAAY4C,eAAe/C,EAAE/D,EAAE,IAAInB,EAAEmM,SAAQ,GAAInM,GAAGmB,EAAErB,OAAOsB,yBAAyBoB,EAAE0C,QAAG,GAA3I,IAAElF,EAAemB,EAAyI,CCApwB,SAASA,EAAEA,GAAG,OAAOnB,EAAE,IAAImB,EAAE+G,OAAM,EAAGhB,WAAU,GAAI,uaCHvD,IAAMo6B,SAAStsB,4BACF04B,qBAAAA,sBAAoBC,EAAA3O,kBAAAC,wBAAAA,OAAG,SAAAC,EAAO0O,EAAc9b,EAAU2O,GAAM,IAAAoN,EAAAnN,SAAAzB,sBAAAA,MAAA,SAAAE,GAAA,IAAA,IAAA2O,EAAAC,UAAA,OAAA5O,UAAAA,EAAAA,MAAA,OAIpE,OAHK0O,EAAc,cACFD,IAAAA,oBACJ9b,GACbqN,EAAAA,OAAAA,YAE0BkB,8BAA0B17B,OAAI28B,WAAJ,MAAuC,uBAE3E,kBAAA,WACHz6B,aAAegnC,IACtBpN,GAAO,WAJJC,EAAQvB,EAAAA,MAKTuB,IAAWvB,EAAAA,OAAA,MAAA,MACNuB,EAAQ,OAAA,OAAAvB,IAAAA,QAELuB,YAAe,OAAA,OAAAvB,mBAAAA,EAAAA,MAAA,QAAA,MAAAA,IAAAA,SAAAA,EAAAA,GAAAA,aAAAA,EAAAA,GAAA,QAAA,UAAA,OAAAA,IAAAA,WAAAD,EAAA,KAAA,CAAA,aAKnC,OAAA,SAnBgCK,EAAAC,EAAAc,GAAAqN,OAAAA,EAAAA,MAAAhtC,KAAAP,WAA1B,EAAMstC,+cAoBM1O,kBAAAC,sBAAAA,MAAG,SAAAS,IAAAgB,IAAAA,EAAAA,EAAAA,QAAAzB,OAAAA,+BAAA,SAAAa,GAAA,IAAA,IAAAkO,EAAAC,IAAA,OAAAnO,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACKM,iBAASkB,iBAA2B,iBAEzD,OAFY,OAARZ,EAAQZ,UAAAA,iBAGPY,aAAe,OAAA,YAAA,OAAAZ,IAAAA,WAAAJ,OAEKV,kBAAAC,sBAAAA,MAAG,SAAAuB,EAAO1O,EAAU2O,GAAM,IAAAoN,EAAAnN,SAAAzB,sBAAAA,MAAA,SAAA2B,GAAA,oBAAA,OAAAA,IAAAA,MAAAA,IAAAA,OAAA,OAGpD,OAFKiN,EAAc,UACN/b,GACb8O,IAAAA,QAAAA,YAE0BP,sCAA8BiB,0CAAuC,uBAE/E,kBAAA,WACHz6B,aAAegnC,IACtBpN,GAAO,WAJJC,EAAQE,IAAAA,OAKTF,GAAWE,CAAAA,YAAA,MAAA,MACNF,EAAQ,OAAA,OAAAE,YAELF,EAAAA,OAAe,OAAA,OAAAE,EAAAA,cAAAA,WAAA,QAAA,MAAAA,aAAAA,EAAAA,GAAAA,EAAAA,SAAAA,EAAAA,GAAA,QAAA,UAAA,OAAAA,IAAAA,WAAAJ,EAAA,KAAA,CAAA,QAbLvB,KAmBxB,4DAAMiP,kBAAWC,IAAAA,EAAAA,UAAAA,EAAAnP,kBAAAC,+BAAG,SAAA6E,EAAOsK,EAAU3N,GAAMC,IAAAA,EAAAA,EAAAA,EAAAzB,OAAAA,+BAAA,SAAAgF,GAAA,IAAA,IAAAoK,EAAAC,IAAA,OAAArK,UAAAA,EAAAA,MAAA,OAAA,OAAAA,EAAAA,OAAAA,EAAAA,OAEnB5D,qCAA8BiB,wBAA8B8M,GAAY,UAAA,aAElF,kBAAA,OACV3N,GAAO,WAHJC,EAAQuD,IAAAA,OAITvD,GAAWuD,CAAAA,YAAA,MAAA,MACNvD,EAAQ,OAAA,OAAAuD,IAAAA,QAELvD,YAAe,OAAA,OAAAuD,IAAAA,QAAAA,KAAAA,IAAAA,OAAA,QAAA,MAAAA,aAAAA,EAAAA,GAAAA,IAAAA,SAAAA,EAAAA,GAAA,QAAA,YAAA,OAAAA,EAAAA,UAAAH,EAAA,KAAA,CAAA,aAKnC,OAAA,SAduBc,EAAAI,GAAAmJ,OAAAA,EAAAA,EAAAA,MAAAxtC,KAAAP,UAAA,CAAA,KCrCjB,SAASmuC,sBAAsB/E,EAAM1X,OAelC0c,mBAdDhF,QAAiB7yB,IAAT6yB,GAGTiF,EAAQ,IAAIv4B,KAAKszB,UACjB1X,EAEO2c,iBAAkC,iDAOvCC,EAAMD,IAAAA,UAAAA,QAAAA,gBACND,EAAQC,EAAAA,uBAA8B,gBACtCE,EAAOF,YACb9pC,GAAAA,OAAU+pC,SAAV/pC,MAAiB6pC,UAAjB7pC,MAA2BgqC,KAfhB,KAoER,SAASC,oBAAoBC,iBAE1BC,GADAtF,EAAO,IAAItzB,KAAK24B,uCAEhBtF,EAAUC,IAAAA,UAAAA,UAAAA,aAChB,MAAA,GAAA7kC,OAAUmqC,OAAVnqC,OAAmB4kC,GAEhB,SAASwF,sBAAsBrzB,GAC5BozB,IAAAA,EAAAA,QAAAA,EAAQ1hC,aAAWsO,MAAXtO,0BACRm8B,GAAW7tB,wCACjB/W,OAAUmqC,eAASvF,GAgFhB,SAASyF,aAAa3sC,EAAO4sC,EAAcnd,GAC1CzvB,IAAAA,EAAAA,QAAAA,OAAAA,MAAAA,MAEEM,SAASmvB,EAAoBzvB,EAAQA,EAAQ4sC,SAC/Cnd,OAAqBmd,EACd7hC,OAAAA,MAAWzK,KAAXyK,UAAAA,MAAsC,8BAGtC8hC,GAAwDC,EAAAC,eAA5BzsC,iCAAf0sC,EAAWF,KACzBG,EAAmBJ,UAAoB,6BAC7C,KAAA,MAAUI,SAAV,MAA8BD,KA0BtC,SAAAE,UAAA,IAAAC,EAAA,CAAA,MAAA,OAAA,MAAA,WAAA,qBAAA,iBAAA,MAAA,UAAA,gBAAA,UAAA,QAAA,QAAA,QAAA,QAAA,MAAA,SAAA,QAAA,SAAA,SAAA,OAAA,MAAA,UAAA,cAAA,OAAA,QAAA,gBAAA,SAAA,QAAA,gBAAA,YAAA,eAAA,WAAA,QAAA,YAAA,QAAA,WAAA,OAAA,QAAA,MAAA,MAAA,UAAA,SAAA,SAAA,SAAA,gBAAA,QAAA,iBAAA,QAAA,WAAA,SAAA,aAAA,UAAA,YAAA,QAAA,MAAA,UAAA,MAAA,OAAA,WAAA,cAAA,iBAAA,UAAA,aAAA,aAAA,SAAA,UAAA,QAAA,UAAA,QAAA,MAAA,gBAAA,oBAAA,aAAA,WAAA,cAAA,SAAA,eAAA,UAAA,YAAA,SAAA,OAAAD,QAAA,WAAA,OAAAC,CAAA,IAAA,k2DD/I0BC,IAAAA,EAAAA,UAAAzQ,kBAAAC,+BAAG,SAAAmF,EAAO9F,GAAOuP,IAAAnN,EAAAmN,EAAAA,SAAA5O,wBAAAA,OAAA,SAAAoF,GAAA,cAAA,OAAAA,EAAAA,KAAAA,IAAAA,OAAA,OACI,OAArCwJ,EAAchnC,OAAAA,MAAey3B,GAAQ+F,YACpBjE,WAAK,MAAIkB,WAAJ,MAA8C,uBAE7D,6BAGHuM,IACR,OANY,OAARnN,EAAQ2D,IAAAA,MAAAA,IAAAA,QAAAA,KAOP3D,IAAAA,SAAe,OAAA,UAAA,OAAA2D,IAAAA,WAAAD,EATAnF,IAAnB,KAAA,2RAAA,kWE9DA,sprECGPyQ,QAAAC,EAAAC,GAAA,IAAAC,EAAAC,UAAA,OAAAJ,QAAA,SAAAK,EAAAC,GAAA,OAAAH,EAAAE,GAAA,IAAA,GAAAJ,EAAAC,EAAA,CAAA,sXAAA,IACSK,OAAAA,oBAQL,SAAAC,IAAc/uB,IAAAA,EAAAA,EAAAA,eAAA8hB,qBAAAiN,IACV/uB,EAAAgvB,WAAAxvC,KAAAuvC,IACA/uB,iBACAA,EAAAA,UACAA,aACAA,aACAA,aACAA,EAAAA,YAAmBA,CACtBivB,CAAAA,OAAAA,UAAAF,EAhB2Bn8B,KAgB3BivB,aAAAkN,EAAA,CAAA,OAAA,WAUD,SAAaG,WACTC,cAAAJ,IAAAI,KAAA3vC,KAAA2vC,CAAA,CAAmBD,IAMf1vC,OAAA,OACAA,iBAEP,kBACD,SAAO4vC,WACHD,cAAAJ,WAAAvvC,KAAA2vC,CAAA,CAAaC,IACTA,mBAAmC5vC,eACnCA,KAAA6vC,OAAc7vC,gBACd,KAAAA,KAAAmO,QAAenO,OAAA,SACf,KAAAA,aAAmBA,iBACnBA,OAAA,MAAiBA,mBAExB,kBACD,mBAAiB8vC,EAAA9vC,OACTA,OAAA,MACAyU,YAAW,mBACPq7B,YACAA,WAFJr7B,SAMAzU,gBACAA,KAAA+vC,cAEP,kBACD,mBAAQC,OAAQ,KAAuFC,GAAEjD,IAAAvtC,YAAAA,YAAAuW,IAAAvW,aAAAA,aAAJ,CAAA,mBAA1EwwC,EAAE9hC,OAAU,KAAZ+hC,EAAAlD,IAAAA,4BAAgCkD,EAAEC,OAAc,KAAhBC,EAAApD,IAAAA,QAAqBoD,EAAEC,OAAY,KAAdC,EAAAtD,aAAesD,EAC3FtwC,OAAA,MAAcgwC,EACdhwC,aAAemO,EACfnO,KAAAmwC,YAAmBA,EACnBnwC,OAAA,MAAiBqwC,EACjBrwC,KAAAuwC,UACIvwC,OAAA,OACAA,iBAGR,mBACA,WACIA,eAFJ,GAGC,OAAA,WACD,WACI2b,IAAAA,EAAAA,EAAAA,OAAAA,6BAA2B3b,aAF9B,GAGA,oBACD,0BD/EyB,SAACuwC,EAAQP,EAAO7hC,EAASgiC,EAAaE,EAAWG,EAAOT,GAC7E,IAAAU,EAAAC,QAACH,IASDI,EAAkBrmC,SAAAA,2BAElBqmC,EAAkBrmC,0BAClBqmC,KACArmC,KAAAA,WAAAA,cAA0BqmC,IAGxBC,EAAeC,EAAIC,kBAAAA,mBAAAC,uBAAA,idAAA,OAMPf,EAOA7hC,EAKRgiC,EAAcU,EAAIG,mBAAAA,oBAAAD,uBAAA,oCAAA,cACFhB,EAECM,GAEfQ,EAAII,mBAAAA,oBAAAF,uBACUP,UAAAA,OAAAA,IAU1Bv9B,EAAO29B,EAAcD,KAhDXA,EAAkBrmC,mBAAAA,QAEpB2I,KAAW09B,EALhB,CCgFQO,CAAclxC,aAAaA,KAAA6vC,eAA4B7vC,aAAcA,iBAA2BA,OAAA,SAAqBA,KAAAwwC,MAAA/1B,KAAgBza,MAAOA,OAAA,cAAmBA,MAFzK,IAGA,CAAA,gBA5DD,iBACW,QACK,MAAQiG,gBACR,MAAQQ,gBACP,MAAQA,oBACJ,MAAQR,mBACV,MAAQI,QAsD1B,MA7EIipC,oBACkB,CACnB6B,IAAU9oC,UACV+oC,IAAGN,kBAAAA,mBAAAC,uBAAA,oBAHX,IAAIxB,MAOCD,gBAoGLH,UAAA,IAAAkC,EAAA,CAAA,SAAA,gBAAA,eAAA,SAAA,aAAA,MAAA,WAAA,SAAA,qBAAA,UAAA,oBAAA,MAAA,aAAA,OAAA,YAAA,iBAAA,4FAAA,SAAA,QAAA,gBAAA,gBAAA,aAAA,YAAA,cAAA,eAAA,aAAA,SAAA,cAAA,WAAA,iBAAA,WAAA,YAAA,WAAA,cAAA,QAAA,SAAA,YAAA,YAAA,QAAA,OAAAlC,QAAA,WAAA,OAAAkC,CAAA,IAAA,CA5BApxC,WAAW,CACP6f,EAAS,MAAQrZ,SACjB5F,qBAAAA,KAA0B1B,SAC3BowC,0CAAkC,GACrCtvC,WAAW,CACPsH,IACA1G,0BAA0BoF,UAC3BspC,0CAA2B,GAC9BtvC,WAAW,CACPsH,IACA1G,0BAA0B4F,SAC3B8oC,gBAAAA,gBAHHtvC,UAG8B,GAC9BA,WAAW,CACPsH,IACA1G,0BAA0B4F,SAC3B8oC,MAAAA,yBAA4B,GAC/BtvC,WAAW,CACPsH,IACA1G,qBAAAA,KAA0BoF,UAC3BspC,gCAHHtvC,UAGmC,GACnCA,WAAW,CACPsH,IACA1G,0BAA0BwF,SAC3BkpC,MAAAA,2BAA8B,GACjCA,MAAQtvC,WAAW,CACfqxC,wBACAzwC,qBAAAA,KAAgC,KACjC0uC,m17CC3GH,IAAM5O,OAAStsB,sBAAAA,MACFk9B,sBAAwB,SAACC,EAAmB7T,EAAS8T,EAAetgB,EAAUugB,EAAQC,EAAWC,EAAYC,EAAQC,EAAWC,EAAoBC,EAAeC,EAAgBC,EAAgBC,EAAeC,EAAWC,EAAgBC,EAAQC,EAAWC,EAAkBC,EAAWC,EAAiBC,EAAeC,EAAiBC,EAAkBC,EAAYC,EAAUC,EAAWC,EAAsBC,mBAAuBrC,OAAAA,EAAIC,kBAAAA,mBAAAC,uBACzba,YAAAA,OAAAA,OAAAA,+gFAAAA,OAAAA,OAAAA,OAAAA,OAAAA,urCAAAA,g4CAAAA,0IAAAA,OAAAA,4BAAAA,OAAAA,wSAAAA,OAAAA,OAAAA,0VAAAA,OAAAA,OAAAA,OAAAA,+GAAAA,iFAAAA,EAAaf,EAAIG,iBAAAA,kBAAAD,2OAICpQ,cACwFxP,4GAgB5DsgB,WAYFtgB,EAAAA,kBAmCAA,kBA+BAA,gBApGyY0f,YAmIzY1f,kCAiCAA,EAAAA,IApKyY0f,QAoL7ZmB,MAAAA,GAAAA,OAAamB,EAAbnB,IAAAA,cAAAmB,mBAAsDtC,EAAII,iBAAAA,kBAAAF,4yDAK9B5f,EAAAA,IAL0B0f,KAQ9CqC,EAAqBrC,EAAIuC,iBAAAA,kBAAArC,uBAGd5f,GAAAA,OAAAA,cAAAA,GACC,SAACtvB,UAAMoxC,EAAqBpxC,UAAAA,MAJnBgvC,OAeLmB,MAAAA,GAAAA,OAAaqB,EAAbrB,IAAAA,YAAmB,EAAnBqB,IAAAA,QAAAA,OAA6C,SAACC,EAAmBxkC,GAAKykC,IAAAA,EAAAA,EAAAA,EAAK1C,OAAAA,EAAI2C,iBAAAA,kBAAAzC,uBAAA,GAAA,OAAA,OAAA,OAAA,gBAO3F5f,EAAAA,IAPuF0f,QAQvFmB,MAAAA,GAAAA,OAAauB,EAAbvB,EAAAA,WAAmB,EAAnBuB,IAAAA,MAAAA,SAAuDzkC,cAC5FqiB,2BACAA,EAAAA,IAV4H0f,KAe/FyC,IAAAA,MAAAA,cAAmC,SAACG,EAAK3kC,GAAK4kC,IAAAC,EAAAD,EAAAA,EAAK7C,OAAAA,EAAI+C,iBAAAA,kBAAA7C,uBAAA,wCAAA,ilBAAA,OAAA,w4DAAA,yIAAA,OAAA,6QAAA,SACvDjiC,EAAY+hC,EAAIgD,iBAAAA,kBAAA9C,uBAAA,qCAAA,OAAA,gBAEZ5f,MAFQ0f,KAE6C6C,OAAAA,EAAIzB,EAAewB,iBAAkB,EAAjCC,IAAAA,MAChDD,IAAAA,eAAiCtiB,WAClCid,sBAAsBkF,IAAAA,QAAAA,MAA+BxkC,GAA/BwkC,cAWbG,MAAAA,OAAG,EAAHA,UAAkBK,OAAAA,EAEvB7B,EAAewB,MAAAA,OAAAA,EAAAA,IAAAA,eAAfK,IAAAA,MAA4CC,OAAAA,EAGpC9B,EAAewB,MAAAA,OAAG,EAAHA,IAAAA,aAAmB,EAAlCM,UAAwCC,OAAAA,EAKhD/B,EAAewB,MAAAA,SAAAA,EAAAA,mBAAiB,EAAhCO,IAAAA,MAEKP,MAAAA,SAAAA,EAAAA,YAAgBQ,OAAAA,EAIbhC,EAAewB,MAAAA,OAAAA,EAAAA,EAAAA,mBAAfQ,EAAAA,UAQJhG,oBAAoBwF,MAAAA,OAAG,EAAHA,WAEpB7F,sBAAsB6F,MAAAA,OAAAA,EAAAA,EAAAA,cAAoBtiB,YAG1CA,YAA+CsiB,MAAAA,SAAAA,gBAM/CA,MAAAA,OAAG,EAAHA,UAcArF,sBAAsBqF,MAAAA,OAAG,EAAHA,WAMtBxF,oBAAoBwF,MAAAA,SAAAA,EAAAA,aAEpB7F,sBAAsB6F,MAAAA,OAAAA,EAAAA,UAAkBtiB,YAGxCA,EAAAA,IA5E+B0f,MA4EgB4C,MAAAA,OAAG,EAAHA,IAAAA,qBAYvBtiB,WACtBwP,OAA8B8S,MAAAA,OAAG,EAAHA,EAAAA,yBAGlCtiB,wBACoDsiB,MAAAA,SAAAA,YAAgBA,MAAAA,SAAAA,IAAAA,cACpEtiB,wBAA+C+iB,OAAAA,EACzCZ,IAAAA,QADyCY,OACNA,EAAnCA,EAAAA,aAAkDplC,SAAM,EAAxDolC,mBAQZ/iB,EAAAA,IAvGuC0f,MAwGnCsD,OAAAA,EAAAb,YAAAa,OAAmCA,EAAnCA,IAAAA,MAAkDrlC,WAAlDqlC,aAAkEC,OAAAA,EAAId,IAAAA,QAAJc,OAAuCA,EAAnCA,IAAAA,MAAkDtlC,SAAM,EAAxDslC,oBAExEjjB,EAAAA,IA1GqC0f,QA2GvCwD,OAAAA,EAAAf,IAAAA,QAAAe,OAAmCA,EAAnCA,IAAAA,MAAkDvlC,SAAlDulC,EAAAA,IAAAA,aAA6EC,OAAAA,EAAAhB,IAAAA,QAAAgB,OAAmCA,EAAnCA,UAAkDxlC,SAAlDwlC,EAAAA,IAAAA,OAAgFzD,EAAI0D,iBAAAA,kBAAAxD,wCAAAyD,OAAAA,EAAYlB,IAAAA,QAAZkB,OAA+CA,EAAnCA,UAAkD1lC,SAAlD0lC,EAAAA,EAAAA,mBAW7KC,OAAAA,EAAAnB,EAAAA,oBAAAmB,OAAmCA,EAAnCA,IAAAA,MAAkD3lC,SAAM,EAAxD2lC,WAA8E5D,EAAI6D,iBAAAA,kBAAA3D,wCAAA4D,OAAAA,EAAYrB,IAAAA,QAAZqB,OAA+CA,EAAnCA,EAAAA,aAAkD7lC,SAAM,EAAxD6lC,WAAqF9D,EAAI+D,kBAAAA,mBAAA7D,uBAAqB,GAAA,eAG1M5f,0CACF0jB,OAAAA,EAAAvB,YAAAuB,OAAmCA,EAAnCA,EAAAA,aAAkD/lC,WAAlD+lC,iBAA2EC,OAAAA,EAAAxB,IAAAA,QAAAwB,OAAmCA,EAAnCA,EAAAA,aAAkDhmC,WAAlDgmC,IAAAA,OAA4EjE,EAAIkE,kBAAAA,mBAAAhE,uBAAA,sBAAAiE,OAAAA,EAAY1B,IAAAA,QAAZ0B,OAA+CA,EAAnCA,UAAkDlmC,WAAlDkmC,cAAmFrB,OAAAA,EAiBlPL,EAAAA,oBAjBkPK,OAiB/MA,EAAnCA,IAAAA,MAAkD7kC,WAAlD6kC,EAAAA,mBASRxiB,WH5PvC,SAAS8jB,eAAexB,GACvByB,IAAAA,EAAAA,QAAAA,EAAY,IAAI3/B,KAAKk+B,kBAGlBrF,uBAFO,IAAI74B,KAAKk+B,IAAAA,iBACayB,iBAGxC,CGuPsDD,CAAexB,YACrBtiB,MAtJqC0f,KAsJmB4C,IAAAA,UArKlDJ,IAmLmOtB,IAAAA,kDAG3N5gB,EAAAA,IA7MsB0f,YAgNtB1f,2BAGAA,kBAGAA,IAAAA,mBAKR+gB,EAAAA,KAAmB,SAACiD,OAAQC,aAAKvE,EAAIwE,kBAAAA,mBAAAtE,uBAEjCuE,kXH1QnD,SAASA,0BAA0BC,GAASC,IAAAA,EAAAA,QAAAA,IAAG/1C,wBAAAuW,IAAAvW,aAAAA,eAAMg2C,IAAGh2C,YAAAA,YAAAuW,IAAAvW,aAAAA,eAAMi2C,IAAGj2C,wBAAAuW,IAAAvW,aAAAA,eAAM0xB,IAAQ1xB,kBAAAA,kBAAAuW,EAClF,OAAQu/B,GACJ,YACI,WAAA,MAAUC,IAAV,MAAAxxC,gBAAmBmtB,EAAAA,IAAnB,MACJ,OAAA,KACI,iBAAUskB,UAAVzxC,cAAmBmtB,mBACvB,UACI,iBAAUukB,yBAASvkB,kBACvB,QACI,UGiQ8CmkB,CAA0BH,MAAAA,OAAQ,EAARA,IAAAA,MAAmBnD,MAAAA,GAAAA,OAAa2D,EAAb3D,gBAAA2D,EAAAA,IAAAA,MAA4B3D,MAAAA,GAAAA,OAAa4D,EAAb5D,IAAAA,cAAA4D,IAAAA,MAA4B5D,MAAAA,GAAAA,OAAaoD,EAAbpD,EAAAA,WAAAoD,EAAAA,UAA6BjkB,GAGlIkd,aAAa8G,IAAAA,MAAexC,EAAexhB,GAI3Ckd,aAAa8G,UAAcxC,EAAexhB,GAI1Ckd,aAAa8G,UAAgBA,UAAcxC,EAAexhB,cAK1DA,wBAEQkd,aAAa8D,EAAeQ,EAAexhB,GAC1CuhB,EAKCE,EACuDb,cAChEA,SAAsB5gB,EAAAA,eAAuDA,MAvPpD0f,cAyP9B1f,EAAAA,IAzP8B0f,KA2P1BxC,aAAa+D,EAAYD,EAAeQ,EAAexhB,GAA8BuhB,eASzGvhB,EAAAA,IAxbqZ0f,YA0bxX1f,IAAAA,kCAM+DkhB,qBAGtEA,EAAAA,2BACD,kBAAMQ,mBAcf1hB,gCAEkCA,0DApduW0f,KAsd7YwB,IAAAA,QAAAA,MAA2CxB,EAAIgF,kBAAAA,mBAAA9E,uBAGCuB,0pBAAAA,MAAAA,SAAAA,EAAAA,KAAY,SAACwD,GAASjF,IAAAA,EAAAA,EAAAA,OAAAA,EAAIkF,kBAAAA,mBAAAhF,uBACT,GAAA,+YAAA,WAAA,OAAA,2NAAA,kBAAM+B,EAAWgD,EADZjF,QAGdiF,IAAAA,oBACUnV,OAAUmV,MAAAA,OAAAA,EAAAA,UAAwBA,MAAAA,SAAAA,UACCA,MAAAA,OAAAA,EAAAA,kBAOrGzD,EAAAA,WAAAA,MAA2CxB,EAAImF,kBAAAA,mBAAAjF,uBAAA,+BAAA,cAGhB5f,kCAIvBmhB,MAAAA,OAAM,EAANA,EAAAA,KAAY,SAACwD,GAASjF,IAAAA,EAAAA,EAAAA,OAAAA,EAAIoF,kBAAAA,mBAAAlF,uBACsB+E,UAAAA,OAAAA,OAAAA,OAAAA,OAAAA,OAAAA,OAAAA,mDAAAA,iCACjB3kB,MAFT0f,KAGkBiF,EAAAA,uBAET3kB,EAAAA,SACS2kB,mBAET3kB,EAAAA,IART0f,KASkBiF,mBAET3kB,EAAAA,IAXT0f,KAYkBiF,EAAAA,uBAET3kB,EAAAA,IAdT0f,KAekB2B,EAAoBhB,EAAoB7T,aAC5EmY,UAAkBjF,EAAIqF,kBAAAA,mBAAAnF,uBACS5f,GAAAA,OAAAA,OAAAA,OAAAA,SAAAA,KAEb2kB,IAAAA,MAAyBA,MAAAA,SAAAA,2BAQH3kB,MAlCT0f,KAoC/B0B,MAawDF,8BAG1DA,GAAuC,kBAAMQ,IAAAA,KAzhBsVhC,YAuiBzY1f,kBAEkCA,+CACtCkhB,MAAAA,KAA4BxB,EAAIsF,kBAAAA,mBAAApF,uBAAA,GAAA,wqFAAA,cAGrB5f,wEAe4BA,EAAAA,IAlBX0f,YAmBqB1f,kDAnBrB0f,KAoB4B4B,iBAcjBthB,sBAEnBshB,IAAAA,MACAA,iBAgBmBthB,IAAAA,aACUshB,uBA0B7DthB,MA1nByZ0f,QA8nB7ZmB,MAAAA,GAAAA,OAAaoE,EAAbpE,IAAAA,YAAmB,EAAnBoE,IAAAA,QAAAA,OAAsDvF,EAAIwF,kBAAAA,mBAAAtF,uBAE1CiB,iBAAAA,MAAAA,GAAAA,OAAasE,EAAbtE,gBAAAsE,EAAAA,IAAAA,QAAAA,OAA6C,SAAChD,EAAmBxkC,kBAAU+hC,EAAI0F,kBAAAA,mBAAAxF,uBAAA,GAAA,2PAAA,OAAA,OAAA,k/FAG6BjiC,2BAChGA,cAAmBqiB,EAAAA,IAAnBriB,WAAqEqiB,WAA2CqlB,OAAAA,EAMxGvE,EAAeqB,MAAAA,GAAAA,OAAiBmD,EAAjBnD,IAAAA,YAAAmD,EAAAA,iBAAfD,EAAAA,UAAmEE,OAAAA,EAGnEzE,EAAeqB,MAAAA,GAAAA,OAAiBqD,EAAjBrD,EAAAA,gBAAAqD,EAAAA,qBAAfD,UAOIpD,MAAAA,GAAAA,OAAiBsD,EAAjBtD,IAAAA,YAA0B,EAA1BsD,EAAAA,cAGA3I,oBAAoBqF,MAAAA,GAAAA,OAAiBuD,EAAjBvD,IAAAA,YAAAuD,EAAAA,WAUNlW,OAA8B2S,MAAAA,GAAAA,OAAiBwD,EAAjBxD,IAAAA,YAAAwD,EAAAA,UAiB5CxD,MAAAA,GAAAA,OAAiByD,EAAjBzD,EAAAA,cAAAyD,EAAAA,UAGA9I,oBAAoBqF,MAAAA,GAAAA,OAAiB0D,EAAjB1D,IAAAA,YAAA0D,EAAAA,oBAWpB7lB,WAGAyc,sBAAsB0F,MAAAA,GAAAA,OAAiB2D,EAAjB3D,IAAAA,YAA0B,EAA1B2D,EAAAA,cAA2C9lB,UAMjEA,mBH7lB/D,SAAS+lB,WAAWC,GAChBA,IAAAA,EAAAA,QAAAA,OAAAA,MAAAA,OAAI,EAAJA,IAAAA,OAAU,SAAA1D,kBAAOA,IAAAA,MAAwBA,IAAAA,KAAzC0D,IAAAA,WAEX,CG6lBsED,CAAW5D,MAAAA,GAAAA,OAAiB8D,EAAjB9D,EAAAA,cAAA8D,EAAAA,EAAAA,WAWjCvG,EAAIwG,kBAAAA,mBAAAtG,uBAAA,GAAA,iBAYtB5f,WAKAkd,aAAa+D,EAAWO,EAAexhB,GAA8BuhB,WAMrEvhB,WAKAkd,aAAa8D,EAAeQ,EAAexhB,GAA8BuhB,WASzEvhB,WAKAkd,aAAa+D,EAAYD,EAAeQ,EAAexhB,GAA8BuhB,EAQ/Db,GACD,SAAChwC,kBAAMkxC,EAASlxC,IAAAA,QAAAA,kBAIjBsvB,EAAAA,IA9wByY0f,KA+wB/Xc,SACNxgB,yDAEJA,IAAAA,UAAoCugB,GAG/CG,GAAUC,EAAYjB,EAAIyG,kBAAAA,mBAAAvG,uBAAA,sjCAUO5f,EAAAA,IAVX0f,YAWf1f,qDAOQ,WAAM6hB,OAAAA,GAvyB+XnC,mBAyyBjZwB,GAAoCA,MAApCA,cACrClhB,kBACAA,wBAgB+C0gB,EAAmBA,GAAoB,SAAChwC,kBAAMkxC,EAASlxC,IAAAA,QAAAA,kBAGzFsvB,EAAAA,SACUwgB,WACwDxgB,EAAAA,IAh0BuW0f,YAi0Bza1f,aAAoCugB,GAE3CG,GAAUC,EAAYjB,EAAI0G,kBAAAA,mBAAAxG,uBAAA,+BAUmC5f,8BAAwDA,eAS9Gkd,aAAa+D,EAAYD,EAAeQ,EAAexhB,GAA8BuhB,GAE7E,kBAAMM,MAElBX,YAAoCA,oBAC7ClhB,MADSkhB,cAETlhB,EAAAA,IA51Bsb0f,KAu2BrZY,EAv2B/B,ECLP+F,UAAAC,QAAA,SAAAA,QAAAC,EAAAC,GAAA,IAAAC,EAAAC,UAAA,OAAAJ,QAAA,SAAAK,EAAAC,GAAA,OAAAH,EAAAE,GAAA,IAAA,GAAAJ,EAAAC,EAAA,CAAA,MAAA,IAAA,IAAAK,EAAAP,QAAAQ,EAAAJ,YAAA,IAAA,GAAA,SAAA3jC,SAAA8jC,EAAA,OAAA9jC,SAAA8jC,EAAA,MAAA,GAAA9jC,SAAA8jC,EAAA,MAAA,GAAA9jC,SAAA8jC,EAAA,MAAA,GAAA9jC,SAAA8jC,EAAA,MAAA,GAAA9jC,SAAA8jC,EAAA,MAAA,IAAA9jC,SAAA8jC,EAAA,MAAA,IAAA9jC,SAAA8jC,EAAA,MAAA,GAAA9jC,SAAA8jC,EAAA,MAAA,EAAA9jC,SAAA8jC,EAAA,MAAA,KAAA9jC,SAAA8jC,EAAA,MAAA,KAAA9jC,SAAA8jC,EAAA,MAAA,IAAA9jC,SAAA8jC,EAAA,MAAA,IAAA,MAAAC,EAAA10C,KAAA00C,EAAA9jC,QAAA,CAAA,MAAA+jC,GAAAD,EAAA10C,KAAA00C,EAAA9jC,QAAA,CAAA,EAAA,GAAO,IAAMgkC,OAAS,mBAAA,uBAAA,+CAAA,qBAAA,WAMX,cAAA,mBAAA,iCAAA,wGAAA,mBAAA,WAaD,cAAA,+FAAA,mBAAA,iCAAA,iDAaA,oDAAA,iCAAA,mBAAA,+FAAA,cAaG,cAAA,mBAAA,gIAAA,mBAAA,0BAaF,kEAAA,mBAAA,mBAAA,mBAAA,iFAaF,4BAAA,mBAAA,mBAAA,mBAAA,iCAAA,mBAAA,4DAaG,gKAAA,mBAAA,YAaD,cAAA,6GAAA,iCAAA,8CAaC,kEAAA,mBAAA,mBAAA,qFAAA,WAaF,cAAA,yIAAA,mBAAA,mBAAA,YAaC,6DAAA,mBAAA,mBAAA,mBAAA,0FAaE,qFAAA,mBAAA,mBAAA,mBAAA,oEAaH,cAAA,iCAAA,2HAAA,mBAAA,WAaA,cAAA,qFAAA,mBAAA,mBAAA,8DAaD,+CAAA,mBAAA,iCAAA,2FAaC,cAAA,2HAAA,mBAAA,mBAAA,mBAAA,aAaE,uEAAA,mBAAA,mBAAA,mBAAA,mBAAA,mBAAA,mBAAA,mBAAA,aAaA,sHAAA,mBAAA,sEAaA,cAAA,mBAAA,mBAAA,gIAAA,4BAaC,cAAA,+FAAA,iCAAA,mBAAA,iDAaH,oDAAA,mBAAA,mBAAA,mBAAA,+FAAA,WAaA,cAAA,mBAAA,kHAAA,mBAAA,mBAAA,mBAAA,yqFClRH,SAASC,aAAaC,GACzBC,IAAAA,EAAAA,QAEA,IACI,IAEU12B,EAFJ22B,EAASryC,aAAWmyC,MACtBG,QAAOD,OACP,KAIA,OAJM32B,EAAOtX,SAAAA,qBACbnL,eAAeo5C,YAAgB,SAAAvL,WAAE5sC,GAAgBq4C,EAAAhK,eAAAzB,SAAXtrC,EAAK+2C,KACvC72B,EAAAA,QAAAA,QAAsB,KAAA5d,OAAgB5D,GAAOsB,EADjDvC,GASR,CAHA,MAAO0C,GAAAA,CAmBS,SAAV62C,EAAWC,EAAKC,WAGZp4C,GAFAq4C,EAAM3kC,SAASykC,EAAAA,oBACfG,EAAMrsC,OAAAA,WAAkBmsC,GACpBnsC,OAAAA,UAAcA,gBAAaosC,OAAaC,KAC5Cl2C,EAAI6J,OAAAA,UAAcA,gBAAcosC,UAAoBC,IACpDhyC,EAAI2F,KAAAA,QAAcA,oBAAaosC,GAAcC,gCAC1Bt4C,QAAYoC,MAAUkE,iBAApC,SAtBf,CAyBe,SAATiyC,EAAUJ,EAAKC,GACXC,IAAAA,EAAAA,EAEAr4C,GAFAq4C,EAAM3kC,SAASykC,sBACfG,EAAMrsC,kBAAkBmsC,GACpBnsC,OAAAA,UAAcA,OAAAA,SAAaosC,OAAaC,KAC5Cl2C,EAAI6J,iBAAcA,KAAAA,OAAcosC,UAAoBC,IACpDhyC,EAAI2F,iBAAcA,oBAAaosC,GAAcC,gBACnD,gBAAyBt4C,QAAYoC,MAAUkE,KAApC,sBAKTkyC,EAAS,IACLN,EAHJO,GAlCeZ,EAkCQA,SAhCrBA,WACOA,GAGGF,OAAOE,IAOdF,SAAAA,SAJI3nB,cA6BJkoB,EAAQO,UACRP,EAAQO,UACRP,EAAQO,UACRP,EAAQO,UACRA,MACAF,EAAOE,UACPF,EAAOE,UACPF,EAAOE,UACPF,EAAOE,UACPF,EAAOE,OA/ClB,IAkDMr3B,EAAOtX,WAAAA,MAEbnL,SAAAA,MAAe65C,YAAgB,SAAAE,WAAE94C,GAAgBotC,EAAAiB,eAAAyK,SAAXx3C,EAAK8rC,KACvC5rB,UAAAA,6BAAsB,MAAgBxhB,GAAOsB,ynFC1E9C,mCAAMy3C,WAAa,CACtB,gBAAA,wBAAA,MACA,gCAAA,MACA,6CACA,4CACA,8CACA,wCAAA,MACA,gBAAA,wBAAA,MACA,gBAAA,wBAAA,MACA,gBAAA,qDACA,8CACA,8CACA,8CACA,+DACA,gBAAA,wBAAA,MACA,gBAAA,mDACA,8CACA,qCACA,8CACA,gBAAA,qDACA,gBAAA,wBAAA,MACA,gBAAA,wBAAA,MACA,qEACA,wCACA,2CACA,gBAAA,qDACA,gBAAA,wBAAA,MACA,gBAAA,wBAAA,MACA,iEACA,uCACA,wEACA,wCAAA,MACA,gBAAA,gDACA,qCAAA,MACA,gBAAA,8BACA,8CACA,0CACA,wCAAA,MACA,+BAAA,MACA,gBAAA,wBAAA,MACA,gBAAA,8BACA,4DACA,8CACA,4CACA,qCAAA,MACA,kCAAA,MACA,+BAAA,MACA,gBAAA,mDACA,gBAAA,wBAAA,MACA,gBAAA,wBAAA,6HCvCJC,UAAA,IAAAC,EAAA,CAAA,QAAA,OAAA,QAAA,QAAA,WAAA,OAAA,wCAAA,uCAAA,qBAAA,qBAAA,WAAA,QAAA,uBAAA,WAAA,QAAA,OAAA,OAAA,UAAA,8BAAA,sDAAA,QAAA,oBAAA,MAAA,qBAAA,eAAA,iBAAA,OAAA,kBAAA,eAAA,iBAAA,mBAAA,WAAA,iBAAA,OAAA,aAAA,aAAA,YAAA,OAAA,cAAA,WAAA,gBAAA,OAAA,MAAA,kBAAA,WAAA,cAAA,OAAA,cAAA,kBAAA,SAAA,OAAA,cAAA,0CAAA,iDAAA,mBAAA,gBAAA,eAAA,QAAA,YAAA,oBAAA,iBAAA,MAAA,2CAAA,aAAA,oBAAA,eAAA,OAAA,0FAAA,kBAAA,oBAAA,8FAAA,aAAA,YAAA,qBAAA,UAAA,SAAA,kBAAA,iBAAA,OAAA,cAAA,WAAA,eAAA,uBAAA,gBAAA,OAAA,iCAAA,YAAA,YAAA,YAAA,MAAA,SAAA,UAAA,YAAA,QAAA,gBAAA,gBAAA,YAAA,SAAA,kBAAA,gBAAA,cAAA,gBAAA,SAAA,MAAA,SAAA,qBAAA,cAAA,iBAAA,MAAA,aAAA,OAAA,SAAA,YAAA,YAAA,SAAA,mBAAA,iBAAA,QAAA,eAAA,gBAAA,cAAA,UAAA,kBAAA,oBAAA,iBAAA,YAAA,cAAA,sFAAA,WAAA,OAAA,QAAA,WAAA,gBAAA,SAAA,MAAA,SAAA,cAAA,gBAAA,QAAA,aAAA,YAAA,cAAA,oBAAA,WAAA,cAAA,gBAAA,YAAA,QAAA,aAAA,cAAA,OAAA,gBAAA,OAAA,SAAA,sBAAA,cAAA,SAAA,UAAA,iBAAA,cAAA,UAAA,YAAA,MAAA,cAAA,OAAA,WAAA,QAAA,gBAAA,SAAA,MAAA,iBAAA,mBAAA,mBAAA,UAAA,MAAA,OAAA,UAAA,SAAA,SAAA,YAAA,UAAA,MAAA,SAAA,WAAA,cAAA,SAAA,YAAA,WAAA,gBAAA,UAAA,gBAAA,QAAA,cAAA,gBAAA,aAAA,oBAAA,MAAA,2DAAA,WAAA,SAAA,QAAA,OAAA,aAAA,wBAAA,UAAA,cAAA,OAAA,WAAA,qBAAA,OAAAD,QAAA,WAAA,OAAAC,CAAA,IAAA,0UAAA,IAAMC,cAAgB,IAAIzY,cACpB0Y,cAAgB,IAAIrN,cACtBsN,gBAAaC,eAAAA,MA2Cb,MAqXqBC,EADpBC,EAAAA,IAvCAC,IAvGAC,EA3EkBC,EAAAA,IAXHC,IADfC,EAvDAC,EAAAA,EAxFDC,EAAAC,QAAA,SAAAX,EAAYY,EAAgBC,OAAgB75B,mBAAA8hB,qBAAAkX,IACxCh5B,EAAAgvB,WAAAxvC,KAAAw5C,YACsBY,EACtB55B,UAAsB65B,EACtB75B,EAAAA,qBACAA,EAAAA,YACAA,aACAA,EAAAA,QACAA,iBACAA,aACAA,EAAAA,SACAA,EAAAA,iBACAA,EAAAA,sBACAA,EAAAA,qBACAA,eACAA,IAAAA,SACAA,aACAA,EAAAA,cACAA,IAAAA,SACAA,aACAA,aACAA,IAAAA,MAAqB,KACrBA,UAAuB,KACvBA,aACAA,UAAmB,GACnBA,EAAAA,eAAsB,GACtBA,EAAAA,eAAsB,GACtBA,IAAAA,QACAA,YACAA,YACAA,IAAAA,SACAA,IAAAA,SACAA,IAAAA,SACAA,aACAA,YACAA,IAAAA,SACAA,EAAAA,QAAe,iDAKfA,UAAa,GACbA,aACAA,aACAA,UAAgB,KAChBA,EAAAA,SACAA,IAAAA,aACAA,EAAAA,eACAA,EAAAA,mBACAA,EAAAA,eAAsB84B,cACtB94B,EAAAA,eAAsB+4B,cAAc/4B,SACvCivB,UAAA+J,EA9F2CpmC,KA8F3CivB,aAAAmX,EAAA,CAAA,gBAtFD,kBACWx5C,YAqFV,MAnFD,SAAa0B,GACH44C,IAIIC,EAJJD,EAAAA,EAAAA,EAAWt6C,KAAAw6C,UAEbx6C,cAEMu6C,EADY,IAAIE,gBAAgB9+B,SAAAA,eAChB++B,MAAAA,QACDH,IAAkBv6C,aAEnCA,aAAiBu6C,GAKjBv6C,KAAAw6C,UAAiB94C,EAGZ1B,eACDA,KAAA26C,wBACA36C,OAAA,WAMRA,aAAiB0B,EAGrB1B,KAAA2H,gBAAA,KAA+B2yC,KAClC,gBACD,sBACWt6C,OAAA,cAA2BA,KAAAmxB,aAA+BnxB,KAAA46C,iBACpE,kBAqDD,mBACIjL,cAAA6J,SAAAx5C,KAAA2vC,CAAA,IACA3vC,OAAA,MAAeA,KAAA66C,OACf76C,uBAEAA,iBACH,kBACD,eAOUu6C,MALDv6C,gBAKCu6C,EADY,IAAIE,gBAAgB9+B,0BAChB++B,gBAGlB16C,OAAA,MAAiBu6C,EAEjBv6C,OAAA,mBAEMA,eAENA,eACAA,oBAEP,kBACD,mBACU86C,EAAa,IAAIC,IAAIp/B,eAAAA,MACrBq/B,EAAS,IAAIP,gBAAgBK,WAEnCE,YAAAA,KAAuBh7C,OAAA,OAEjBi7C,aAAYH,uBAAuBE,aACzCr/B,OAAAA,UAAAA,MAA4B,MAAQs/B,KAEvC,OAAA,YAAAhB,EAAAA,EAAAA,EAAA5b,kBAAAC,+BACD,SAAAC,EAAmBmR,OAAkBwL,aAAA5c,+BAAA,SAAAE,GAAA,IAAA,IAAA2c,EAAAC,IAAA,OAAA5c,UAAAA,WAAA,OAEjC,OADAmR,cAAA6J,SAAAx5C,KAAA2vC,CAAA,CAAmBD,IACnBlR,EAAAA,OACMx+B,eAAiB,OACvBA,OAAA,QACAA,KAAAoyC,UAAiBpyC,eACjBA,oBACIA,OAAA,QACAo4C,aAAap4C,cACbA,gBAIAA,eACMq7C,EAAkB/wC,4BACxB+wC,aACAA,EAAAA,KAAuBr7C,KAAAs7C,eACvBhxC,yBAA0B+wC,MAIpBA,EAAkB/wC,0BACxB+wC,WACAA,8FACA/wC,WAAAA,cAA0B+wC,SAG1Br7C,KAAA+iB,MACazY,WAAAA,wBACbsX,KAAqC5hB,OAAA,OACxC,QAAA,YAAA,OAAAw+B,eAAAD,EAAAv+B,UACJ,SA/BiB4+B,UAAAqb,IAAAA,MAAAj6C,KAAAP,cAAA,qBAgClB,SAAQiwC,GACJC,cAAA6J,SAAAx5C,KAAA2vC,CAAA,CAAcD,GAjCA,GAkCjB,8BACD,WAAmBI,IAAAA,EAAAA,EAAAA,EAAA9vC,KACfutC,mBAA2BvtC,KAAAu7C,SAA3BhO,MAA8C,SAACiO,GACvCA,IAEMC,EAFND,EAAAA,EAAAA,IAAAA,QACA1L,WAAa4L,OAAAA,EAAAF,IAAAA,YAAAE,EAAAA,IAAAA,WACPD,EAAYv1C,KAAAA,OAAWy1C,OAAAA,EAAAH,IAAAA,YAAAG,EAAAA,EAAAA,eAC7B7L,IAAAA,OAAgB2L,MAAAA,OAAS,EAATA,eAChB3L,IAAAA,MAAuB2L,MAAAA,OAAS,EAATA,UACvB3L,UAAa2L,MAAAA,GAAAA,OAASG,EAATH,IAAAA,YAAAG,EAAAA,IAAAA,OAA0B,SAAC9F,WAC9B+F,EAAc1C,oBAAgB,SAAA2C,GAAQA,IAAAA,EAAAA,EAAAA,OAAAA,uBAA4BhG,MAAAA,SAAAA,IAAAA,MAAAA,cAApDqD,IACpB3Z,OAAAA,eAAAA,eAAA,CAAA,EACOsW,GAAI,CAAA,EAAA,WACG+F,MAAAA,SAAAA,YAAyB,sBAI3C/L,IAAAA,MAAgB5pC,OAAAA,OAAW61C,OAAAA,EAAAP,EAAAA,gBAAa,EAAbO,kBAC3BjM,EAAAA,yBAfRvC,MAkBH,6BAAAyM,EAAA3b,kBAAAC,+BACD,SAAAS,QAAAic,MAAA1c,OAAAA,wBAAAA,OAAA,SAAAa,GAAA,IAAA,IAAA6c,EAAAC,IAAA,OAAA9c,UAAAA,WAAA,UACQ6b,EAAS,IAAIP,gBAAgB9+B,wBACjC3b,OAAA,MAAe,WACAg7C,EAAAA,MAAAA,wBACIA,EAAAA,MAAAA,wBACAA,EAAAA,sBAEfrd,iBAA0B39B,KAAA29B,UAAA,OAA8B39B,aAAAk8C,cAA0B,OAAA/c,EAAAA,OAC5En/B,aAAmBA,cADyDm/B,IAAAA,QAAA,MAC5C,OAAA,UAAA,OAAAA,eAAAJ,EAAA/+B,UAE7C,WAVeg6C,OAAAA,EAAAA,MAAAh6C,KAAAP,UAAV08C,IAAU,uBAAApC,EAAA1b,kBAAAC,+BAWhB,SAAAuB,EAAoBlC,GAAOW,IAAAA,EAAAA,EAAAA,OAAAA,wBAAAA,OAAA,SAAA2B,GAAA,cAAA,OAAAA,UAAAA,IAAAA,OAAA,UAClBjgC,KAAAo6C,eAAAnP,MAAwBhL,EAAAA,OAAA,MAAA,OAAAA,YACnBjgC,uBAAyB,OAEnCA,OAAA,MAAuB29B,GAAS,OAAA,YAAA,OAAAsC,eAAAJ,EAAA7/B,UACnC,SALkB6+B,UAAAkb,EAAAA,MAAA/5C,KAAAP,cAAA,mBAAAq6C,EAAAA,EAAAA,EAAAzb,kBAAAC,+BAMnB,SAAA6E,EAAwBxF,GAAO,IAAA6d,EAAAY,EAAAC,EAAAC,EAAAC,SAAAje,sBAAAA,MAAA,SAAAgF,GAAA,IAAA,IAAAkZ,EAAArC,UAAA,OAAA7W,UAAAA,WAAA,OACJ,OAAvBtjC,gBAAuBsjC,YACOtjC,aAAoB29B,GAAQ,OAArC,OAAjB8e,EAAiBnZ,UAAAA,EAAAA,OAAAA,YAECtjC,KAAAq6C,uBAAkCoC,EAAmBz8C,OAAA,OAAa,OAA3E,OAAHw7C,EAAGlY,UAAAA,IAAAA,SACkBtjC,OAAA,cAAwBw7C,EAAAA,WAAc,WAA3DkB,EAAYpZ,WACdqZ,EAAUz2C,aAAWw2C,YAOE,OALnBL,EAAWn2C,aAAWy2C,mBAC1B38C,KAAAgyC,cAAqBqK,EACrBr8C,aAAmBq8C,UACnBr8C,KAAA48C,eACA58C,KAAA68C,gBAAuBF,IAAAA,MACvB38C,eAAuBsjC,aACjBtjC,eAPasjC,EAAAA,QAAA,MAOU,QAC7BwZ,OAAAA,EAAI98C,OAAA,QAAA88C,EAAAA,wBAAAA,QACIP,EAAQD,OAAAA,EAAGt8C,mBAAoB,EAApBs8C,IAAAA,sBACft8C,eAAA,OAAmB,SAAC81C,WAChBA,UAAgBA,IAAAA,QAAkByG,CADtC,KAIJv8C,eACAA,aAAqBA,eACrBA,aAAiBA,eACjBA,OAAA,MAAmBA,KAAAoyC,UAAiBpyC,OAAA,WAChCo8C,OAAAA,EAAIp8C,mBAAgB,EAApBo8C,YACAp8C,eAAA,gBACH,QAELA,gBAAwBsjC,aAAA,MAAA,WAAAA,aAAAA,EAAAA,GAAAA,mBAGpBA,EAAAA,KAAAA,MACyB,OAAzBtjC,aAAA+8C,KAAyBzZ,IAAAA,SACnBtjC,uBAFcsjC,aAAA,MAEW,QAAA,OAAAA,IAAAA,SACzBtjC,OAAA,MAAuB29B,GAAQ,QAAA2F,EAAAA,QAAA,MAAA,QAGrCtjC,OAAA,SACAA,eAAA,gBAA0F,QAAA,UAAA,OAAAsjC,EAAAA,UAAAH,EAAAnjC,KAAA,CAAA,QA7CnFs+B,KAgDlB,SA1CsBqB,GAAAma,OAAAA,EAAAA,EAAAA,MAAA95C,KAAAP,UAAjBu9C,IAAiB,kBA2CvB,WACQxmC,IAAAA,EAAAA,EAAAA,IACJ,OAAAxW,aAAAsJ,SAAyB,SAAC2zC,WACtBA,mBAA2B,SAACC,GACxB1mC,GAAO0mC,EAAP1mC,UAFR,IAKOA,IACV,OAAA,WACD,eAAkB2mC,MAAAC,EAAAp9C,KACVq9C,IACJF,OAAAA,EAAIn9C,eAAJm9C,EAAAA,QAAAA,SAAoC,SAACG,EAAKxuC,GAClCwuC,IAEIC,EAFJD,EAAAA,EAAAA,EAAAA,eAEIC,EAAWH,YAAAA,eAAgC,SAACE,0BAAQA,WAAuBA,IAAAA,OAAaD,CAA7ED,OAEXG,UAAsBD,EAEtBF,UAAAA,gBAAkCtuC,MAEtCuuC,KAGAC,UAAYxuC,OAGvB,uBAAA+qC,EAAAxb,kBAAAC,wBAAAA,OACD,SAAAmF,QAAAwJ,EAAAuO,EAAAgC,MAAAlf,OAAAA,+BAAA,SAAAoF,GAAA,IAAA,IAAA+Z,EAAAC,IAAA,OAAAha,UAAAA,WAAA,OAWO,OAVCuJ,EAAe,GACnB0Q,OAAAA,iBAAAA,OAAkBA,EAAlBA,YAAAA,EAAAA,oBAAAA,SAAsD,SAACC,WACnDA,EAAAA,yBAA+B,SAACnK,WACvBxG,EAAAA,SAAsBwG,EAAAA,gBACvBxG,IAAAA,MAAkBwG,WAEjBxG,EAAAA,SAAsBwG,YACvBxG,EAAAA,KAAkBwG,IAAAA,MAL1BmK,GADJD,IASGja,IAAAA,QAAAA,EAAAA,OAEiBqJ,qBAAqBE,EAAcjtC,OAAA,YAAuBA,cAAa,QAAnFw7C,EAAG9X,EAAAA,MACH8X,cACAx7C,KAAAiyC,eAAsBuJ,IAAAA,MACtBx7C,aAAmBw7C,IAAAA,sBACbqC,SAAqBrC,YAAAA,UAAP,KAA2Ct1C,OAAAA,MAAWs1C,YAAAA,OAAwBA,IAAAA,MAAAA,SAClGx7C,OAAA,MAAsB69C,EAAAA,YACtB79C,KAAAsuC,aAAoBuP,EAAAA,iBAEpB79C,KAAAoM,eACAoxC,OAAAA,EAAIhC,YAAAgC,YACAx9C,OAAA,MAAaw7C,EAAAA,UAAAA,WACTx7C,gBACAo4C,aAAap4C,OAAA,OACbA,KAAA2H,iBAGX+7B,EAAAA,QAAA,MAAA,QAAAA,aAAAA,EAAAA,GAAAA,aAGoB,QAAA,OAAA,KAAA,OAAAA,IAAAA,WAAAD,EAAAzjC,KAAA,CAAA,QAjC5Bs+B,KAmCA,kBAlCqBub,UAAA75C,KAAAP,cAAA,yBAmCtB,eAKmIq+C,kBAH3HC,OAAAA,EAAI/9C,eAAJ+9C,OAAkBA,EAAlBA,gBAAAA,EAAAA,oBAAA,OAA0DC,EAAIh+C,eAA9D,OAAgFg+C,EAAlBA,YAAAA,EAAAA,uBAAAA,SACvDC,OAAAA,EAAIj+C,KAAAgyC,gBAAJiM,OAAkBA,EAAlBA,IAAAA,QAAAA,OAAwBA,EAAxBA,2BAAAA,EAAAA,iBAEFC,OAAAA,uBAAAA,OAAkBA,EAAlBA,gBAAwB,EAAxBA,EAAAA,sBAAAA,QAAAA,OAAwDC,EAAIn+C,eAA5Dk+C,OAA8EC,EAAlBA,YAAAA,sBAC1DL,OAAAA,EAAI99C,eAAJ89C,OAAkBA,EAAlBA,YAAAA,OAAwBA,EAAxBA,IAAAA,SAAAA,wBAAAA,EAAAA,EAAAA,aAEXM,OAAAA,EAAOp+C,eAAPo+C,OAAyBA,EAAlBA,EAAAA,WAAwB,EAAxBA,YAAAA,OAAqD,SAACC,EAAOT,WACzDS,OAAAA,IAAST,MAAAA,GAAAA,OAASU,EAATV,gBAAAU,EAAAA,sBAEvB,kBACD,eAMSC,EACGv8C,EANJuN,MACAivC,EAAU,sBAEVjvC,EADAkvC,OAAAA,EAAIz+C,eAAJy+C,OAAkBA,EAAlBA,YAAAA,eAAAA,UAA4DC,OAAAA,uBAAAA,OAAkBA,EAAlBA,kBAAAA,IAAAA,MAAAA,QACxDC,OAAAA,EAAG3+C,OAAA,QAAH2+C,OAAqBA,EAAlBA,IAAAA,QAAHA,OAA2BA,EAAxBA,EAAAA,qCAAAA,EAAAA,IAAAA,OAGH38C,EAAS,GACbw8C,EAAAA,SAAgB,SAAAjJ,GACZvzC,UAAY,SAAWuzC,oBAE3BqJ,OAAAA,iBAAAA,OAAkBA,EAAlBA,EAAAA,OAAAA,IAAAA,eAAsD,SAAChB,GACnDA,IAAAA,EAAAA,EAAAA,IAAAA,uBAA8C,SAACzI,GACvCqJ,IACMK,EADNL,EAAAA,EAAAA,IAAAA,MAAiBrJ,aACX0J,EAAY78C,IAAAA,OAAY,SAAAxB,UAAKA,YAAc20C,EAAAA,cAE7C0J,EAAAA,MAAkB1J,IAAAA,MAClB0J,EAAAA,KAAiB1J,EAAAA,gBAMOn/B,KAApC8oC,OAAAA,EAAI9+C,KAAAgyC,gBAAJ8M,OAAkBA,EAAlBA,IAAAA,YAAwB,EAAxBA,aACA98C,EAASA,IAAAA,OAAc,SAAAxB,GAAKA,IAAAA,EAAAA,EAAAA,OAAAA,cAAnBwB,IAAAA,UAE2BgU,KAApC+oC,OAAAA,EAAI/+C,OAAA,QAAJ++C,OAAkBA,EAAlBA,IAAAA,YAAwB,EAAxBA,aACA/8C,EAASA,WAAc,SAAAxB,iBAAKA,SAAnBwB,KAGTA,OADqCgU,KAArCuoC,OAAAA,EAAIv+C,eAAJu+C,OAAkBA,EAAlBA,IAAAA,YAAwB,EAAxBA,EAAAA,QACSv8C,WAAc,SAAAxB,kBAAKA,IAAAA,kBAEzBwB,GAEXhC,aAAsBuP,IACzB,kBACD,mBACIvP,cAA2BA,eAC9B,kBACD,SAAiBg/C,YACbh/C,KAAAqyC,eAAsB2M,aAClBA,UACIC,OAAAA,EAAIj/C,OAAA,YAAM,EAAVi/C,EAAAA,UACAj/C,KAAAk/C,eAAmB,SAAAp4C,UAAKA,gBACxB9G,kBAAA,YAGX,OAAA,WACD,SAAW81C,WACP91C,OAAA,eAAmB,SAAA8G,UAAKA,EAAAA,WAAxB,IACAgvC,aACA91C,KAAA2H,kBACH,kBACD,SAASw3C,GACLn/C,aAAcm/C,IACjB,uBAAAvF,EAAAvb,kBAAAC,+BACD,SAAAsF,EAAqBr0B,OAAIwzB,aAAAzE,+BAAA,SAAAwF,GAAA,cAAA,OAAAA,IAAAA,MAAAA,IAAAA,OAAA,OAAA,OAAAA,YACO9jC,qBAAwBkG,aAAeqJ,IAAM,OAAtD,OAAbwzB,EAAae,IAAAA,MAAAA,IAAAA,eACZ,aACUf,IAChB,OAAA,YAAA,OAAAe,eAAAF,EAAA5jC,UACJ,SALmB2jC,UAAAiW,UAAA55C,KAAAP,cAAA,kBAMpB,eAGcu7C,MADNh7C,OAAA,QACMg7C,EAAS,IAAIP,mBACnBO,aAA0Bh7C,cAC1B2b,OAAAA,4BAA0B3b,0BAAsBg7C,EAAAA,aAGhDr/B,iBAAAA,MAAuB3b,OAAA,QAE9B,kBACD,eAaco/C,MAZ4DC,EAAAr/C,KADhEgwC,IAAKvwC,wBAAAuW,IAAAvW,aAAAA,eAAgB0O,KAAAA,IAAO1O,UAAAA,aAAAuW,IAAAvW,aAAAA,eACgB0wC,KAAAA,MAAW1wC,wBAAAuW,IAAAvW,eAAAA,aACvD6/C,EAAQt/C,OAAA,sBAEVs/C,GACAA,UAAY,OACRtP,UACA7hC,cACAgiC,iBAIJnwC,KAAAmwC,cACMiP,EAAWG,aAAY,mBACzBF,gBACIA,IAAAA,QACAG,cAAcJ,GACdC,EAAAA,0BAIf,mBAAA1F,EAAAA,EAAAA,EAAAtb,kBAAAC,wBAAAA,OACD,SAAA6F,QAAAsY,EAAAC,EAAAC,EAAA8C,aAAAnhB,wBAAAA,OAAA,SAAA8F,GAAA,cAAA,OAAAA,UAAAA,EAAAA,MAAA,OAO8B,OAN1BpkC,OAAA,SACIuP,EAAO,WACIvP,OAAA,MAAA0/C,wBACI1/C,KAAAqyC,sBACPryC,OAAA,OAEcokC,YAAAA,IAAAA,QAEQpkC,OAAA,MAAoBuP,GAAK,OAAlC,OAAjBktC,EAAiBrY,IAAAA,MAAAA,YACHpkC,OAAA,QAAA,MAA8By8C,EAAmBz8C,KAAAu7C,SAAa,OAAvE,OAAHC,EAAGpX,UAAAA,IAAAA,SACkBpkC,aAAA2/C,IAAwBnE,WAAc,QAA3DkB,EAAYtY,WACduY,EAAUz2C,aAAWw2C,IAErBC,aACA38C,OAAA,SAEMg7C,EAAS,IAAIP,gBAEfz6C,OAAA,OACAg7C,qBAA0Bh7C,cAExBy/C,EAAczE,YACpBr/B,eAAAA,KAAuB8jC,aAAiB9C,sBAAqB8C,GAAgB9C,IAAAA,QAI7E38C,oBAA4B28C,qBAC5B38C,KAAA4xC,eACHxN,aAAA,MAAA,WAAAA,IAAAA,SAAAA,EAAAA,GAAAA,IAAAA,eAGGA,EAAAA,KAAAA,aAAwBA,EAAAA,KAAAA,MACC,OAAzBpkC,OAAA,MAAA+8C,KAAyB3Y,aACnBpkC,OAAA,gBAFsCokC,aAAA,MAEb,QAAA,OAAAA,aACzBpkC,KAAA4/C,kBAAsB,QAAA,OAAA,KAAA,OAAAxb,IAAAA,WAAAD,EAAAnkC,KAAA,CAAA,QApCvCs+B,KAuCA,WAtCoBqb,OAAAA,EAAAA,EAAAA,MAAA35C,KAAAP,UAAfmgD,IAAe,OAAA,gBAAAlG,EAAArb,kBAAAC,+BAuCrB,SAAAiG,QAAAsb,aAAAvhB,+BAAA,SAAAqG,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,UACI3kC,KAAA8/C,eACA9/C,gBACKA,cAAW2kC,YAAA,MAAA,OAAAA,oBAAA,sBAGZ,MAGkD,OADlD3kC,KAAA+/C,4BACkDpb,YAAAA,MAHxBA,YAAA,MAGwB,iBAGlD,eAC0H,OAA1H3kC,eAAA,gBAA0H2kC,IAAAA,cADrFA,aAAA,MACqF,WAG1H3kC,OAAA,iBACM6/C,EAAYG,OAAAA,EAAGhgD,OAAA,YAAU,EAAVggD,IAAAA,OAAiB,SAAClK,UAASA,EAAAA,YAChD91C,qBAAyC6/C,MAAAA,OAAY,EAAZA,YAExC7/C,KAAAo6C,eAAAnP,MAAwBtG,aAAA,MAAA,OAAAA,EAAAA,QACnB3kC,OAAA,gBAAyB,QAAA,OAAA2kC,aAE7B3kC,eAAsB,QAAA,YAAA,OAAA2kC,EAAAA,UAAAJ,EAAAvkC,KA9DXs+B,KA+DpB,kBAxBcob,UAAA15C,KAAAP,cAAA,OAAA,WAyBf,SAAqBwgD,WACjBjgD,OAAA,MAAgBigD,EAChBjgD,eAEAA,OAAA,QACAA,iBACH,kBACD,WACWuxC,IAAAA,EAAAA,EAAAA,OAAAA,sBAAsBvxC,KAAAwxC,kBAAwBxxC,aAAcA,aAAoBA,KAAAmxB,SAAenxB,aAAYA,aAAeA,OAAA,MAAiBA,aAAaA,OAAA,MAAgBA,aAAyBA,OAAA,MAAoBA,aAAqBA,aAAqBA,OAAA,MAAoBA,aAAgBA,aAAqBA,aAAYA,OAAA,MAAeA,KAAAkgD,gBAAsBlgD,KAAAmgD,SAAengD,KAAAogD,iBAAuBpgD,OAAA,MAAmBA,eAAA,MAA0BA,MAAOA,OAAA,cAA2BA,MAAOA,qBAAqBA,MAAOA,eAAA,MAAmBA,MAAOA,OAAA,MAAAya,KAAoBza,MAAOA,qBAA+BA,MAAOA,OAAA,MAFtmB,IAte2CoT,EAA/BqmC,oBACU,CACnBtI,IAAU9oC,UACV+oC,IAAGN,gBAAAA,iBAAAC,uBAAA,oBAIN0I,kUAqeLx5C,WAAW,CACP6f,EAAS,MAAQ7Z,UACjBpF,yBAA0B1B,SAC3Bq6C,kDAA8C,GACjDv5C,WAAW,CACP6f,EAAS,MAAQrZ,SACjB5F,qBAAAA,KAA0B1B,SAC3Bq6C,wBAAAA,kBAAiC,GACpCv5C,WAAW,CACP6f,EAAS,MAAQrZ,SACjB5F,qBAAAA,KAA0B1B,SAC3Bq6C,cAAAA,gCAA2C,GAC9Cv5C,WAAW,CACP6f,EAAS,MAAQrZ,SACjB5F,0BAA0B1B,SAC3Bq6C,kDAAiC,GACpCv5C,WAAW,CACP6f,EAAS,MAAQrZ,SACjB5F,qBAAAA,KAA0B1B,SAC3Bq6C,8CAAqC,GACxCv5C,WAAW,CACP6f,EAAS,MAAQrZ,SACjB5F,qBAAAA,KAA0B1B,SAC3Bq6C,wBAAAA,0BAAmC,GACtCv5C,WAAW,CACP6f,EAAS,MAAQrZ,SACjB5F,qBAAAA,KAA0B1B,SAC3Bq6C,cAAAA,8BAAkC,GACrCv5C,WAAW,CACP6f,EAAS,MAAQrZ,SACjB5F,0BAA0B1B,SAC3Bq6C,mDAA0C,GAC7Cv5C,WAAW,CACP6f,EAAS,MAAQ7Z,UACjBpF,0BAA0B1B,SAC3Bq6C,kDAA+C,GAClDv5C,WAAW,CACP6f,EAAS,MAAQ7Z,UACjBpF,yBAA0B1B,SAC3Bq6C,cAAAA,oBAHHv5C,UAGiD,GACjDA,WAAW,CACP6f,EAAS,MAAQrZ,SACjB5F,0BAA0B4F,QAC1B5F,qBAAAA,KAAgC,CAAC4F,UAClC+yC,cAAAA,yBAAqC,MACxCv5C,WAAW,CACPsH,IACA1G,yBAA0B4F,SAC3B+yC,wBAAAA,qBAAoC,GACvCv5C,WAAW,CACPsH,IACA1G,yBAA0BoF,UAC3BuzC,wBAAAA,wBAAuC,GAC1Cv5C,WAAW,CACPsH,IACA1G,0BAA0BoF,UAC3BuzC,wBAAAA,gBAHHv5C,UAGsC,GACtCA,WAAW,CACPsH,IACA1G,yBAA0BoF,UAC3BuzC,wBAAAA,gBAHHv5C,UAGyC,GACzCA,WAAW,CACPsH,IACA1G,0BAA0BoF,UAC3BuzC,wBAAAA,gBAHHv5C,UAG2C,GAC3CA,WAAW,CACPsH,IACA1G,yBAA0B1B,SAC3Bq6C,wBAAAA,gBAHHv5C,UAG6C,GAC7CA,WAAW,CACPsH,IACA1G,yBAA0B1B,SAC3Bq6C,cAAAA,iCAA4C,GAC/Cv5C,WAAW,CACPsH,IACA1G,0BAA0BoF,UAC3BuzC,cAAAA,8BAA+C,GAClDv5C,WAAW,CACPsH,IACA1G,0BAA0BiD,QAC3B01C,cAAAA,8BAAwC,GAC3Cv5C,WAAW,CACPsH,IACA1G,yBAA0BiD,QAC3B01C,kDAA2C,GAC9Cv5C,WAAW,CACPsH,IACA1G,yBAA0BiD,QAC3B01C,oDAA2C,GAC9Cv5C,WAAW,CACPsH,IACA1G,yBAA0BwF,SAC3BmzC,kDAA0C,GAC7Cv5C,WAAW,CACPsH,IACA1G,0BAA0BwF,SAC3BmzC,cAAAA,8BAAsC,GACzCv5C,WAAW,CACPsH,IACA1G,0BAA0BwF,SAC3BmzC,kDAAwC,GAC3Cv5C,WAAW,CACPsH,IACA1G,0BAA0B4F,SAC3B+yC,cAAAA,8BAA2C,GAC9Cv5C,WAAW,CACPsH,IACA1G,qBAAAA,KAA0B4F,SAC3B+yC,kDAAuC,GAC1Cv5C,WAAW,CACPsH,IACA1G,qBAAAA,KAA0B4F,SAC3B+yC,cAAAA,8BAAyC,GAC5Cv5C,WAAW,CACPsH,IACA1G,qBAAAA,KAA0BoF,UAC3BuzC,kDAAwC,GAC3Cv5C,WAAW,CACPsH,IACA1G,yBAA0BwF,SAC3BmzC,kDAAsC,GACzCv5C,WAAW,CACPsH,IACA1G,qBAAAA,KAA0BoF,UAC3BuzC,cAAAA,6BAAwC,GAC3Cv5C,WAAW,CACPsH,IACA1G,yBAA0B1B,SAC3Bq6C,kDAAoC,GACvCv5C,WAAW,CACPsH,IACA1G,yBAA0BiD,QAC3B01C,kDAAkC,GACrCv5C,WAAW,CACPsH,IACA1G,qBAAAA,KAA0B4F,SAC3B+yC,kDAAqC,GACxCv5C,WAAW,CACPsH,IACA1G,0BAA0B4F,SAC3B+yC,wBAAAA,6BAA4C,GAC/Cv5C,WAAW,CACPsH,IACA1G,yBAA0B1B,SAC3Bq6C,wBAAAA,gBAHHv5C,UAGwC,GACxCA,WAAW,CACPsH,IACA1G,0BAA0B4F,SAC3B+yC,wBAAAA,mBAAkC,GACrCv5C,WAAW,CACPsH,IACA1G,0BAA0B4F,SAC3B+yC,wBAAAA,gBAHHv5C,UAG2C,GAC3CA,WAAW,CACPsH,IACA1G,0BAA0BwF,SAC3BmzC,wBAAAA,0BAAyC,GAC5Cv5C,WAAW,CACPsH,IACA1G,yBAA0B4F,SAC3B+yC,wBAAAA,4BAA2C,GAC9CA,cAAgBv5C,WAAW,CACvBqxC,oBACAzwC,+BAAgC,CAACggC,cAC7BqL,iBACLsN", "x_google_ignoreList": [0, 1, 2, 3, 4, 6, 10, 11, 12]}