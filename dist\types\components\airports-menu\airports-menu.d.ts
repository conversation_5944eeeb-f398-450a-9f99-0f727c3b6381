import { LitElement } from "lit";
export declare class AirportsMenu extends LitElement {
    static styles: import("lit").CSSResult[];
    tripType: string;
    language: string;
    AirportsDefault: any[];
    AirportsDefaultFiltered: any[];
    searchTerm: string;
    private searchTimeout;
    constructor();
    connectedCallback(): void;
    searchAirPorts(event: Event): Promise<void>;
    continentClick: (continentCode: string) => void;
    itemParentClick: (code: string) => void;
    airportClick: (airport: any) => void;
    render(): import("lit-html").TemplateResult<1>;
}
