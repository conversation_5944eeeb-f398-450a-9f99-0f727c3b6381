import { LitElement, PropertyValues } from "lit";
export declare class FlightSearch extends LitElement {
    static styles: import("lit").CSSResult[];
    mode: string;
    ApiKey: string;
    color: string;
    font: string;
    googleFontsUrl: string;
    vertical: boolean;
    isChild: boolean;
    isSetColorTitle: boolean;
    redirect_uri: string;
    _isShowBox: boolean;
    autoLanguageParam: boolean;
    private _language;
    private _hasCheckedURL;
    get language(): string;
    set language(value: string);
    _isShowBottom: boolean;
    showLanguageSelect: boolean;
    private _ApiKey;
    isMobile: boolean;
    departure: string;
    arrival: string;
    AirportsDefault: never[];
    isReady: boolean;
    departureAirport: string;
    arrivalAirport: string;
    isRT: boolean;
    adult: number;
    child: number;
    infant: number;
    passengerString: string;
    selectedDates: Date[];
    isSubmitForm: boolean;
    _vertical: boolean;
    AirportListSelected: any[];
    departureCode: string;
    arrivalCode: string;
    private _userToggledBox;
    private datePickerInstance;
    constructor();
    connectedCallback(): void;
    private checkLanguageFromURL;
    private updateURLWithLanguage;
    getQueryDefault(): void;
    getAirports(): Promise<void>;
    firstUpdated(): Promise<void>;
    protected updated(_changedProperties: PropertyValues): void;
    openDatePicker(): void;
    initDatePicker(): void;
    hidePrevMonthButton(instance: any): void;
    checkDevice(): void;
    convertToLunar(date: any): string;
    handleAirportClick(event: CustomEvent): void;
    toggleDropdown(event: Event): void;
    private toggleShowBox;
    clickAirportItem(airport: any, type: any): void;
    changeTypeTrip(isRT: boolean): void;
    searchFlights(): void;
    validateForm(): boolean;
    changeQuantity(event: Event, type: string, isIncrease: boolean): void;
    getValueDisplayQuantity(): string;
    swapAirport(event: Event): void;
    render(): import("lit-html").TemplateResult<1> | undefined;
    handleLanguageChange(newLang: string): void;
}
