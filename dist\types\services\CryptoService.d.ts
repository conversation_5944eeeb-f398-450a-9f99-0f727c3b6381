export declare class CryptoService {
    private keyPair;
    encryptionKeyPair: any;
    constructor();
    gra(): Promise<{
        publicKey: string;
        privateKey: string;
    }>;
    ga(): Promise<CryptoKey>;
    ea(aesKey: CryptoKey, data: string): Promise<{
        encryptedData: ArrayBuffer;
        iv: Uint8Array;
    }>;
    irpu(pem: string): Promise<CryptoKey>;
    irpr(pem: string): Promise<CryptoKey>;
    era(publicKey: CryptoKey, aesKey: CryptoKey): Promise<ArrayBuffer>;
    dra(privateKey: CryptoKey, encryptedData: any): Promise<ArrayBuffer>;
    he(publicKeyPem: string, data: string): Promise<string>;
    hd(privateKeyBem: string, encryptedText: string): Promise<string>;
    bts(buffer: ArrayBuffer): string;
    da(aesKeyBuffer: ArrayBuffer, encryptedData: Uint8Array): Promise<string>;
    encrypt(publicKey: string, plainText: string): Promise<string>;
    decrypt(privateKey: string, encryptedText: string): Promise<string>;
    private importPublicKey;
    private importPrivateKey;
    private arrayBufferToPEM;
    private arrayBufferToBase64;
    private base64ToArrayBuffer;
    private pemToArrayBuffer;
    gr(): Promise<{
        ep: string;
        sp: string;
        ss: string;
        s: string;
    }>;
    textToBase64(text: string): string;
    private sc;
    gc(name: string): string | null;
    rc(name: string): void;
    ra(): void;
    spu(): Promise<void>;
    dsk(): Promise<string>;
    eda(data: string): Promise<string>;
    dda(encryptedData: string): Promise<string>;
    csi(): Promise<void>;
    iih(): Promise<void>;
    ch(): boolean;
    wk(): Promise<void>;
    gdi(): Promise<string>;
}
