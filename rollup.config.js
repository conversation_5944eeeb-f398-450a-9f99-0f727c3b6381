import resolve from "@rollup/plugin-node-resolve";
import commonjs from "@rollup/plugin-commonjs";
import typescript from "@rollup/plugin-typescript";
import postcss from "rollup-plugin-postcss";
import url from "@rollup/plugin-url";
import image from "@rollup/plugin-image";
import babel from "@rollup/plugin-babel";
import terser from "@rollup/plugin-terser";
import obfuscator from "rollup-plugin-obfuscator";
import { promises as fs } from "fs";
import path from "path";
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';

function removeWhitespacePlugin() {
    return {
        name: "remove-whitespace",
        writeBundle: async (options) => {
            const outputDir = options.dir || "dist"; // Thư mục đầu ra

            // Hàm đệ quy để đọc tất cả file trong thư mục và thư mục con
            async function processDirectory(directory) {
                const files = await fs.readdir(directory, { withFileTypes: true });

                for (const file of files) {
                    const fullPath = path.join(directory, file.name);

                    if (file.isDirectory()) {
                        await processDirectory(fullPath); // Đệ quy nếu là thư mục
                    } else if (file.name.endsWith(".js")) {
                        try {
                            let content = await fs.readFile(fullPath, "utf-8");
                            // Xóa khoảng trắng dư thừa
                            content = content.replace(/\s{2,}/g, " ");
                            // Xóa dòng trống
                            content = content.replace(/\n\s*\n/g, "\n");

                            await fs.writeFile(fullPath, content, "utf-8");
                            console.log(`✅ Cleaned whitespace in: ${fullPath}`);
                        } catch (error) {
                            console.error(`❌ Error processing ${fullPath}:`, error);
                        }
                    }
                }
            }

            await processDirectory(outputDir); // Bắt đầu xử lý từ thư mục gốc
        },
    };
}
const components = [
    // "flight-search",
    // "trip-selection",
    // "trip-passenger",
    // "trip-payment",
    // "trip-result",
    // "trip-repayment",
    // "trip-available",
    "index"
];
export default components.map(component => ({
    input: component === "index" ? "src/index.ts" : `src/components/${component}/index.ts`,

    output: {
        dir: "dist",
        format: "esm",
        entryFileNames: (chunkInfo) => {
            if (component === "index") {
                return "index.js";  // Xuất ra dist/index.js
            }
            const relativePath = path.relative("src/components", chunkInfo.facadeModuleId);
            const dirname = path.dirname(relativePath);
            return `${dirname}/index.js`;
        },
        inlineDynamicImports: true,         // ⚡ Gộp tất cả dependencies vào file chính
        preserveModules: false,             // ⚡ Tắt tách module riêng
        sourcemap: true,                // ⚡ Bật sourcemap cho tất cả các file
        preserveModules: false,
    },
    preserveEntrySignatures: "strict",
    treeshake: true, // Loại bỏ file không dùng
    plugins: [
        resolve({
            moduleDirectories: ["node_modules"], // Đảm bảo tìm module trong node_modules
            extensions: [".js", ".ts"], // Hỗ trợ các file .js và .ts
        }),
        commonjs(),
        typescript({
            tsconfig: "./tsconfig.json", // Đảm bảo bạn có file tsconfig.json
            sourceMap: true, // Tắt source map nếu không cần
            declaration: true, // Xuất file .d.ts
            declarationDir: "dist/types", // Thư mục chứa file .d.ts
        }),

        babel({
            babelHelpers: "bundled",
            extensions: [".js", ".ts"],
            exclude: "node_modules/**", // Bỏ qua các file trong node_modules
        }),

        postcss({
            extract: false, // <-- Quan trọng: Đặt thành false
            inject: false,  // <-- Quan trọng: Đặt thành false (để Lit tự quản lý việc inject)
            minimize: true,
            config: "./postcss.config.cjs",
            plugins: [tailwindcss, autoprefixer],
        }),

        // url({
        //     include: ["**/*.svg", "**/*.png", "**/*.jpg", "**/*.gif", "**/*.worker.ts"],
        //     limit: 0,
        //     fileName: "[dirname][hash][extname]",
        //     destDir: "dist",
        // }),
        // image(),

        // obfuscator({ // 🔥 Chạy trước Terser
        //     compact: true,
        //     controlFlowFlattening: true, // Tắt để tránh phá vỡ cấu trúc Lit
        //     stringArray: true,
        //     stringArrayThreshold: 1, // Mã hóa MỌI chuỗi (kể cả dùng 1 lần)
        //     stringArrayIndexShift: true, // Thêm nhiễu vào index mảng
        //     rotateStringArray: true, // Xáo trộn mảng chuỗi
        //     transformObjectKeys: true, // Giữ nguyên key object (tránh lỗi Lit properties)
        //     renameProperties: true, // Không đổi tên properties (bảo vệ @property, @query)
        //     identifierNamesGenerator: 'mangled', // Đổi tên biến thành _0x...
        // }),

        // terser({
        //     compress: { drop_console: true },
        //     format: { comments: false },
        //     mangle: false,
        //     keep_fnames: true,
        //     keep_classnames: true
        // }),
        // removeWhitespacePlugin()
    ],
}));
