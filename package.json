{"name": "flight-search-ui", "version": "1.0.32", "description": "A library for flight search functionality.", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/types/index.d.ts", "scripts": {"dev": "rollup -c --watch", "build:css": "tailwindcss -i ./src/styles/styles.css -o ./dist/tailwind.css --watch", "build": "rollup -c", "preview": "vite preview", "start": "vite"}, "keywords": ["flight-search-ui", "flightSearch", "ngocmai"], "author": "rae<PERSON>", "license": "MIT", "homepage": "", "files": ["dist", "LICENSE", "README.md"], "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@webcomponents/webcomponentsjs": "^2.8.0", "crypto-js": "^4.2.0", "flatpickr": "^4.6.13", "lit": "^3.3.1", "rollup-plugin-web-worker-loader": "^1.7.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/plugin-transform-template-literals": "^7.27.1", "@babel/preset-env": "^7.28.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-html": "^2.0.0", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.4", "@rollup/plugin-url": "^8.0.2", "@tailwindcss/postcss": "^4.1.11", "@types/crypto-js": "^4.2.2", "autoprefixer": "^10.4.21", "html-minifier-terser": "^7.2.0", "javascript-obfuscator": "^4.1.1", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "postcss-nested": "^7.0.2", "postcss-preset-env": "^10.2.4", "postcss-simple-vars": "^7.0.1", "rollup": "^4.46.2", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-lit-css": "^5.0.2", "rollup-plugin-node-polyfills": "^0.2.1", "rollup-plugin-obfuscator": "^1.1.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "tailwindcss": "^3.4.17", "tslib": "^2.8.1", "typescript": "^5.9.2", "vite": "^6.3.5"}}