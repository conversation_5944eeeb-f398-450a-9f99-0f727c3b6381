import { css, LitElement, PropertyValues, unsafeCSS } from "lit";
import { CryptoService } from "../../services/CryptoService";
import { FlightService } from "../../services/FlightService";
import { customElement, property, state } from "lit/decorators.js";
import { getAirportInfoByCode, getFeatures } from "../../services/WorldServices";
import { Modal } from "../modal/modal";
import { TripRePaymentTemplate } from "./trip-repayment-template";
import { setnmtColors } from "../../services/ColorService";
import { BANK_LOGOS } from "../../share/data/bank-logos";
import styles from '../../styles/styles.css';

const cryptoService = new CryptoService();
const flightService = new FlightService();

@customElement("trip-repayment")
export class TripRePayment extends LitElement {
    static styles = [
        unsafeCSS(styles),
        css`
        :host {
          font-family: var(--nmt-font, 'Roboto', sans-serif);
        }`
    ];

    @property({ type: Boolean }) autoFillOrderCode = false;
    @property({ type: String }) mode = "online";
    @property({ type: String }) googleFontsUrl = "";
    @property({ type: String }) font = "";
    @property({ type: String }) termsUrl = 'terms-and-policies';
    @property({ type: String }) ApiKey = '';
    @property({ type: String }) color = "";
    @property({ type: String }) uri_searchBox = "";
    @property({ type: Boolean }) showLanguageSelect = false;
    @property({ type: Boolean }) autoLanguageParam = false; // Bật/tắt tính năng tự động thêm language param

    private _language = "vi";
    private _hasCheckedURL = false; // Flag để tránh kiểm tra URL nhiều lần

    @property({ type: String })
    get language(): string {
        return this._language;
    }

    set language(value: string) {
        const oldValue = this._language;

        // Chỉ kiểm tra URL nếu autoLanguageParam được bật
        if (this.autoLanguageParam) {
            const urlParams = new URLSearchParams(window.location.search);
            const languageParam = urlParams.get('language');

            if (languageParam && languageParam !== this._language) {
                // URL có language parameter - luôn ưu tiên URL
                this._language = languageParam;
                console.log('Language overridden from URL parameter:', this._language);
            } else {
                // URL không có language parameter - sử dụng giá trị được set
                this._language = value;
                console.log('Language set from property:', this._language);
                // Tự động thêm vào URL nếu chưa có
                if (!this._hasCheckedURL) {
                    this.updateURLWithLanguage();
                    this._hasCheckedURL = true;
                }
            }
        } else {
            // Không sử dụng URL parameter, chỉ set giá trị trực tiếp
            this._language = value;
            console.log('Language set from property (autoLanguageParam disabled):', this._language);
        }

        this.requestUpdate('language', oldValue);
    }

    get currencySymbolAv(): string{
        return this.convertedVND === 1 || this.language === 'vi'  ? '₫' : this.currencySymbol;
    }

    @state() private _ApiKey: string = '';
    @state() private _isLoading: boolean = false;
    @state() private _agree: boolean = true;
    @state() private _isSubmit: boolean = false;
    @state() private _isNotValid: boolean = false;
    @state() private _orderDetails: any = null;
    @state() private _orderAvailable: any = null;
    @state() private _isShowDetailsTrip: boolean = false;
    @state() private _passengers: any[] = [];
    @state() private _inforAirports: any[] = [];
    @state() private _pricePaxInfor: any[] = [];
    @state() private _servicePrice: number = 0;
    @state() private _sumPrice: number = 0;
    @state() private _totalPrice: number = 0;
    @state() private _paymentMethod: string = '';
    @state() private titleModal: string = '';
    @state() private contentModal: string = '';
    @state() private isCountDown: boolean = false;
    @state() private countdown: number = 0;
    @state() private isShowModal: boolean = false;
    @state() private request: any = {
        OrderCode: '',
        PhoneCustomer: '',
        EmailCustomer: ''
    };

    @state() private banks: any[] = [];
    @state() private bankNote: string = '';
    @state() private transferContent: string = '';
    @state() private cashInfo: any = null;
    @state() private agent: string = '';
    @state() private displayMode: 'total' | 'perPassenger' = 'total';
    @state() private convertedVND: number = 1;
    @state() private currencySymbol: string = '₫';

    constructor(
        private _cryptoService: CryptoService,
        private _flightService: FlightService
    ) {
        super();

        this._cryptoService = cryptoService;
        this._flightService = flightService;
    }

    connectedCallback(): void {
        super.connectedCallback();
        this._ApiKey = this.ApiKey;
        this.removeAttribute("ApiKey");

        // Kiểm tra URL ngay khi component được kết nối, trường hợp không có attribute language
        this.checkLanguageFromURL();
    }

    private checkLanguageFromURL(): void {
        // Chỉ kiểm tra URL nếu autoLanguageParam được bật
        if (!this.autoLanguageParam) {
            console.log('autoLanguageParam disabled, skipping URL check');
            return;
        }

        const urlParams = new URLSearchParams(window.location.search);
        const languageParam = urlParams.get('language');

        if (languageParam) {
            // URL có language parameter - set giá trị từ URL
            this._language = languageParam;
            console.log('Language initialized from URL parameter:', this._language);
            this.requestUpdate('language');
        } else if (!this._hasCheckedURL) {
            // URL không có language parameter - tự động thêm vào URL với giá trị mặc định
            this.updateURLWithLanguage();
            this._hasCheckedURL = true;
        }
    }
    private updateURLWithLanguage(): void {
        const currentUrl = new URL(window.location.href);
        const params = new URLSearchParams(currentUrl.search);

        // Thêm hoặc cập nhật parameter language
        params.set('language', this._language);

        // Cập nhật URL mà không reload trang
        const newUrl = `${currentUrl.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newUrl);
        console.log('URL updated with language parameter:', newUrl);
    }

    protected async firstUpdated(_changedProperties: PropertyValues): Promise<void> {
        super.firstUpdated(_changedProperties);

        // this.checkDevice();
        await this.getRequest();
        this.getPricePax();
        this._sumPrice = this.getSumPrice();
        this.loadPaymentValue();

        if (this.color !== "") {
            setnmtColors(this.color);
            this.requestUpdate();
        }

        console.log(this.googleFontsUrl);
        // Handle Google Fonts
        if (this.googleFontsUrl) {
            const googleFontsLink = document.createElement('link');
            googleFontsLink.rel = 'stylesheet';
            googleFontsLink.href = this.googleFontsUrl;
            document.head.appendChild(googleFontsLink);
        } else {
            // Default font if no Google Fonts URL provided
            const googleFontsLink = document.createElement('link');
            googleFontsLink.rel = 'stylesheet';
            googleFontsLink.href = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap';
            document.head.appendChild(googleFontsLink);
        }

        console.log('font', this.font);
        if (this.font !== "") {
            const root = document.documentElement;
            root.style.setProperty('--nmt-font', this.font);
        }
    }

    protected updated(_changedProperties: PropertyValues): void {
        super.updated(_changedProperties);
    }

    loadPaymentValue(): void {
        getFeatures('cash;credit', this._ApiKey).then((res: any) => {
            if (res.isSuccessed) {
                this.agent = res.resultObj?.agent || '';
                const bankModel = JSON.parse(res.resultObj?.credit || '{}');
                this.bankNote = bankModel?.note || '';
                this.transferContent = bankModel?.transferContent;

                this.banks = bankModel?.banksInfo?.map((bank: any) => {
                    const matchedLogo = BANK_LOGOS.find(logo => logo.name.toLowerCase() === bank?.bankName.toLowerCase());
                    return {
                        ...bank,
                        logoPath: matchedLogo?.logoPath || null,
                        selected: false
                    };
                });

                this.cashInfo = JSON.parse(res.resultObj?.cash || '{}');
                this.setPaymentMethod('bank-transfer')
            }
        });

    }

    async getRequest() {
        var params = new URLSearchParams(window.location.search);
        this.request = {
            OrderCode: params.get('OrderCode') || '',
            PhoneCustomer: params.get('PhoneCustomer') || '',
            EmailCustomer: params.get('EmailCustomer') || ''
        };
        if (this.request.OrderCode && this.request.PhoneCustomer && this.request.EmailCustomer) {
            await this.AvailableTrip(this.request);
        }
    }
    async AvailableTrip(request: any) {
        if (!this._cryptoService.ch()) {
            await this._cryptoService.spu();
        }
        this.CallAvailableTrip(request);
    }
    async CallAvailableTrip(request: any) {
        this._isLoading = true;

        var payloadsEncrypted = await this.RequestEncrypt(request);

        try {
            const res = await this._flightService.AvailableTrip(payloadsEncrypted, this._ApiKey);
            const resDecrypted = await this._cryptoService.dda(res.resultObj);
            var resJson = JSON.parse(resDecrypted);
            if (resJson.IsSuccessed) {
                var noteData = JSON.parse(resJson.ResultObj.Note);
                this._orderDetails = noteData;

                this._passengers = noteData.paxList;
                this._isNotValid = true;
                this._orderAvailable = resJson.ResultObj;
                this.formatPassenger();
                await this.getInforAirports();
                if (this._orderAvailable?.PaymentMethod.includes('bank-transfer')) {
                    var bankName = this._orderAvailable?.PaymentMethod.split('_')[1];
                    this.banks.forEach((bank) => {
                        bank.selected = bank.bankName === bankName;
                    });
                }

                this.getPricePax();
                this._servicePrice = this.getSumServicePrice();
                this._sumPrice = this.getSumPrice();
                this._totalPrice = this._sumPrice + this._servicePrice;

                if (this._orderAvailable?.Status !== 0) {
                    this.openModal('Thông báo', 'Đơn hàng đã được thanh toán hoặc hết hạn thanh toán', false);
                }
            }
            this._isLoading = false;
        } catch (error: any) {
            if (error.status === 403) {
                this._cryptoService.ra();
                await this._cryptoService.spu();
                await this.CallAvailableTrip(request);
            } else {
                this._isLoading = false;
                this.openModal('Thông báo', 'Đơn hàng đã được thanh toán hoặc hết hạn thanh toán', false);
            }
        }
    }
    getSumServicePrice(): number {
        var sum = 0;
        this._passengers.forEach((passenger: any) => {
            passenger.baggages.forEach((baggage: any) => {
                sum += baggage.Price;
            });
        });
        return sum;
    }
    formatPassenger() {
        var indexInfant = 0;
        this._orderDetails?.paxList.forEach((pax: any, index: number) => {
            if (pax.type == 'infant') {
                //get pax adult index same index infant
                var paxAdult = this._orderDetails.paxList.find((pax: any) => pax.type == 'adult' && pax.index == indexInfant);
                if (paxAdult) {
                    paxAdult.withInfant = pax;
                    //remove pax infant
                    this._orderDetails.paxList.splice(index, 1);
                }
                indexInfant++;
            } else {
                pax.index = index;
            }
        });
    }
    async getInforAirports() {
        var airportsCode: string[] = [];
        this._orderDetails?.full?.InventoriesSelected.forEach((inventory: any) => {
            inventory.segment.Legs.forEach((leg: any) => {
                if (!airportsCode.includes(leg.DepartureCode)) {
                    airportsCode.push(leg.DepartureCode);
                }
                if (!airportsCode.includes(leg.ArrivalCode)) {
                    airportsCode.push(leg.ArrivalCode);
                }
            });
        });
        try {
            var res = await getAirportInfoByCode(airportsCode, this.language || 'vi', this._ApiKey);
            if (res.isSuccessed) {
                this._inforAirports = res.resultObj;
                this.displayMode = res.feature.displayMode || 'total';
                const currencyObj = typeof res.feature.currency === 'string' ? JSON.parse(res.feature.currency) : res.feature.currency;
                this.currencySymbol = currencyObj.symbol || '₫';
                this.convertedVND = currencyObj.convertedVND || 1;
            }
            if (this.mode === "online") {
                if (res.feature?.color) {
                    this.color = res.feature.color;
                    if (this.color !== "") {
                        setnmtColors(this.color);
                        this.requestUpdate();
                    }
                }
            }
        } catch (error: any) {
            console.error(error);
        }
    }
    getSumPrice(): number {
        //check combine
        if (this._orderDetails?.full?.InventoriesSelected.length === 1 && this._orderDetails?.full?.InventoriesSelected[0].combine) {
            return this._orderDetails?.full?.InventoriesSelected[0].inventorySelected?.SumPrice || 0;
        } else if (this._orderDetails?.full?.InventoriesSelected.length > 1 && this._orderDetails?.full?.InventoriesSelected[0].combine) {
            return this._orderDetails?.full?.InventoriesSelected[1].inventorySelected?.SumPrice || 0;
        }
        return this._orderDetails?.full?.InventoriesSelected.reduce((total: number, inventory: any) => {
            return total + (inventory?.inventorySelected?.SumPrice || 0);
        }, 0);
    }
    getPricePax() {
        var data: any[] = [];
        var typePax = ['ADT', 'CHD', 'INF'];
        if (this._orderDetails?.full?.InventoriesSelected[0].combine && this._orderDetails?.full?.InventoriesSelected.length > 1) {
            data = this._orderDetails?.full?.InventoriesSelected[1].inventorySelected?.FareInfos;
        } else {
            var result: {
                PaxType: string,
                Fare: number,
                Tax: number,
            }[] = [];
            typePax.forEach(paxType => {
                result.push({ PaxType: paxType, Fare: 0, Tax: 0 });
            });

            this._orderDetails?.full?.InventoriesSelected.forEach((inventory: any) => {
                inventory.inventorySelected.FareInfos.forEach((fareInfo: any) => {
                    if (typePax.includes(fareInfo.PaxType)) {
                        const paxResult = result.find(r => r.PaxType === fareInfo.PaxType);
                        if (paxResult) {
                            paxResult.Fare += fareInfo.Fare;
                            paxResult.Tax += fareInfo.Tax;
                        }
                    }
                });
            });

            // Remove entries with 0 count
            if (this._orderDetails?.full?.adult === undefined) {
                result = result.filter(r => r.PaxType !== 'ADT');
            }
            if (this._orderDetails?.full?.child === undefined) {
                result = result.filter(r => r.PaxType !== 'CHD');
            }
            if (this._orderDetails?.full?.infant === undefined) {
                result = result.filter(r => r.PaxType !== 'INF');
            }

            data = result;
        }
        this._pricePaxInfor = data;
    }
    showDetailsTrip() {
        this._isShowDetailsTrip = !this._isShowDetailsTrip;
    }
    setPaymentMethod(method: string): void {
        this._paymentMethod = method;
        if (method.includes('bank-transfer')) {
            if (this.banks?.length > 0) {
                this.banks.forEach(b => b.selected = false);
                this.banks[0].selected = true;
            }
        }
    }

    selectBank(bank: any) {
        this.banks.forEach(b => b.selected = false);
        bank.selected = true;
        this.requestUpdate();
    }
    setAgree(agree: boolean) {
        this._agree = agree;
    }
    async RequestEncrypt(data: any): Promise<any> {
        const encryptedData = await this._cryptoService.eda(JSON.stringify(data));
        return {
            EncryptData: encryptedData
        };
    }
    reSearchTrip() {
        // Chỉ thêm language parameter nếu autoLanguageParam được bật
        if (this.autoLanguageParam) {
            const params = new URLSearchParams();
            params.append('language', this.language);
            window.location.href = `${this.uri_searchBox}?${params.toString()}`;
        } else {
            window.location.href = this.uri_searchBox;
        }
    }
    openModal(title: string = 'Thông báo', content: string = `Thời gian đặt vé đã hết hạn.\n
        Vui lòng tải lại trang để xem kết quả mới nhất.`, isCountDown: boolean = true) {

        const modal = this.renderRoot.querySelector("modal-notification") as Modal;

        // Kiểm tra nếu modal tồn tại
        if (modal) {
            modal.start({
                title,
                content,
                isCountDown,
                countdown: 10,
            });
        }

        if (this.isCountDown) {
            const interval = setInterval(() => {
                this.countdown--;
                if (this.countdown === 0) {
                    clearInterval(interval);
                    this.reSearchTrip();
                }
            }, 1000);
        }
    }
    async CallRequestTrip() {
        this._isLoading = true;
        var data = {
            OrderCode: this._orderAvailable.OrderCode,
            PaymentMethod: this._paymentMethod,
            Amount: this._totalPrice
        }
        console.log("data", data);
        try {
            var payloadsEncrypted = await this.RequestEncrypt(data);

            const res = await this._flightService.RePayment(payloadsEncrypted, this._ApiKey);
            const resDecrypted = await this._cryptoService.dda(res.resultObj);
            var resJson = JSON.parse(resDecrypted);
            console.log('resJson hold trip', resJson);
            if (resJson.IsSuccessed) {
                this._isLoading = false;

                // this.router.navigateByUrl('/TripResult?' + params.toString());
                const params = new URLSearchParams();

                // Chỉ thêm language parameter nếu autoLanguageParam được bật
                if (this.autoLanguageParam) {
                    params.append('language', this.language);
                }

                const queryString = params.toString();
                window.location.href = queryString ? `${resJson.ResultObj}&${queryString}` : resJson.ResultObj;
            } else {
                console.log('show notification');
                this.openModal('Thông báo', resJson.Message + "\nVui lòng tìm lại hành trình.", true);
                this._isLoading = false;
            }
        } catch (error: any) {
            if (error.status === 403 || error.status === 401) {
                this._cryptoService.ra();
                await this._cryptoService.spu();
                await this.CallRequestTrip();
            }
        }
    }
    async onPayment() {
        this.isShowModal = false;
        this._isSubmit = true;
        if (!this._agree) {
            return;
        }
        if (this._paymentMethod === '') {
            console.log("isShowModal", this.isShowModal);
            this.openModal('Thông báo', 'Vui lòng chọn phương thức thanh toán.', false);

            console.log("isShowModal12123", this.isShowModal);
            return;
        }
        if (this._paymentMethod === 'credit-card') {
            this.openModal('Thông báo', 'Phương thức thanh toán chưa được hỗ trợ. Vui lòng chọn phương thức thanh toán khác.', false);
            return;
        }
        if (this._paymentMethod === 'bank-transfer') {
            const selectedBank = this.banks?.find((bank) => bank.selected);
            this._paymentMethod = 'bank-transfer_' + selectedBank?.bankName;
        }
        if (!this._cryptoService.ch()) {
            await this._cryptoService.spu();
        }
        await this.CallRequestTrip();
    }

    handleLanguageChange(newLang: string) {
        this.language = newLang;
        this.getInforAirports();

        // Tự động cập nhật URL với language mới
        this.updateURLWithLanguage();

        this.requestUpdate();
    }

    render() {
        return TripRePaymentTemplate(
            this.autoFillOrderCode,
            this.request,
            this.uri_searchBox,
            this.language,
            this.agent,
            this.termsUrl,
            this._isLoading,
            this._agree,
            this._isSubmit,
            this._isShowDetailsTrip,
            this._orderDetails,
            this._inforAirports,
            this._pricePaxInfor,
            this._servicePrice,
            this._sumPrice,
            this._paymentMethod,
            this.banks,
            this.bankNote,
            this.transferContent,
            this.cashInfo,
            this.currencySymbolAv,
            this.convertedVND,
            this.showDetailsTrip.bind(this),
            this.setPaymentMethod.bind(this),
            this.selectBank.bind(this),
            this.setAgree.bind(this),
            this.onPayment.bind(this),
            this.handleLanguageChange.bind(this),
            this.showLanguageSelect // truyền thuộc tính mới
        );
    }
}

