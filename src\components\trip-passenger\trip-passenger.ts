import { css, LitElement, PropertyValues, unsafeCSS } from "lit";
import { TripPassengerTemplate } from "./trip-passenger-template";
import { localStorageService } from "../../services/LocalStorageService";
import { customElement, property, state } from "lit/decorators.js";
import flatpickr from "flatpickr";
import { CryptoService } from "../../services/CryptoService";
import { FlightService } from "../../services/FlightService";
import { getAirportInfoByCode, phones } from "../../services/WorldServices";
import { validateEmail, validatePhone } from "../../utils/dateUtils";
import { setnmtColors } from "../../services/ColorService";
import styles from '../../styles/styles.css';

const cryptoService = new CryptoService();
const flightService = new FlightService();

@customElement("trip-passenger")
export class TripPassenger extends LitElement {
    static styles = [
        unsafeCSS(styles),
        css`
        :host {
          font-family: var(--nmt-font, 'Roboto', sans-serif);
        }`
    ];

    @property({ type: String }) mode = "online";
    @property({ type: String }) googleFontsUrl = "";
    @property({ type: String }) font = "";
    @property({ type: String }) ApiKey = '';
    @property({ type: String }) color = "";
    @property({ type: String }) redirect_uri = "TripPayment";
    @property({ type: String }) uri_searchBox = "";
    @property({ type: String }) autoRandomBirthday = "true";
    @property({ type: Boolean }) showLanguageSelect = false;
    @property({ type: Boolean }) autoLanguageParam = false; // Bật/tắt tính năng tự động thêm language param

    private _language = "vi";
    private _hasCheckedURL = false; // Flag để tránh kiểm tra URL nhiều lần

    @property({ type: String })
    get language(): string {
        return this._language;
    }

    set language(value: string) {
        const oldValue = this._language;

        // Chỉ kiểm tra URL nếu autoLanguageParam được bật
        if (this.autoLanguageParam) {
            const urlParams = new URLSearchParams(window.location.search);
            const languageParam = urlParams.get('language');

            if (languageParam && languageParam !== this._language) {
                // URL có language parameter - luôn ưu tiên URL
                this._language = languageParam;
                console.log('Language overridden from URL parameter:', this._language);
            } else {
                // URL không có language parameter - sử dụng giá trị được set
                this._language = value;
                console.log('Language set from property:', this._language);
                // Tự động thêm vào URL nếu chưa có
                if (!this._hasCheckedURL) {
                    this.updateURLWithLanguage();
                    this._hasCheckedURL = true;
                }
            }
        } else {
            // Không sử dụng URL parameter, chỉ set giá trị trực tiếp
            this._language = value;
            console.log('Language set from property (autoLanguageParam disabled):', this._language);
        }

        this.requestUpdate('language', oldValue);
    }

    get isAutoRandomBirthday(): boolean {
        return this.autoRandomBirthday === "true";
    }

    get currencySymbolAv(): string{
        return this.convertedVND === 1 || this.language === 'vi'  ? '₫' : this.currencySymbol;
    }

    @state() private _ApiKey: string = '';
    @state() private dataCartSticket: any = null;
    @state() private inforAirports: any[] = [];
    @state() private PriceAncillaries: any[] = [];
    @state() private _pricePaxInfor: any[] = [];
    @state() private _passengers: any[] = [];
    @state() private _phoneCodes: any[] = [];
    @state() private _servicePrice: number = 0;
    @state() private _sumPrice: number = 0;
    @state() private _isLoading: boolean = false;
    @state() private _isShowDetailsTrip: boolean = false;
    @state() private _isGlobal: boolean = false;
    @state() private _isSubmit: boolean = false;
    @state() isMobile: boolean = false;
    @state() isInitDatePicker: boolean = false;
    @state() isInitDatePickerPS: boolean = false;
    @state() private displayMode: 'total' | 'perPassenger' = 'total';
    @state() private convertedVND: number = 1;
    @state() private currencySymbol: string = '₫';
    groupBySessionID: any[] = [];


    @state() private flatpickrInstances: any[] = [];
    @state() private flatpickrInstancesPS: any[] = [];
    @state() private inforContact: InforContact = {
        phoneMain: '',
        emailMain: '',
        phoneOther: '',
        emailOther: '',
        note: '',
        AreaCodePhoneMain: '+84',
        LemailMain: 'VN',
        AreaCodePhoneOther: '+84',
        LemailOther: 'VN'
    }

    constructor(
        private _cryptoService: CryptoService,
        private _flightService: FlightService
    ) {
        super();

        this._cryptoService = cryptoService;
        this._flightService = flightService;
    }
    connectedCallback(): void {
        super.connectedCallback();
        this._ApiKey = this.ApiKey;
        this.removeAttribute("ApiKey");

        // Kiểm tra URL ngay khi component được kết nối, trường hợp không có attribute language
        this.checkLanguageFromURL();
    }

    private checkLanguageFromURL(): void {
        // Chỉ kiểm tra URL nếu autoLanguageParam được bật
        if (!this.autoLanguageParam) {
            console.log('autoLanguageParam disabled, skipping URL check');
            return;
        }

        const urlParams = new URLSearchParams(window.location.search);
        const languageParam = urlParams.get('language');

        if (languageParam) {
            // URL có language parameter - set giá trị từ URL
            this._language = languageParam;
            console.log('Language initialized from URL parameter:', this._language);
            this.requestUpdate('language');
        } else if (!this._hasCheckedURL) {
            // URL không có language parameter - tự động thêm vào URL với giá trị mặc định
            this.updateURLWithLanguage();
            this._hasCheckedURL = true;
        }
    }
    private updateURLWithLanguage(): void {
        const currentUrl = new URL(window.location.href);
        const params = new URLSearchParams(currentUrl.search);

        // Thêm hoặc cập nhật parameter language
        params.set('language', this._language);

        // Cập nhật URL mà không reload trang
        const newUrl = `${currentUrl.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newUrl);
        console.log('URL updated with language parameter:', newUrl);
    }


    protected async firstUpdated(_changedProperties: PropertyValues): Promise<void> {
        super.firstUpdated(_changedProperties);

        var dataCart = localStorageService.getItem("cartTicket", "cartTicket");
        this.dataCartSticket = JSON.parse(dataCart);
        this._sumPrice = this.getSumPrice();
        this.initCheckGlobal();
        this.getPricePax();
        this.initPassengers();
        await this.getInforAirports();
        await this.PriceAncillary();
        await this.getPhoneCodes();

        if (this.color !== "") {
            setnmtColors(this.color);
            this.requestUpdate();
        }

        console.log(this.googleFontsUrl);
        // Handle Google Fonts
        if (this.googleFontsUrl) {
            const googleFontsLink = document.createElement('link');
            googleFontsLink.rel = 'stylesheet';
            googleFontsLink.href = this.googleFontsUrl;
            document.head.appendChild(googleFontsLink);
        } else {
            // Default font if no Google Fonts URL provided
            const googleFontsLink = document.createElement('link');
            googleFontsLink.rel = 'stylesheet';
            googleFontsLink.href = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap';
            document.head.appendChild(googleFontsLink);
        }

        console.log('font', this.font);
        if (this.font !== "") {
            const root = document.documentElement;
            root.style.setProperty('--nmt-font', this.font);
        }
    }

    protected updated(_changedProperties: PropertyValues): void {
        super.updated(_changedProperties);
        this.checkDevice();

        this.initDatePicker();
        this.initDatePickerPassportExprid();
    }
    updatePhoneCode(event: any) {
        const phoneValue = event.target.value.replace(/\D/g, ''); // Chỉ giữ lại số
        this.inforContact.AreaCodePhoneMain = `+ ${phoneValue}`;
    }
    async getPhoneCodes() {
        try {
            var res = await phones();
            if (res.isSuccessed) {
                this._phoneCodes = res.resultObj;
            }
        } catch (error: any) {
            console.error(error);
        }
    }

    async getInforAirports() {
        var airportsCode: string[] = [];
        this.dataCartSticket?.InventoriesSelected.forEach((inventory: any) => {
            inventory.segment.Legs.forEach((leg: any) => {
                if (!airportsCode.includes(leg.DepartureCode)) {
                    airportsCode.push(leg.DepartureCode);
                }
                if (!airportsCode.includes(leg.ArrivalCode)) {
                    airportsCode.push(leg.ArrivalCode);
                }
            });
        });
        try {
            var res = await getAirportInfoByCode(airportsCode, this.language || 'vi', this._ApiKey);
            if (res.isSuccessed) {
                this.inforAirports = res.resultObj;
                this.displayMode = res.feature.displayMode || 'total';
                const currencyObj = typeof res.feature.currency === 'string' ? JSON.parse(res.feature.currency) : res.feature.currency;
                this.currencySymbol = currencyObj.symbol || '₫';
                this.convertedVND = currencyObj.convertedVND || 1;
            }
            if (this.mode == "online") {
                if (res.feature?.color) {
                    this.color = res.feature.color;
                }
            }
        } catch (error: any) {
            console.error(error);
        }
    }

    async PriceAncillary() {
        if (!this._cryptoService.ch()) {
            await this._cryptoService.spu();
        }
        await this.CallPriceAncillary();
    }
    async RequestEncrypt(dataCodeRef: any[]): Promise<any> {
        const payloadsEncrypted = await Promise.all(
            dataCodeRef.map(async (payload) => {
                const encryptedData = await this._cryptoService.eda(JSON.stringify(payload));
                return {
                    EncryptData: encryptedData
                };
            })
        );
        return payloadsEncrypted;
    }
    async CallPriceAncillary() {
        var dataCodeRef: any[] = [];
        let has403Error = false;

        if (!Array.isArray(this.dataCartSticket.dataCodeRef)) {
            dataCodeRef.push(this.dataCartSticket.dataCodeRef);
        } else {
            dataCodeRef = this.dataCartSticket.dataCodeRef;
        }
        this.groupBySessionID = dataCodeRef;
        var payloadsEncrypted = await this.RequestEncrypt(dataCodeRef);

        const priceAncillaryPromises = payloadsEncrypted.map(async (payload: any) => {
            if (has403Error) return;
            try {
                const res = await this._flightService.PriceAncillary(payload, this._ApiKey);
                const resDecrypted = await this._cryptoService.dda(res.resultObj);
                var resJson = JSON.parse(resDecrypted);
                this.PriceAncillaries.push(resJson.ResultObj);
            } catch (error: any) {
                if (error.status !== 200) {
                    if (!has403Error) {
                        has403Error = true;
                        this._cryptoService.ra();
                        await this._cryptoService.spu();
                        await this.CallPriceAncillary();
                        return;
                    }
                }
            }
        });
        await Promise.allSettled(priceAncillaryPromises);
    }
    reSearchTrip() {
        // this.modalService.dismissAll();
        var url = this.dataCartSticket?.url;
        // this.router.navigateByUrl(url);
        //go to url
        const urlObj = new URL(url);
        const params = new URLSearchParams(urlObj.search);

        // Chỉ thêm language parameter nếu autoLanguageParam được bật
        if (this.autoLanguageParam) {
            params.set('language', this.language);
        }

        const queryString = params.toString();
        window.location.href = queryString ? `${urlObj.pathname}?${queryString}` : urlObj.pathname;
    }

    checkDevice() {
        const width = window.innerWidth;
        this.isMobile = width <= 768;
    }
    initCheckGlobal() {
        this.dataCartSticket.InventoriesSelected.forEach((inventory: any) => {
            if (this.CheckisGlobal(inventory.segment.DepartureCode) || this.CheckisGlobal(inventory.segment.ArrivalCode)) {
                this._isGlobal = true;
            }
        });
    }
    CheckisGlobal(airport: string | undefined): boolean {
        if (!airport) return false;
        const domesticAirports = [
            "SGN", "HAN", "HPH", "DIN", "VDO", "DAD", "CXR", "DLI", "VII", "HUI", "THD",
            "BMV", "PXU", "UIH", "VDH", "TBB", "VCL", "PQC", "VCS", "VCA", "CAH", "VKG"
        ];
        return !domesticAirports.includes(airport);
    }
    getBaggageOfType(airline: string, type: string): any {
        if (this.PriceAncillaries.length === 0) {
            return [];
        }

        const res = this.PriceAncillaries.reduce((acc: any[], ancillary: any) => {
            ancillary.AirSegments?.forEach((segment: any) => {
                if (`${segment.DepartureCode}-${segment.ArrivalCode}` === type && segment.Airlines === airline) {
                    const filteredSSR = segment.ListSSR.filter((ssr: any) => ssr.WeightBag > 0);
                    acc.push(...filteredSSR);
                }
            });
            return acc;
        }, []);
        return res;
    }
    getSumPrice(): number {
        //check combine
        if (this.dataCartSticket?.InventoriesSelected.length === 1 && this.dataCartSticket?.InventoriesSelected[0].combine) {
            return this.dataCartSticket?.InventoriesSelected[0].inventorySelected?.SumPrice || 0;
        } else if (this.dataCartSticket?.InventoriesSelected.length > 1 && this.dataCartSticket?.InventoriesSelected[0].combine) {
            return this.dataCartSticket?.InventoriesSelected[1].inventorySelected?.SumPrice || 0;
        }
        return this.dataCartSticket?.InventoriesSelected.reduce((total: number, inventory: any) => {
            return total + (inventory?.inventorySelected?.SumPrice || 0);
        }, 0);
    }

    getPricePax() {
        var data: any[] = [];
        var typePax = ['ADT', 'CHD', 'INF'];
        if (this.dataCartSticket?.InventoriesSelected[0].combine && this.dataCartSticket?.InventoriesSelected.length > 1) {
            data = this.dataCartSticket?.InventoriesSelected[1].inventorySelected?.FareInfos;
        } else {
            var result: {
                PaxType: string,
                Fare: number,
                Tax: number,
            }[] = [];
            typePax.forEach(paxType => {
                result.push({ PaxType: paxType, Fare: 0, Tax: 0 });
            });

            this.dataCartSticket?.InventoriesSelected.forEach((inventory: any) => {
                inventory.inventorySelected.FareInfos.forEach((fareInfo: any) => {
                    if (typePax.includes(fareInfo.PaxType)) {
                        const paxResult = result.find(r => r.PaxType === fareInfo.PaxType);
                        if (paxResult) {
                            paxResult.Fare += fareInfo.Fare;
                            paxResult.Tax += fareInfo.Tax;
                        }
                    }
                });
            });

            // Remove entries with 0 count
            if (this.dataCartSticket?.adult === undefined || this.dataCartSticket?.adult === 0) {
                result = result.filter(r => r.PaxType !== 'ADT');
            }
            if (this.dataCartSticket?.child === undefined || this.dataCartSticket?.child === 0) {
                result = result.filter(r => r.PaxType !== 'CHD');
            }
            if (this.dataCartSticket?.infant === undefined || this.dataCartSticket?.infant === 0) {
                result = result.filter(r => r.PaxType !== 'INF');
            }

            data = result;
        }
        this._pricePaxInfor = data;
    }
    initPassengers() {
        // init adult passengers
        for (let i = 0; i < this.dataCartSticket?.adult; i++) {
            const baggages = this.getInitialBaggages();
            const dateRange = this.getRangeDatePicker('adult');
            const birthDate = this.isAutoRandomBirthday ? this.getRandomDate(dateRange[0], dateRange[1]) : undefined;
            const birthDateString = this.isAutoRandomBirthday && birthDate ? this.formatDateToString(birthDate) : '';
            console.log('birthDate', birthDate);
            const passerger: Passenger = {
                type: 'adult',
                gender: 'MR',
                fullname: '',
                birthday: birthDate,
                birthdaytString: birthDateString,
                country: '',
                passport: '',
                passportDate: undefined,
                passportDateString: '',
                baggages: baggages,
                isShowNS: false,
                isShowPassport: this._isGlobal
            };
            this._passengers.push(passerger);
        }
        // init child passengers
        for (let i = 0; i < this.dataCartSticket?.child; i++) {
            const baggages = this.getInitialBaggages();
            const dateRange = this.getRangeDatePicker('child');
            const birthDate = this.isAutoRandomBirthday ? this.getRandomDate(dateRange[0], dateRange[1]) : undefined;
            const birthDateString = this.isAutoRandomBirthday && birthDate ? this.formatDateToString(birthDate) : '';
            var passerger: Passenger = {
                type: 'child',
                gender: 'MSTR',
                fullname: '',
                birthday: birthDate,
                birthdaytString: birthDateString,
                country: '',
                passport: '',
                passportDate: undefined,
                passportDateString: '',
                baggages: baggages,
                isShowNS: false,
                isShowPassport: false
            };
            this._passengers.push(passerger);
        }
        // init infant passengers
        for (let i = 0; i < this.dataCartSticket?.infant; i++) {
            const dateRange = this.getRangeDatePicker('infant');
            const birthDate = this.isAutoRandomBirthday ? this.getRandomDate(dateRange[0], dateRange[1]) : undefined;
            const birthDateString = this.isAutoRandomBirthday && birthDate ? this.formatDateToString(birthDate) : '';
            var passerger: Passenger = {
                type: 'infant',
                gender: 'MSTR',
                fullname: '',
                birthday: birthDate,
                birthdaytString: birthDateString,
                country: '',
                passport: '',
                passportDateString: '',
                passportDate: undefined,
                baggages: [],
                isShowNS: false,
                isShowPassport: false
            };
            this._passengers.push(passerger);
        }
    }

    /**
     * Formats a Date object to string in dd/MM/yyyy format
     * @param date The date to format
     * @returns Formatted date string
     */
    formatDateToString(date: Date): string {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    }

    getRangeDatePicker(paxType: string): Date[] {
        var dateStartStr = this.dataCartSticket?.InventoriesSelected[0].segment.DepartureDate;
        var dateStartTrip = new Date(dateStartStr);
        if (paxType === 'adult') {
            var maxDate = new Date(dateStartTrip);
            maxDate.setFullYear(maxDate.getFullYear() - 12);

            var minDate = new Date(dateStartTrip);
            minDate.setFullYear(minDate.getFullYear() - 100);
            return [minDate, maxDate];
        } else if (paxType === 'child') {
            var maxDate = new Date(dateStartTrip);
            maxDate.setFullYear(maxDate.getFullYear() - 2);
            var minDate = new Date(dateStartTrip);
            minDate.setFullYear(minDate.getFullYear() - 12);
            return [minDate, maxDate];
        }
        else if (paxType === 'infant') {
            var maxDate = new Date(dateStartTrip);
            var minDate = new Date(dateStartTrip);
            minDate.setFullYear(maxDate.getFullYear() - 2);
            return [minDate, maxDate];
        }
        return [new Date(), new Date()];
    }
    initDatePickerPassportExprid() {
        if (this.isInitDatePickerPS) return;
        const datePickers = this.renderRoot?.querySelectorAll(".datePickerPS");
        const dateFormatValue = this.language === 'vi' ? "d/m/Y" : "m/d/Y";
        if (datePickers) {
            datePickers.forEach((datePicker: any, index: number) => {
                this.isInitDatePickerPS = true;
                const instance = flatpickr(datePicker, {
                    dateFormat: dateFormatValue, // Định dạng ngày
                    allowInput: true, // Cho phép nhập từ bàn phím
                    clickOpens: false, // Tắt mở lịch khi nhấp vào ô nhập
                    disableMobile: true, // Tắt giao diện mặc định trên thiết bị di động
                    minDate: new Date(), // Ngày bắt đầu
                    onChange: (selectedDates: Date[]) => {
                        if (selectedDates.length > 0) {
                            const selectedDate = selectedDates[0];
                            const formattedDate = flatpickr.formatDate(selectedDate, dateFormatValue);
                            datePicker.value = formattedDate;
                            this._passengers[index].passportDate = selectedDate; // Cập nhật ngày sinh
                        }
                    },
                });
                this.flatpickrInstancesPS.push(instance);
            });
        }
    }
    openDatePickerPS(index: number) {
        if (this.flatpickrInstancesPS && this.flatpickrInstancesPS[index]) {
            this.flatpickrInstancesPS[index].open(); // Mở datepicker
        }
    }

    initDatePicker() {
        if (this.isInitDatePicker) return;
        const datePickers = this.renderRoot?.querySelectorAll(".datepickerBD");
        const dateFormatValue = this.language === 'vi' ? "d/m/Y" : "m/d/Y";
        if (datePickers) {
            datePickers.forEach((datePicker: any, index: number) => {
                this.isInitDatePicker = true;
                const paxType = this._passengers[index]?.type;
                console.log("paxType", paxType);
                const rangeDate = this.getRangeDatePicker(paxType);
                console.log("rangeDate", rangeDate);
                const instance = flatpickr(datePicker, {
                    dateFormat: dateFormatValue, // Định dạng ngày
                    allowInput: true, // Cho phép nhập từ bàn phím
                    clickOpens: false, // Tắt mở lịch khi nhấp vào ô nhập
                    disableMobile: true, // Tắt giao diện mặc định trên thiết bị di động
                    minDate: rangeDate[0], // Ngày bắt đầu
                    maxDate: rangeDate[1], // Ngày kết thúc
                    defaultDate: this._passengers[index]?.birthday || null, // Ngày mặc định
                    onChange: (selectedDates: Date[]) => {
                        if (selectedDates.length > 0) {
                            const selectedDate = selectedDates[0];
                            const formattedDate = flatpickr.formatDate(selectedDate, dateFormatValue);
                            datePicker.value = formattedDate;
                            this._passengers[index].birthday = selectedDate; // Cập nhật ngày sinh
                        }
                    },
                });
                this.flatpickrInstances.push(instance);
            });
        }
    }

    openDatePicker(index: number) {
        console.log("openDatePicker", index, this.flatpickrInstances);
        if (this.flatpickrInstances && this.flatpickrInstances[index]) {
            this.flatpickrInstances[index].open(); // Mở datepicker
        }
    }
    validateBirthday(birthday: any | undefined, type: string): boolean {
        const dateDeparture = new Date(this.dataCartSticket?.InventoriesSelected[0]?.segment.DepartureDate);
        if (dateDeparture === undefined || dateDeparture === null) {
            return false;
        }

        if (birthday === undefined || birthday === null) {
            return false;
        }
        //infant < 2, child < 12, adult >= 12
        var date = new Date(birthday.year, birthday.month - 1, birthday.day);
        var age = dateDeparture.getFullYear() - date.getFullYear();
        var month = dateDeparture.getMonth() - date.getMonth();
        if (month < 0 || (month === 0 && dateDeparture.getDate() < date.getDate())) {
            age--;
        }
        if (type === 'infant' && age >= 2) {
            return false;
        }
        else if (type === 'child' && age >= 12) {
            return false;
        }
        else if (type === 'adult' && age < 12) {
            return false;
        }
        return true;
    }

    updateBirthday(event: any, passenger: Passenger, index: number) {
        const value = this.language === 'vi' ? this.inputDateInDatePicker(event) : this.inputDateInDatePicker_MMddyyyy(event);

        // Cập nhật giá trị hiển thị trong input
        const inputId = `pax${index}`;
        const inputBirthday = this.renderRoot.querySelector<HTMLInputElement>(`#${inputId}`);
        if (inputBirthday) {
            inputBirthday.value = value;
        }

        // Cập nhật giá trị vào biến trung gian (birthdaytString)
        passenger.birthdaytString = value;

        // Kiểm tra nếu giá trị nhập đủ 10 ký tự (dd/MM/yyyy)
        if (value.length === 10) {
            let day: number, month: number, year: number;
        
            if (this.language === 'vi') {
                [day, month, year] = value.split('/').map(Number);
            } else {
                [month, day, year] = value.split('/').map(Number);
            }

            if (this.isValidDate(day, month, year)) {
                // Cập nhật giá trị vào passenger.birthday
                passenger.birthday = { day, month, year };
            } else {
                var date = new Date(year, month - 1, day);
                passenger.birthday = date;
                passenger.birthdaytString = ''; // Xóa giá trị nếu không hợp lệ

                // Sử dụng setDate để cập nhật giao diện
                if (this.flatpickrInstances[index]) {
                    this.flatpickrInstances[index].setDate(date, true); // true để kích hoạt sự kiện onChange
                }
            }
        } else {
            passenger.birthday = undefined; // Xóa giá trị nếu chưa đủ độ dài
        }
        this.requestUpdate(); // Đảm bảo giao diện được cập nhật
    }

    inputDateInDatePicker(event: any): any {
        let value = event.target.value.replace(/[^0-9]/g, ''); // Chỉ giữ lại số

        // Kiểm tra xem người dùng có đang xóa hay không
        const isDeleting = event.inputType === 'deleteContentBackward' || event.inputType === 'deleteContentForward';

        // Kiểm tra và điều chỉnh giá trị nhập vào
        if (value.length === 1) {
            if (value > '3') {
                value = `0${value}`;
            }
        } else if (value.length === 2) {
            const day = parseInt(value, 10);
            if (day < 1 || day > 31) {
                value = value.slice(0, 1);
            }
        } else if (value.length === 3) {
            const monthChar = value[2];
            if (monthChar > '1' && monthChar !== '2') {
                value = `${value.slice(0, 2)}0${monthChar}`;
            } else if (monthChar === '2') {
                const day = parseInt(value.slice(0, 2), 10);
                if (day > 29) {
                    value = value.slice(0, 2);
                } else {
                    value = `${value.slice(0, 2)}0${monthChar}`;
                }
            }
        } else if (value.length === 4) {
            const month = parseInt(value.slice(2), 10);
            const day = parseInt(value.slice(0, 2), 10);
            if (month < 1 || month > 12) {
                value = value.slice(0, 3);
            } else if (month === 4 || month === 6 || month === 9 || month === 11) {
                if (day > 30) {
                    value = value.slice(0, 3);
                }
            } else if (month === 2) {
                if (day > 29) {
                    value = value.slice(0, 3);
                }
            }
        } else if (value.length === 8) {
            const year = parseInt(value.slice(4), 10);
            const month = parseInt(value.slice(2, 4), 10);
            const day = parseInt(value.slice(0, 2), 10);
            if (month === 2 && day === 29 && !this.isLeapYear(year)) {
                value = value.slice(0, 7);
            }
        }

        // Định dạng lại giá trị theo dd/MM/yyyy (chỉ khi không phải đang xóa)
        if (!isDeleting) {
            if (value.length === 2) {
                value = `${value}/`;
            } else if (value.length > 2 && value.length < 4) {
                value = `${value.slice(0, 2)}/${value.slice(2)}`;
            } else if (value.length === 4) {
                value = `${value.slice(0, 2)}/${value.slice(2)}/`;
            } else if (value.length > 4) {
                value = `${value.slice(0, 2)}/${value.slice(2, 4)}/${value.slice(4, 8)}`; // Giới hạn năm đến 4 chữ số
            }
        } else {
            if (value.length === 2) {
                value = `${value.slice(0)}`;
            } else if (value.length > 2 && value.length < 4) {
                value = `${value.slice(0, 2)}/${value.slice(2)}`;
            } else if (value.length === 4) {
                value = `${value.slice(0, 2)}/${value.slice(2)}`;
            } else if (value.length > 4) {
                value = `${value.slice(0, 2)}/${value.slice(2, 4)}/${value.slice(4, 8)}`; // Giới hạn năm đến 4 chữ số
            }
        }
        return value;
    }

    inputDateInDatePicker_MMddyyyy(event: any): any 
    {
        let value = event.target.value.replace(/[^0-9]/g, ''); // Chỉ giữ lại số

        // Kiểm tra xem người dùng có đang xóa hay không
        const isDeleting = event.inputType === 'deleteContentBackward' || event.inputType === 'deleteContentForward';

        // Kiểm tra và điều chỉnh giá trị nhập vào
        if (value.length === 1) {
            if (value > '1') {
                value = `0${value}`;
            }
        } else if (value.length === 2) {
            const month = parseInt(value, 10);
            if (month < 1 || month > 12) {
                value = value.slice(0, 1);
            }
        } else if (value.length === 3) {
            const dayChar = value[2];
            if (dayChar > '3') {
                value = `${value.slice(0, 2)}0${dayChar}`;
            }
        } else if (value.length === 4) {
            const month = parseInt(value.slice(0, 2), 10);
            const day = parseInt(value.slice(2, 4), 10);

            const maxDay = this.getMaxDayOfMonth(month, 2024); // Giả định năm nhuận tạm thời
            if (day < 1 || day > maxDay) {
                value = value.slice(0, 3);
            }
        } else if (value.length === 8) {
            const month = parseInt(value.slice(0, 2), 10);
            const day = parseInt(value.slice(2, 4), 10);
            const year = parseInt(value.slice(4, 8), 10);

            const maxDay = this.getMaxDayOfMonth(month, year);
            if (day > maxDay) {
                value = value.slice(0, 7);
            }
        }

        // Định dạng lại giá trị theo MM/dd/yyyy
        if (!isDeleting) {
            if (value.length === 2) {
                value = `${value}/`;
            } else if (value.length > 2 && value.length < 4) {
                value = `${value.slice(0, 2)}/${value.slice(2)}`;
            } else if (value.length === 4) {
                value = `${value.slice(0, 2)}/${value.slice(2)}/`;
            } else if (value.length > 4) {
                value = `${value.slice(0, 2)}/${value.slice(2, 4)}/${value.slice(4, 8)}`;
            }
        } else {
            if (value.length === 2) {
                value = `${value.slice(0)}`;
            } else if (value.length > 2 && value.length < 4) {
                value = `${value.slice(0, 2)}/${value.slice(2)}`;
            } else if (value.length === 4) {
                value = `${value.slice(0, 2)}/${value.slice(2)}`;
            } else if (value.length > 4) {
                value = `${value.slice(0, 2)}/${value.slice(2, 4)}/${value.slice(4, 8)}`;
            }
        }

        return value;
    }

    // Hàm phụ: Trả về số ngày tối đa trong tháng
    getMaxDayOfMonth(month: number, year: number): number {
        if (month === 2) {
            return this.isLeapYear(year) ? 29 : 28;
        }
        if ([4, 6, 9, 11].includes(month)) {
            return 30;
        }
        return 31;
    }


    updatepassportDate(event: any, passenger: Passenger, index: number) {
        const value = this.language === 'vi' ? this.inputDateInDatePicker(event) : this.inputDateInDatePicker_MMddyyyy(event);

        // Cập nhật giá trị hiển thị trong input
        const inputId = `passportDate${index}`;
        const inputPassportDate = this.renderRoot.querySelector<HTMLInputElement>(`#${inputId}`);
        if (inputPassportDate) {
            inputPassportDate.value = value;
        }

        // Cập nhật giá trị vào biến trung gian (birthdaytString)
        passenger.passportDateString = value;

        // Kiểm tra nếu giá trị nhập đủ 10 ký tự (dd/MM/yyyy)
        if (value.length === 10) {
            let day: number, month: number, year: number;
        
            if (this.language === 'vi') {
                [day, month, year] = value.split('/').map(Number);
            } else {
                [month, day, year] = value.split('/').map(Number);
            }

            if (this.isValidDate(day, month, year)) {
                // Cập nhật giá trị vào passenger.birthday
                passenger.passportDate = { day, month, year };
            } else {
                var date = new Date(year, month - 1, day);
                passenger.passportDate = date;
                passenger.passportDateString = ''; // Xóa giá trị nếu không hợp lệ

                // Sử dụng setDate để cập nhật giao diện
                if (this.flatpickrInstancesPS[index]) {
                    this.flatpickrInstancesPS[index].setDate(date, true); // true để kích hoạt sự kiện onChange
                }
            }
        } else {
            passenger.passportDate = undefined; // Xóa giá trị nếu chưa đủ độ dài
        }
        this.requestUpdate(); // Đảm bảo giao diện được cập nhật
    }
    isValidDate(day: number, month: number, year: number): boolean {
        const date = new Date(year, month - 1, day);
        return (
            date.getFullYear() === year &&
            date.getMonth() === month - 1 &&
            date.getDate() === day
        );
    }
    isLeapYear(year: number): boolean {
        return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
    }

    /**
     * Generates a random date between two dates
     * @param startDate The start date of the range
     * @param endDate The end date of the range
     * @returns A random date between startDate and endDate
     */
    getRandomDate(startDate: Date, endDate: Date): Date {
        // Convert dates to timestamps
        const startTimestamp = startDate.getTime();
        const endTimestamp = endDate.getTime();

        // Generate random timestamp between start and end
        const randomTimestamp = startTimestamp + Math.random() * (endTimestamp - startTimestamp);

        // Create new date from random timestamp
        return new Date(randomTimestamp);
    }

    getInitialBaggages(): Baggage[] {
        var baggages: Baggage[] = [];
        for (let j = 0; j < this.dataCartSticket?.InventoriesSelected.length; j++) {
            var segment = this.dataCartSticket?.InventoriesSelected[j]?.segment;
            baggages.push({
                type: `${segment?.DepartureCode}-${segment?.ArrivalCode}`,
                SsrCode: '',
                Price: 0,
                airline: segment?.Airlines,
                CurrencyCode: 'VND',
                ArrivalCode: segment?.ArrivalCode,
                DepartureCode: segment?.DepartureCode,
                DepartureDate: segment?.DepartureDate,
                WeightBag: 0
            });
        }
        return baggages;
    }

    showDetailsTrip() {
        this._isShowDetailsTrip = !this._isShowDetailsTrip;
    }
    togglePassportVisibility(passenger: any) {
        passenger.isShowPassport = !passenger.isShowPassport;
        this.requestUpdate(); // Ensure the UI updates
    }
    updateFullname(event: any, passenger: Passenger) {
        passenger.fullname = event.target.value.toUpperCase();
        this.requestUpdate(); // Cập nhật giao diện nếu cần
    }
    updateGender(event: Event, passenger: any) {
        const selectElement = event.target as HTMLSelectElement;
        passenger.gender = selectElement.value; // Cập nhật giá trị gender
        this.requestUpdate(); // Cập nhật giao diện nếu cần
    }
    updateCountry(event: any, passenger: Passenger) {
        passenger.country = event.target.value.toUpperCase();
        this.requestUpdate(); // Cập nhật giao diện nếu cần
    }
    changeBaggage(event: any, passenger: Passenger, baggage: any) {
        this.PriceAncillaries.forEach((ancillary: any) => {
            ancillary.AirSegments?.forEach((segment: any) => {
                if (`${segment.DepartureCode}-${segment.ArrivalCode}` === baggage.type && segment.Airlines === baggage.airline) {
                    segment.ListSSR.forEach((ssr: any) => {
                        if (ssr.SSRCode === event.target.value) {
                            baggage.Price = ssr.Price;
                            baggage.SsrCode = event.target.value;
                            baggage.WeightBag = ssr.WeightBag;
                        }
                    });
                }
            });
        });
        this._servicePrice = this.getSumServicePrice();
        this.requestUpdate(); // Cập nhật giao diện nếu cần
    }
    getSumServicePrice(): number {
        var sum = 0;
        this._passengers.forEach((passenger: Passenger) => {
            passenger.baggages.forEach((baggage: Baggage) => {
                sum += baggage.Price;
            });
        });
        return sum;
    }
    updateLemailMain(event: any) {
        this.inforContact.LemailMain = event.target.value;
    }
    validateForm(): boolean {
        var isValid = true;
        this._passengers.forEach((passenger: Passenger) => {
            if (!passenger.fullname) {
                isValid = false;
                return;
            }
            if (!this.validateBirthday(passenger.birthday, passenger.type)) {
                isValid = false;
                return;
            }
            if ((passenger.isShowPassport || this._isGlobal) && passenger.type === 'adult') {
                if (!passenger.country) {
                    isValid = false;
                    return;
                }
                if (!passenger.passport) {
                    isValid = false;
                    return;
                }
                if (!passenger.passportDate) {
                    isValid = false;
                    return;
                }
            }
        });

        if (!validatePhone(this.inforContact.phoneMain)) {
            isValid = false;
        }
        if (!validateEmail(this.inforContact.emailMain)) {
            isValid = false;
        }

        if (this.inforContact.phoneOther.length > 0 && !validatePhone(this.inforContact.phoneOther)) {
            isValid = false;
        }
        if (this.inforContact.emailOther.length > 0 && !validateEmail(this.inforContact.emailOther)) {
            isValid = false;
        }

        return isValid;
    }
    getFirstAndLastName(fullname: string) {
        const names = fullname.trim().split(' ');
        const lastName = names.shift(); // Lấy phần tử đầu tiên làm lastName
        const firstName = names.join(' '); // Phần còn lại làm firstName
        return { firstName, lastName };
    }
    GetSegment(): any {
        var result: any[] = [];
        this.dataCartSticket.InventoriesSelected.forEach((element: any) => {
            var segmentResult: {
                Legs: any[],
                BookingInfos: any[],
                SumPrice: number,
            } = {
                Legs: [],
                BookingInfos: [],
                SumPrice: element.inventorySelected.SumPrice
            }
            element.segment.Legs.forEach((leg: any) => {
                var stringLeg = `Combine:${element.segment.combine || false}_${leg.OperatingAirlines}${leg.FlightNumber}_${leg.DepartureCode}_${leg.ArrivalCode}_${leg.DepartureDate}_${leg.ArrivalDate}`;
                segmentResult.Legs.push(stringLeg);
            });
            element.inventorySelected.BookingInfos.forEach((bookingInfo: any) => {
                var stringBookingInfo = `${bookingInfo.CabinName}_${bookingInfo.FareType}_${bookingInfo.BookingCode}`;
                segmentResult.BookingInfos.push(stringBookingInfo);
            });

            result.push(segmentResult);
        });
        return result;
    }
    RequestBookTrip(): any {
        var request = {
            adult: this.dataCartSticket.adult,
            child: this.dataCartSticket.child,
            infant: this.dataCartSticket.infant,
            passengers: this._passengers,
            inforContact: this.inforContact
        };
        var groupBySessionID = this.groupBySessionID;

        var requestBookTrips: any[] = [];
        var id = 0;
        var adultIndex = 0;

        // Create ListPax once
        const listPax = request.passengers.reduce((acc: any[], pax: any) => {
            const { firstName, lastName } = this.getFirstAndLastName(pax.fullname);
            if (pax.type === 'infant') {
                const adult = acc.find((p: any) => p.Id === adultIndex);
                if (adult && !adult.Infant) {
                    adult.Infant = {
                        Gender: pax.gender,
                        LastName: lastName,
                        FirstName: firstName,
                        Brith: pax.birthday !== undefined ? `${pax.birthday.year || pax.birthday.getFullYear()}-${(pax.birthday.month || pax.birthday.getMonth() + 1).toString().padStart(2, '0')}-${(pax.birthday.day || pax.birthday.getDate()).toString().padStart(2, '0')}` : null,
                    };
                }
                adultIndex++;
            } else {
                acc.push({
                    Id: id++,
                    Gender: pax.gender,
                    LastName: lastName,
                    FirstName: firstName,
                    Brith: pax.birthday !== undefined ? `${pax.birthday.year || pax.birthday.getFullYear()}-${(pax.birthday.month || pax.birthday.getMonth() + 1).toString().padStart(2, '0')}-${(pax.birthday.day || pax.birthday.getDate()).toString().padStart(2, '0')}` : null,
                    CccdCode: pax.passport,
                    PassportCode: pax.passport,
                    PassportExpDate: pax.passportDate !== undefined ? `${pax.passportDate.year}-${pax.passportDate.month.toString().padStart(2, '0')}-${pax.passportDate.day.toString().padStart(2, '0')}` : null,
                    PassportCountryCode: pax.country,
                    ListSsr: pax.baggages
                        .filter((baggage: any) => baggage.SsrCode).map((baggage: any) => {
                            return {
                                SsrCode: baggage.SsrCode,
                                Price: baggage.Price,
                                CurrencyCode: baggage.CurrencyCode,
                                DepartureCode: baggage.DepartureCode,
                                ArrivalCode: baggage.ArrivalCode,
                                DepartureDate: baggage.DepartureDate,
                            }
                        }),
                    Infant: null
                });
            }
            return acc;
        }, []);

        groupBySessionID.forEach((item: any) => {
            requestBookTrips.push({
                SessionID: item.sessionID,
                GroupCodeRef: item.groupCodeRef,
                ListCode: item.listCode,
                AreaCodePhone: 0,
                Phone: request.inforContact.phoneMain,
                Email: request.inforContact.emailMain,
                Lemail: request.inforContact.LemailMain,
                ListPax: listPax,
                Adult: request.adult,
                Child: request.child,
                Infant: request.infant,
            });
        });
        return requestBookTrips;
    }
    async CallGoToPayment2() {
        this._isLoading = true;
        var requestBookTrips = this.RequestBookTrip();
        var note = {
            request: requestBookTrips,
            paxList: this._passengers,
            summary: this.GetSegment(),
            full: this.dataCartSticket,
            totalPrice: this._sumPrice + this._servicePrice
        }
        var request = {
            depart: this.dataCartSticket.InventoriesSelected[0].segment.DepartureCode,
            arrival: this.dataCartSticket.InventoriesSelected[0].segment.ArrivalCode,
            departDate: this.dataCartSticket.InventoriesSelected[0].segment.DepartureDate,
            returnDate: this.dataCartSticket.InventoriesSelected.length > 1 ? this.dataCartSticket.InventoriesSelected[this.dataCartSticket.InventoriesSelected.length - 1].segment.ArrivalDate : null,
            adult: this.dataCartSticket.adult,
            child: this.dataCartSticket.child,
            infant: this.dataCartSticket.infant,
            customerName: this._passengers[0].fullname,
            phoneNumber: this.inforContact.phoneMain,
            email: this.inforContact.emailMain,
            note: JSON.stringify(note)
        }

        localStorageService.setItem("pnrPassenger", request, "pnrPassenger");
        const params = new URLSearchParams();

        // Chỉ thêm language parameter nếu autoLanguageParam được bật
        if (this.autoLanguageParam) {
            params.append('language', this.language);
        }

        const queryString = params.toString();
        window.location.href = queryString ? `/${this.redirect_uri}?${queryString}` : `/${this.redirect_uri}`;
    }
    async goToPayment() {
        this._isSubmit = true;
        if (!this.validateForm()) {
            console.log("validateForm", this.validateForm());
            return;
        }
        if (!this._cryptoService.ch()) {
            await this._cryptoService.spu();
        }
        await this.CallGoToPayment2();
    }
    updatePassport = (event: Event, passenger: any) => {
        const input = event.target as HTMLInputElement;
        passenger.passport = input.value; // Cập nhật giá trị mới
    };

    updatePhoneMain(event: any) {
        this.inforContact.phoneMain = event.target.value;
    }

    updateEmailMain(event: any) {
        this.inforContact.emailMain = event.target.value;
    }
    handleLanguageChange(newLang: string) {
        this.language = newLang;
        this.getInforAirports();

        // Tự động cập nhật URL với language mới
        this.updateURLWithLanguage();

        this.requestUpdate();
    }

    render() {
        return TripPassengerTemplate(
            this.uri_searchBox,
            this.language,
            this._isLoading,
            this._isGlobal,
            this._isSubmit,
            this._isShowDetailsTrip,
            this.dataCartSticket,
            this.inforContact,
            this.inforAirports,
            this._pricePaxInfor, // Đảm bảo dữ liệu được truyền đúng
            this._passengers,
            this._phoneCodes,
            this._servicePrice,
            this._sumPrice,
            this.currencySymbolAv,
            this.convertedVND,
            this.reSearchTrip.bind(this),
            this.showDetailsTrip.bind(this),
            this.getBaggageOfType.bind(this),
            this.togglePassportVisibility.bind(this),
            this.updateBirthday.bind(this),
            this.openDatePicker.bind(this),
            this.validateBirthday.bind(this),
            this.updateFullname.bind(this),
            this.updateGender.bind(this),
            this.updateCountry.bind(this),
            this.openDatePickerPS.bind(this),
            this.updatepassportDate.bind(this),
            this.changeBaggage.bind(this),
            this.updatePhoneCode.bind(this),
            this.updateLemailMain.bind(this),
            this.updatePassport.bind(this),
            this.updatePhoneMain.bind(this),
            this.updateEmailMain.bind(this),
            this.goToPayment.bind(this),
            this.handleLanguageChange.bind(this),
            this.showLanguageSelect // truyền thuộc tính mới
        );
    }
}

