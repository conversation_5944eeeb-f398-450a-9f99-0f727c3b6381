import { LitElement, PropertyValues } from "lit";
export declare class CalendersTrip extends LitElement {
    static styles: import("lit").CSSResult[];
    private _isLoading;
    private _isError;
    private calendarWeek;
    dateStart: Date;
    language: string;
    constructor();
    firstUpdated(): void;
    updated(changedProperties: PropertyValues): void;
    updateCalendarWeek(): void;
    createCalendarWeek(date: Date): Date[];
    calenderWeekChange: (day: Date) => void;
    render(): import("lit-html").TemplateResult<1>;
}
