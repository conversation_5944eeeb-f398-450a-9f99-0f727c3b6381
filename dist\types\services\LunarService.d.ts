export declare class LunarService {
    private PI;
    private mDay;
    private mMonth;
    private mYear;
    private mTimeZone;
    mLunarDay: number;
    mLunarYear: number;
    mLunarMonth: number;
    constructor();
    setDate(day: number, month: number, year: number, timeZone: number): void;
    private getNewMoonDay;
    private jdFromDate;
    private getSunLongitude;
    private getLunarMonth11;
    private getLeapMonthOffset;
    convertToLunar(): Date;
    static convertToLunar(year: number, month: number, day: number, timeZone?: number): Date;
}
