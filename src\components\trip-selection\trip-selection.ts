import { css, LitElement, PropertyValues, unsafeCSS } from "lit";
import { customElement, property, state } from "lit/decorators.js";
import { TripSelectionTemplate } from "./trip-selection-template";
import { localStorageService } from "../../services/LocalStorageService";
import { formatDate } from "../../utils/dateUtils";
import { CryptoService } from "../../services/CryptoService";
import { FlightService } from "../../services/FlightService";
import { getAirportInfoByCode } from "../../services/WorldServices";
import { CheckResponseAdvance } from "../../workers/search-trip.worker";
import { formatNumber } from "../../utils/dateUtils";
import { combineGroupsEnd, combineGroupsStart, filterLegsInRange, filterSearchTripResponseByCombine, filterTimeDepartureDateInRange, getGroupCodeRefSelected } from "../../workers/format-search-trip.worker";
import styles from '../../styles/styles.css';
import { Modal } from "../modal";


const cryptoService = new CryptoService();
const flightService = new FlightService();

@customElement("trip-selection")
export class TripSelection extends LitElement {
  static styles = [
    unsafeCSS(styles),
    css`
        :host {
        font-family: var(--nmt-font, 'Roboto', sans-serif);
        }
        `
  ];
  @property({ type: String }) mode = "online";
  @property({ type: String }) color = "";
  @property({ type: String }) font = "";
  @property({ type: String }) googleFontsUrl = "";
  @property({ type: String }) ApiKey = '';
  @property({ type: String }) redirect_uri = "TripPassengers";
  @property({ type: Boolean }) autoLanguageParam = false; // Bật/tắt tính năng tự động thêm language param

  private _language = "vi";
  private _hasCheckedURL = false; // Flag để tránh kiểm tra URL nhiều lần

  @property({ type: String })
  get language(): string {
    return this._language;
  }

  set language(value: string) {
    const oldValue = this._language;

    // Chỉ kiểm tra URL nếu autoLanguageParam được bật
    if (this.autoLanguageParam) {
      const urlParams = new URLSearchParams(window.location.search);
      const languageParam = urlParams.get('language');

      if (languageParam && languageParam !== this._language) {
        // URL có language parameter - luôn ưu tiên URL
        this._language = languageParam;
        
      } else {
        // URL không có language parameter - sử dụng giá trị được set
        this._language = value;
        // Tự động thêm vào URL nếu chưa có
        if (!this._hasCheckedURL) {
          this.updateURLWithLanguage();
          this._hasCheckedURL = true;
        }
      }
    } else {
      // Không sử dụng URL parameter, chỉ set giá trị trực tiếp
      this._language = value;
    }

    this.requestUpdate('language', oldValue);
  }
  @property({ type: String }) uri_searchBox = "";
  @property({ type: Boolean }) showFromText = true;
  @property({ type: Boolean }) showDetailTicket = true;
  @property({ type: Boolean }) showDetailFareRule = true;
  @property({ type: Boolean }) showMoreClassic = true;
  @property({ type: String }) autoShowPriceDetails = "false";
  @property({ type: Boolean }) showLanguageSelect = false;
  @property({ type: Boolean }) hideMultiSegmentFlights = false;

  get isAutoShowPriceDetails(): boolean {
    return this.autoShowPriceDetails === "true";
  }
  get currencySymbolAv(): string{
    return this.convertedVND === 1 || this.language === 'vi'  ? '₫' : this.currencySymbol;
  }

  @state() private _ApiKey: string = '';
  @state() private searchTripResponseMain: any[] = [];
  @state() private _searchTripResponse: any[] = [];
  @state() private _searchTripRequest: TripSelectionModelRq | null = null;
  @state() private _isLoading: boolean = false;
  @state() private _isGlobal: boolean = false;
  @state() private _isMobileDevice: boolean = false;
  @state() private _isShowLoading: boolean = false;
  @state() private _isError: boolean = false;
  @state() private _isShowPriceDetail: boolean = false;
  @state() private InventoriesSelected: any[] = [];
  @state() private inforAirports: any = {};
  @state() private _airItinerarySelected: number = 0;
  @state() private pricePaxInfor: any[] = [];
  @state() private _sumPrice: number = 0;
  @state() private _progress: number = 0;
  @state() private calendarWeek: Date[] = [];
  @state() private _isShowDetail: boolean = false;
  @state() private _isShowPriceDetails: boolean = false;
  @state() private canScrollLeft: boolean = false;
  @state() private canScrollRight: boolean = false;
  @state() private currentSortOption: 'price' | 'airline' | 'departure' = 'price';
  @state() private dateStart: Date = new Date();
  @state() private showOffcanvas: boolean = false;
  @state() private showMultiSegmentFlights: boolean = false;

  @state() private isCountDown: boolean = false;
  @state() private countdown: number = 0;
  @state() private isopenModalResult: boolean = false;

  @state() private departureItem: any = null;
  @state() private arrivalItem: any = null;
  @state() private allAirlineResult: any[] = [];
  @state() private segmentsSelected: {
    segmentKeyRef: any,
    sumPrice: any
  }[] = [];

  @state() private fareRulesRquest: any[] = [];
  @state() private fareRules: any[] = [];
  @state() private maxCountLeg: number = 0;
  @state() private displayMode: 'total' | 'perPassenger' = 'total';
  @state() private convertedVND: number = 1;
  @state() private currencySymbol: string = '₫';
  @state() private isCurrencyLoaded: boolean = false;

  private _retryCount = 0;
  private readonly _maxRetry = 2;

  rangeSlider: [number, number] = [0, 24];
  rangeSliderLegs: [number, number] = [0, 0];
  constructor(
    private _cryptoService: CryptoService,
    private _flightService: FlightService
  ) {
    super();

    this._cryptoService = cryptoService;
    this._flightService = flightService;
  }

  connectedCallback(): void {
    super.connectedCallback();
    this._ApiKey = this.ApiKey;
    this.removeAttribute("ApiKey");

    // Kiểm tra URL ngay khi component được kết nối, trường hợp không có attribute language
    this.checkLanguageFromURL();
  }

  private checkLanguageFromURL(): void {
    // Chỉ kiểm tra URL nếu autoLanguageParam được bật
    if (!this.autoLanguageParam) {
      return;
    }

    const urlParams = new URLSearchParams(window.location.search);
    const languageParam = urlParams.get('language');

    if (languageParam) {
      // URL có language parameter - set giá trị từ URL
      this._language = languageParam;
      this.requestUpdate('language');
    } else if (!this._hasCheckedURL) {
      // URL không có language parameter - tự động thêm vào URL với giá trị mặc định
      this.updateURLWithLanguage();
      this._hasCheckedURL = true;
    }
  }

  private updateURLWithLanguage(): void {
    const currentUrl = new URL(window.location.href);
    const params = new URLSearchParams(currentUrl.search);

    // Thêm hoặc cập nhật parameter language
    params.set('language', this._language);

    // Cập nhật URL mà không reload trang
    const newUrl = `${currentUrl.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  }

  private async loadInitialAirportInfo(): Promise<void> {
    // Load thông tin sân bay ban đầu từ thông tin trong request
    if (this._searchTripRequest?.OriginDestinationTrip) {
      const initialAirports: string[] = [];

      this._searchTripRequest.OriginDestinationTrip.forEach(trip => {
        if (trip.OriginCode && !initialAirports.includes(trip.OriginCode)) {
          initialAirports.push(trip.OriginCode);
        }
        if (trip.DestinationCode && !initialAirports.includes(trip.DestinationCode)) {
          initialAirports.push(trip.DestinationCode);
        }
      });

      if (initialAirports.length > 0) {
        await this.getInforAirports(initialAirports, false);

        // Cập nhật departureItem và arrivalItem từ inforAirports sau khi load
        this.updateDepartureArrivalItems();
      }
    }
  }

  private updateDepartureArrivalItems(): void {
    if (this._searchTripRequest?.OriginDestinationTrip && this.inforAirports) {
      const departureCode = this._searchTripRequest.OriginDestinationTrip[0]?.OriginCode;
      const arrivalCode = this._searchTripRequest.OriginDestinationTrip[0]?.DestinationCode;

      if (departureCode && this.inforAirports[departureCode]) {
        this.departureItem = {
          code: departureCode,
          name: this.inforAirports[departureCode].name,
          cityName: this.inforAirports[departureCode].cityName
        };
      }

      if (arrivalCode && this.inforAirports[arrivalCode]) {
        this.arrivalItem = {
          code: arrivalCode,
          name: this.inforAirports[arrivalCode].name,
          cityName: this.inforAirports[arrivalCode].cityName
        };
      }
    }
  }

  protected async firstUpdated(_changedProperties: PropertyValues): Promise<void> {
    super.firstUpdated(_changedProperties);

    // Đảm bảo sort option mặc định được set
    this.currentSortOption = 'price';

    // Khởi tạo giá trị mặc định cho showMultiSegmentFlights
    if (this.hideMultiSegmentFlights) {
      this.showMultiSegmentFlights = false;
    }

    this.loadDataRequest();
    this.dateStart = new Date(this._searchTripRequest?.OriginDestinationTrip[0].OriginDate || '');
    this.calendarWeek = this.createCalendarWeek(this.dateStart);

    // Load thông tin sân bay ngay từ đầu với thông tin từ request
    await this.loadInitialAirportInfo();

    this.clearData();
    await this.SearchTrip();
    this.requestUpdate();
  }

  protected updated(_changedProperties: PropertyValues): void {
    super.updated(_changedProperties);
    this.checkDevice();
  }

  checkDevice() {
    const width = window.innerWidth;
    this._isMobileDevice = width <= 768;
  }

  CheckisGlobal(airport: string | undefined): boolean {
    if (!airport) return false;
    const domesticAirports = [
      "SGN", "HAN", "HPH", "DIN", "VDO", "DAD", "CXR", "DLI", "VII", "HUI", "THD",
      "BMV", "PXU", "UIH", "VDH", "TBB", "VCL", "PQC", "VCS", "VCA", "CAH", "VKG"
    ];
    return !domesticAirports.includes(airport);
  }
  async RequestEncrypt(): Promise<any> {
    var airlines = ["VN", "VJ", "QH", "VU"];
    const airlinesGlobal = ["VN", "VJ", "QH", "VU", "1G", "AK"];
    if (!this._searchTripRequest) return;
    if (this.CheckisGlobal(this._searchTripRequest.OriginDestinationTrip[0].OriginCode) || this.CheckisGlobal(this._searchTripRequest.OriginDestinationTrip[0].DestinationCode)) {
      airlines = airlinesGlobal;
      this._isGlobal = true;
    }

    const payloads = airlines.map((airline) => ({
      ...this._searchTripRequest,
      airlines: airline,
    }));

    const payloadsEncrypted = await Promise.all(
      payloads.map(async (payload) => {
        const encryptedData = await this._cryptoService.eda(JSON.stringify(payload));
        return {
          EncryptData: encryptedData
        };
      })
    );
    return payloadsEncrypted;
  }

  getTotalPriceLowestWithMode(segment: any) {
    var sumprice = this.displayMode === 'total' ? segment?.LowestInventory?.SumPrice : (segment?.LowestInventory?.FareInfos[0]?.Fare + segment?.LowestInventory?.FareInfos[0]?.Tax);
    return formatNumber(sumprice, this.convertedVND, this.language);
  }

  getTotalPriceWithMode() {
    var sumprice = this.displayMode === 'total'
      ? this.segmentsSelected[this._airItinerarySelected].sumPrice
      : (this.InventoriesSelected[this._airItinerarySelected]?.inventorySelected?.FareInfos[0]?.Fare + this.InventoriesSelected[this._airItinerarySelected]?.inventorySelected?.FareInfos[0]?.Tax);
    return formatNumber(sumprice, this.convertedVND, this.language);
  }

  getInventoryPriceWithMode(inventory: any) {
    var sumprice = this.displayMode === 'total'
      ? inventory.SumPrice
      : inventory.FareInfos[0]?.Fare + inventory.FareInfos[0]?.Tax;
    return formatNumber(sumprice, this.convertedVND, this.language);
  }

  calculateMaxCountLeg() {
    let maxLegs = 0;
    this._searchTripResponse?.forEach((response: any) => {
      response?.SearchTripAirs?.forEach((air: any) => {
        air.AirItinerary[this._airItinerarySelected]?.AirSegments.forEach((segment: any) => {
          const legCount = segment.Legs?.length || 0;
          if (legCount > maxLegs) {
            maxLegs = legCount;
          }
        });
      });
    });
    this.maxCountLeg = maxLegs;
    this.rangeSliderLegs = [0, this.maxCountLeg];
  }
  async SearchTrip() {
    this._isLoading = true;
    this._isShowLoading = true;
    this._progress = 0;
    if (!this._cryptoService.ch()) {
      await this._cryptoService.spu();
    }
    await this.CallSearchTrip();

    this._isLoading = false;
    this._progress = 100;
    this._isShowLoading = false;
    this.calculateMaxCountLeg();
  }
  async CallSearchTrip() {
    var payloadsEncrypted = await this.RequestEncrypt();

    const progressPerPayload = 100 / payloadsEncrypted.length;
    let hasError = false;
    let isAirportInfoLoaded = false;
    const searchTripPromises = payloadsEncrypted.map(async (payload: any) => {
      if (this._retryCount >= this._maxRetry) return;
      try {
        const res = await this._flightService.SearchTrip(payload, this._ApiKey);
        const resDecrypted = await this._cryptoService.dda(res.resultObj);
        var resJson = JSON.parse(resDecrypted);

        this._progress += progressPerPayload;
        console.log(resJson);
        if (resJson.IsSuccessed) {
          var response = await this.checkResponseAdvance(resJson.ResultObj);
          if (response.success && !response.done && response.resultObj) {
            this._isLoading = false;
            this.searchTripResponseMain.push(response.resultObj);
            this._searchTripResponse = this.searchTripResponseMain;
            this.allAirlineResult = this.getAllAirlines();
            this.requestUpdate();

            // Gọi getInforAirports ngay khi có kết quả đầu tiên
            if (!isAirportInfoLoaded && this.searchTripResponseMain.length === 1) {
              isAirportInfoLoaded = true;
              const airports = this.getAllAirports();
              this.getInforAirports(airports);
            }
          }
        }
      } catch (error: any) {
        if (this._retryCount < this._maxRetry) {
          this._retryCount++;
          this._cryptoService.ra();
          await this._cryptoService.spu();
          await this.CallSearchTrip();
          return;
        } else {
          hasError = true;
        }
      }
    });

    await Promise.allSettled(searchTripPromises);

    if (this._retryCount < this._maxRetry && !hasError) {
      this.openModalResult();
      var airports = this.getAllAirports();
      // Gọi lại getInforAirports cho tất cả sân bay, KHÔNG reset isCurrencyLoaded
      this.getInforAirports(airports, true); // truyền thêm flag để không reset loading
      this.fareRulesRquest = this.getAllFareType();
      if (!this._isGlobal) {
        await this.getFareRules(this.fareRulesRquest);
      }
      this.allAirlineResult = this.getAllAirlines();
      if (this.searchTripResponseMain?.length > 0) {
        this.checkExpiryDate(this.getMinExpDate()?.toString());
      }
      await this.combineGroupsStart();

      this.sortWithPrice();
      this.requestUpdate();
    }
  }
  checkExpiryDate(expiryDate: string) {
    const expiry = new Date(expiryDate);
    const currentTime = new Date();
    let timeUntilExpiry = expiry.getTime() - currentTime.getTime();

    if (timeUntilExpiry > 0) {
      const interval = setInterval(() => {
        timeUntilExpiry -= 1000;

        if (timeUntilExpiry <= 0) {
          clearInterval(interval);
          this.openModal();
        }
      }, 1000);
    } else {
      this.openModal();
    }
  }

  openModal(title: string = 'Thông báo', content: string = `Thời gian đặt vé đã hết hạn.\n
    Vui lòng tải lại trang để xem kết quả mới nhất.`, isCountDown: boolean = true) {

    const modal = this.renderRoot.querySelector("modal-notification") as Modal;

    // Kiểm tra nếu modal tồn tại
    if (modal) {
      modal.start({
        title,
        content,
        isCountDown,
        countdown: 10,
      });
    }

    if (this.isCountDown) {
      const interval = setInterval(() => {
        this.countdown--;
        if (this.countdown === 0) {
          clearInterval(interval);
          this.reSearchTrip();
        }
      }, 1000);
    }
  }

  reSearchTrip() {
    const params = new URLSearchParams();

    // Chỉ thêm language parameter nếu autoLanguageParam được bật
    if (this.autoLanguageParam) {
      params.append('language', this.language);
    }

    const queryString = params.toString();
    window.location.href = queryString ? `${this.uri_searchBox}?${queryString}` : this.uri_searchBox;
  }

  async combineGroupsStart() {
    var result = combineGroupsStart(this.searchTripResponseMain);
    if (result) {
      this._searchTripResponse = result || [];
      this.allAirlineResult = this.getAllAirlines();
    }
  }

  async getFareRules(request: any) {
    if (request.length === 0) return;
    this.fareRules = await this._flightService.FareRules(request, this.language);
    if (this._searchTripResponse.length === 0) return;
    this._searchTripResponse.forEach((response: any) => {
      response.SearchTripAirs?.forEach((air: any) => {
        if (!air.Combine) {
          air.AirItinerary?.forEach((itinerary: any) => {
            itinerary.AirSegments?.forEach((segment: any) => {
              segment.Inventories?.forEach((inventory: any) => {
                if (inventory.BookingInfos[0].FareType) {
                  inventory.FareRule = this.getFareRuleByFareType(inventory.BookingInfos[0]?.FareType);
                }
              });
            });
          });
        }
      });
    });

  }
  getFareRuleByFareType(fareType: string): any {
    fareType = fareType.toUpperCase();
    return this.fareRules.find((item: any) => item.fareType.toUpperCase().includes(fareType));
  }

  getAllFareType(): any[] {
    var fareTypes: any[] = [];
    if (this._searchTripResponse.length === 0) return fareTypes;
    this._searchTripResponse?.forEach((response: any) => {
      response?.SearchTripAirs?.forEach((air: any) => {
        air.AirItinerary?.forEach((itinerary: any) => {
          itinerary?.AirSegments?.forEach((segment: any) => {
            segment?.Inventories?.forEach((inventory: any) => {
              inventory?.BookingInfos?.forEach((bookingInfo: any) => {
                if (bookingInfo.FareType === undefined) return;
                var fareType = {
                  FareType: bookingInfo.FareType,
                  Airline: segment.Airlines
                }
                if (!fareTypes.find((item: any) => item.FareType === bookingInfo.FareType)) {
                  fareTypes.push(fareType);
                }
              });
            });
          });
        });
      });
    });
    return fareTypes;
  }
  async getInforAirports(airportsCode: any, skipLoadingFlag = false) {
    if (!skipLoadingFlag) {
      this.isCurrencyLoaded = false;
    }
    try {
      var res = await getAirportInfoByCode(airportsCode, this.language || "vi", this._ApiKey);
      if (res.isSuccessed) {
        this.inforAirports = res.resultObj;
        this.displayMode = res.feature.displayMode || 'total';
        const currencyObj = typeof res.feature.currency === 'string' ? JSON.parse(res.feature.currency) : res.feature.currency;
        this.currencySymbol = currencyObj.symbol || '₫';
        this.convertedVND = currencyObj.convertedVND || 1;
        this.isCurrencyLoaded = true;

        // Cập nhật departureItem và arrivalItem nếu chưa có thông tin
        if (!this.departureItem || !this.arrivalItem) {
          this.updateDepartureArrivalItems();
        }
      }
    } catch (error: any) {
      console.error(error);
      this.isCurrencyLoaded = false;
    }
  }

  getAllAirports(): string[] {
    var airports: string[] = [];
    this.searchTripResponseMain?.forEach((response: any) => {
      response?.SearchTripAirs?.forEach((air: any) => {
        air.AirItinerary.forEach((itinerary: any) => {
          itinerary?.AirSegments?.forEach((segment: any) => {
            segment?.Legs?.forEach((leg: any) => {
              if (!airports.includes(leg.ArrivalCode)) {
                airports.push(leg.ArrivalCode);
              }
              if (!airports.includes(leg.DepartureCode)) {
                airports.push(leg.DepartureCode);
              }
            });
          });
        });
      });
    });
    return airports;
  }

  openModalResult() {
    this.countdown = 3;
    this.isopenModalResult = true;
    const interval = setInterval(() => {
      this.countdown--;
      if (this.countdown === 0) {
        clearInterval(interval);
        this.isopenModalResult = false;
      }
    }, 1000);
  }
  checkResponseAdvance(resultObj: any): any {
    return CheckResponseAdvance(resultObj);
  }
  clearData() {
    this.searchTripResponseMain = [];
    this._searchTripResponse = [];
    this.segmentsSelected = [];
    this.InventoriesSelected = [];
    this.allAirlineResult = [];
    // this._isShowModel = false;
    this._airItinerarySelected = 0;
  }

  createCalendarWeek(date: Date): Date[] {
    const calendarWeek: Date[] = [];
    for (let i = -3; i <= 3; i++) {
      const newDate = new Date(date);
      newDate.setDate(date.getDate() + i);
      calendarWeek.push(newDate);
    }
    return calendarWeek;
  }


  loadDataRequest() {
    var params = new URLSearchParams(window.location.search);
    var request: TripSelectionModelRq = {
      Airlines: '',
      Adult: Number(params.get('Adult')) || 0,
      Child: Number(params.get('Child')) || 0,
      Infant: Number(params.get('Infant')) || 0,
      OriginDestinationTrip: [],
      PromoCode: null,
      TypeTrip: ''
    };
    var originDestinationTrip: OriginDestinationTrip[] = [];
    originDestinationTrip.push({
      OriginCode: params.get('departure') || '',
      DestinationCode: params.get('arrival') || '',
      OriginDate: params.get('dateStart') || ''
    });
    if (params.get('dateEnd')) {
      originDestinationTrip.push({
        OriginCode: params.get('arrival') || '',
        DestinationCode: params.get('departure') || '',
        OriginDate: params.get('dateEnd') || ''
      });
    }
    request.OriginDestinationTrip = originDestinationTrip;
    request.TypeTrip = request.OriginDestinationTrip.length > 1 ? 'RT' : 'OW';
    this._searchTripRequest = request;

    var searchFlight = localStorageService.getItem('searchFlight', 'searchFlight');
    if (searchFlight) {
      const dataR = JSON.parse(searchFlight);
      // Chỉ cập nhật nếu chưa có thông tin từ loadInitialAirportInfo
      if (!this.departureItem) {
        this.departureItem = dataR.airports?.find((x: any) => x.type === 'departure')?.airport;
      }
      if (!this.arrivalItem) {
        this.arrivalItem = dataR.airports?.find((x: any) => x.type === 'arrival')?.airport;
      }
    }
    this.requestUpdate();

  }

  showPriceDetail() {
    this._isShowPriceDetail = !this._isShowPriceDetail;
    this.getPricePax();
  }
  setShowOffcanvas(show: boolean) {
    this.showOffcanvas = show;
  }


  getPricePax() {
    var data;
    var typePax = ['ADT', 'CHD', 'INF'];
    if (this.InventoriesSelected[0].combine && this.InventoriesSelected.length > 1) {
      data = this.InventoriesSelected[1].inventorySelected?.FareInfos;
    } else {
      var result: {
        PaxType: string,
        Fare: number,
        Tax: number,
      }[] = [];
      typePax.forEach(paxType => {
        result.push({ PaxType: paxType, Fare: 0, Tax: 0 });
      });

      this.InventoriesSelected.forEach((inventory: any) => {
        inventory.inventorySelected.FareInfos.forEach((fareInfo: any) => {
          if (typePax.includes(fareInfo.PaxType)) {
            const paxResult = result.find(r => r.PaxType === fareInfo.PaxType);
            if (paxResult) {
              paxResult.Fare += fareInfo.Fare;
              paxResult.Tax += fareInfo.Tax;
            }
          }
        });
      });

      if (this._searchTripRequest?.Adult === undefined || this._searchTripRequest?.Adult === 0) {
        result = result.filter(r => r.PaxType !== 'ADT');
      }
      if (this._searchTripRequest?.Child === undefined || this._searchTripRequest?.Child === 0) {
        result = result.filter(r => r.PaxType !== 'CHD');
      }
      if (this._searchTripRequest?.Infant === undefined || this._searchTripRequest?.Infant === 0) {
        result = result.filter(r => r.PaxType !== 'INF');
      }
      data = result;
    }
    this.pricePaxInfor = data;
  }


  checkNextStep(): boolean {
    if (this._searchTripRequest && this._progress === 100) {
      if (!this._isLoading && this.searchTripResponseMain.length > 0
        && this.segmentsSelected[this._airItinerarySelected] !== undefined
        && this._airItinerarySelected !== this._searchTripRequest?.OriginDestinationTrip?.length - 1) {
        return true;
      }
    }
    return false;
  }

  async nextStep() {
    let result: any;
    try {
      if (this.InventoriesSelected.length > 0 && this._isGlobal && this.InventoriesSelected[this._airItinerarySelected || 0].combine === true) {
        result = await this.combineGroupsEnd();
      } else {
        result = await this.filterSearchTripResponseByCombine(false);
      }
    } catch (error) {
    } finally {
      if (result) {
        // Xóa dữ liệu cũ
        this._searchTripResponse = [];
        await this.updateComplete;

        // Cập nhật dữ liệu mới
        this._searchTripResponse = result;
        this.allAirlineResult = this.getAllAirlines();
        this._airItinerarySelected++;

        // Đợi DOM cập nhật
        await this.updateComplete;

        // Sắp xếp lại theo giá sau khi lọc
        this.sortWithPrice();

        // Force update để đảm bảo LitElement cập nhật giao diện
        this.requestUpdate();
      }
      window.scrollTo(0, 0);
    }
  }
  handleRangeSliderChange = async (event: CustomEvent) => {
    const newRange = event.detail;
    // Chỉ thực hiện khi giá trị thay đổi
    if (this.rangeSlider[0] !== newRange[0] || this.rangeSlider[1] !== newRange[1]) {
      this.rangeSlider = newRange;
      await this.filterTimeDepartureDateInRange();
      this.sortWithDepartureDate();
    }
  };

  handleRangeSliderLeg = async (event: CustomEvent) => {
    const newRange = event.detail;
    // Chỉ thực hiện khi giá trị thay đổi
    if (this.rangeSliderLegs[0] !== newRange[0] || this.rangeSliderLegs[1] !== newRange[1]) {
      this.rangeSliderLegs = newRange;
      await this.filterLegsInRange();
      this.sortWithDepartureDate();
    }
  };

  async filterLegsInRange() {
    const minLegs = this.rangeSliderLegs[0];
    const maxLegs = this.rangeSliderLegs[1];

    const result = await filterLegsInRange(
      minLegs,
      maxLegs,
      this.allAirlineResult,
      this.searchTripResponseMain,
      this._airItinerarySelected
    );

    // Reset và cập nhật lại dữ liệu
    this._searchTripResponse = [];
    this.requestUpdate();

    setTimeout(() => {
      this._searchTripResponse = JSON.parse(JSON.stringify(result));
      this.requestUpdate();
      this.forceUpdateDOM();
    }, 0);
  }

  async filterTimeDepartureDateInRange() {
    const hourStart = this.rangeSlider[0];
    const hourEnd = this.rangeSlider[1];

    this._searchTripResponse = [];

    const result = await filterTimeDepartureDateInRange(
      hourStart,
      hourEnd,
      this.allAirlineResult,
      this.searchTripResponseMain,
      this._airItinerarySelected
    );

    this._searchTripResponse = [...result]; // Tạo bản sao mới để đảm bảo LitElement nhận ra sự thay đổi

    // Đợi DOM cập nhật
    await this.updateComplete;

    // Sắp xếp lại theo giá sau khi lọc
    this.sortWithPrice();

    this.requestUpdate();
  }

  async combineGroupsEnd(): Promise<any> {
    return combineGroupsEnd(
      this.searchTripResponseMain,
      this.InventoriesSelected[this._airItinerarySelected || 0].segment,
      this.InventoriesSelected[this._airItinerarySelected || 0].combine
    );
  }
  filterSearchTripResponseByCombine(combine: boolean): Promise<any> {
    return filterSearchTripResponseByCombine(this.searchTripResponseMain, combine);
  }
  getAllAirlines(): any[] {
    var airlines: any[] = [];
    if (this._searchTripResponse.length === 0) return airlines;
    this._searchTripResponse?.forEach((response: any) => {
      response?.SearchTripAirs?.forEach((air: any) => {
        air.AirItinerary[this._airItinerarySelected]?.AirSegments.forEach((segment: any) => {
          var airline = {
            airlines: segment.Airlines,
            airlinesName: segment.AirlinesName,
            checked: false
          }
          if (!airlines.find((item: any) => item.airlines === segment.Airlines)) {
            airlines.push(airline);
          }
        });
      });
    });
    return airlines;
  }

  selectSegment(bookGDS: string, airCodeRef: string, combine: boolean, segmentKeyRef: any, sumPrice: any) {
    var checkSegment = this.segmentsSelected.find((item: any) => item.segmentKeyRef === segmentKeyRef);
    if (!checkSegment) {
      this.selectInventory(bookGDS, airCodeRef, combine, segmentKeyRef, sumPrice);
    }

  }

  selectInventory(bookGDS: string, airCodeRef: string, combine: boolean, segmentKeyRef: any, sumPrice: any) {
    this._isShowDetail = false;
    this._isShowPriceDetails = this.isAutoShowPriceDetails;
    var segmentValue = {
      segmentKeyRef: segmentKeyRef,
      sumPrice: sumPrice
    }

    var segmentST = this._searchTripResponse.map((response: any) => {
      return response.SearchTripAirs?.map((air: any) => {
        return air.AirItinerary[this._airItinerarySelected]?.AirSegments.find((segment: any) => segment.KeyRef === segmentKeyRef);
      }).filter((segment: any) => segment !== undefined);
    }).flat().find((segment: any) => segment !== undefined);

    var inventorySelected = segmentST?.Inventories.find((inventory: any) => inventory.SumPrice === sumPrice);
    if (!this.segmentsSelected[this._airItinerarySelected]) {
      this.segmentsSelected.push(segmentValue);
      this.InventoriesSelected.push(
        {
          airCodeRef: airCodeRef,
          segment: segmentST,
          inventorySelected: inventorySelected,
          combine: combine,
          bookGDS: bookGDS
        }
      )
    } else {
      this.segmentsSelected[this._airItinerarySelected] = segmentValue;
      this.InventoriesSelected[this._airItinerarySelected] = {
        airCodeRef: airCodeRef,
        segment: segmentST,
        inventorySelected: inventorySelected,
        combine: combine,
        bookGDS: bookGDS
      }
    }
    this.checkScroll();
    this._sumPrice = this.getSumPrice();
    this.requestUpdate();
  }

  getSumPrice(): number {
    if (this.InventoriesSelected.length === 1 && this.InventoriesSelected[0].combine) {
      return this.InventoriesSelected[0].inventorySelected?.SumPrice || 0;
    } else if (this.InventoriesSelected.length > 1 && this.InventoriesSelected[0].combine) {
      return this.InventoriesSelected[1].inventorySelected?.SumPrice || 0;
    }
    return this.InventoriesSelected.reduce((total, inventory) => {
      return total + (inventory?.inventorySelected?.SumPrice || 0);
    }, 0);
  }


  checkScroll() {
    setTimeout(() => {
      const container = this.renderRoot.querySelector('.scroll-container');
      if (!container) {
        return;
      }
      const scrollLeft = container.scrollLeft;
      const clientWidth = container.clientWidth;
      const scrollWidth = container.scrollWidth;

      this.canScrollLeft = scrollLeft > 0;
      this.canScrollRight = Math.ceil(scrollLeft + clientWidth) < scrollWidth;
    }, 100);
  }
  isShowDetailSegment(segmentMain: any) {
    segmentMain.IsShowDetail = segmentMain.IsShowDetail === undefined ? true : !segmentMain.IsShowDetail;
    if (this.isAutoShowPriceDetails) {
      this._isShowPriceDetails = true;
    }
    this._searchTripResponse.map((response: any) => {
      return response.SearchTripAirs?.map((air: any) => {
        return air.AirItinerary[this._airItinerarySelected]?.AirSegments.map((segment: any) => {
          if (segmentMain.KeyRef !== segment.KeyRef) {
            segment.IsShowDetail = false;
          }
        });
      });
    });

    this.requestUpdate();
  }
  async reSelectFlight() {
    // Xóa dữ liệu cũ
    this.segmentsSelected = [];
    this.InventoriesSelected = [];
    this.allAirlineResult = [];
    this._airItinerarySelected = 0;
    this._sumPrice = 0;
    this._searchTripResponse = [];

    // Đợi DOM cập nhật
    await this.updateComplete;

    // Cập nhật dữ liệu mới
    var airports = this.getAllAirports();
    await this.getInforAirports(airports);
    await this.combineGroupsStart();

    // Đợi DOM cập nhật
    await this.updateComplete;

    // Sắp xếp lại theo giá
    this.sortWithPrice();

    // Force update để đảm bảo LitElement cập nhật giao diện
    this.requestUpdate();
  }

  showPriceDetails(event: Event) {
    event.stopPropagation();
    this._isShowPriceDetails = !this._isShowPriceDetails;
  }

  scrollInventory(arror: string) {
    const container = this.renderRoot.querySelector('.scroll-container');
    if (!container) {
      return;
    }
    const scrollAmount = this._isMobileDevice ? 200 : 230;
    if (arror === 'left') {
      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    } else {
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
    this.checkScroll();
  }
  checkBookingButton(): boolean {
    if (this.segmentsSelected.length === this._searchTripRequest?.OriginDestinationTrip?.length) {
      return true;
    }
    return false;
  }
  async nextPage() {
    var dataCodeRef = await this.getGroupCodeRefSelected();
    var data = {
      adult: this._searchTripRequest?.Adult,
      child: this._searchTripRequest?.Child,
      infant: this._searchTripRequest?.Infant,
      InventoriesSelected: this.InventoriesSelected,
      expDate: this.getMinExpDate(),
      url: window.location.href,
      dataCodeRef: dataCodeRef
    }
    localStorageService.setItem('cartTicket', JSON.stringify(data), 'cartTicket');
    const params = new URLSearchParams();

    // Chỉ thêm language parameter nếu autoLanguageParam được bật
    if (this.autoLanguageParam) {
      params.append('language', this.language);
    }

    const queryString = params.toString();
    window.location.href = queryString ? `${this.redirect_uri}?${queryString}` : this.redirect_uri;
  }
  getMinExpDate(): Date {
    var minExpDate: any;
    this.searchTripResponseMain.forEach((response: any, index: number) => {
      if (index === 0) {
        minExpDate = new Date(response.ExpDate);
      }
      var expDate = new Date(response.ExpDate);
      if (expDate < minExpDate) {
        minExpDate = expDate;
      }
    });
    return minExpDate;
  }
  getGroupCodeRefSelected(): Promise<any> {
    return getGroupCodeRefSelected(this.searchTripResponseMain, this.InventoriesSelected);
  }
  sortWithPrice() {
    this.currentSortOption = 'price';
    const segmentContainer = this.renderRoot.querySelector('.segmentContainer');
    if (segmentContainer) {
      const segmentItems = segmentContainer.querySelectorAll('.segment-item');
      const segmentItemsArray = Array.from(segmentItems);

      segmentItemsArray.sort((a: any, b: any) => {
        return Number(a.getAttribute('data-price')) - Number(b.getAttribute('data-price'));
      });

      // Xóa hết các phần tử con hiện tại
      segmentItemsArray.forEach(item => segmentContainer.removeChild(item));

      // Thêm lại theo thứ tự đã sort
      segmentItemsArray.forEach(item => segmentContainer.appendChild(item));

      // Force update để đảm bảo LitElement cập nhật giao diện
      this.requestUpdate();

      // Áp dụng lại filter đa chặng sau khi sort
      setTimeout(() => {
        this.applyMultiSegmentFilter();
      }, 0);
    }
  }
  sortWithAirline() {
    this.currentSortOption = 'airline';
    const segmentContainer = this.renderRoot.querySelector('.segmentContainer');
    if (segmentContainer) {
      const segmentItems = segmentContainer.querySelectorAll('.segment-item');
      const segmentItemsArray = Array.from(segmentItems);

      // Sắp xếp phần tử
      segmentItemsArray.sort((a: any, b: any) => {
        return a.getAttribute('data-airline').localeCompare(b.getAttribute('data-airline'));
      });

      // Xóa hết các phần tử con hiện tại
      segmentItemsArray.forEach(item => segmentContainer.removeChild(item));

      // Thêm lại theo thứ tự đã sort
      segmentItemsArray.forEach(item => segmentContainer.appendChild(item));

      // Force update để đảm bảo LitElement cập nhật giao diện
      this.requestUpdate();

      // Áp dụng lại filter đa chặng sau khi sort
      setTimeout(() => {
        this.applyMultiSegmentFilter();
      }, 0);
    }
  }
  sortWithDepartureDate() {
    this.currentSortOption = 'departure';
    const segmentContainer = this.renderRoot.querySelector('.segmentContainer');
    if (segmentContainer) {
      const segmentItems = segmentContainer.querySelectorAll('.segment-item');
      const segmentItemsArray = Array.from(segmentItems);

      segmentItemsArray.sort((a: any, b: any) => {
        return new Date(a.getAttribute('data-departure')).getTime() - new Date(b.getAttribute('data-departure')).getTime();
      });

      // Xóa hết các phần tử con hiện tại
      segmentItemsArray.forEach(item => segmentContainer.removeChild(item));

      // Thêm lại theo thứ tự đã sort
      segmentItemsArray.forEach(item => segmentContainer.appendChild(item));

      // Force update để đảm bảo LitElement cập nhật giao diện
      this.requestUpdate();

      // Áp dụng lại filter đa chặng sau khi sort
      setTimeout(() => {
        this.applyMultiSegmentFilter();
      }, 0);
    }
  }
  async filterByAirlines() {
    const selectedAirlines = this.allAirlineResult
      .filter(airline => airline.checked && airline.airlines !== 'all')
      .map(airline => airline.airlines);


    // Nếu không có hãng nào được chọn, hiển thị tất cả
    if (selectedAirlines.length === 0) {
      this._searchTripResponse = JSON.parse(JSON.stringify(this.searchTripResponseMain));
      this.requestUpdate();
      this.forceUpdateDOM();
      return;
    }

    let filteredResults = this.searchTripResponseMain.map((response: any) => {
      // Tạo bản sao của response để tránh thay đổi dữ liệu gốc
      const newResponse = JSON.parse(JSON.stringify(response));

      // Lọc SearchTripAirs
      newResponse.SearchTripAirs = newResponse.SearchTripAirs.filter((air: any) => {
        // Lọc AirItinerary
        air.AirItinerary = air.AirItinerary.map((itinerary: any) => {
          // Lọc AirSegments
          itinerary.AirSegments = itinerary.AirSegments.filter((segment: any) => {
            return selectedAirlines.includes(segment.Airlines);
          });
          return itinerary;
        });
        // Chỉ giữ lại AirItinerary có AirSegments
        return air.AirItinerary.some((itinerary: any) => itinerary.AirSegments.length > 0);
      });

      return newResponse;
    }).filter((response: any) => response.SearchTripAirs.length > 0);

    filteredResults = await combineGroupsStart(filteredResults);

    // Reset và cập nhật lại dữ liệu
    this._searchTripResponse = [];
    this.requestUpdate();

    setTimeout(() => {
      this._searchTripResponse = filteredResults;
      this.requestUpdate();
      this.forceUpdateDOM();
    }, 0);
  }

  filterAirlines(event: Event, airline: string) {
    event.stopPropagation();
    const checked = event.target ? (event.target as HTMLInputElement).checked : false;

    // Cập nhật trạng thái checked
    if (airline === 'all') {
      // Nếu chọn "Tất cả"
      this.allAirlineResult = this.allAirlineResult.map((item: any) => ({
        ...item,
        checked: checked
      }));
    } else {
      // Nếu chọn một hãng cụ thể
      this.allAirlineResult = this.allAirlineResult.map((item: any) => ({
        ...item,
        checked: item.airlines === airline ? !item.checked : item.checked
      }));

      // Kiểm tra nếu tất cả các hãng đều được chọn
      const allChecked = this.allAirlineResult.every(item => item.checked);
      // Cập nhật trạng thái của checkbox "Tất cả"
      this.allAirlineResult = this.allAirlineResult.map((item: any) => ({
        ...item,
        checked: item.airlines === 'all' ? allChecked : item.checked
      }));
    }

    // Force update trước khi filter
    this.requestUpdate();

    // Thực hiện filter sau khi đã cập nhật trạng thái
    setTimeout(() => {
      this.filterByAirlines();
      this.sortWithPrice();
      this.requestUpdate();
      this.forceUpdateDOM();
      window.scrollTo(0, 0);
    }, 0);
  }

  checkedAllAirlinesStatus(): boolean {
    // Kiểm tra xem tất cả các hãng (trừ "Tất cả") có được chọn không
    return this.allAirlineResult
      .filter(item => item.airlines !== 'all')
      .every(item => item.checked);
  }

  // Kiểm tra xem segment có phải là chuyến bay đa chặng không
  isMultiSegmentFlight(segment: any): boolean {
    if (!segment || !segment.Legs) {
      return false;
    }
    const legCount = Array.isArray(segment.Legs) ? segment.Legs.length : 0;
    return legCount > 1;
  }

  // Toggle hiển thị chuyến bay đa chặng
  toggleMultiSegmentFlights() {
    this.showMultiSegmentFlights = !this.showMultiSegmentFlights;
    this.requestUpdate();

    // Sau khi toggle, cần áp dụng lại filter và sort theo option hiện tại
    setTimeout(() => {
      this.applyMultiSegmentFilter();

      // Sắp xếp lại theo currentSortOption đang chọn (không áp dụng filter để tránh duplicate)
      switch (this.currentSortOption) {
        case 'price':
          this.sortWithPriceOnly();
          break;
        case 'airline':
          this.sortWithAirlineOnly();
          break;
        case 'departure':
          this.sortWithDepartureDateOnly();
          break;
        default:
          this.sortWithPriceOnly(); // fallback
          break;
      }

      this.forceUpdateDOM();

    }, 0);
  }

  // Sort methods không áp dụng filter (để tránh duplicate filter calls)
  sortWithPriceOnly() {
    this.currentSortOption = 'price';
    const segmentContainer = this.renderRoot.querySelector('.segmentContainer');
    if (segmentContainer) {
      const segmentItems = segmentContainer.querySelectorAll('.segment-item');
      const segmentItemsArray = Array.from(segmentItems);

      segmentItemsArray.sort((a: any, b: any) => {
        return Number(a.getAttribute('data-price')) - Number(b.getAttribute('data-price'));
      });

      // Xóa hết các phần tử con hiện tại
      segmentItemsArray.forEach(item => segmentContainer.removeChild(item));

      // Thêm lại theo thứ tự đã sort
      segmentItemsArray.forEach(item => segmentContainer.appendChild(item));

      this.requestUpdate();
    }
  }

  sortWithAirlineOnly() {
    this.currentSortOption = 'airline';
    const segmentContainer = this.renderRoot.querySelector('.segmentContainer');
    if (segmentContainer) {
      const segmentItems = segmentContainer.querySelectorAll('.segment-item');
      const segmentItemsArray = Array.from(segmentItems);

      segmentItemsArray.sort((a: any, b: any) => {
        return a.getAttribute('data-airline').localeCompare(b.getAttribute('data-airline'));
      });

      segmentItemsArray.forEach(item => segmentContainer.removeChild(item));
      segmentItemsArray.forEach(item => segmentContainer.appendChild(item));

      this.requestUpdate();
    }
  }

  sortWithDepartureDateOnly() {
    this.currentSortOption = 'departure';
    const segmentContainer = this.renderRoot.querySelector('.segmentContainer');
    if (segmentContainer) {
      const segmentItems = segmentContainer.querySelectorAll('.segment-item');
      const segmentItemsArray = Array.from(segmentItems);

      segmentItemsArray.sort((a: any, b: any) => {
        return new Date(a.getAttribute('data-departure')).getTime() - new Date(b.getAttribute('data-departure')).getTime();
      });

      segmentItemsArray.forEach(item => segmentContainer.removeChild(item));
      segmentItemsArray.forEach(item => segmentContainer.appendChild(item));

      this.requestUpdate();
    }
  }

  // Áp dụng filter đa chặng trên DOM
  applyMultiSegmentFilter() {
    if (!this.hideMultiSegmentFlights) return;

    const segmentContainer = this.renderRoot.querySelector('.segmentContainer');
    if (segmentContainer) {
      const segmentItems = segmentContainer.querySelectorAll('.segment-item');

      let hiddenCount = 0;
      let shownCount = 0;

      segmentItems.forEach((segmentElement: Element) => {
        // Sử dụng data attribute để kiểm tra nhanh hơn
        const isMultiSegment = segmentElement.getAttribute('data-multi-segment') === 'true';
        const shouldHide = isMultiSegment && !this.showMultiSegmentFlights;

        if (shouldHide) {
          (segmentElement as HTMLElement).style.display = 'none';
          hiddenCount++;
        } else {
          (segmentElement as HTMLElement).style.display = 'block';
          shownCount++;
        }
      });
    }
  }

  // Lấy segment data từ DOM element
  getSegmentDataFromElement(element: Element): any | null {
    try {
      // Tìm segment data từ _searchTripResponse dựa trên data attributes
      const price = element.getAttribute('data-price');
      const airline = element.getAttribute('data-airline');
      const departure = element.getAttribute('data-departure');

      if (!price || !airline || !departure) return null;

      // Tìm segment tương ứng trong data
      for (const response of this._searchTripResponse) {
        for (const searchTripAir of response?.SearchTripAirs || []) {
          const airSegments = searchTripAir.AirItinerary[this._airItinerarySelected]?.AirSegments || [];
          for (const segment of airSegments) {
            if (segment?.LowestInventory?.SumPrice?.toString() === price &&
                segment.AirlinesName === airline &&
                segment.DepartureDate === departure) {
              return segment;
            }
          }
        }
      }
      return null;
    } catch (error) {
      console.error('Error getting segment data from element:', error);
      return null;
    }
  }

  // Phương thức để force update DOM
  private forceUpdateDOM() {
    const segmentContainer = this.renderRoot.querySelector('.segmentContainer');
    if (segmentContainer) {
      const segmentItems = segmentContainer.querySelectorAll('.segment-item');
      segmentItems.forEach(item => {
        const element = item as HTMLElement;
        const display = element.style.display;
        element.style.display = 'none';
        void element.offsetHeight; // Force reflow
        element.style.display = display;
      });
    }
  }

  handleWeekChange(event: CustomEvent) {
    this.dateStart = event.detail;
    this.calendarWeek = this.createCalendarWeek(this.dateStart);
    var params = new URLSearchParams(window.location.search);
    params.set('dateStart', formatDate(this.dateStart));
    window.history.replaceState({}, '', `${window.location.pathname}?${params}`);

    window.location.href = `${window.location.pathname}?${params.toString()}`;
  }

  getFileNameFromUrl(): string {
    const urlPath = window.location.pathname;
    return urlPath.substring(urlPath.lastIndexOf("/") + 1);
  }


  handleLanguageChange(newLang: string) {
    this.language = newLang;
    // Cập nhật lại passengerString nếu có thông tin hành khách
    const airports = this.getAllAirports();
    this.getInforAirports(airports);
    if (!this._isGlobal) {
      this.getFareRules(this.fareRulesRquest);
    }
    // Nếu cần load lại dữ liệu theo ngôn ngữ mới thì có thể gọi các hàm load lại dữ liệu ở đây
    // Ví dụ: this.getAirports(); hoặc các hàm tương tự nếu có

    // Tự động cập nhật URL với language mới
    this.updateURLWithLanguage();

    // Cập nhật lại giao diện
    this.requestUpdate();
  }

  render() {
    const search_redirect = this.getFileNameFromUrl();
    return TripSelectionTemplate(
      this.uri_searchBox,
      this.mode,
      this.language,
      this.color,
      this.font,
      this.googleFontsUrl,
      this._ApiKey,
      this.currencySymbolAv,
      this.convertedVND,
      this._isLoading,
      this._isShowLoading,
      this._isError,
      this._isMobileDevice,
      this._isGlobal,
      this._searchTripResponse,
      this.departureItem,
      this.arrivalItem,
      this._isShowPriceDetail,
      this._isShowPriceDetails,
      this.InventoriesSelected,
      this._airItinerarySelected,
      this.inforAirports,
      this.pricePaxInfor,
      this._searchTripRequest,
      this._sumPrice,
      this._progress,
      this.dateStart,
      this.segmentsSelected,
      this.canScrollLeft,
      this.canScrollRight,
      this.allAirlineResult,
      this.rangeSlider,
      this.rangeSliderLegs,
      this.maxCountLeg,
      this.showOffcanvas,
      this.isopenModalResult,
      this.showFromText,
      this.showDetailTicket,
      this.showDetailFareRule,
      this.showMoreClassic,
      search_redirect,
      // event
      this.getTotalPriceWithMode.bind(this),
      this.getTotalPriceLowestWithMode.bind(this),
      this.getInventoryPriceWithMode.bind(this),
      this.showPriceDetail.bind(this),
      this.checkNextStep.bind(this),
      this.nextStep.bind(this),
      this.selectSegment.bind(this),
      this.selectInventory.bind(this),
      this.isShowDetailSegment.bind(this),
      this.showPriceDetails.bind(this),
      this.scrollInventory.bind(this),
      this.checkBookingButton.bind(this),
      this.nextPage.bind(this),
      this.sortWithPrice.bind(this),
      this.sortWithAirline.bind(this),
      this.sortWithDepartureDate.bind(this),
      this.currentSortOption,
      this.filterAirlines.bind(this),
      this.checkedAllAirlinesStatus.bind(this),
      this.handleWeekChange.bind(this),
      this.handleRangeSliderChange.bind(this),
      this.handleRangeSliderLeg.bind(this),
      this.reSelectFlight.bind(this),
      this.setShowOffcanvas.bind(this),
      this.isCurrencyLoaded, // truyền sang template
      this.handleLanguageChange.bind(this),
      this.showLanguageSelect, // truyền thuộc tính mới
      this.hideMultiSegmentFlights,
      this.showMultiSegmentFlights,
      this.isMultiSegmentFlight.bind(this),
      this.toggleMultiSegmentFlights.bind(this)
    );
  }

  static get observedAttributes() {
    return [
      ...super.observedAttributes,
      'showfromtext',
      'showdetailticket',
      'showdetailfarerule',
      'show-more-classic',
      'hidemultisegmentflights',
    ];
  }

  attributeChangedCallback(name: string, oldVal: any, newVal: any) {
    super.attributeChangedCallback?.(name, oldVal, newVal);
    if (name === 'showfromtext') {
      if (newVal === null) {
        this.showFromText = true;
      } else if (newVal === 'false') {
        this.showFromText = false;
      } else {
        this.showFromText = true;
      }
    }
    if (name === 'showdetailticket') {
      if (newVal === null) {
        this.showDetailTicket = true;
      } else if (newVal === 'false') {
        this.showDetailTicket = false;
      } else {
        this.showDetailTicket = true;
      }
    }
    if (name === 'showdetailfarerule') {
      if (newVal === null) {
        this.showDetailFareRule = true;
      } else if (newVal === 'false') {
        this.showDetailFareRule = false;
      } else {
        this.showDetailFareRule = true;
      }
    }
    if (name === 'show-more-classic') {
      if (newVal === null) {
        this.showMoreClassic = true;
      } else if (newVal === 'false') {
        this.showMoreClassic = false;
      } else {
        this.showMoreClassic = true;
      }
    }
    if (name === 'hidemultisegmentflights') {
      if (newVal === null) {
        this.hideMultiSegmentFlights = false;
      } else if (newVal === 'false') {
        this.hideMultiSegmentFlights = false;
      } else {
        this.hideMultiSegmentFlights = true;
      }
    }
  }
}

