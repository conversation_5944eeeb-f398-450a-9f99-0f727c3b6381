import { html } from "lit";
import { convertDurationToHour, formatDateTo_ddMMyyyy, formatDateToString, formatNumber, getDurarionLeg, getFlights, getPassengerDescription, getPassengerDescriptionV2, getTimeFromDateTime, getTypePassenger, validateEmail, validatePhone } from "../../utils/dateUtils";
import { environment } from "../../environments/environment";

const apiUrl = environment.apiUrl;

export const TripPassengerTemplate = (
    uri_searchBox: string,
    language: string,
    _isLoading: boolean,
    _isGlobal: boolean,
    _isSubmit: boolean,
    _isShowDetailsTrip: boolean,
    _dataCartSticket: any,
    _inforContact: any,
    _inforAirports: any[],
    _pricePaxInfor: any[],
    _passengers: any[],
    _phoneCodes: any[],
    _servicePrice: number,
    _sumPrice: number,
    _currencySymbol: string,
    _convertedVND: number,
    reSearchTrip: () => void,
    showDetailsTrip: () => void,
    getBaggageOfType: (airline: string, type: string) => any,
    togglePassportVisibility: (passenger: any) => void,
    updateBirthday: (event: any, passenger: any, index: number) => void,
    openDatePicker: (index: number) => void,
    validateBirthday: (birthday: string, type: string) => boolean,
    updateFullname: (event: Event, passenger: any) => void,
    updateGender: (event: Event, passenger: any) => void,
    updateCountry: (event: Event, passenger: any) => void,
    openDatePickerPS: (index: number) => void,
    updatepassportDate: (event: Event, passenger: any, index: number) => void,
    changeBaggage: (event: Event, passenger: any, baggage: any) => void,
    updatePhoneCode: (event: Event) => void,
    updateLemailMain: (event: Event) => void,
    updatePassport: (event: Event, passenger: any) => void,
    updatePhoneMain: (event: Event) => void,
    updateEmailMain: (event: Event) => void,
    goToPayment: () => void,
    handleLanguageChange: (value: string) => void,
    showLanguageSelect: boolean
) => {
    return html`
    ${_isLoading ? html`
    <div class="static" *ngIf="isLoading">
        <div class="loader-container">
            <span class="loader"></span>
            <img src="${apiUrl}/assets/img/background/trip_loading2.gif"/>
            <span class="loadidng-vertical bg-gradient-to-br from-nmt-600 to-nmt-400 shadow-lg shadow-white">
                ${language === 'vi' ? 'Đang kiểm tra chuyến bay, vui lòng chờ trong giây lát...' : 'Checking flight, please wait a moment...'}
            </span>
        </div>
    </div>
    ` : ""}
    
    <div class="w-full min-h-screen bg-gray-100 relative z-50  max-md:pb-24">
    ${_dataCartSticket ? html`
    <div class="max-w-7xl mx-auto min-h-[70vh]  pb-8 relative">
        <div class="pt-4 pb-8">
            <div class="w-full max-w-4xl mx-auto max-md:overflow-x-scroll max-md:overflow-y-hidden max-md:pb-[10px]">
                <div class="flex items-center justify-center max-md:space-x-2">
                    <div class="flex items-center">
                        <div class="relative group">
                            <div
                                class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white  cursor-pointer">
                                <a href="/${uri_searchBox}"
                                    class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                    <div class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500"><svg
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-search w-5 h-5 text-white">
                                            <circle cx="11" cy="11" r="8"></circle>
                                            <path d="m21 21-4.3-4.3"></path>
                                        </svg>
                                    </div>
                                    <span
                                        class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden"> ${language === 'vi' ? 'Tìm kiếm' : 'Search'}
                                        </span>
    </a>
                            </div>
                        </div>
                        <div
                            class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-white text-gray-400 max-md:hidden">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5">
                                <path d="m9 18 6-6-6-6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="relative group">
                            <div
                                class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-pointer">
                                <div @click="${reSearchTrip}"
                                    class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                    <div class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-ticket w-5 h-5 text-white">
                                            <path
                                                d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z">
                                            </path>
                                            <path d="M13 5v2"></path>
                                            <path d="M13 17v2"></path>
                                            <path d="M13 11v2"></path>
                                        </svg>
                                    </div>
                                    <span class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden">
                                        ${language === 'vi' ? 'Chọn vé' : 'Select ticket'}
                                    </span>
                                </div>

                            </div>
                        </div>
                        <div
                            class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-white text-gray-400 max-md:hidden">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5">
                                <path d="m9 18 6-6-6-6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="relative group">
                            <div
                                class="flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed">
                                <div
                                    class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                    <div class="rounded-lg p-2 transition-colors duration-300 bg-nmt-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-user w-5 h-5 text-white">
                                            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                            <circle cx="12" cy="7" r="4"></circle>
                                        </svg>
                                    </div><span
                                        class="font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 ">${language === 'vi' ? 'Thông tin' : 'Information'}</span>
                                </div>
                                <div class="absolute -bottom-[5px] left-1/2 transform -translate-x-1/2">
                                    <div class="w-2.5 h-2.5 rounded-full bg-nmt-500 ring-4 ring-white"></div>
                                </div>
                            </div>
                        </div>
                        <div
                            class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5">
                                <path d="m9 18 6-6-6-6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="relative group">
                            <div
                                class="flex items-center h-12 md:px-6 px-2 bg-white rounded-lg transition-all duration-300 ease-in-out border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 cursor-not-allowed">
                                <div
                                    class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                    <div
                                        class="rounded-lg p-2 transition-colors duration-300 bg-gray-100 group-hover:bg-gray-200">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-check w-5 h-5 text-gray-600">
                                            <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                            <line x1="2" x2="22" y1="10" y2="10"></line>
                                        </svg>
                                    </div>
                                    <span
                                        class="font-medium tracking-wide whitespace-nowrap text-[15px] text-gray-600 max-md:hidden">${language === 'vi' ? 'Thanh toán' : 'Payment'}</span>
                                </div>

                            </div>
                        </div>
                        <div
                            class="md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400 max-md:hidden">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5">
                                <path d="m9 18 6-6-6-6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="relative group">
                            <div
                                class="flex items-center h-12 md:px-6 px-2 bg-white rounded-lg transition-all duration-300 ease-in-out border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 cursor-not-allowed">
                                <div
                                    class="flex items-center gap-3 transition-transform duration-300 group-hover:scale-105">
                                    <div
                                        class="rounded-lg p-2 transition-colors duration-300 bg-gray-100 group-hover:bg-gray-200">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-check w-5 h-5 text-gray-600">
                                            <path d="M20 6 9 17l-5-5"></path>
                                        </svg>
                                    </div><span
                                        class="font-medium tracking-wide whitespace-nowrap text-[15px] text-gray-600 max-md:hidden">${language === 'vi' ? 'Hoàn tất' : 'Complete'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="grid grid-cols-12 md:gap-8 gap-4 relative z-10">
            <!-- start passengers -->
             
            <div class="md:col-span-9 col-span-12 space-y-4 max-md:order-2">
                
            ${_dataCartSticket?.InventoriesSelected.length > 0 ? html`
                            <div class="rounded-lg bg-white shadow-lg">
                    <div
                        class="rounded-t-lg py-2  border-b border-gray-200 w-full px-4 flex justify-between items-center">
                        <h1 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                            ${language === 'vi' ? 'Chi tiết chuyến bay' : 'Flight Details'}
                        </h1>
<div class="flex justify-end items-center  ">
                            ${showLanguageSelect ? html`
                            <select id="language" 
                                class=" text-sm  bg-gray-200 rounded-lg px-2 py-1  border-none focus:outline-none text-gray-600 "
                                .value=${language}
                                @change=${(e: Event) => handleLanguageChange((e.target as HTMLSelectElement).value)}
                            >
                                <option style="background-color: #f0f0f0; color: black;" value="en">English</option>
                                <option style="background-color: #f0f0f0; color: black;" value="vi">Tiếng Việt</option>
                            </select>
                            ` : ''}
                        </div>
                    </div>

                    <div class=" bg-gray-100 border-gray-200 ">
                        
                        ${_dataCartSticket?.InventoriesSelected.map((itinerarySelected: any, index: number) => html`
                        <div class="w-full bg-gray-100 mt-4 border-b border-gray-200 shadow-md rounded-lg">
<!-- start flight infor -->
<div class="bg-white rounded-e-lg rounded-bl-lg ">
    <div class="py-[2px]">
        <span
            class=" bg-gradient-to-br  from-nmt-600 to-nmt-300 md:text-base text-sm text-white font-extrabold pl-2 py-1 pe-6 md:pe-12 rounded-tr-full">
            ${language === 'vi' ? 'Chi tiết hành trình:' : 'Journey Details:'}
            ${_dataCartSticket?.InventoriesSelected.length > 1 && index % 2 === 1 ?
            (language === 'vi' ? 'Chiều về' : 'Return') :
            (language === 'vi' ? 'Chiều đi' : 'Departure')}
        </span>
    </div>
    <div class="w-full  rounded-lg">
    ${itinerarySelected.segment.Legs.map((leg: any, index: number) => html`
    ${index > 0 ? html`
    <div class="w-full bg-gray-600 p-1 text-white text-center text-sm">
        ${language === 'vi' ? 'Trung chuyển tại' : 'Transit at'} ${_inforAirports[leg.DepartureCode]?.name}
        <strong>(${leg.DepartureCode})</strong> - ${language === 'vi' ? 'Thời gian' : 'Duration'}:
        <strong>${convertDurationToHour(itinerarySelected.segment.Legs[index].StopTime)}</strong>
    </div>
    ` : ""}
   
    <div class="grid grid-cols-12 border-t border-gray-200  rounded-lg">
        <div
            class="col-span-7 flex flex-col justify-center items-center text-black py-2 md:px-8  border-black rounded-lg">
            <div
                class="w-full flex justify-between items-center px-4 md:text-sm  text-[10px]">
                <div class="text-left">
                    <span class="font-extrabold">
                        (${leg?.DepartureCode})
                    </span>
                    ${_inforAirports[leg?.DepartureCode]?.cityName}
                    <div>
                        <span class="text-gray-400">${_inforAirports[leg?.DepartureCode]?.name}</span>
                    </div>
                </div>
                <div class="text-right">

                    ${_inforAirports[leg?.ArrivalCode]?.cityName}
                    <span class="font-extrabold">
                        (${leg?.ArrivalCode})
                    </span>
                    <div>
                        <span class="text-gray-400">
                            ${_inforAirports[leg?.ArrivalCode]?.name}</span>
                    </div>
                </div>
            </div>
            <div class="flex justify-center items-center w-full px-4 gap-2">
                <div class="flex flex-col justify-start items-start">
                    <strong
                        class="md:text-3xl text-base font-extrabold text-nmt-600">
                        ${getTimeFromDateTime(leg?.DepartureDate)}</strong>
                    <span class="${language === 'vi' ? 'md:text-sm' : 'md:text-xs'} text-[10px]  text-nowrap">
                        ${formatDateTo_ddMMyyyy(leg?.DepartureDate, language)}
                    </span>
                    <strong class="md:text-base text-sm font-semibold text-gray-600 text-nowrap">
                        ${language === 'vi' ? 'Nhà ga:' : 'Terminal:'} ${leg?.DepartureTerminal || '-'}
                    </strong>
                </div>
                <div
                    class="w-full flex-col justify-center items-center px-4 text-sm md:px-6">
                    <div class="w-full text-center -mb-2">
                        ${leg?.Equipment}
                    </div>
                    <div class="w-full flex justify-center items-center">
                        <div class="w-full h-[2px] rounded-full bg-nmt-600">
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg"
                            class="w-5 h-5 fill-nmt-600 inline-block ml-[1px]"
                            viewBox="0 0 576 512">
                            <path
                                d="M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z" />
                        </svg>
                    </div>
                    <div class="w-full text-center -mt-2">

                        ${convertDurationToHour(leg?.Duration)}
                    </div>
                </div>
                <div class="flex flex-col justify-end items-end">
                    <strong
                        class="md:text-2xl text-base font-extrabold text-nmt-500">
                        ${getTimeFromDateTime(leg?.ArrivalDate)}</strong>
                    <span class="${language === 'vi' ? 'md:text-sm' : 'md:text-xs'} text-[10px]  text-nowrap">
                        ${formatDateTo_ddMMyyyy(leg?.ArrivalDate, language)}
                    </span>
                    <strong class="md:text-base text-sm font-semibold text-gray-600 text-nowrap">
                         ${language === 'vi' ? 'Nhà ga:' : 'Terminal:'} ${leg?.ArrivalTerminal || '-'}
                    </strong>
                </div>
            </div>

        </div>
        <div class="col-span-5 flex flex-row  rounded-lg">
            <div
                class="w-[1px] h-full bg-[100%_10px] bg-[linear-gradient(to_bottom,_transparent_50%,_#fb7740_50%)] bg-[length:100%_10px]">
            </div>
            <div
                class="px-4 py-2 flex flex-col items-center justify-center w-full md:text-sm text-[10px]">
                <span class="text-xs font-bold">${language === 'vi' ? 'Hãng vận chuyển' : 'Airline'}</span>
                <img src="${apiUrl}/assets/img/airlines/${leg?.OperatingAirlines}.png"
                    class=" w-auto h-12 mx-auto my-1">

                <span>${language === 'vi' ? 'Chuyến bay:' : 'Flight:'} <span
                        class="text-nmt-500 font-extrabold tracking-wide">${leg?.Airlines + leg?.FlightNumber}</span></span>
                <span>${language === 'vi' ? 'Loại vé:' : 'Ticket Type:'}
                    <strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.CabinName}</strong>

                </span>
            </div>
        </div>
        <div
            class="col-span-12 border-t border-gray-200 p-2 flex justify-end items-center bg-gray-50/80  rounded-lg">
            <span class="text-end text-xs text-gray-800">
                ${language === 'vi' ? 'Loại vé:' : 'Ticket Type:'} <strong>
                    ${itinerarySelected.inventorySelected?.BookingInfos[index]?.FareType || itinerarySelected.inventorySelected?.BookingInfos[index]?.CabinName}
                </strong>
                | ${language === 'vi' ? 'Hành lý xách tay:' : 'Hand Baggage:'}
                ${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandBaggage > 1 && itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag !== 0 ? html`<strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandBaggage}</strong>` : ``}
                
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-luggage w-4 h-4 text-blue-500 flex-shrink-0  inline-block">
                    <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16">
                    </path>
                    <rect width="20" height="14" x="2" y="6" rx="2">
                    </rect>
                </svg>
                ${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag > 0 ? html`<strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.HandWeightBag}</strong>` : html`<strong>7</strong>`}

                kg
                | ${language === 'vi' ? 'Hành lý ký gửi:' : 'Checked Baggage:'}
                ${itinerarySelected.inventorySelected?.BookingInfos[index]?.BagPieces > 1 && itinerarySelected.inventorySelected?.BookingInfos[index]?.WeightBag !== 0 ? html`<strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.BagPieces}</strong>` : ``}

             
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-luggage w-4 h-4 text-blue-500 flex-shrink-0  inline-block">
                    <path
                        d="M6 20h0a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h0">
                    </path>
                    <path d="M8 18V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v14">
                    </path>
                    <path d="M10 20h4"></path>
                    <circle cx="16" cy="20" r="2"></circle>
                    <circle cx="8" cy="20" r="2"></circle>
                </svg>

                <strong>${itinerarySelected.inventorySelected?.BookingInfos[index]?.WeightBag}</strong>
                kg
                | <svg class="w-4 h-4 inline-block text-gray-800 dark:text-white"
                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                    height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round"
                        stroke-linejoin="round" stroke-width="2"
                        d="M12 8v4l3 3m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
                ${language === 'vi' ? 'Thời gian bay:' : 'Flight time:'}
                <strong>${getDurarionLeg(leg)}</strong>
                | ${language === 'vi' ? 'Máy bay:' : 'Aircraft:'} <strong>${leg.Equipment}</strong>
            </span>
        </div>
    </div>
    `)}
    </div>

</div>
<!-- end flight infor -->
</div>
`)}
                    </div>

                    <div
                        class="col-span-12 border-t transition-all duration-700  ease-in-out    grid grid-cols-1 justify-end items-center bg-[#f8e2de] rounded-b-lg border-[1px] border-white shadow-[0px_0px_15px_#cccccc96,0px_0px_15px_#44444400_inset] ${_isShowDetailsTrip ? '!h-auto !w-full !opacity-100 p-2' : 'opacity-0 w-0 h-0 overflow-hidden'}">
                        <div class="grid grid-cols-4 border-b border-gray-600 md:text-sm text-xs">
                            <div class="text-start font-bold">
                                ${language === 'vi' ? 'Hành khách' : 'Passenger'}
                            </div>
                            <div class="text-end font-bold">
                                ${language === 'vi' ? 'Giá vé' : 'Ticket Price'}
                            </div>
                            <div class="text-end font-bold">
                                ${language === 'vi' ? 'Thuế' : 'Tax'}
                            </div>
                            <div class="text-end font-bold">
                                ${language === 'vi' ? 'Giá Bán' : 'Total Price'}
                            </div>
                        </div>


                        ${_pricePaxInfor.map((fareInfo: any) => html`<div class="grid grid-cols-4 border-b border-gray-600 py-1 md:text-sm text-xs">
                        <div class="text-start">
                            ${getPassengerDescriptionV2(fareInfo?.PaxType, _dataCartSticket?.adult, _dataCartSticket?.child, _dataCartSticket?.infant, language)}
                        </div>
                        <div class="text-end">
                            ${formatNumber(fareInfo.Fare, _convertedVND, language)}

                        </div>
                        <div class="text-end">
                            ${formatNumber(fareInfo.Tax, _convertedVND, language)}

                        </div>
                        <div class="text-end">
                            ${formatNumber(fareInfo.Fare + fareInfo.Tax, _convertedVND, language)}
                        </div>
                    </div>`)}
                     
                        <div class=" text-right md:text-sm text-xs">
                            ${language === 'vi' ? 'Phí dịch vụ:' : 'Service Fee:'} <strong class="md:text-xl text-base font-bold text-nmt-600">
                                <strong class="text-base text-nmt-600">
                                    ${formatNumber(_servicePrice, _convertedVND, language)} </strong>
                            </strong>${_currencySymbol}
                        </div>

                    </div>
                    <div class="w-full text-right md:text-sm text-xs py-4 pe-2">
                        <span @click="${showDetailsTrip}"
                        class="text-nmt-600 cursor-pointer hover:underline text-sm underline ${_isShowDetailsTrip ? 'text-red-600' : ''}">
                              ${_isShowDetailsTrip ? `${language === 'vi' ? 'Ẩn chi tiết' : 'Hide details'}` : `${language === 'vi' ? 'Chi tiết' : 'Details'}`}
                        </span>
                        ${language === 'vi' ? 'Tổng cộng:' : 'Total:'} <strong class="md:text-xl text-base font-bold text-nmt-600">

                            ${formatNumber(_sumPrice + _servicePrice, _convertedVND, language)
                } </strong> <small>${_currencySymbol}</small>
                    </div>
                </div>
            ` : ""}

            ${_dataCartSticket?.InventoriesSelected.length > 0 ? html`
            <div class="rounded-lg bg-white shadow-lg pb-4">
                    <div class="rounded-t-lg py-2  border-b border-gray-200 w-full px-4">
                        <h1 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white">
                            ${language === 'vi' ? 'Thông tin hành khách' : 'Passenger Information'}
                        </h1>
                    </div>
                    <div class="md:px-4">
                    ${_passengers.map((passenger: any, index: number) =>
                    html`
                        <div class="border  border-gray-200 rounded-lg shadow mt-4 pb-4 bg-gray-50/80">
                                <div class="text-base font-bold text-gray-800 dark:text-white mb-1 px-4 pt-4 flex items-center justify-start gap-1">
                                    ${getTypePassenger(passenger, _passengers, language)}:
                            ${!_isGlobal && passenger.type === 'adult' ? html`
                            <div @click="${() => togglePassportVisibility(passenger)}"
                                        class="rounded-md border group fill-nmt-600 group-hover:fill-white items-center justify-center w-fit inline-block border-nmt-600 px-1 h-full py-[2px] text-nmt-600 hover:text-white hover:bg-nmt-600 font-medium text-sm cursor-pointer ${passenger?.isShowPassport ? 'text-white bg-nmt-600 fill-white' : ''}">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" class="h-4 w-6 ">
                                            <path
                                                d="M512 80c8.8 0 16 7.2 16 16l0 320c0 8.8-7.2 16-16 16L64 432c-8.8 0-16-7.2-16-16L48 96c0-8.8 7.2-16 16-16l448 0zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l448 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zM208 256a64 64 0 1 0 0-128 64 64 0 1 0 0 128zm-32 32c-44.2 0-80 35.8-80 80c0 8.8 7.2 16 16 16l192 0c8.8 0 16-7.2 16-16c0-44.2-35.8-80-80-80l-64 0zM376 144c-13.3 0-24 10.7-24 24s10.7 24 24 24l80 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-80 0zm0 96c-13.3 0-24 10.7-24 24s10.7 24 24 24l80 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-80 0z" />
                                        </svg>
                                    </div>
                            ` : ""}
                                    
                                </div>
                                <div class="flex max-md:flex-col gap-2 px-4">
                                    <div class="w-full flex gap-2">
                                    ${passenger.type === 'adult' ? html`
     <select @change="${(e: Event) => updateGender(e, passenger)}"
                                            class="md:h-12 h-11 bg-gray-50 border min-w-20 border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-nmt-500 focus:border-nmt-500 block w-fit p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500">
                                            <option value="MR">${language === 'vi' ? 'Ông' : 'Mr'}</option>
                                            <option value="MRS">${language === 'vi' ? 'Bà' : 'Mrs'}</option>
                                            <option value="MS">${language === 'vi' ? 'Cô' : 'Ms'}</option>
                                        </select>
                                    ` : html`
    <select @change="${(e: Event) => updateGender(e, passenger)}"
                                            class="md:h-12 h-11 bg-gray-50 border min-w-[84px] border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-nmt-500 focus:border-nmt-500 block w-fit p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500">
                                            <option value="MSTR">${language === 'vi' ? 'Bé trai' : 'Master'}</option>
                                            <option value="MISS">${language === 'vi' ? 'Bé gái' : 'Miss'}</option>
                                        </select>
                                    `}
                                       
                                        <div class="w-full h-full">
                                            <input maxLength="100"
                                                @input="${(e: Event) => updateFullname(e, passenger)}"
                                                type="text"
                                                .value="${passenger.fullname}"
                                                class="md:h-12 h-11 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-nmt-600 focus:border-nmt-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500 ${passenger.fullname === '' && _isSubmit ? 'border-red-500 animate-pulse' : ''}"
                                                placeholder="${language === 'vi' ? 'Nhập họ và tên khách' : 'Enter passenger name'}">
                                        </div>
                                    </div>
                                    <div class="flex relative">
                                        <!-- <span class="text-xs text-red-600 absolute -top-3 -right-3">(*)</span> -->
                                        <div
                                            class="px-2 py-2.5 h-full rounded-s-lg flex items-center justify-center bg-gray-50 border border-gray-300 text-gray-900 text-nowrap">
                                            ${language === 'vi' ? 'Ngày sinh:' : 'Date of birth:'}</div>
                                        <input type="text" inputmode="numeric"
                                            class="datepickerBD rounded-none min-w-[6.5rem]  bg-gray-50 border text-gray-900 focus:ring-nmt-500 focus:border-nmt-500 block flex-1  w-full text-sm border-gray-300 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500 
                                            ${!validateBirthday(passenger.birthday, passenger.type) && _isSubmit || !validateBirthday(passenger.birthday, passenger.type) && passenger.birthday ? 'border-red-500 animate-pulse' : ''}"
                                            placeholder="${language === 'vi' ? 'dd/MM/yyyy' : 'MM/dd/yyyy'}" id="pax${index}"
                                            .value="${passenger.birthday ? formatDateToString(passenger.birthday, language) : ''}"
                                            @input="${(e: Event) => updateBirthday(e, passenger, index)}"
                                            />
                                        <button
                                            class="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border rounded-e-0 border-gray-300 border-e-0 rounded-e-md dark:bg-gray-600 dark:text-gray-400 dark:border-gray-600"
                                            @click="${() => openDatePicker(index)}"
                                            type="button">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-gray-500"
                                                viewBox="0 0 448 512">
                                                <path
                                                    d="M128 0c17.7 0 32 14.3 32 32l0 32 128 0 0-32c0-17.7 14.3-32 32-32s32 14.3 32 32l0 32 48 0c26.5 0 48 21.5 48 48l0 48L0 160l0-48C0 85.5 21.5 64 48 64l48 0 0-32c0-17.7 14.3-32 32-32zM0 192l448 0 0 272c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 192zm64 80l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm128 0l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zM64 400l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zm112 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16z" />
                                            </svg>
                                        </button>
    
                                    </div>
    
                                </div>
                                ${passenger.isShowPassport ? html`
                                <div>
                                    <div class="col-span-3">
                                        <span class="block  text-sm font-medium text-gray-900 dark:text-white bg-gray-100 py-1 px-4">${language === 'vi' ? 'Thông tin Passport:' : 'Passport Information:'}</span>
                                    </div>
                                    <div class="grid md:grid-cols-3 grid-cols-1 gap-x-4 px-4 mt-2">
                                        <div class="w-full">
                                            <label class="block  text-sm text-gray-900 dark:text-white">${language === 'vi' ? 'Quốc gia' : 'Country'}
                                                <span class="text-xs text-red-600">${language === 'vi' ? '(mã 3 chữ)' : '(3 letter code)'}</span></label>
                                            <input type="text"  maxlength="3"
                                                .value="${passenger.country}"
                                                @input="${(e: Event) => updateCountry(e, passenger)}"
                                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-nmt-600 focus:border-nmt-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500 ${passenger.country === '' && _isSubmit && passenger.isShowPassport ? 'border-red-500 animate-pulse' : ''}"
                                                placeholder="${language === 'vi' ? 'Nhập Quốc tịch' : 'Enter Nationality'}">
                                        </div>
                                        <div class="w-full">
                                            <label class="block  text-sm text-gray-900 dark:text-white">${language === 'vi' ? 'Số PassPort' : 'Passport Number'}</label>
                                            <input type="text"  
                                                .value="${passenger.passport}"
                                                @input="${(e: Event) => updatePassport(e, passenger)}"
                                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-nmt-600 focus:border-nmt-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500 ${passenger.passport === '' && _isSubmit && passenger.isShowPassport ? 'border-red-500 animate-pulse' : ''}"
                                                placeholder="${language === 'vi' ? 'Nhập số Passport' : 'Enter Passport Number'}">
                                        </div>
                                        <div class="w-full">
                                            <label class="block text-sm text-gray-900 dark:text-white">${language === 'vi' ? 'Ngày hết hạn' : 'Expiry Date'}</label>
                                            <div class="flex">
                                                <input type="text" inputmode="numeric"
                                                    class="datePickerPS rounded-none rounded-s-lg min-w-[6.5rem]  bg-gray-50 border text-gray-900 focus:ring-nmt-500 focus:border-nmt-500 block flex-1  w-full text-sm border-gray-300 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500 ${passenger.passportDate === undefined && _isSubmit && passenger.isShowPassport ? 'border-red-500 animate-pulse' : ''}"
                                                    placeholder="${language === 'vi' ? 'dd/MM/yyyy' : 'MM/dd/yyyy'}" id="passportDate${index}"
                                                    .value="${passenger.passportDate ? formatDateToString(passenger.passportDate, language) : ''}"
                                            @input="${(e: Event) => updatepassportDate(e, passenger, index)}"
                                                     />
                                                <button
                                                    class="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border rounded-e-0 border-gray-300 border-e-0 rounded-e-md dark:bg-gray-600 dark:text-gray-400 dark:border-gray-600"
                                                    @click="${() => openDatePickerPS(index)}"
                                                    type="button">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-gray-500"
                                                        viewBox="0 0 448 512">
                                                        <path
                                                            d="M128 0c17.7 0 32 14.3 32 32l0 32 128 0 0-32c0-17.7 14.3-32 32-32s32 14.3 32 32l0 32 48 0c26.5 0 48 21.5 48 48l0 48L0 160l0-48C0 85.5 21.5 64 48 64l48 0 0-32c0-17.7 14.3-32 32-32zM0 192l448 0 0 272c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 192zm64 80l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm128 0l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zM64 400l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zm112 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16z" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                ` : ""}
                                ${passenger.type !== 'infant' ? html`
                                <div class="px-4 mt-2 md:w-fit w-full space-y-2">
                                ${passenger.baggages.map((baggage: any, index: number) =>
                        html`<div class="flex  min-w-[calc(33%-0.5rem)] w-full">
                                        <div
                                            class="px-2 py-2.5 h-full  border border-gray-300 rounded-s-lg  flex items-center justify-center bg-gray-100 text-gray-900 text-nowrap">
                                            ${baggage?.type}
                                        </div>
                                        <select @change="${(e: Event) => changeBaggage(e, passenger, baggage)}"
                                            class="rounded-e-lg min-w-[6.5rem]  bg-gray-50 border text-gray-900 focus:ring-nmt-500 focus:border-nmt-500 block flex-1  w-full text-sm border-gray-300 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500">
                                            <option value="">
                                            ${_dataCartSticket?.InventoriesSelected[index].inventorySelected?.BookingInfos[0]?.WeightBag > 0 ?
                                `${_dataCartSticket?.InventoriesSelected[index].inventorySelected?.BookingInfos[0]?.WeightBag} KG ${language === 'vi' ? 'ký gửi miễn phí' : 'free checked baggage'}` :
                                `${language === 'vi' ? 'Không hành lý ký gửi' : 'No checked baggage'}`}
                                            </option>
                                            ${getBaggageOfType(baggage.airline, baggage.type).map((bag: any) => html`
                                                <option value="${bag.SSRCode}">${bag.WeightBag} KG ${language === 'vi' ? 'ký gửi' : 'checked baggage'} (${formatNumber(bag.Price, _convertedVND, language)}) ${_currencySymbol}</option>
                                            `)}
                                            
                                        </select>
                                    </div>
                        `)}
                                   
                                </div>
                                ` : html``}
                                
                            </div>
                        `
                )}

</div>
    <div class="my-4 w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#d2d3d3_50%)] bg-[length:10px_100%]"></div>
    <div class="px-4">
        <strong>${language === 'vi' ? 'Ghi chú: ' : 'Note: '}</strong>
        <div class="text-red-600"> <span>- (*) </span>: ${language === 'vi' ? 'Bắt buộc' : 'Required'}</div>
    </div>
    </div>
    <div class="rounded-lg bg-white shadow-lg pb-4">
        <div class="rounded-t-lg py-2  border-b border-gray-200 w-full px-4">
            <h1 class="text-lg font-bold tracking-tight text-gray-900 dark:text-white">${language === 'vi' ? 'Thông tin liên hệ' : 'Contact Information'}</h1>
        </div>
        <div>
            <div>
                <div class="text-base font-bold text-gray-800 dark:text-white px-4 pt-4 flex items-center justify-start gap-1">
                    ${language === 'vi' ? 'Liên hệ chính' : 'Main contact'}: <span class="text-xs text-red-600 font-normal"> (*) </span>
                </div>
                <div class="grid md:grid-cols-2 grid-cols-1 gap-x-4 gap-y-2 px-4 mt-2">
                <div class="w-full">
                    <div class="flex">
                        <span class="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border rounded-e-0 border-gray-300 border-e-0 rounded-s-md dark:bg-gray-600 dark:text-gray-400 dark:border-gray-600"> 
                        <svg aria-hidden="true" xmlns = "http://www.w3.org/2000/svg" fill="currentColor" viewBox = "0 0 19 18" class="w-4 h-4 text-gray-500 dark:text-gray-400">
                            <path d="M18 13.446a3.02 3.02 0 0 0-.946-1.985l-1.4-1.4a3.054 3.054 0 0 0-4.218 0l-.7.7a.983.983 0 0 1-1.39 0l-2.1-2.1a.983.983 0 0 1 0-1.389l.7-.7a2.98 2.98 0 0 0 0-4.217l-1.4-1.4a2.824 2.824 0 0 0-4.218 0c-3.619 3.619-3 8.229 1.752 12.979C6.785 16.639 9.45 18 11.912 18a7.175 7.175 0 0 0 5.139-2.325A2.9 2.9 0 0 0 18 13.446Z" ></path>
                        </svg>
                    </span>
                    <select
                    @change="${(e: Event) => updatePhoneCode(e)}"
                     class="  bg-gray-50 border pe-2 w-20 rounded-none text-gray-900 focus:ring-nmt-500 focus:border-nmt-500  text-sm border-gray-300 p-0  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500" >
                    ${_phoneCodes.map((phone: any) => html`<option value="${phone.value}" ?selected="${language === 'vi' ? phone.name === "Vietnam" : phone.name === "United States"}" >+${phone.value} (${phone.name}) </option>`)}
                    </select>

    <input type = "text" maxlength = "12"  inputmode="numeric" .value="${_inforContact.phoneMain}"
        @input="${(e: Event) => updatePhoneMain(e)}"
        minlength = "7" placeholder = "************"
        class="rounded-none rounded-e-lg bg-gray-50 border text-gray-900 focus:ring-nmt-500 focus:border-nmt-500 block flex-1 min-w-0 w-full text-sm border-gray-300 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500 ng-untouched ng-pristine ng-invalid ${!validatePhone(_inforContact.phoneMain) && _isSubmit ? 'border-red-500 animate-pulse' : ''}" >
    </div>
    </div>
    <div class="w-full">
        <div class="flex">
            <span class="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border rounded-e-0 border-gray-300 border-e-0 rounded-s-md dark:bg-gray-600 dark:text-gray-400 dark:border-gray-600" >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox = "0 0 512 512"
                    class="w-4 h-4 text-gray-500 dark:text-gray-400">
                    <path d="M64 112c-8.8 0-16 7.2-16 16l0 22.1L220.5 291.7c20.7 17 50.4 17 71.1 0L464 150.1l0-22.1c0-8.8-7.2-16-16-16L64 112zM48 212.2L48 384c0 8.8 7.2 16 16 16l384 0c8.8 0 16-7.2 16-16l0-171.8L322 328.8c-38.4 31.5-93.7 31.5-132 0L48 212.2zM0 128C0 92.7 28.7 64 64 64l384 0c35.3 0 64 28.7 64 64l0 256c0 35.3-28.7 64-64 64L64 448c-35.3 0-64-28.7-64-64L0 128z" />
            </svg>
            </span>
    <select
    @change="${(e: Event) => updateLemailMain(e)}"
    class=" bg-gray-50 border pe-2 w-fit rounded-none text-gray-900 focus:ring-nmt-500 focus:border-nmt-500  text-sm border-gray-300 p-0  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500" >
        <option value="VN" ?selected="${language === 'vi'}" > VN </option>
        <option value = "EN" ?selected="${language === 'en'}" > EN </option>
        <option value = "KO"> KO </option>
        <option value = "JP"> JP </option>
        <option value = "ZH"> ZH </option>
        <option value = "TW"> TW </option>
    </select>
    <input  .value="${_inforContact.emailMain}" type = "email"  placeholder = "<EMAIL>"
    @input="${(e: Event) => updateEmailMain(e)}"
    class="rounded-none rounded-e-lg bg-gray-50 border text-gray-900 focus:ring-nmt-500 focus:border-nmt-500 block flex-1 min-w-0 w-full text-sm border-gray-300 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-nmt-500 dark:focus:border-nmt-500 ng-untouched ng-pristine ng-invalid ${!validateEmail(_inforContact.emailMain) && _isSubmit ? 'border-red-500 animate-pulse' : ''}" >
    </div>
    </div>
    </div>
    </div>

    
    <div class="my-4 w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#d2d3d3_50%)] bg-[length:10px_100%]" ></div>
    <div class="px-4 md:mt-4 mt-2 flex md:flex-row md:justify-between">
        <span class="text-sm text-gray-600 dark:text-gray-400">${language === 'vi' ? 'Vui lòng kiểm tra thông tin liên hệ trước khi tiếp tục' : 'Please check your contact information before continuing'}</span>
     <button @click=${goToPayment}
                            class="max-md:hidden h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                            <div> ${language === 'vi' ? 'Tiếp tục' : 'Continue'}</div>
                        </button>
    </div>
    </div>
    </div>
        ` : html`

        `}
                <div
                    class="flex items-center z-50 justify-between w-full bg-white p-4 rounded-lg mt-4 shadow-2xl sticky bottom-0 border md:hidden">
                    <div class=" flex flex-col justify-start  items-start">
                        <div>
                            <strong class="text-xl text-nmt-600 text-right w-full">
                                ${formatNumber(_sumPrice + _servicePrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>
                        </div>
                        <div>
                            <span @click=${showDetailsTrip}
                                class="text-nmt-600 cursor-pointer underline ${_isShowDetailsTrip ? 'text-red-600' : ''}">
                                ${_isShowDetailsTrip ? 'Ẩn chi tiết' : 'Chi tiết'}
                            </span>
                        </div>
                    </div>
                    <button @click=${goToPayment}
                        class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                        <div>${language === 'vi' ? 'Tiếp tục' : 'Continue'}</div>
                        <div><svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                fill="currentColor" viewBox="0 0 24 24" class="w-6 h-6 text-white  animate-pulse">
                                <path fill-rule="evenodd"
                                    d="M3 4a1 1 0 0 0-.822 1.57L6.632 12l-4.454 6.43A1 1 0 0 0 3 20h13.153a1 1 0 0 0 .822-.43l4.847-7a1 1 0 0 0 0-1.14l-4.847-7a1 1 0 0 0-.822-.43H3Z"
                                    clip-rule="evenodd"></path>
                            </svg></div>
                    </button>
                </div>
            </div>
            <!-- end passengers -->

            <div
                class="md:col-span-3 max-md:order-1 max-md:hidden col-span-12 md:pb-6 bg-white rounded-lg shadow-lg h-[calc(100vh-5.5rem)] md:sticky md:top-11 overflow-y-auto max-md:h-fit relative">
                ${_dataCartSticket?.InventoriesSelected.length > 0 ? html`
                <div>
                ${_dataCartSticket?.InventoriesSelected.map((itinerarySelected: any, index: number) => html`
                    <div class="w-full relative ">
                        <!-- Depart -->
                        <h1 class="bg-[#c9efff] text-gray-800 border-b border-gray-200 w-full px-4 py-2 text-base font-bold ${index % 2 === 1 ? 'bg-[#fffbb3]' : ''} ">
                            ${index % 2 === 0 ? (language === 'vi' ? "Chuyến đi" : "Departure") : (language === 'vi' ? "Chuyến về" : "Return")}
                        </h1>
                        <div
                            class="flex flex-col justify-between items-center text-gray-800 py-2 border-r border-white ">
                            <div class="w-full flex justify-between items-center px-4 text-xs font-bold">
                                <div class="text-left">
                                    ${_inforAirports[itinerarySelected?.segment?.DepartureCode]?.cityName}
                                </div>
                                <div class="text-right">
                                    ${_inforAirports[itinerarySelected?.segment?.ArrivalCode]?.cityName}
                                </div>

                            </div>
                            <div class="flex justify-start items-center w-full px-4 gap-2 ">
                                <div class="flex flex-col justify-start items-start">
                                    <strong class="text-base font-bold rounded-full bg-white text-nmt-600">
                                        ${itinerarySelected?.segment?.DepartureCode}
                                    </strong>
                                    <strong class="text-xl font-bold ">
                                        ${getTimeFromDateTime(itinerarySelected?.segment?.DepartureDate)}
                                    </strong>

                                </div>
                                <div class="w-full flex flex-col justify-center items-center">
                                    <div
                                        class="px-3  inline-flex justify-center items-center max-w-full text-nmt-600 bg-white box-border">
                                        <span
                                            class="relative  text-nmt-600 font-normal h-[18px] tracking-[0px] leading-[18px] overflow-hidden text-ellipsis whitespace-nowrap">

                                            <img src="${apiUrl}/assets/img/airlines/${itinerarySelected?.segment?.Airlines}.png" class="h-full w-auto">
                                        </span>
                                    </div>
                                    <div class="w-full flex justify-center items-center">
                                        <div class="w-full h-[2px] rounded-full bg-nmt-600">
                                        </div>
                                        <svg xmlns="http://www.w3.org/2000/svg"
                                            class="w-5 h-5 fill-nmt-600 inline-block ml-[1px]"
                                            viewBox="0 0 576 512">
                                            <path
                                                d="M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z" />
                                        </svg>
                                    </div>

                                </div>
                                <div class="flex flex-col justify-end items-end">
                                    <strong class="text-base font-bold rounded-full bg-white text-nmt-600">
                                        ${itinerarySelected?.segment?.ArrivalCode}
                                    </strong>
                                    <strong class="text-xl font-bold ">
                                        ${getTimeFromDateTime(itinerarySelected?.segment?.ArrivalDate)}
                                    </strong>
                                </div>

                            </div>
                            <div
                                class="w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#d2d3d3_50%)] bg-[length:10px_100%]">
                            </div>
                            <div class="flex justify-between items-center w-full px-4 mt-2 text-sm">
                                <div [ngClass]="{'flex flex-col': itinerarySelected?.segment?.Legs.length > 1}">
                                    <span>
                                        ${language === 'vi' ? 'Ngày:' : 'Date:'}
                                    </span>
                                    <strong class="text-xs">
                                        ${formatDateTo_ddMMyyyy(itinerarySelected?.segment?.DepartureDate, language)}
                                    </strong>
                                </div>
                                <div
                                    [ngClass]="{'flex flex-col items-end justify-end': itinerarySelected?.segment?.Legs.length > 1}">
                                    <span>
                                        ${language === 'vi' ? 'Chuyến:' : 'Flight:'}
                                    </span>
                                    <strong class="py-1 px-2 text-xs rounded-full bg-gray-200 text-right">
                                        ${getFlights(itinerarySelected?.segment?.Legs)}
                                    </strong>
                                </div>
                            </div>

                        </div>
                    </div>
                `)}
                    
                  
                
                `: html`
                <div class="py-4 text-center text-gray-600">
                        ${language === 'vi' ? 'Chưa chọn chuyến bay' : 'No flight selected'}
                    </div>
                `}
                  <div
                        class="w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#d2d3d3_50%)] bg-[length:10px_100%]">
                    </div>
                

                <div class="w-full flex justify-between items-center px-4 mt-2 font-normal">
                    <div>
                        <span>
                            ${language === 'vi' ? 'Giá vé:' : 'Ticket price:'}
                        </span>
                    </div>
                    <div>
                        <strong class="text-base text-nmt-600">
                            ${formatNumber(_sumPrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>
                    </div>
                </div>
                <div class="w-full flex justify-between items-center px-4 mt-2 font-normal">
                    <div>
                        <span>
                            ${language === 'vi' ? 'Giá dịch vụ:' : 'Service price:'}
                        </span>
                    </div>
                    <div>
                        <strong class="text-base text-nmt-600">
                            ${formatNumber(_servicePrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>
                    </div>
                </div>
                <div
                    class="w-full h-[1px] bg-[100%_10px] bg-[linear-gradient(to_right,_transparent_50%,_#fb7740_50%)] bg-[length:10px_100%]">
                </div>
                <div class="w-full flex justify-between items-center px-4 mt-2 font-bold">
                    <div>
                        <span>
                            ${language === 'vi' ? 'Tổng giá:' : 'Total price:'}
                        </span>
                    </div>
                    <div class=" flex justify-end max-md:flex-col items-end">
                        <strong class="text-xl text-nmt-600 text-right w-full">
                            ${formatNumber(_sumPrice + _servicePrice, _convertedVND, language)} </strong> <small>${_currencySymbol}</small>
                    </div>
                </div>
                <div
                    class="z-50 flex items-center justify-center w-full bg-white p-4  mt-4 sticky bottom-0 ">
                    <span class="relative group">
                        <button @click=${goToPayment}
                            class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                            <div> ${language === 'vi' ? 'Tiếp tục' : 'Continue'}</div>
                        </button>
                    </span>

                </div>
                <hr
                    class="w-full my-4 opacity-25 h-[1px] border-t-0 bg-[linear-gradient(90deg,_transparent,_rgba(0,_0,_0,_.4),_transparent)]">
            </div>
        </div>
    </div>
    ` : html`
    <div class="contents">
        <div class="w-full max-w-md mx-auto bg-nmt-50 rounded-xl shadow-lg overflow-hidden md:max-w-2xl">
            <div class="md:flex">
                <div class="p-8">
                    <div class="uppercase tracking-wide text-sm text-nmt-600 font-semibold">
                        ${language === 'vi' ? 'Thông báo' : 'Notification'}
                    </div>
                    <h2 class="mt-2 text-3xl leading-tight font-bold text-nmt-900">
                        ${language === 'vi' ? 'Không tìm thấy thông tin đặt chỗ' : 'Booking information not found'}
                    </h2>
                    <p class="mt-4 text-nmt-700">
                        ${language === 'vi'
                ? 'Rất tiếc, chúng tôi không thể tìm thấy thông tin đặt chỗ của bạn. Vui lòng thử lại hoặc liên hệ với bộ phận hỗ trợ.'
                : 'Sorry, we could not find your booking information. Please try again or contact support.'}
                    </p>
                    <div class="mt-6">
                        <button @click=${reSearchTrip} type="button"
                            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-nmt-500 to-red-500 hover:from-nmt-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-nmt-500 shadow-md transition-all duration-150 ease-in-out">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-refresh-cw w-5 h-5 mr-2">
                                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                                <path d="M21 3v5h-5"></path>
                                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                                <path d="M8 16H3v5"></path>
                            </svg>
                            ${language === 'vi' ? 'Tải Lại' : 'Reload'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    `}

</div>
    `;
}
