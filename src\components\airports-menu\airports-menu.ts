import { css, LitElement, unsafeCSS } from "lit";
import { customElement, property } from "lit/decorators.js";
import { airportsMenuTemplate } from "./airports-menu-template";
import { searchAirport } from "../../services/WorldServices";
import styles from '../../styles/styles.css';


@customElement("airports-menu")
export class AirportsMenu extends LitElement {
    static styles = [
        unsafeCSS(styles),
        css`
        :host {
        font-family: var(--nmt-font, 'Roboto', sans-serif);
        }
        `
    ];

    @property({ type: String }) tripType = "departure";
    @property({ type: String }) language = "vi";
    @property({ type: Array }) AirportsDefault: any[] = [];

    @property({ type: Array }) AirportsDefaultFiltered: any[] = [];
    @property({ type: String }) searchTerm = "";
    private searchTimeout: any = null;

    constructor() {
        super();
    }

    connectedCallback() {
        super.connectedCallback();
    }

    async searchAirPorts(event: Event) {
        const target = event.target as HTMLInputElement;
        this.searchTerm = target.value;

        // Hủy timeout trước đó nếu người dùng tiếp tục nhập
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // Đợi 500ms trước khi thực hiện tìm kiếm
        this.searchTimeout = setTimeout(async () => {
            if (this.searchTerm === '') {
                this.AirportsDefaultFiltered = [];
                this.requestUpdate();
                return;
            }

            const searchTermLower = this.searchTerm.toLowerCase();
            var filteredAirports = this.AirportsDefault
                .filter((airport: any) =>
                    airport.name?.toLowerCase().includes(searchTermLower) ||
                    airport.cityName?.toLowerCase().includes(searchTermLower) ||
                    airport.code?.toLowerCase() === searchTermLower
                );

            // Loại bỏ các sân bay trùng lặp
            filteredAirports = filteredAirports.filter((airport: any, index: number, self: any) =>
                index === self.findIndex((t: any) => t.code === airport.code)
            );

            if (filteredAirports.length === 0) {
                const request = {
                    Language: this.language,
                    keyword: this.searchTerm
                };
                var res = await searchAirport(request);
                this.AirportsDefaultFiltered = res.resultObj;
            } else {
                this.AirportsDefaultFiltered = filteredAirports;
            }
            //check if only one item and this is parent item then select it
            if (this.AirportsDefaultFiltered.length === 1 && this.AirportsDefaultFiltered[0].isParent) {
                this.AirportsDefaultFiltered[0].selected = true;
            }

            this.requestUpdate();
        }, 500); // Delay 500ms trước khi gọi API
    }


    continentClick = (continentCode: string) => {
        this.AirportsDefault.forEach((continent: any) => {
            continent.continentCode === continentCode ? continent.selected = true : continent.selected = false;
        });

        this.requestUpdate();
    }

    itemParentClick = (code: string) => {
        this.AirportsDefaultFiltered.forEach((continent: any) => {
            continent.code === code ? continent.selected = !continent.selected : continent.selected = false;
        });
        this.requestUpdate();
    }

    airportClick = (airport: any) => {
        this.dispatchEvent(
            new CustomEvent("airport-click", {
                detail: { airport, tripType: this.tripType },
                bubbles: true,
                composed: true,
            })
        );
    };

    render() {
        return airportsMenuTemplate(
            this.AirportsDefault,
            this.AirportsDefaultFiltered,
            this.searchTerm,
            this.language,
            this.searchAirPorts.bind(this),
            this.continentClick.bind(this),
            this.itemParentClick.bind(this),
            this.airportClick.bind(this)
        );
    }
}

