import { html } from "lit";
import { formatDate_ddMM, getDayOfWeek, range } from "../../utils/dateUtils";

export const CalendersTripTemplate = (
    dateStart: Date,
    language: string,
    _isLoading: boolean,
    _isError: boolean,
    calendarWeek: Date[],
    calenderWeekChange: (day: Date) => void
) => html`
     <div class="grid grid-cols-7  bg-gradient-to-br from-nmt-600 to-nmt-300 calender rounded-lg mb-4">
                ${_isLoading && !_isError ? html`
                    ${range(1, 7).map((day) => html`
                    <div class="flex flex-col space-y-2 p-2">
                        <div class="h-4 w-16 bg-white/20 rounded animate-pulse"></div>
                        <div class="h-4 w-12 bg-white/20 rounded animate-pulse"></div>
                    </div>
                    `)}
                ` : html`
                ${!_isLoading && !_isError ? html`
                ${calendarWeek.map((day, index) => html`
                 <div @click=${() => calenderWeekChange(day)}
                        class="md:text-base text-sm  text-center p-2 border-r border-white calender-day cursor-pointer ${index === 3 ? 'bg-gray-100 text-nmt-600 border border-nmt-600 shadow-lg font-bold' : 'text-gray-50'}">
                        <div class="font-semibold">${getDayOfWeek(day, language)}</div>
                        <div>${formatDate_ddMM(day, language)}</div>
                </div>
                `)}` : ''}
                `}
                </div>
`;