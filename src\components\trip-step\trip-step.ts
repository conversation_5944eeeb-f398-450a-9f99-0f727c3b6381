import { css, LitElement, unsafeCSS } from "lit";
import { tripStepTemplate } from "./trip-step-template";
import { customElement, property } from "lit/decorators.js";
import styles from '../../styles/styles.css';


@customElement("trip-steps")
export class TripSteps extends LitElement {
    static styles = [
        unsafeCSS(styles),
        css`
        :host {
        font-family: var(--nmt-font, 'Roboto', sans-serif);
        }
        `
    ];

    @property({ type: String }) language = "vi";
    @property({ type: String }) uri_searchBox = "";
    constructor() {
        super();
    }

    render() {
        return tripStepTemplate(
            this.language,
            this.uri_searchBox
        );
    }
}

