{"version": 3, "file": "index.js", "sources": ["../../node_modules/tslib/tslib.es6.js", "../../node_modules/@lit/reactive-element/css-tag.js", "../../node_modules/@lit/reactive-element/reactive-element.js", "../../node_modules/lit-html/lit-html.js", "../../node_modules/lit-element/lit-element.js", "../../src/environments/environment.ts", "../../node_modules/@fingerprintjs/fingerprintjs/dist/fp.esm.js", "../../src/utils/deviceUtils.ts", "../../src/services/CryptoService.ts", "../../src/services/FlightService.ts", "../../src/utils/dateUtils.ts", "../../src/components/trip-result/trip-result-template.ts", "../../node_modules/@lit/reactive-element/decorators/custom-element.js", "../../node_modules/@lit/reactive-element/decorators/property.js", "../../node_modules/@lit/reactive-element/decorators/state.js", "../../src/services/WorldServices.ts", "../../src/interface/DefaultColors.ts", "../../src/services/ColorService.ts", "../../src/components/trip-result/trip-result.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\r\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\r\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nvar ownKeys = function(o) {\r\n    ownKeys = Object.getOwnPropertyNames || function (o) {\r\n        var ar = [];\r\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\r\n        return ar;\r\n    };\r\n    return ownKeys(o);\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose, inner;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n            if (async) inner = dispose;\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    var r, s = 0;\r\n    function next() {\r\n        while (r = env.stack.pop()) {\r\n            try {\r\n                if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\r\n                if (r.dispose) {\r\n                    var result = r.dispose.call(r.value);\r\n                    if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n                }\r\n                else s |= 1;\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\r\n    if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\r\n        return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\r\n            return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\r\n        });\r\n    }\r\n    return path;\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __esDecorate: __esDecorate,\r\n    __runInitializers: __runInitializers,\r\n    __propKey: __propKey,\r\n    __setFunctionName: __setFunctionName,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n    __rewriteRelativeImportExtension: __rewriteRelativeImportExtension,\r\n};\r\n", "/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t=globalThis,e=t.ShadowRoot&&(void 0===t.ShadyCSS||t.ShadyCSS.nativeShadow)&&\"adoptedStyleSheets\"in Document.prototype&&\"replace\"in CSSStyleSheet.prototype,s=Symbol(),o=new WeakMap;class n{constructor(t,e,o){if(this._$cssResult$=!0,o!==s)throw Error(\"CSSResult is not constructable. Use `unsafeCSS` or `css` instead.\");this.cssText=t,this.t=e}get styleSheet(){let t=this.o;const s=this.t;if(e&&void 0===t){const e=void 0!==s&&1===s.length;e&&(t=o.get(s)),void 0===t&&((this.o=t=new CSSStyleSheet).replaceSync(this.cssText),e&&o.set(s,t))}return t}toString(){return this.cssText}}const r=t=>new n(\"string\"==typeof t?t:t+\"\",void 0,s),i=(t,...e)=>{const o=1===t.length?t[0]:e.reduce(((e,s,o)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if(\"number\"==typeof t)return t;throw Error(\"Value passed to 'css' function must be a 'css' function result: \"+t+\". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.\")})(s)+t[o+1]),t[0]);return new n(o,t,s)},S=(s,o)=>{if(e)s.adoptedStyleSheets=o.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet));else for(const e of o){const o=document.createElement(\"style\"),n=t.litNonce;void 0!==n&&o.setAttribute(\"nonce\",n),o.textContent=e.cssText,s.appendChild(o)}},c=e?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e=\"\";for(const s of t.cssRules)e+=s.cssText;return r(e)})(t):t;export{n as CSSResult,S as adoptStyles,i as css,c as getCompatibleStyle,e as supportsAdoptingStyleSheets,r as unsafeCSS};\n//# sourceMappingURL=css-tag.js.map\n", "import{getCompatibleStyle as t,adoptStyles as s}from\"./css-tag.js\";export{CSSResult,adoptStyles,css,getCompatibleStyle,supportsAdoptingStyleSheets,unsafeCSS}from\"./css-tag.js\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */const{is:i,defineProperty:e,getOwnPropertyDescriptor:r,getOwnPropertyNames:h,getOwnPropertySymbols:o,getPrototypeOf:n}=Object,a=globalThis,c=a.trustedTypes,l=c?c.emptyScript:\"\",p=a.reactiveElementPolyfillSupport,d=(t,s)=>t,u={toAttribute(t,s){switch(s){case Boolean:t=t?l:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,s){let i=t;switch(s){case Boolean:i=null!==t;break;case Number:i=null===t?null:Number(t);break;case Object:case Array:try{i=JSON.parse(t)}catch(t){i=null}}return i}},f=(t,s)=>!i(t,s),y={attribute:!0,type:String,converter:u,reflect:!1,hasChanged:f};Symbol.metadata??=Symbol(\"metadata\"),a.litPropertyMetadata??=new WeakMap;class b extends HTMLElement{static addInitializer(t){this._$Ei(),(this.l??=[]).push(t)}static get observedAttributes(){return this.finalize(),this._$Eh&&[...this._$Eh.keys()]}static createProperty(t,s=y){if(s.state&&(s.attribute=!1),this._$Ei(),this.elementProperties.set(t,s),!s.noAccessor){const i=Symbol(),r=this.getPropertyDescriptor(t,i,s);void 0!==r&&e(this.prototype,t,r)}}static getPropertyDescriptor(t,s,i){const{get:e,set:h}=r(this.prototype,t)??{get(){return this[s]},set(t){this[s]=t}};return{get(){return e?.call(this)},set(s){const r=e?.call(this);h.call(this,s),this.requestUpdate(t,r,i)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)??y}static _$Ei(){if(this.hasOwnProperty(d(\"elementProperties\")))return;const t=n(this);t.finalize(),void 0!==t.l&&(this.l=[...t.l]),this.elementProperties=new Map(t.elementProperties)}static finalize(){if(this.hasOwnProperty(d(\"finalized\")))return;if(this.finalized=!0,this._$Ei(),this.hasOwnProperty(d(\"properties\"))){const t=this.properties,s=[...h(t),...o(t)];for(const i of s)this.createProperty(i,t[i])}const t=this[Symbol.metadata];if(null!==t){const s=litPropertyMetadata.get(t);if(void 0!==s)for(const[t,i]of s)this.elementProperties.set(t,i)}this._$Eh=new Map;for(const[t,s]of this.elementProperties){const i=this._$Eu(t,s);void 0!==i&&this._$Eh.set(i,t)}this.elementStyles=this.finalizeStyles(this.styles)}static finalizeStyles(s){const i=[];if(Array.isArray(s)){const e=new Set(s.flat(1/0).reverse());for(const s of e)i.unshift(t(s))}else void 0!==s&&i.push(t(s));return i}static _$Eu(t,s){const i=s.attribute;return!1===i?void 0:\"string\"==typeof i?i:\"string\"==typeof t?t.toLowerCase():void 0}constructor(){super(),this._$Ep=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Em=null,this._$Ev()}_$Ev(){this._$ES=new Promise((t=>this.enableUpdating=t)),this._$AL=new Map,this._$E_(),this.requestUpdate(),this.constructor.l?.forEach((t=>t(this)))}addController(t){(this._$EO??=new Set).add(t),void 0!==this.renderRoot&&this.isConnected&&t.hostConnected?.()}removeController(t){this._$EO?.delete(t)}_$E_(){const t=new Map,s=this.constructor.elementProperties;for(const i of s.keys())this.hasOwnProperty(i)&&(t.set(i,this[i]),delete this[i]);t.size>0&&(this._$Ep=t)}createRenderRoot(){const t=this.shadowRoot??this.attachShadow(this.constructor.shadowRootOptions);return s(t,this.constructor.elementStyles),t}connectedCallback(){this.renderRoot??=this.createRenderRoot(),this.enableUpdating(!0),this._$EO?.forEach((t=>t.hostConnected?.()))}enableUpdating(t){}disconnectedCallback(){this._$EO?.forEach((t=>t.hostDisconnected?.()))}attributeChangedCallback(t,s,i){this._$AK(t,i)}_$EC(t,s){const i=this.constructor.elementProperties.get(t),e=this.constructor._$Eu(t,i);if(void 0!==e&&!0===i.reflect){const r=(void 0!==i.converter?.toAttribute?i.converter:u).toAttribute(s,i.type);this._$Em=t,null==r?this.removeAttribute(e):this.setAttribute(e,r),this._$Em=null}}_$AK(t,s){const i=this.constructor,e=i._$Eh.get(t);if(void 0!==e&&this._$Em!==e){const t=i.getPropertyOptions(e),r=\"function\"==typeof t.converter?{fromAttribute:t.converter}:void 0!==t.converter?.fromAttribute?t.converter:u;this._$Em=e,this[e]=r.fromAttribute(s,t.type),this._$Em=null}}requestUpdate(t,s,i){if(void 0!==t){if(i??=this.constructor.getPropertyOptions(t),!(i.hasChanged??f)(this[t],s))return;this.P(t,s,i)}!1===this.isUpdatePending&&(this._$ES=this._$ET())}P(t,s,i){this._$AL.has(t)||this._$AL.set(t,s),!0===i.reflect&&this._$Em!==t&&(this._$Ej??=new Set).add(t)}async _$ET(){this.isUpdatePending=!0;try{await this._$ES}catch(t){Promise.reject(t)}const t=this.scheduleUpdate();return null!=t&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){if(!this.isUpdatePending)return;if(!this.hasUpdated){if(this.renderRoot??=this.createRenderRoot(),this._$Ep){for(const[t,s]of this._$Ep)this[t]=s;this._$Ep=void 0}const t=this.constructor.elementProperties;if(t.size>0)for(const[s,i]of t)!0!==i.wrapped||this._$AL.has(s)||void 0===this[s]||this.P(s,this[s],i)}let t=!1;const s=this._$AL;try{t=this.shouldUpdate(s),t?(this.willUpdate(s),this._$EO?.forEach((t=>t.hostUpdate?.())),this.update(s)):this._$EU()}catch(s){throw t=!1,this._$EU(),s}t&&this._$AE(s)}willUpdate(t){}_$AE(t){this._$EO?.forEach((t=>t.hostUpdated?.())),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$EU(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$ES}shouldUpdate(t){return!0}update(t){this._$Ej&&=this._$Ej.forEach((t=>this._$EC(t,this[t]))),this._$EU()}updated(t){}firstUpdated(t){}}b.elementStyles=[],b.shadowRootOptions={mode:\"open\"},b[d(\"elementProperties\")]=new Map,b[d(\"finalized\")]=new Map,p?.({ReactiveElement:b}),(a.reactiveElementVersions??=[]).push(\"2.0.4\");export{b as ReactiveElement,u as defaultConverter,f as notEqual};\n//# sourceMappingURL=reactive-element.js.map\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t=globalThis,i=t.trustedTypes,s=i?i.createPolicy(\"lit-html\",{createHTML:t=>t}):void 0,e=\"$lit$\",h=`lit$${Math.random().toFixed(9).slice(2)}$`,o=\"?\"+h,n=`<${o}>`,r=document,l=()=>r.createComment(\"\"),c=t=>null===t||\"object\"!=typeof t&&\"function\"!=typeof t,a=Array.isArray,u=t=>a(t)||\"function\"==typeof t?.[Symbol.iterator],d=\"[ \\t\\n\\f\\r]\",f=/<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g,v=/-->/g,_=/>/g,m=RegExp(`>|${d}(?:([^\\\\s\"'>=/]+)(${d}*=${d}*(?:[^ \\t\\n\\f\\r\"'\\`<>=]|(\"|')|))|$)`,\"g\"),p=/'/g,g=/\"/g,$=/^(?:script|style|textarea|title)$/i,y=t=>(i,...s)=>({_$litType$:t,strings:i,values:s}),x=y(1),b=y(2),w=y(3),T=Symbol.for(\"lit-noChange\"),E=Symbol.for(\"lit-nothing\"),A=new WeakMap,C=r.createTreeWalker(r,129);function P(t,i){if(!a(t)||!t.hasOwnProperty(\"raw\"))throw Error(\"invalid template strings array\");return void 0!==s?s.createHTML(i):i}const V=(t,i)=>{const s=t.length-1,o=[];let r,l=2===i?\"<svg>\":3===i?\"<math>\":\"\",c=f;for(let i=0;i<s;i++){const s=t[i];let a,u,d=-1,y=0;for(;y<s.length&&(c.lastIndex=y,u=c.exec(s),null!==u);)y=c.lastIndex,c===f?\"!--\"===u[1]?c=v:void 0!==u[1]?c=_:void 0!==u[2]?($.test(u[2])&&(r=RegExp(\"</\"+u[2],\"g\")),c=m):void 0!==u[3]&&(c=m):c===m?\">\"===u[0]?(c=r??f,d=-1):void 0===u[1]?d=-2:(d=c.lastIndex-u[2].length,a=u[1],c=void 0===u[3]?m:'\"'===u[3]?g:p):c===g||c===p?c=m:c===v||c===_?c=f:(c=m,r=void 0);const x=c===m&&t[i+1].startsWith(\"/>\")?\" \":\"\";l+=c===f?s+n:d>=0?(o.push(a),s.slice(0,d)+e+s.slice(d)+h+x):s+h+(-2===d?i:x)}return[P(t,l+(t[s]||\"<?>\")+(2===i?\"</svg>\":3===i?\"</math>\":\"\")),o]};class N{constructor({strings:t,_$litType$:s},n){let r;this.parts=[];let c=0,a=0;const u=t.length-1,d=this.parts,[f,v]=V(t,s);if(this.el=N.createElement(f,n),C.currentNode=this.el.content,2===s||3===s){const t=this.el.content.firstChild;t.replaceWith(...t.childNodes)}for(;null!==(r=C.nextNode())&&d.length<u;){if(1===r.nodeType){if(r.hasAttributes())for(const t of r.getAttributeNames())if(t.endsWith(e)){const i=v[a++],s=r.getAttribute(t).split(h),e=/([.?@])?(.*)/.exec(i);d.push({type:1,index:c,name:e[2],strings:s,ctor:\".\"===e[1]?H:\"?\"===e[1]?I:\"@\"===e[1]?L:k}),r.removeAttribute(t)}else t.startsWith(h)&&(d.push({type:6,index:c}),r.removeAttribute(t));if($.test(r.tagName)){const t=r.textContent.split(h),s=t.length-1;if(s>0){r.textContent=i?i.emptyScript:\"\";for(let i=0;i<s;i++)r.append(t[i],l()),C.nextNode(),d.push({type:2,index:++c});r.append(t[s],l())}}}else if(8===r.nodeType)if(r.data===o)d.push({type:2,index:c});else{let t=-1;for(;-1!==(t=r.data.indexOf(h,t+1));)d.push({type:7,index:c}),t+=h.length-1}c++}}static createElement(t,i){const s=r.createElement(\"template\");return s.innerHTML=t,s}}function S(t,i,s=t,e){if(i===T)return i;let h=void 0!==e?s._$Co?.[e]:s._$Cl;const o=c(i)?void 0:i._$litDirective$;return h?.constructor!==o&&(h?._$AO?.(!1),void 0===o?h=void 0:(h=new o(t),h._$AT(t,s,e)),void 0!==e?(s._$Co??=[])[e]=h:s._$Cl=h),void 0!==h&&(i=S(t,h._$AS(t,i.values),h,e)),i}class M{constructor(t,i){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=i}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(t){const{el:{content:i},parts:s}=this._$AD,e=(t?.creationScope??r).importNode(i,!0);C.currentNode=e;let h=C.nextNode(),o=0,n=0,l=s[0];for(;void 0!==l;){if(o===l.index){let i;2===l.type?i=new R(h,h.nextSibling,this,t):1===l.type?i=new l.ctor(h,l.name,l.strings,this,t):6===l.type&&(i=new z(h,this,t)),this._$AV.push(i),l=s[++n]}o!==l?.index&&(h=C.nextNode(),o++)}return C.currentNode=r,e}p(t){let i=0;for(const s of this._$AV)void 0!==s&&(void 0!==s.strings?(s._$AI(t,s,i),i+=s.strings.length-2):s._$AI(t[i])),i++}}class R{get _$AU(){return this._$AM?._$AU??this._$Cv}constructor(t,i,s,e){this.type=2,this._$AH=E,this._$AN=void 0,this._$AA=t,this._$AB=i,this._$AM=s,this.options=e,this._$Cv=e?.isConnected??!0}get parentNode(){let t=this._$AA.parentNode;const i=this._$AM;return void 0!==i&&11===t?.nodeType&&(t=i.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,i=this){t=S(this,t,i),c(t)?t===E||null==t||\"\"===t?(this._$AH!==E&&this._$AR(),this._$AH=E):t!==this._$AH&&t!==T&&this._(t):void 0!==t._$litType$?this.$(t):void 0!==t.nodeType?this.T(t):u(t)?this.k(t):this._(t)}O(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}T(t){this._$AH!==t&&(this._$AR(),this._$AH=this.O(t))}_(t){this._$AH!==E&&c(this._$AH)?this._$AA.nextSibling.data=t:this.T(r.createTextNode(t)),this._$AH=t}$(t){const{values:i,_$litType$:s}=t,e=\"number\"==typeof s?this._$AC(t):(void 0===s.el&&(s.el=N.createElement(P(s.h,s.h[0]),this.options)),s);if(this._$AH?._$AD===e)this._$AH.p(i);else{const t=new M(e,this),s=t.u(this.options);t.p(i),this.T(s),this._$AH=t}}_$AC(t){let i=A.get(t.strings);return void 0===i&&A.set(t.strings,i=new N(t)),i}k(t){a(this._$AH)||(this._$AH=[],this._$AR());const i=this._$AH;let s,e=0;for(const h of t)e===i.length?i.push(s=new R(this.O(l()),this.O(l()),this,this.options)):s=i[e],s._$AI(h),e++;e<i.length&&(this._$AR(s&&s._$AB.nextSibling,e),i.length=e)}_$AR(t=this._$AA.nextSibling,i){for(this._$AP?.(!1,!0,i);t&&t!==this._$AB;){const i=t.nextSibling;t.remove(),t=i}}setConnected(t){void 0===this._$AM&&(this._$Cv=t,this._$AP?.(t))}}class k{get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}constructor(t,i,s,e,h){this.type=1,this._$AH=E,this._$AN=void 0,this.element=t,this.name=i,this._$AM=e,this.options=h,s.length>2||\"\"!==s[0]||\"\"!==s[1]?(this._$AH=Array(s.length-1).fill(new String),this.strings=s):this._$AH=E}_$AI(t,i=this,s,e){const h=this.strings;let o=!1;if(void 0===h)t=S(this,t,i,0),o=!c(t)||t!==this._$AH&&t!==T,o&&(this._$AH=t);else{const e=t;let n,r;for(t=h[0],n=0;n<h.length-1;n++)r=S(this,e[s+n],i,n),r===T&&(r=this._$AH[n]),o||=!c(r)||r!==this._$AH[n],r===E?t=E:t!==E&&(t+=(r??\"\")+h[n+1]),this._$AH[n]=r}o&&!e&&this.j(t)}j(t){t===E?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,t??\"\")}}class H extends k{constructor(){super(...arguments),this.type=3}j(t){this.element[this.name]=t===E?void 0:t}}class I extends k{constructor(){super(...arguments),this.type=4}j(t){this.element.toggleAttribute(this.name,!!t&&t!==E)}}class L extends k{constructor(t,i,s,e,h){super(t,i,s,e,h),this.type=5}_$AI(t,i=this){if((t=S(this,t,i,0)??E)===T)return;const s=this._$AH,e=t===E&&s!==E||t.capture!==s.capture||t.once!==s.once||t.passive!==s.passive,h=t!==E&&(s===E||e);e&&this.element.removeEventListener(this.name,this,s),h&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){\"function\"==typeof this._$AH?this._$AH.call(this.options?.host??this.element,t):this._$AH.handleEvent(t)}}class z{constructor(t,i,s){this.element=t,this.type=6,this._$AN=void 0,this._$AM=i,this.options=s}get _$AU(){return this._$AM._$AU}_$AI(t){S(this,t)}}const Z={M:e,P:h,A:o,C:1,L:V,R:M,D:u,V:S,I:R,H:k,N:I,U:L,B:H,F:z},j=t.litHtmlPolyfillSupport;j?.(N,R),(t.litHtmlVersions??=[]).push(\"3.2.1\");const B=(t,i,s)=>{const e=s?.renderBefore??i;let h=e._$litPart$;if(void 0===h){const t=s?.renderBefore??null;e._$litPart$=h=new R(i.insertBefore(l(),t),t,void 0,s??{})}return h._$AI(t),h};export{Z as _$LH,x as html,w as mathml,T as noChange,E as nothing,B as render,b as svg};\n//# sourceMappingURL=lit-html.js.map\n", "import{ReactiveElement as t}from\"@lit/reactive-element\";export*from\"@lit/reactive-element\";import{render as e,noChange as s}from\"lit-html\";export*from\"lit-html\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */class r extends t{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){const t=super.createRenderRoot();return this.renderOptions.renderBefore??=t.firstChild,t}update(t){const s=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Do=e(s,this.renderRoot,this.renderOptions)}connectedCallback(){super.connectedCallback(),this._$Do?.setConnected(!0)}disconnectedCallback(){super.disconnectedCallback(),this._$Do?.setConnected(!1)}render(){return s}}r._$litElement$=!0,r[\"finalized\"]=!0,globalThis.litElementHydrateSupport?.({LitElement:r});const i=globalThis.litElementPolyfillSupport;i?.({LitElement:r});const o={_$AK:(t,e,s)=>{t._$AK(e,s)},_$AL:t=>t._$AL};(globalThis.litElementVersions??=[]).push(\"4.1.1\");export{r as LitElement,o as _$LE};\n//# sourceMappingURL=lit-element.js.map\n", "export const environment = {\n    production: true,\n    // apiUrl: 'https://api.ngocmaitravel.vn',\n    // apiUrl: 'https://************',\n    // apiUrl: 'https://test01.ngocmaitravel.vn',\n    apiUrl: 'https://abi-ota.nmbooking.vn',\n    // apiUrl: 'https://localhost:7065',\n    publicKey: \"LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0NCk1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBeC9DT1ZhTWpQMGt5c0RiRlhkZkhyLzhQOEg5bEsveVoNCis2NERvZWdTSERSeENqb0NQMGxMZEtqb2VMa1hWaWE4UTBzQ3dzU1VIeHhIc3lkeWVhY0tUN0hHWG1WbFBaQXlJbHh1RjRPc0ZvWkoNCmEzQUVrSTdPMEpBeFFrcmFENzdURGJoeHBzSHdKUmtnWEI4aVR5NzQwOS83NjQ2VForcGdGTzMvSVlVT0M2SEplMEIvdkVadzkzeTkNCitodGlWa0ZtZEorQ2JkeGZyaEZrWHU5ZGRWeWptTENRODFIOTRJV2xOdm5PdlFHTkNpNWNHenpIZTVGTldpVy9PRGZrNkRKN0tJUUINCmlieUovVVoxdnJlKzExTytPWGZ0bkwwcHFybThvTnE5dURneWZQUW1US1Rxb0pDaGl6ZEFFTVlyNDVsQ3UybUJJNmVtVFhJWWVJYlANCm9uenZpUUlEQVFBQg0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tDQo=\",\n};\n", "/**\n * FingerprintJS v4.6.1 - Copyright (c) FingerprintJS, Inc, 2025 (https://fingerprint.com)\n *\n * Licensed under Business Source License 1.1 https://mariadb.com/bsl11/\n * Licensor: FingerprintJS, Inc.\n * Licensed Work: FingerprintJS browser fingerprinting library\n * Additional Use Grant: None\n * Change Date: Four years from first release for the specific version.\n * Change License: MIT, text at https://opensource.org/license/mit/ with the following copyright notice:\n * Copyright 2015-present FingerprintJS, Inc.\n */\n\nimport { __awaiter, __generator, __assign, __spreadArray } from 'tslib';\n\nvar version = \"4.6.1\";\n\nfunction wait(durationMs, resolveWith) {\n    return new Promise(function (resolve) { return setTimeout(resolve, durationMs, resolveWith); });\n}\n/**\n * Allows asynchronous actions and microtasks to happen.\n */\nfunction releaseEventLoop() {\n    // Don't use setTimeout because Chrome throttles it in some cases causing very long agent execution:\n    // https://stackoverflow.com/a/6032591/1118709\n    // https://github.com/chromium/chromium/commit/0295dd09496330f3a9103ef7e543fa9b6050409b\n    // Reusing a MessageChannel object gives no noticeable benefits\n    return new Promise(function (resolve) {\n        var channel = new MessageChannel();\n        channel.port1.onmessage = function () { return resolve(); };\n        channel.port2.postMessage(null);\n    });\n}\nfunction requestIdleCallbackIfAvailable(fallbackTimeout, deadlineTimeout) {\n    if (deadlineTimeout === void 0) { deadlineTimeout = Infinity; }\n    var requestIdleCallback = window.requestIdleCallback;\n    if (requestIdleCallback) {\n        // The function `requestIdleCallback` loses the binding to `window` here.\n        // `globalThis` isn't always equal `window` (see https://github.com/fingerprintjs/fingerprintjs/issues/683).\n        // Therefore, an error can occur. `call(window,` prevents the error.\n        return new Promise(function (resolve) { return requestIdleCallback.call(window, function () { return resolve(); }, { timeout: deadlineTimeout }); });\n    }\n    else {\n        return wait(Math.min(fallbackTimeout, deadlineTimeout));\n    }\n}\nfunction isPromise(value) {\n    return !!value && typeof value.then === 'function';\n}\n/**\n * Calls a maybe asynchronous function without creating microtasks when the function is synchronous.\n * Catches errors in both cases.\n *\n * If just you run a code like this:\n * ```\n * console.time('Action duration')\n * await action()\n * console.timeEnd('Action duration')\n * ```\n * The synchronous function time can be measured incorrectly because another microtask may run before the `await`\n * returns the control back to the code.\n */\nfunction awaitIfAsync(action, callback) {\n    try {\n        var returnedValue = action();\n        if (isPromise(returnedValue)) {\n            returnedValue.then(function (result) { return callback(true, result); }, function (error) { return callback(false, error); });\n        }\n        else {\n            callback(true, returnedValue);\n        }\n    }\n    catch (error) {\n        callback(false, error);\n    }\n}\n/**\n * If you run many synchronous tasks without using this function, the JS main loop will be busy and asynchronous tasks\n * (e.g. completing a network request, rendering the page) won't be able to happen.\n * This function allows running many synchronous tasks such way that asynchronous tasks can run too in background.\n */\nfunction mapWithBreaks(items, callback, loopReleaseInterval) {\n    if (loopReleaseInterval === void 0) { loopReleaseInterval = 16; }\n    return __awaiter(this, void 0, void 0, function () {\n        var results, lastLoopReleaseTime, i, now;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    results = Array(items.length);\n                    lastLoopReleaseTime = Date.now();\n                    i = 0;\n                    _a.label = 1;\n                case 1:\n                    if (!(i < items.length)) return [3 /*break*/, 4];\n                    results[i] = callback(items[i], i);\n                    now = Date.now();\n                    if (!(now >= lastLoopReleaseTime + loopReleaseInterval)) return [3 /*break*/, 3];\n                    lastLoopReleaseTime = now;\n                    return [4 /*yield*/, releaseEventLoop()];\n                case 2:\n                    _a.sent();\n                    _a.label = 3;\n                case 3:\n                    ++i;\n                    return [3 /*break*/, 1];\n                case 4: return [2 /*return*/, results];\n            }\n        });\n    });\n}\n/**\n * Makes the given promise never emit an unhandled promise rejection console warning.\n * The promise will still pass errors to the next promises.\n * Returns the input promise for convenience.\n *\n * Otherwise, promise emits a console warning unless it has a `catch` listener.\n */\nfunction suppressUnhandledRejectionWarning(promise) {\n    promise.then(undefined, function () { return undefined; });\n    return promise;\n}\n\n/*\n * This file contains functions to work with pure data only (no browser features, DOM, side effects, etc).\n */\n/**\n * Does the same as Array.prototype.includes but has better typing\n */\nfunction includes(haystack, needle) {\n    for (var i = 0, l = haystack.length; i < l; ++i) {\n        if (haystack[i] === needle) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * Like `!includes()` but with proper typing\n */\nfunction excludes(haystack, needle) {\n    return !includes(haystack, needle);\n}\n/**\n * Be careful, NaN can return\n */\nfunction toInt(value) {\n    return parseInt(value);\n}\n/**\n * Be careful, NaN can return\n */\nfunction toFloat(value) {\n    return parseFloat(value);\n}\nfunction replaceNaN(value, replacement) {\n    return typeof value === 'number' && isNaN(value) ? replacement : value;\n}\nfunction countTruthy(values) {\n    return values.reduce(function (sum, value) { return sum + (value ? 1 : 0); }, 0);\n}\nfunction round(value, base) {\n    if (base === void 0) { base = 1; }\n    if (Math.abs(base) >= 1) {\n        return Math.round(value / base) * base;\n    }\n    else {\n        // Sometimes when a number is multiplied by a small number, precision is lost,\n        // for example 1234 * 0.0001 === 0.12340000000000001, and it's more precise divide: 1234 / (1 / 0.0001) === 0.1234.\n        var counterBase = 1 / base;\n        return Math.round(value * counterBase) / counterBase;\n    }\n}\n/**\n * Parses a CSS selector into tag name with HTML attributes.\n * Only single element selector are supported (without operators like space, +, >, etc).\n *\n * Multiple values can be returned for each attribute. You decide how to handle them.\n */\nfunction parseSimpleCssSelector(selector) {\n    var _a, _b;\n    var errorMessage = \"Unexpected syntax '\".concat(selector, \"'\");\n    var tagMatch = /^\\s*([a-z-]*)(.*)$/i.exec(selector);\n    var tag = tagMatch[1] || undefined;\n    var attributes = {};\n    var partsRegex = /([.:#][\\w-]+|\\[.+?\\])/gi;\n    var addAttribute = function (name, value) {\n        attributes[name] = attributes[name] || [];\n        attributes[name].push(value);\n    };\n    for (;;) {\n        var match = partsRegex.exec(tagMatch[2]);\n        if (!match) {\n            break;\n        }\n        var part = match[0];\n        switch (part[0]) {\n            case '.':\n                addAttribute('class', part.slice(1));\n                break;\n            case '#':\n                addAttribute('id', part.slice(1));\n                break;\n            case '[': {\n                var attributeMatch = /^\\[([\\w-]+)([~|^$*]?=(\"(.*?)\"|([\\w-]+)))?(\\s+[is])?\\]$/.exec(part);\n                if (attributeMatch) {\n                    addAttribute(attributeMatch[1], (_b = (_a = attributeMatch[4]) !== null && _a !== void 0 ? _a : attributeMatch[5]) !== null && _b !== void 0 ? _b : '');\n                }\n                else {\n                    throw new Error(errorMessage);\n                }\n                break;\n            }\n            default:\n                throw new Error(errorMessage);\n        }\n    }\n    return [tag, attributes];\n}\n/**\n * Converts a string to UTF8 bytes\n */\nfunction getUTF8Bytes(input) {\n    // Benchmark: https://jsbench.me/b6klaaxgwq/1\n    // If you want to just count bytes, see solutions at https://jsbench.me/ehklab415e/1\n    var result = new Uint8Array(input.length);\n    for (var i = 0; i < input.length; i++) {\n        // `charCode` is faster than encoding, so we prefer that when it's possible\n        var charCode = input.charCodeAt(i);\n        // In case of non-ASCII symbols we use proper encoding\n        if (charCode > 127) {\n            return new TextEncoder().encode(input);\n        }\n        result[i] = charCode;\n    }\n    return result;\n}\n\n/*\n * Based on https://github.com/karanlyons/murmurHash3.js/blob/a33d0723127e2e5415056c455f8aed2451ace208/murmurHash3.js\n */\n/**\n * Adds two 64-bit values (provided as tuples of 32-bit values)\n * and updates (mutates) first value to write the result\n */\nfunction x64Add(m, n) {\n    var m0 = m[0] >>> 16, m1 = m[0] & 0xffff, m2 = m[1] >>> 16, m3 = m[1] & 0xffff;\n    var n0 = n[0] >>> 16, n1 = n[0] & 0xffff, n2 = n[1] >>> 16, n3 = n[1] & 0xffff;\n    var o0 = 0, o1 = 0, o2 = 0, o3 = 0;\n    o3 += m3 + n3;\n    o2 += o3 >>> 16;\n    o3 &= 0xffff;\n    o2 += m2 + n2;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o1 += m1 + n1;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o0 += m0 + n0;\n    o0 &= 0xffff;\n    m[0] = (o0 << 16) | o1;\n    m[1] = (o2 << 16) | o3;\n}\n/**\n * Multiplies two 64-bit values (provided as tuples of 32-bit values)\n * and updates (mutates) first value to write the result\n */\nfunction x64Multiply(m, n) {\n    var m0 = m[0] >>> 16, m1 = m[0] & 0xffff, m2 = m[1] >>> 16, m3 = m[1] & 0xffff;\n    var n0 = n[0] >>> 16, n1 = n[0] & 0xffff, n2 = n[1] >>> 16, n3 = n[1] & 0xffff;\n    var o0 = 0, o1 = 0, o2 = 0, o3 = 0;\n    o3 += m3 * n3;\n    o2 += o3 >>> 16;\n    o3 &= 0xffff;\n    o2 += m2 * n3;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o2 += m3 * n2;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o1 += m1 * n3;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o1 += m2 * n2;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o1 += m3 * n1;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o0 += m0 * n3 + m1 * n2 + m2 * n1 + m3 * n0;\n    o0 &= 0xffff;\n    m[0] = (o0 << 16) | o1;\n    m[1] = (o2 << 16) | o3;\n}\n/**\n * Provides left rotation of the given int64 value (provided as tuple of two int32)\n * by given number of bits. Result is written back to the value\n */\nfunction x64Rotl(m, bits) {\n    var m0 = m[0];\n    bits %= 64;\n    if (bits === 32) {\n        m[0] = m[1];\n        m[1] = m0;\n    }\n    else if (bits < 32) {\n        m[0] = (m0 << bits) | (m[1] >>> (32 - bits));\n        m[1] = (m[1] << bits) | (m0 >>> (32 - bits));\n    }\n    else {\n        bits -= 32;\n        m[0] = (m[1] << bits) | (m0 >>> (32 - bits));\n        m[1] = (m0 << bits) | (m[1] >>> (32 - bits));\n    }\n}\n/**\n * Provides a left shift of the given int32 value (provided as tuple of [0, int32])\n * by given number of bits. Result is written back to the value\n */\nfunction x64LeftShift(m, bits) {\n    bits %= 64;\n    if (bits === 0) {\n        return;\n    }\n    else if (bits < 32) {\n        m[0] = m[1] >>> (32 - bits);\n        m[1] = m[1] << bits;\n    }\n    else {\n        m[0] = m[1] << (bits - 32);\n        m[1] = 0;\n    }\n}\n/**\n * Provides a XOR of the given int64 values(provided as tuple of two int32).\n * Result is written back to the first value\n */\nfunction x64Xor(m, n) {\n    m[0] ^= n[0];\n    m[1] ^= n[1];\n}\nvar F1 = [0xff51afd7, 0xed558ccd];\nvar F2 = [0xc4ceb9fe, 0x1a85ec53];\n/**\n * Calculates murmurHash3's final x64 mix of that block and writes result back to the input value.\n * (`[0, h[0] >>> 1]` is a 33 bit unsigned right shift. This is the\n * only place where we need to right shift 64bit ints.)\n */\nfunction x64Fmix(h) {\n    var shifted = [0, h[0] >>> 1];\n    x64Xor(h, shifted);\n    x64Multiply(h, F1);\n    shifted[1] = h[0] >>> 1;\n    x64Xor(h, shifted);\n    x64Multiply(h, F2);\n    shifted[1] = h[0] >>> 1;\n    x64Xor(h, shifted);\n}\nvar C1 = [0x87c37b91, 0x114253d5];\nvar C2 = [0x4cf5ad43, 0x2745937f];\nvar M$1 = [0, 5];\nvar N1 = [0, 0x52dce729];\nvar N2 = [0, 0x38495ab5];\n/**\n * Given a string and an optional seed as an int, returns a 128 bit\n * hash using the x64 flavor of MurmurHash3, as an unsigned hex.\n * All internal functions mutates passed value to achieve minimal memory allocations and GC load\n *\n * Benchmark https://jsbench.me/p4lkpaoabi/1\n */\nfunction x64hash128(input, seed) {\n    var key = getUTF8Bytes(input);\n    seed = seed || 0;\n    var length = [0, key.length];\n    var remainder = length[1] % 16;\n    var bytes = length[1] - remainder;\n    var h1 = [0, seed];\n    var h2 = [0, seed];\n    var k1 = [0, 0];\n    var k2 = [0, 0];\n    var i;\n    for (i = 0; i < bytes; i = i + 16) {\n        k1[0] = key[i + 4] | (key[i + 5] << 8) | (key[i + 6] << 16) | (key[i + 7] << 24);\n        k1[1] = key[i] | (key[i + 1] << 8) | (key[i + 2] << 16) | (key[i + 3] << 24);\n        k2[0] = key[i + 12] | (key[i + 13] << 8) | (key[i + 14] << 16) | (key[i + 15] << 24);\n        k2[1] = key[i + 8] | (key[i + 9] << 8) | (key[i + 10] << 16) | (key[i + 11] << 24);\n        x64Multiply(k1, C1);\n        x64Rotl(k1, 31);\n        x64Multiply(k1, C2);\n        x64Xor(h1, k1);\n        x64Rotl(h1, 27);\n        x64Add(h1, h2);\n        x64Multiply(h1, M$1);\n        x64Add(h1, N1);\n        x64Multiply(k2, C2);\n        x64Rotl(k2, 33);\n        x64Multiply(k2, C1);\n        x64Xor(h2, k2);\n        x64Rotl(h2, 31);\n        x64Add(h2, h1);\n        x64Multiply(h2, M$1);\n        x64Add(h2, N2);\n    }\n    k1[0] = 0;\n    k1[1] = 0;\n    k2[0] = 0;\n    k2[1] = 0;\n    var val = [0, 0];\n    switch (remainder) {\n        case 15:\n            val[1] = key[i + 14];\n            x64LeftShift(val, 48);\n            x64Xor(k2, val);\n        // fallthrough\n        case 14:\n            val[1] = key[i + 13];\n            x64LeftShift(val, 40);\n            x64Xor(k2, val);\n        // fallthrough\n        case 13:\n            val[1] = key[i + 12];\n            x64LeftShift(val, 32);\n            x64Xor(k2, val);\n        // fallthrough\n        case 12:\n            val[1] = key[i + 11];\n            x64LeftShift(val, 24);\n            x64Xor(k2, val);\n        // fallthrough\n        case 11:\n            val[1] = key[i + 10];\n            x64LeftShift(val, 16);\n            x64Xor(k2, val);\n        // fallthrough\n        case 10:\n            val[1] = key[i + 9];\n            x64LeftShift(val, 8);\n            x64Xor(k2, val);\n        // fallthrough\n        case 9:\n            val[1] = key[i + 8];\n            x64Xor(k2, val);\n            x64Multiply(k2, C2);\n            x64Rotl(k2, 33);\n            x64Multiply(k2, C1);\n            x64Xor(h2, k2);\n        // fallthrough\n        case 8:\n            val[1] = key[i + 7];\n            x64LeftShift(val, 56);\n            x64Xor(k1, val);\n        // fallthrough\n        case 7:\n            val[1] = key[i + 6];\n            x64LeftShift(val, 48);\n            x64Xor(k1, val);\n        // fallthrough\n        case 6:\n            val[1] = key[i + 5];\n            x64LeftShift(val, 40);\n            x64Xor(k1, val);\n        // fallthrough\n        case 5:\n            val[1] = key[i + 4];\n            x64LeftShift(val, 32);\n            x64Xor(k1, val);\n        // fallthrough\n        case 4:\n            val[1] = key[i + 3];\n            x64LeftShift(val, 24);\n            x64Xor(k1, val);\n        // fallthrough\n        case 3:\n            val[1] = key[i + 2];\n            x64LeftShift(val, 16);\n            x64Xor(k1, val);\n        // fallthrough\n        case 2:\n            val[1] = key[i + 1];\n            x64LeftShift(val, 8);\n            x64Xor(k1, val);\n        // fallthrough\n        case 1:\n            val[1] = key[i];\n            x64Xor(k1, val);\n            x64Multiply(k1, C1);\n            x64Rotl(k1, 31);\n            x64Multiply(k1, C2);\n            x64Xor(h1, k1);\n        // fallthrough\n    }\n    x64Xor(h1, length);\n    x64Xor(h2, length);\n    x64Add(h1, h2);\n    x64Add(h2, h1);\n    x64Fmix(h1);\n    x64Fmix(h2);\n    x64Add(h1, h2);\n    x64Add(h2, h1);\n    return (('00000000' + (h1[0] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h1[1] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h2[0] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h2[1] >>> 0).toString(16)).slice(-8));\n}\n\n/**\n * Converts an error object to a plain object that can be used with `JSON.stringify`.\n * If you just run `JSON.stringify(error)`, you'll get `'{}'`.\n */\nfunction errorToObject(error) {\n    var _a;\n    return __assign({ name: error.name, message: error.message, stack: (_a = error.stack) === null || _a === void 0 ? void 0 : _a.split('\\n') }, error);\n}\nfunction isFunctionNative(func) {\n    return /^function\\s.*?\\{\\s*\\[native code]\\s*}$/.test(String(func));\n}\n\nfunction isFinalResultLoaded(loadResult) {\n    return typeof loadResult !== 'function';\n}\n/**\n * Loads the given entropy source. Returns a function that gets an entropy component from the source.\n *\n * The result is returned synchronously to prevent `loadSources` from\n * waiting for one source to load before getting the components from the other sources.\n */\nfunction loadSource(source, sourceOptions) {\n    var sourceLoadPromise = suppressUnhandledRejectionWarning(new Promise(function (resolveLoad) {\n        var loadStartTime = Date.now();\n        // `awaitIfAsync` is used instead of just `await` in order to measure the duration of synchronous sources\n        // correctly (other microtasks won't affect the duration).\n        awaitIfAsync(source.bind(null, sourceOptions), function () {\n            var loadArgs = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                loadArgs[_i] = arguments[_i];\n            }\n            var loadDuration = Date.now() - loadStartTime;\n            // Source loading failed\n            if (!loadArgs[0]) {\n                return resolveLoad(function () { return ({ error: loadArgs[1], duration: loadDuration }); });\n            }\n            var loadResult = loadArgs[1];\n            // Source loaded with the final result\n            if (isFinalResultLoaded(loadResult)) {\n                return resolveLoad(function () { return ({ value: loadResult, duration: loadDuration }); });\n            }\n            // Source loaded with \"get\" stage\n            resolveLoad(function () {\n                return new Promise(function (resolveGet) {\n                    var getStartTime = Date.now();\n                    awaitIfAsync(loadResult, function () {\n                        var getArgs = [];\n                        for (var _i = 0; _i < arguments.length; _i++) {\n                            getArgs[_i] = arguments[_i];\n                        }\n                        var duration = loadDuration + Date.now() - getStartTime;\n                        // Source getting failed\n                        if (!getArgs[0]) {\n                            return resolveGet({ error: getArgs[1], duration: duration });\n                        }\n                        // Source getting succeeded\n                        resolveGet({ value: getArgs[1], duration: duration });\n                    });\n                });\n            });\n        });\n    }));\n    return function getComponent() {\n        return sourceLoadPromise.then(function (finalizeSource) { return finalizeSource(); });\n    };\n}\n/**\n * Loads the given entropy sources. Returns a function that collects the entropy components.\n *\n * The result is returned synchronously in order to allow start getting the components\n * before the sources are loaded completely.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction loadSources(sources, sourceOptions, excludeSources, loopReleaseInterval) {\n    var includedSources = Object.keys(sources).filter(function (sourceKey) { return excludes(excludeSources, sourceKey); });\n    // Using `mapWithBreaks` allows asynchronous sources to complete between synchronous sources\n    // and measure the duration correctly\n    var sourceGettersPromise = suppressUnhandledRejectionWarning(mapWithBreaks(includedSources, function (sourceKey) { return loadSource(sources[sourceKey], sourceOptions); }, loopReleaseInterval));\n    return function getComponents() {\n        return __awaiter(this, void 0, void 0, function () {\n            var sourceGetters, componentPromises, componentArray, components, index;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, sourceGettersPromise];\n                    case 1:\n                        sourceGetters = _a.sent();\n                        return [4 /*yield*/, mapWithBreaks(sourceGetters, function (sourceGetter) { return suppressUnhandledRejectionWarning(sourceGetter()); }, loopReleaseInterval)];\n                    case 2:\n                        componentPromises = _a.sent();\n                        return [4 /*yield*/, Promise.all(componentPromises)\n                            // Keeping the component keys order the same as the source keys order\n                        ];\n                    case 3:\n                        componentArray = _a.sent();\n                        components = {};\n                        for (index = 0; index < includedSources.length; ++index) {\n                            components[includedSources[index]] = componentArray[index];\n                        }\n                        return [2 /*return*/, components];\n                }\n            });\n        });\n    };\n}\n/**\n * Modifies an entropy source by transforming its returned value with the given function.\n * Keeps the source properties: sync/async, 1/2 stages.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction transformSource(source, transformValue) {\n    var transformLoadResult = function (loadResult) {\n        if (isFinalResultLoaded(loadResult)) {\n            return transformValue(loadResult);\n        }\n        return function () {\n            var getResult = loadResult();\n            if (isPromise(getResult)) {\n                return getResult.then(transformValue);\n            }\n            return transformValue(getResult);\n        };\n    };\n    return function (options) {\n        var loadResult = source(options);\n        if (isPromise(loadResult)) {\n            return loadResult.then(transformLoadResult);\n        }\n        return transformLoadResult(loadResult);\n    };\n}\n\n/*\n * Functions to help with features that vary through browsers\n */\n/**\n * Checks whether the browser is based on Trident (the Internet Explorer engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isTrident() {\n    var w = window;\n    var n = navigator;\n    // The properties are checked to be in IE 10, IE 11 and not to be in other browsers in October 2020\n    return (countTruthy([\n        'MSCSSMatrix' in w,\n        'msSetImmediate' in w,\n        'msIndexedDB' in w,\n        'msMaxTouchPoints' in n,\n        'msPointerEnabled' in n,\n    ]) >= 4);\n}\n/**\n * Checks whether the browser is based on EdgeHTML (the pre-Chromium Edge engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isEdgeHTML() {\n    // Based on research in October 2020\n    var w = window;\n    var n = navigator;\n    return (countTruthy(['msWriteProfilerMark' in w, 'MSStream' in w, 'msLaunchUri' in n, 'msSaveBlob' in n]) >= 3 &&\n        !isTrident());\n}\n/**\n * Checks whether the browser is based on Chromium without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isChromium() {\n    // Based on research in October 2020. Tested to detect Chromium 42-86.\n    var w = window;\n    var n = navigator;\n    return (countTruthy([\n        'webkitPersistentStorage' in n,\n        'webkitTemporaryStorage' in n,\n        (n.vendor || '').indexOf('Google') === 0,\n        'webkitResolveLocalFileSystemURL' in w,\n        'BatteryManager' in w,\n        'webkitMediaStream' in w,\n        'webkitSpeechGrammar' in w,\n    ]) >= 5);\n}\n/**\n * Checks whether the browser is based on mobile or desktop Safari without using user-agent.\n * All iOS browsers use WebKit (the Safari engine).\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isWebKit() {\n    // Based on research in August 2024\n    var w = window;\n    var n = navigator;\n    return (countTruthy([\n        'ApplePayError' in w,\n        'CSSPrimitiveValue' in w,\n        'Counter' in w,\n        n.vendor.indexOf('Apple') === 0,\n        'RGBColor' in w,\n        'WebKitMediaKeys' in w,\n    ]) >= 4);\n}\n/**\n * Checks whether this WebKit browser is a desktop browser.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isDesktopWebKit() {\n    // Checked in Safari and DuckDuckGo\n    var w = window;\n    var HTMLElement = w.HTMLElement, Document = w.Document;\n    return (countTruthy([\n        'safari' in w,\n        !('ongestureend' in w),\n        !('TouchEvent' in w),\n        !('orientation' in w),\n        HTMLElement && !('autocapitalize' in HTMLElement.prototype),\n        Document && 'pointerLockElement' in Document.prototype,\n    ]) >= 4);\n}\n/**\n * Checks whether this WebKit browser is Safari.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * Warning! The function works properly only for Safari version 15.4 and newer.\n */\nfunction isSafariWebKit() {\n    // Checked in Safari, Chrome, Firefox, Yandex, UC Browser, Opera, Edge and DuckDuckGo.\n    // iOS Safari and Chrome were checked on iOS 11-18. DuckDuckGo was checked on iOS 17-18 and macOS 14-15.\n    // Desktop Safari versions 12-18 were checked.\n    // The other browsers were checked on iOS 17 and 18; there was no chance to check them on the other OS versions.\n    var w = window;\n    return (\n    // Filters-out Chrome, Yandex, DuckDuckGo (macOS and iOS), Edge\n    isFunctionNative(w.print) &&\n        // Doesn't work in Safari < 15.4\n        String(w.browser) === '[object WebPageNamespace]');\n}\n/**\n * Checks whether the browser is based on Gecko (Firefox engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isGecko() {\n    var _a, _b;\n    var w = window;\n    // Based on research in September 2020\n    return (countTruthy([\n        'buildID' in navigator,\n        'MozAppearance' in ((_b = (_a = document.documentElement) === null || _a === void 0 ? void 0 : _a.style) !== null && _b !== void 0 ? _b : {}),\n        'onmozfullscreenchange' in w,\n        'mozInnerScreenX' in w,\n        'CSSMozDocumentRule' in w,\n        'CanvasCaptureMediaStream' in w,\n    ]) >= 4);\n}\n/**\n * Checks whether the browser is based on Chromium version ≥86 without using user-agent.\n * It doesn't check that the browser is based on Chromium, there is a separate function for this.\n */\nfunction isChromium86OrNewer() {\n    // Checked in Chrome 85 vs Chrome 86 both on desktop and Android. Checked in macOS Chrome 128, Android Chrome 127.\n    var w = window;\n    return (countTruthy([\n        !('MediaSettingsRange' in w),\n        'RTCEncodedAudioFrame' in w,\n        '' + w.Intl === '[object Intl]',\n        '' + w.Reflect === '[object Reflect]',\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on Chromium version ≥122 without using user-agent.\n * It doesn't check that the browser is based on Chromium, there is a separate function for this.\n */\nfunction isChromium122OrNewer() {\n    // Checked in Chrome 121 vs Chrome 122 and 129 both on desktop and Android\n    var w = window;\n    var URLPattern = w.URLPattern;\n    return (countTruthy([\n        'union' in Set.prototype,\n        'Iterator' in w,\n        URLPattern && 'hasRegExpGroups' in URLPattern.prototype,\n        'RGB8' in WebGLRenderingContext.prototype,\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on WebKit version ≥606 (Safari ≥12) without using user-agent.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * @see https://en.wikipedia.org/wiki/Safari_version_history#Release_history Safari-WebKit versions map\n */\nfunction isWebKit606OrNewer() {\n    // Checked in Safari 9–18\n    var w = window;\n    return (countTruthy([\n        'DOMRectList' in w,\n        'RTCPeerConnectionIceEvent' in w,\n        'SVGGeometryElement' in w,\n        'ontransitioncancel' in w,\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on WebKit version ≥616 (Safari ≥17) without using user-agent.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * @see https://developer.apple.com/documentation/safari-release-notes/safari-17-release-notes Safari 17 release notes\n * @see https://tauri.app/v1/references/webview-versions/#webkit-versions-in-safari Safari-WebKit versions map\n */\nfunction isWebKit616OrNewer() {\n    var w = window;\n    var n = navigator;\n    var CSS = w.CSS, HTMLButtonElement = w.HTMLButtonElement;\n    return (countTruthy([\n        !('getStorageUpdates' in n),\n        HTMLButtonElement && 'popover' in HTMLButtonElement.prototype,\n        'CSSCounterStyleRule' in w,\n        CSS.supports('font-size-adjust: ex-height 0.5'),\n        CSS.supports('text-transform: full-width'),\n    ]) >= 4);\n}\n/**\n * Checks whether the device is an iPad.\n * It doesn't check that the engine is WebKit and that the WebKit isn't desktop.\n */\nfunction isIPad() {\n    // Checked on:\n    // Safari on iPadOS (both mobile and desktop modes): 8, 11-18\n    // Chrome on iPadOS (both mobile and desktop modes): 11-18\n    // Safari on iOS (both mobile and desktop modes): 9-18\n    // Chrome on iOS (both mobile and desktop modes): 9-18\n    // Before iOS 13. Safari tampers the value in \"request desktop site\" mode since iOS 13.\n    if (navigator.platform === 'iPad') {\n        return true;\n    }\n    var s = screen;\n    var screenRatio = s.width / s.height;\n    return (countTruthy([\n        // Since iOS 13. Doesn't work in Chrome on iPadOS <15, but works in desktop mode.\n        'MediaSource' in window,\n        // Since iOS 12. Doesn't work in Chrome on iPadOS.\n        !!Element.prototype.webkitRequestFullscreen,\n        // iPhone 4S that runs iOS 9 matches this, but it is not supported\n        // Doesn't work in incognito mode of Safari ≥17 with split screen because of tracking prevention\n        screenRatio > 0.65 && screenRatio < 1.53,\n    ]) >= 2);\n}\n/**\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getFullscreenElement() {\n    var d = document;\n    return d.fullscreenElement || d.msFullscreenElement || d.mozFullScreenElement || d.webkitFullscreenElement || null;\n}\nfunction exitFullscreen() {\n    var d = document;\n    // `call` is required because the function throws an error without a proper \"this\" context\n    return (d.exitFullscreen || d.msExitFullscreen || d.mozCancelFullScreen || d.webkitExitFullscreen).call(d);\n}\n/**\n * Checks whether the device runs on Android without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isAndroid() {\n    var isItChromium = isChromium();\n    var isItGecko = isGecko();\n    var w = window;\n    var n = navigator;\n    var c = 'connection';\n    // Chrome removes all words \"Android\" from `navigator` when desktop version is requested\n    // Firefox keeps \"Android\" in `navigator.appVersion` when desktop version is requested\n    if (isItChromium) {\n        return (countTruthy([\n            !('SharedWorker' in w),\n            // `typechange` is deprecated, but it's still present on Android (tested on Chrome Mobile 117)\n            // Removal proposal https://bugs.chromium.org/p/chromium/issues/detail?id=699892\n            // Note: this expression returns true on ChromeOS, so additional detectors are required to avoid false-positives\n            n[c] && 'ontypechange' in n[c],\n            !('sinkId' in new Audio()),\n        ]) >= 2);\n    }\n    else if (isItGecko) {\n        return countTruthy(['onorientationchange' in w, 'orientation' in w, /android/i.test(n.appVersion)]) >= 2;\n    }\n    else {\n        // Only 2 browser engines are presented on Android.\n        // Actually, there is also Android 4.1 browser, but it's not worth detecting it at the moment.\n        return false;\n    }\n}\n/**\n * Checks whether the browser is Samsung Internet without using user-agent.\n * It doesn't check that the browser is based on Chromium, please use `isChromium` before using this function.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isSamsungInternet() {\n    // Checked in Samsung Internet 21, 25 and 27\n    var n = navigator;\n    var w = window;\n    var audioPrototype = Audio.prototype;\n    var visualViewport = w.visualViewport;\n    return (countTruthy([\n        'srLatency' in audioPrototype,\n        'srChannelCount' in audioPrototype,\n        'devicePosture' in n,\n        visualViewport && 'segments' in visualViewport,\n        'getTextInformation' in Image.prototype, // Not available in Samsung Internet 21\n    ]) >= 3);\n}\n\n/**\n * A deep description: https://fingerprint.com/blog/audio-fingerprinting/\n * Inspired by and based on https://github.com/cozylife/audio-fingerprint\n *\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * Audio signal is noised in private mode of Safari 17, so audio fingerprinting is skipped in Safari 17.\n */\nfunction getAudioFingerprint() {\n    if (doesBrowserPerformAntifingerprinting$1()) {\n        return -4 /* SpecialFingerprint.KnownForAntifingerprinting */;\n    }\n    return getUnstableAudioFingerprint();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableAudioFingerprint() {\n    var w = window;\n    var AudioContext = w.OfflineAudioContext || w.webkitOfflineAudioContext;\n    if (!AudioContext) {\n        return -2 /* SpecialFingerprint.NotSupported */;\n    }\n    // In some browsers, audio context always stays suspended unless the context is started in response to a user action\n    // (e.g. a click or a tap). It prevents audio fingerprint from being taken at an arbitrary moment of time.\n    // Such browsers are old and unpopular, so the audio fingerprinting is just skipped in them.\n    // See a similar case explanation at https://stackoverflow.com/questions/46363048/onaudioprocess-not-called-on-ios11#46534088\n    if (doesBrowserSuspendAudioContext()) {\n        return -1 /* SpecialFingerprint.KnownForSuspending */;\n    }\n    var hashFromIndex = 4500;\n    var hashToIndex = 5000;\n    var context = new AudioContext(1, hashToIndex, 44100);\n    var oscillator = context.createOscillator();\n    oscillator.type = 'triangle';\n    oscillator.frequency.value = 10000;\n    var compressor = context.createDynamicsCompressor();\n    compressor.threshold.value = -50;\n    compressor.knee.value = 40;\n    compressor.ratio.value = 12;\n    compressor.attack.value = 0;\n    compressor.release.value = 0.25;\n    oscillator.connect(compressor);\n    compressor.connect(context.destination);\n    oscillator.start(0);\n    var _a = startRenderingAudio(context), renderPromise = _a[0], finishRendering = _a[1];\n    // Suppresses the console error message in case when the fingerprint fails before requested\n    var fingerprintPromise = suppressUnhandledRejectionWarning(renderPromise.then(function (buffer) { return getHash(buffer.getChannelData(0).subarray(hashFromIndex)); }, function (error) {\n        if (error.name === \"timeout\" /* InnerErrorName.Timeout */ || error.name === \"suspended\" /* InnerErrorName.Suspended */) {\n            return -3 /* SpecialFingerprint.Timeout */;\n        }\n        throw error;\n    }));\n    return function () {\n        finishRendering();\n        return fingerprintPromise;\n    };\n}\n/**\n * Checks if the current browser is known for always suspending audio context\n */\nfunction doesBrowserSuspendAudioContext() {\n    // Mobile Safari 11 and older\n    return isWebKit() && !isDesktopWebKit() && !isWebKit606OrNewer();\n}\n/**\n * Checks if the current browser is known for applying anti-fingerprinting measures in all or some critical modes\n */\nfunction doesBrowserPerformAntifingerprinting$1() {\n    return (\n    // Safari ≥17\n    (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) ||\n        // Samsung Internet ≥26\n        (isChromium() && isSamsungInternet() && isChromium122OrNewer()));\n}\n/**\n * Starts rendering the audio context.\n * When the returned function is called, the render process starts finishing.\n */\nfunction startRenderingAudio(context) {\n    var renderTryMaxCount = 3;\n    var renderRetryDelay = 500;\n    var runningMaxAwaitTime = 500;\n    var runningSufficientTime = 5000;\n    var finalize = function () { return undefined; };\n    var resultPromise = new Promise(function (resolve, reject) {\n        var isFinalized = false;\n        var renderTryCount = 0;\n        var startedRunningAt = 0;\n        context.oncomplete = function (event) { return resolve(event.renderedBuffer); };\n        var startRunningTimeout = function () {\n            setTimeout(function () { return reject(makeInnerError(\"timeout\" /* InnerErrorName.Timeout */)); }, Math.min(runningMaxAwaitTime, startedRunningAt + runningSufficientTime - Date.now()));\n        };\n        var tryRender = function () {\n            try {\n                var renderingPromise = context.startRendering();\n                // `context.startRendering` has two APIs: Promise and callback, we check that it's really a promise just in case\n                if (isPromise(renderingPromise)) {\n                    // Suppresses all unhandled rejections in case of scheduled redundant retries after successful rendering\n                    suppressUnhandledRejectionWarning(renderingPromise);\n                }\n                switch (context.state) {\n                    case 'running':\n                        startedRunningAt = Date.now();\n                        if (isFinalized) {\n                            startRunningTimeout();\n                        }\n                        break;\n                    // Sometimes the audio context doesn't start after calling `startRendering` (in addition to the cases where\n                    // audio context doesn't start at all). A known case is starting an audio context when the browser tab is in\n                    // background on iPhone. Retries usually help in this case.\n                    case 'suspended':\n                        // The audio context can reject starting until the tab is in foreground. Long fingerprint duration\n                        // in background isn't a problem, therefore the retry attempts don't count in background. It can lead to\n                        // a situation when a fingerprint takes very long time and finishes successfully. FYI, the audio context\n                        // can be suspended when `document.hidden === false` and start running after a retry.\n                        if (!document.hidden) {\n                            renderTryCount++;\n                        }\n                        if (isFinalized && renderTryCount >= renderTryMaxCount) {\n                            reject(makeInnerError(\"suspended\" /* InnerErrorName.Suspended */));\n                        }\n                        else {\n                            setTimeout(tryRender, renderRetryDelay);\n                        }\n                        break;\n                }\n            }\n            catch (error) {\n                reject(error);\n            }\n        };\n        tryRender();\n        finalize = function () {\n            if (!isFinalized) {\n                isFinalized = true;\n                if (startedRunningAt > 0) {\n                    startRunningTimeout();\n                }\n            }\n        };\n    });\n    return [resultPromise, finalize];\n}\nfunction getHash(signal) {\n    var hash = 0;\n    for (var i = 0; i < signal.length; ++i) {\n        hash += Math.abs(signal[i]);\n    }\n    return hash;\n}\nfunction makeInnerError(name) {\n    var error = new Error(name);\n    error.name = name;\n    return error;\n}\n\n/**\n * Creates and keeps an invisible iframe while the given function runs.\n * The given function is called when the iframe is loaded and has a body.\n * The iframe allows to measure DOM sizes inside itself.\n *\n * Notice: passing an initial HTML code doesn't work in IE.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction withIframe(action, initialHtml, domPollInterval) {\n    var _a, _b, _c;\n    if (domPollInterval === void 0) { domPollInterval = 50; }\n    return __awaiter(this, void 0, void 0, function () {\n        var d, iframe;\n        return __generator(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    d = document;\n                    _d.label = 1;\n                case 1:\n                    if (!!d.body) return [3 /*break*/, 3];\n                    return [4 /*yield*/, wait(domPollInterval)];\n                case 2:\n                    _d.sent();\n                    return [3 /*break*/, 1];\n                case 3:\n                    iframe = d.createElement('iframe');\n                    _d.label = 4;\n                case 4:\n                    _d.trys.push([4, , 10, 11]);\n                    return [4 /*yield*/, new Promise(function (_resolve, _reject) {\n                            var isComplete = false;\n                            var resolve = function () {\n                                isComplete = true;\n                                _resolve();\n                            };\n                            var reject = function (error) {\n                                isComplete = true;\n                                _reject(error);\n                            };\n                            iframe.onload = resolve;\n                            iframe.onerror = reject;\n                            var style = iframe.style;\n                            style.setProperty('display', 'block', 'important'); // Required for browsers to calculate the layout\n                            style.position = 'absolute';\n                            style.top = '0';\n                            style.left = '0';\n                            style.visibility = 'hidden';\n                            if (initialHtml && 'srcdoc' in iframe) {\n                                iframe.srcdoc = initialHtml;\n                            }\n                            else {\n                                iframe.src = 'about:blank';\n                            }\n                            d.body.appendChild(iframe);\n                            // WebKit in WeChat doesn't fire the iframe's `onload` for some reason.\n                            // This code checks for the loading state manually.\n                            // See https://github.com/fingerprintjs/fingerprintjs/issues/645\n                            var checkReadyState = function () {\n                                var _a, _b;\n                                // The ready state may never become 'complete' in Firefox despite the 'load' event being fired.\n                                // So an infinite setTimeout loop can happen without this check.\n                                // See https://github.com/fingerprintjs/fingerprintjs/pull/716#issuecomment-986898796\n                                if (isComplete) {\n                                    return;\n                                }\n                                // Make sure iframe.contentWindow and iframe.contentWindow.document are both loaded\n                                // The contentWindow.document can miss in JSDOM (https://github.com/jsdom/jsdom).\n                                if (((_b = (_a = iframe.contentWindow) === null || _a === void 0 ? void 0 : _a.document) === null || _b === void 0 ? void 0 : _b.readyState) === 'complete') {\n                                    resolve();\n                                }\n                                else {\n                                    setTimeout(checkReadyState, 10);\n                                }\n                            };\n                            checkReadyState();\n                        })];\n                case 5:\n                    _d.sent();\n                    _d.label = 6;\n                case 6:\n                    if (!!((_b = (_a = iframe.contentWindow) === null || _a === void 0 ? void 0 : _a.document) === null || _b === void 0 ? void 0 : _b.body)) return [3 /*break*/, 8];\n                    return [4 /*yield*/, wait(domPollInterval)];\n                case 7:\n                    _d.sent();\n                    return [3 /*break*/, 6];\n                case 8: return [4 /*yield*/, action(iframe, iframe.contentWindow)];\n                case 9: return [2 /*return*/, _d.sent()];\n                case 10:\n                    (_c = iframe.parentNode) === null || _c === void 0 ? void 0 : _c.removeChild(iframe);\n                    return [7 /*endfinally*/];\n                case 11: return [2 /*return*/];\n            }\n        });\n    });\n}\n/**\n * Creates a DOM element that matches the given selector.\n * Only single element selector are supported (without operators like space, +, >, etc).\n */\nfunction selectorToElement(selector) {\n    var _a = parseSimpleCssSelector(selector), tag = _a[0], attributes = _a[1];\n    var element = document.createElement(tag !== null && tag !== void 0 ? tag : 'div');\n    for (var _i = 0, _b = Object.keys(attributes); _i < _b.length; _i++) {\n        var name_1 = _b[_i];\n        var value = attributes[name_1].join(' ');\n        // Changing the `style` attribute can cause a CSP error, therefore we change the `style.cssText` property.\n        // https://github.com/fingerprintjs/fingerprintjs/issues/733\n        if (name_1 === 'style') {\n            addStyleString(element.style, value);\n        }\n        else {\n            element.setAttribute(name_1, value);\n        }\n    }\n    return element;\n}\n/**\n * Adds CSS styles from a string in such a way that doesn't trigger a CSP warning (unsafe-inline or unsafe-eval)\n */\nfunction addStyleString(style, source) {\n    // We don't use `style.cssText` because browsers must block it when no `unsafe-eval` CSP is presented: https://csplite.com/csp145/#w3c_note\n    // Even though the browsers ignore this standard, we don't use `cssText` just in case.\n    for (var _i = 0, _a = source.split(';'); _i < _a.length; _i++) {\n        var property = _a[_i];\n        var match = /^\\s*([\\w-]+)\\s*:\\s*(.+?)(\\s*!([\\w-]+))?\\s*$/.exec(property);\n        if (match) {\n            var name_2 = match[1], value = match[2], priority = match[4];\n            style.setProperty(name_2, value, priority || ''); // The last argument can't be undefined in IE11\n        }\n    }\n}\n/**\n * Returns true if the code runs in an iframe, and any parent page's origin doesn't match the current origin\n */\nfunction isAnyParentCrossOrigin() {\n    var currentWindow = window;\n    for (;;) {\n        var parentWindow = currentWindow.parent;\n        if (!parentWindow || parentWindow === currentWindow) {\n            return false; // The top page is reached\n        }\n        try {\n            if (parentWindow.location.origin !== currentWindow.location.origin) {\n                return true;\n            }\n        }\n        catch (error) {\n            // The error is thrown when `origin` is accessed on `parentWindow.location` when the parent is cross-origin\n            if (error instanceof Error && error.name === 'SecurityError') {\n                return true;\n            }\n            throw error;\n        }\n        currentWindow = parentWindow;\n    }\n}\n\n// We use m or w because these two characters take up the maximum width.\n// And we use a LLi so that the same matching fonts can get separated.\nvar testString = 'mmMwWLliI0O&1';\n// We test using 48px font size, we may use any size. I guess larger the better.\nvar textSize = '48px';\n// A font will be compared against all the three default fonts.\n// And if for any default fonts it doesn't match, then that font is available.\nvar baseFonts = ['monospace', 'sans-serif', 'serif'];\nvar fontList = [\n    // This is android-specific font from \"Roboto\" family\n    'sans-serif-thin',\n    'ARNO PRO',\n    'Agency FB',\n    'Arabic Typesetting',\n    'Arial Unicode MS',\n    'AvantGarde Bk BT',\n    'BankGothic Md BT',\n    'Batang',\n    'Bitstream Vera Sans Mono',\n    'Calibri',\n    'Century',\n    'Century Gothic',\n    'Clarendon',\n    'EUROSTILE',\n    'Franklin Gothic',\n    'Futura Bk BT',\n    'Futura Md BT',\n    'GOTHAM',\n    'Gill Sans',\n    'HELV',\n    'Haettenschweiler',\n    'Helvetica Neue',\n    'Humanst521 BT',\n    'Leelawadee',\n    'Letter Gothic',\n    'Levenim MT',\n    'Lucida Bright',\n    'Lucida Sans',\n    'Menlo',\n    'MS Mincho',\n    'MS Outlook',\n    'MS Reference Specialty',\n    'MS UI Gothic',\n    'MT Extra',\n    'MYRIAD PRO',\n    'Marlett',\n    'Meiryo UI',\n    'Microsoft Uighur',\n    'Minion Pro',\n    'Monotype Corsiva',\n    'PMingLiU',\n    'Pristina',\n    'SCRIPTINA',\n    'Segoe UI Light',\n    'Serifa',\n    'SimHei',\n    'Small Fonts',\n    'Staccato222 BT',\n    'TRAJAN PRO',\n    'Univers CE 55 Medium',\n    'Vrinda',\n    'ZWAdobeF',\n];\n// kudos to http://www.lalit.org/lab/javascript-css-font-detect/\nfunction getFonts() {\n    var _this = this;\n    // Running the script in an iframe makes it not affect the page look and not be affected by the page CSS. See:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/592\n    // https://github.com/fingerprintjs/fingerprintjs/issues/628\n    return withIframe(function (_, _a) {\n        var document = _a.document;\n        return __awaiter(_this, void 0, void 0, function () {\n            var holder, spansContainer, defaultWidth, defaultHeight, createSpan, createSpanWithFonts, initializeBaseFontsSpans, initializeFontsSpans, isFontAvailable, baseFontsSpans, fontsSpans, index;\n            return __generator(this, function (_b) {\n                holder = document.body;\n                holder.style.fontSize = textSize;\n                spansContainer = document.createElement('div');\n                spansContainer.style.setProperty('visibility', 'hidden', 'important');\n                defaultWidth = {};\n                defaultHeight = {};\n                createSpan = function (fontFamily) {\n                    var span = document.createElement('span');\n                    var style = span.style;\n                    style.position = 'absolute';\n                    style.top = '0';\n                    style.left = '0';\n                    style.fontFamily = fontFamily;\n                    span.textContent = testString;\n                    spansContainer.appendChild(span);\n                    return span;\n                };\n                createSpanWithFonts = function (fontToDetect, baseFont) {\n                    return createSpan(\"'\".concat(fontToDetect, \"',\").concat(baseFont));\n                };\n                initializeBaseFontsSpans = function () {\n                    return baseFonts.map(createSpan);\n                };\n                initializeFontsSpans = function () {\n                    // Stores {fontName : [spans for that font]}\n                    var spans = {};\n                    var _loop_1 = function (font) {\n                        spans[font] = baseFonts.map(function (baseFont) { return createSpanWithFonts(font, baseFont); });\n                    };\n                    for (var _i = 0, fontList_1 = fontList; _i < fontList_1.length; _i++) {\n                        var font = fontList_1[_i];\n                        _loop_1(font);\n                    }\n                    return spans;\n                };\n                isFontAvailable = function (fontSpans) {\n                    return baseFonts.some(function (baseFont, baseFontIndex) {\n                        return fontSpans[baseFontIndex].offsetWidth !== defaultWidth[baseFont] ||\n                            fontSpans[baseFontIndex].offsetHeight !== defaultHeight[baseFont];\n                    });\n                };\n                baseFontsSpans = initializeBaseFontsSpans();\n                fontsSpans = initializeFontsSpans();\n                // add all the spans to the DOM\n                holder.appendChild(spansContainer);\n                // get the default width for the three base fonts\n                for (index = 0; index < baseFonts.length; index++) {\n                    defaultWidth[baseFonts[index]] = baseFontsSpans[index].offsetWidth; // width for the default font\n                    defaultHeight[baseFonts[index]] = baseFontsSpans[index].offsetHeight; // height for the default font\n                }\n                // check available fonts\n                return [2 /*return*/, fontList.filter(function (font) { return isFontAvailable(fontsSpans[font]); })];\n            });\n        });\n    });\n}\n\nfunction getPlugins() {\n    var rawPlugins = navigator.plugins;\n    if (!rawPlugins) {\n        return undefined;\n    }\n    var plugins = [];\n    // Safari 10 doesn't support iterating navigator.plugins with for...of\n    for (var i = 0; i < rawPlugins.length; ++i) {\n        var plugin = rawPlugins[i];\n        if (!plugin) {\n            continue;\n        }\n        var mimeTypes = [];\n        for (var j = 0; j < plugin.length; ++j) {\n            var mimeType = plugin[j];\n            mimeTypes.push({\n                type: mimeType.type,\n                suffixes: mimeType.suffixes,\n            });\n        }\n        plugins.push({\n            name: plugin.name,\n            description: plugin.description,\n            mimeTypes: mimeTypes,\n        });\n    }\n    return plugins;\n}\n\n/**\n * @see https://www.browserleaks.com/canvas#how-does-it-work\n *\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * Canvas image is noised in private mode of Safari 17, so image rendering is skipped in Safari 17.\n */\nfunction getCanvasFingerprint() {\n    return getUnstableCanvasFingerprint(doesBrowserPerformAntifingerprinting());\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableCanvasFingerprint(skipImages) {\n    var _a;\n    var winding = false;\n    var geometry;\n    var text;\n    var _b = makeCanvasContext(), canvas = _b[0], context = _b[1];\n    if (!isSupported(canvas, context)) {\n        geometry = text = \"unsupported\" /* ImageStatus.Unsupported */;\n    }\n    else {\n        winding = doesSupportWinding(context);\n        if (skipImages) {\n            geometry = text = \"skipped\" /* ImageStatus.Skipped */;\n        }\n        else {\n            _a = renderImages(canvas, context), geometry = _a[0], text = _a[1];\n        }\n    }\n    return { winding: winding, geometry: geometry, text: text };\n}\nfunction makeCanvasContext() {\n    var canvas = document.createElement('canvas');\n    canvas.width = 1;\n    canvas.height = 1;\n    return [canvas, canvas.getContext('2d')];\n}\nfunction isSupported(canvas, context) {\n    return !!(context && canvas.toDataURL);\n}\nfunction doesSupportWinding(context) {\n    // https://web.archive.org/web/20170825024655/http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // https://github.com/Modernizr/Modernizr/blob/master/feature-detects/canvas/winding.js\n    context.rect(0, 0, 10, 10);\n    context.rect(2, 2, 6, 6);\n    return !context.isPointInPath(5, 5, 'evenodd');\n}\nfunction renderImages(canvas, context) {\n    renderTextImage(canvas, context);\n    var textImage1 = canvasToString(canvas);\n    var textImage2 = canvasToString(canvas); // It's slightly faster to double-encode the text image\n    // Some browsers add a noise to the canvas: https://github.com/fingerprintjs/fingerprintjs/issues/791\n    // The canvas is excluded from the fingerprint in this case\n    if (textImage1 !== textImage2) {\n        return [\"unstable\" /* ImageStatus.Unstable */, \"unstable\" /* ImageStatus.Unstable */];\n    }\n    // Text is unstable:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/583\n    // https://github.com/fingerprintjs/fingerprintjs/issues/103\n    // Therefore it's extracted into a separate image.\n    renderGeometryImage(canvas, context);\n    var geometryImage = canvasToString(canvas);\n    return [geometryImage, textImage1];\n}\nfunction renderTextImage(canvas, context) {\n    // Resizing the canvas cleans it\n    canvas.width = 240;\n    canvas.height = 60;\n    context.textBaseline = 'alphabetic';\n    context.fillStyle = '#f60';\n    context.fillRect(100, 1, 62, 20);\n    context.fillStyle = '#069';\n    // It's important to use explicit built-in fonts in order to exclude the affect of font preferences\n    // (there is a separate entropy source for them).\n    context.font = '11pt \"Times New Roman\"';\n    // The choice of emojis has a gigantic impact on rendering performance (especially in FF).\n    // Some newer emojis cause it to slow down 50-200 times.\n    // There must be no text to the right of the emoji, see https://github.com/fingerprintjs/fingerprintjs/issues/574\n    // A bare emoji shouldn't be used because the canvas will change depending on the script encoding:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/66\n    // Escape sequence shouldn't be used too because Terser will turn it into a bare unicode.\n    var printedText = \"Cwm fjordbank gly \".concat(String.fromCharCode(55357, 56835) /* 😃 */);\n    context.fillText(printedText, 2, 15);\n    context.fillStyle = 'rgba(102, 204, 0, 0.2)';\n    context.font = '18pt Arial';\n    context.fillText(printedText, 4, 45);\n}\nfunction renderGeometryImage(canvas, context) {\n    // Resizing the canvas cleans it\n    canvas.width = 122;\n    canvas.height = 110;\n    // Canvas blending\n    // https://web.archive.org/web/**************/http://blogs.adobe.com/webplatform/2013/01/28/blending-features-in-canvas/\n    // http://jsfiddle.net/NDYV8/16/\n    context.globalCompositeOperation = 'multiply';\n    for (var _i = 0, _a = [\n        ['#f2f', 40, 40],\n        ['#2ff', 80, 40],\n        ['#ff2', 60, 80],\n    ]; _i < _a.length; _i++) {\n        var _b = _a[_i], color = _b[0], x = _b[1], y = _b[2];\n        context.fillStyle = color;\n        context.beginPath();\n        context.arc(x, y, 40, 0, Math.PI * 2, true);\n        context.closePath();\n        context.fill();\n    }\n    // Canvas winding\n    // https://web.archive.org/web/20130913061632/http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // http://jsfiddle.net/NDYV8/19/\n    context.fillStyle = '#f9c';\n    context.arc(60, 60, 60, 0, Math.PI * 2, true);\n    context.arc(60, 60, 20, 0, Math.PI * 2, true);\n    context.fill('evenodd');\n}\nfunction canvasToString(canvas) {\n    return canvas.toDataURL();\n}\n/**\n * Checks if the current browser is known for applying anti-fingerprinting measures in all or some critical modes\n */\nfunction doesBrowserPerformAntifingerprinting() {\n    // Safari 17\n    return isWebKit() && isWebKit616OrNewer() && isSafariWebKit();\n}\n\n/**\n * This is a crude and primitive touch screen detection. It's not possible to currently reliably detect the availability\n * of a touch screen with a JS, without actually subscribing to a touch event.\n *\n * @see http://www.stucox.com/blog/you-cant-detect-a-touchscreen/\n * @see https://github.com/Modernizr/Modernizr/issues/548\n */\nfunction getTouchSupport() {\n    var n = navigator;\n    var maxTouchPoints = 0;\n    var touchEvent;\n    if (n.maxTouchPoints !== undefined) {\n        maxTouchPoints = toInt(n.maxTouchPoints);\n    }\n    else if (n.msMaxTouchPoints !== undefined) {\n        maxTouchPoints = n.msMaxTouchPoints;\n    }\n    try {\n        document.createEvent('TouchEvent');\n        touchEvent = true;\n    }\n    catch (_a) {\n        touchEvent = false;\n    }\n    var touchStart = 'ontouchstart' in window;\n    return {\n        maxTouchPoints: maxTouchPoints,\n        touchEvent: touchEvent,\n        touchStart: touchStart,\n    };\n}\n\nfunction getOsCpu() {\n    return navigator.oscpu;\n}\n\nfunction getLanguages() {\n    var n = navigator;\n    var result = [];\n    var language = n.language || n.userLanguage || n.browserLanguage || n.systemLanguage;\n    if (language !== undefined) {\n        result.push([language]);\n    }\n    if (Array.isArray(n.languages)) {\n        // Starting from Chromium 86, there is only a single value in `navigator.language` in Incognito mode:\n        // the value of `navigator.language`. Therefore the value is ignored in this browser.\n        if (!(isChromium() && isChromium86OrNewer())) {\n            result.push(n.languages);\n        }\n    }\n    else if (typeof n.languages === 'string') {\n        var languages = n.languages;\n        if (languages) {\n            result.push(languages.split(','));\n        }\n    }\n    return result;\n}\n\nfunction getColorDepth() {\n    return window.screen.colorDepth;\n}\n\nfunction getDeviceMemory() {\n    // `navigator.deviceMemory` is a string containing a number in some unidentified cases\n    return replaceNaN(toFloat(navigator.deviceMemory), undefined);\n}\n\n/**\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * The window resolution is always the document size in private mode of Safari 17,\n * so the window resolution is not used in Safari 17.\n */\nfunction getScreenResolution() {\n    if (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) {\n        return undefined;\n    }\n    return getUnstableScreenResolution();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableScreenResolution() {\n    var s = screen;\n    // Some browsers return screen resolution as strings, e.g. \"1200\", instead of a number, e.g. 1200.\n    // I suspect it's done by certain plugins that randomize browser properties to prevent fingerprinting.\n    // Some browsers even return  screen resolution as not numbers.\n    var parseDimension = function (value) { return replaceNaN(toInt(value), null); };\n    var dimensions = [parseDimension(s.width), parseDimension(s.height)];\n    dimensions.sort().reverse();\n    return dimensions;\n}\n\nvar screenFrameCheckInterval = 2500;\nvar roundingPrecision = 10;\n// The type is readonly to protect from unwanted mutations\nvar screenFrameBackup;\nvar screenFrameSizeTimeoutId;\n/**\n * Starts watching the screen frame size. When a non-zero size appears, the size is saved and the watch is stopped.\n * Later, when `getScreenFrame` runs, it will return the saved non-zero size if the current size is null.\n *\n * This trick is required to mitigate the fact that the screen frame turns null in some cases.\n * See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n */\nfunction watchScreenFrame() {\n    if (screenFrameSizeTimeoutId !== undefined) {\n        return;\n    }\n    var checkScreenFrame = function () {\n        var frameSize = getCurrentScreenFrame();\n        if (isFrameSizeNull(frameSize)) {\n            screenFrameSizeTimeoutId = setTimeout(checkScreenFrame, screenFrameCheckInterval);\n        }\n        else {\n            screenFrameBackup = frameSize;\n            screenFrameSizeTimeoutId = undefined;\n        }\n    };\n    checkScreenFrame();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableScreenFrame() {\n    var _this = this;\n    watchScreenFrame();\n    return function () { return __awaiter(_this, void 0, void 0, function () {\n        var frameSize;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    frameSize = getCurrentScreenFrame();\n                    if (!isFrameSizeNull(frameSize)) return [3 /*break*/, 2];\n                    if (screenFrameBackup) {\n                        return [2 /*return*/, __spreadArray([], screenFrameBackup, true)];\n                    }\n                    if (!getFullscreenElement()) return [3 /*break*/, 2];\n                    // Some browsers set the screen frame to zero when programmatic fullscreen is on.\n                    // There is a chance of getting a non-zero frame after exiting the fullscreen.\n                    // See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n                    return [4 /*yield*/, exitFullscreen()];\n                case 1:\n                    // Some browsers set the screen frame to zero when programmatic fullscreen is on.\n                    // There is a chance of getting a non-zero frame after exiting the fullscreen.\n                    // See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n                    _a.sent();\n                    frameSize = getCurrentScreenFrame();\n                    _a.label = 2;\n                case 2:\n                    if (!isFrameSizeNull(frameSize)) {\n                        screenFrameBackup = frameSize;\n                    }\n                    return [2 /*return*/, frameSize];\n            }\n        });\n    }); };\n}\n/**\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n *\n * Sometimes the available screen resolution changes a bit, e.g. 1900x1440 → 1900x1439. A possible reason: macOS Dock\n * shrinks to fit more icons when there is too little space. The rounding is used to mitigate the difference.\n *\n * The frame width is always 0 in private mode of Safari 17, so the frame is not used in Safari 17.\n */\nfunction getScreenFrame() {\n    var _this = this;\n    if (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) {\n        return function () { return Promise.resolve(undefined); };\n    }\n    var screenFrameGetter = getUnstableScreenFrame();\n    return function () { return __awaiter(_this, void 0, void 0, function () {\n        var frameSize, processSize;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0: return [4 /*yield*/, screenFrameGetter()];\n                case 1:\n                    frameSize = _a.sent();\n                    processSize = function (sideSize) { return (sideSize === null ? null : round(sideSize, roundingPrecision)); };\n                    // It might look like I don't know about `for` and `map`.\n                    // In fact, such code is used to avoid TypeScript issues without using `as`.\n                    return [2 /*return*/, [processSize(frameSize[0]), processSize(frameSize[1]), processSize(frameSize[2]), processSize(frameSize[3])]];\n            }\n        });\n    }); };\n}\nfunction getCurrentScreenFrame() {\n    var s = screen;\n    // Some browsers return screen resolution as strings, e.g. \"1200\", instead of a number, e.g. 1200.\n    // I suspect it's done by certain plugins that randomize browser properties to prevent fingerprinting.\n    //\n    // Some browsers (IE, Edge ≤18) don't provide `screen.availLeft` and `screen.availTop`. The property values are\n    // replaced with 0 in such cases to not lose the entropy from `screen.availWidth` and `screen.availHeight`.\n    return [\n        replaceNaN(toFloat(s.availTop), null),\n        replaceNaN(toFloat(s.width) - toFloat(s.availWidth) - replaceNaN(toFloat(s.availLeft), 0), null),\n        replaceNaN(toFloat(s.height) - toFloat(s.availHeight) - replaceNaN(toFloat(s.availTop), 0), null),\n        replaceNaN(toFloat(s.availLeft), null),\n    ];\n}\nfunction isFrameSizeNull(frameSize) {\n    for (var i = 0; i < 4; ++i) {\n        if (frameSize[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction getHardwareConcurrency() {\n    // sometimes hardware concurrency is a string\n    return replaceNaN(toInt(navigator.hardwareConcurrency), undefined);\n}\n\nfunction getTimezone() {\n    var _a;\n    var DateTimeFormat = (_a = window.Intl) === null || _a === void 0 ? void 0 : _a.DateTimeFormat;\n    if (DateTimeFormat) {\n        var timezone = new DateTimeFormat().resolvedOptions().timeZone;\n        if (timezone) {\n            return timezone;\n        }\n    }\n    // For browsers that don't support timezone names\n    // The minus is intentional because the JS offset is opposite to the real offset\n    var offset = -getTimezoneOffset();\n    return \"UTC\".concat(offset >= 0 ? '+' : '').concat(offset);\n}\nfunction getTimezoneOffset() {\n    var currentYear = new Date().getFullYear();\n    // The timezone offset may change over time due to daylight saving time (DST) shifts.\n    // The non-DST timezone offset is used as the result timezone offset.\n    // Since the DST season differs in the northern and the southern hemispheres,\n    // both January and July timezones offsets are considered.\n    return Math.max(\n    // `getTimezoneOffset` returns a number as a string in some unidentified cases\n    toFloat(new Date(currentYear, 0, 1).getTimezoneOffset()), toFloat(new Date(currentYear, 6, 1).getTimezoneOffset()));\n}\n\nfunction getSessionStorage() {\n    try {\n        return !!window.sessionStorage;\n    }\n    catch (error) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\n// https://bugzilla.mozilla.org/show_bug.cgi?id=781447\nfunction getLocalStorage() {\n    try {\n        return !!window.localStorage;\n    }\n    catch (e) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\nfunction getIndexedDB() {\n    // IE and Edge don't allow accessing indexedDB in private mode, therefore IE and Edge will have different\n    // visitor identifier in normal and private modes.\n    if (isTrident() || isEdgeHTML()) {\n        return undefined;\n    }\n    try {\n        return !!window.indexedDB;\n    }\n    catch (e) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\nfunction getOpenDatabase() {\n    return !!window.openDatabase;\n}\n\nfunction getCpuClass() {\n    return navigator.cpuClass;\n}\n\nfunction getPlatform() {\n    // Android Chrome 86 and 87 and Android Firefox 80 and 84 don't mock the platform value when desktop mode is requested\n    var platform = navigator.platform;\n    // iOS mocks the platform value when desktop version is requested: https://github.com/fingerprintjs/fingerprintjs/issues/514\n    // iPad uses desktop mode by default since iOS 13\n    // The value is 'MacIntel' on M1 Macs\n    // The value is 'iPhone' on iPod Touch\n    if (platform === 'MacIntel') {\n        if (isWebKit() && !isDesktopWebKit()) {\n            return isIPad() ? 'iPad' : 'iPhone';\n        }\n    }\n    return platform;\n}\n\nfunction getVendor() {\n    return navigator.vendor || '';\n}\n\n/**\n * Checks for browser-specific (not engine specific) global variables to tell browsers with the same engine apart.\n * Only somewhat popular browsers are considered.\n */\nfunction getVendorFlavors() {\n    var flavors = [];\n    for (var _i = 0, _a = [\n        // Blink and some browsers on iOS\n        'chrome',\n        // Safari on macOS\n        'safari',\n        // Chrome on iOS (checked in 85 on 13 and 87 on 14)\n        '__crWeb',\n        '__gCrWeb',\n        // Yandex Browser on iOS, macOS and Android (checked in 21.2 on iOS 14, macOS and Android)\n        'yandex',\n        // Yandex Browser on iOS (checked in 21.2 on 14)\n        '__yb',\n        '__ybro',\n        // Firefox on iOS (checked in 32 on 14)\n        '__firefox__',\n        // Edge on iOS (checked in 46 on 14)\n        '__edgeTrackingPreventionStatistics',\n        'webkit',\n        // Opera Touch on iOS (checked in 2.6 on 14)\n        'oprt',\n        // Samsung Internet on Android (checked in 11.1)\n        'samsungAr',\n        // UC Browser on Android (checked in 12.10 and 13.0)\n        'ucweb',\n        'UCShellJava',\n        // Puffin on Android (checked in 9.0)\n        'puffinDevice',\n        // UC on iOS and Opera on Android have no specific global variables\n        // Edge for Android isn't checked\n    ]; _i < _a.length; _i++) {\n        var key = _a[_i];\n        var value = window[key];\n        if (value && typeof value === 'object') {\n            flavors.push(key);\n        }\n    }\n    return flavors.sort();\n}\n\n/**\n * navigator.cookieEnabled cannot detect custom or nuanced cookie blocking configurations. For example, when blocking\n * cookies via the Advanced Privacy Settings in IE9, it always returns true. And there have been issues in the past with\n * site-specific exceptions. Don't rely on it.\n *\n * @see https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cookies.js Taken from here\n */\nfunction areCookiesEnabled() {\n    var d = document;\n    // Taken from here: https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cookies.js\n    // navigator.cookieEnabled cannot detect custom or nuanced cookie blocking configurations. For example, when blocking\n    // cookies via the Advanced Privacy Settings in IE9, it always returns true. And there have been issues in the past\n    // with site-specific exceptions. Don't rely on it.\n    // try..catch because some in situations `document.cookie` is exposed but throws a\n    // SecurityError if you try to access it; e.g. documents created from data URIs\n    // or in sandboxed iframes (depending on flags/context)\n    try {\n        // Create cookie\n        d.cookie = 'cookietest=1; SameSite=Strict;';\n        var result = d.cookie.indexOf('cookietest=') !== -1;\n        // Delete cookie\n        d.cookie = 'cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT';\n        return result;\n    }\n    catch (e) {\n        return false;\n    }\n}\n\n/**\n * Only single element selector are supported (no operators like space, +, >, etc).\n * `embed` and `position: fixed;` will be considered as blocked anyway because it always has no offsetParent.\n * Avoid `iframe` and anything with `[src=]` because they produce excess HTTP requests.\n *\n * The \"inappropriate\" selectors are obfuscated. See https://github.com/fingerprintjs/fingerprintjs/issues/734.\n * A function is used instead of a plain object to help tree-shaking.\n *\n * The function code is generated automatically. See docs/content_blockers.md to learn how to make the list.\n */\nfunction getFilters() {\n    var fromB64 = atob; // Just for better minification\n    return {\n        abpIndo: [\n            '#Iklan-Melayang',\n            '#Kolom-Iklan-728',\n            '#SidebarIklan-wrapper',\n            '[title=\"ALIENBOLA\" i]',\n            fromB64('I0JveC1CYW5uZXItYWRz'),\n        ],\n        abpvn: ['.quangcao', '#mobileCatfish', fromB64('LmNsb3NlLWFkcw=='), '[id^=\"bn_bottom_fixed_\"]', '#pmadv'],\n        adBlockFinland: [\n            '.mainostila',\n            fromB64('LnNwb25zb3JpdA=='),\n            '.ylamainos',\n            fromB64('YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd'),\n        ],\n        adBlockPersian: [\n            '#navbar_notice_50',\n            '.kadr',\n            'TABLE[width=\"140px\"]',\n            '#divAgahi',\n            fromB64('YVtocmVmXj0iaHR0cDovL2cxLnYuZndtcm0ubmV0L2FkLyJd'),\n        ],\n        adBlockWarningRemoval: [\n            '#adblock-honeypot',\n            '.adblocker-root',\n            '.wp_adblock_detect',\n            fromB64('LmhlYWRlci1ibG9ja2VkLWFk'),\n            fromB64('I2FkX2Jsb2NrZXI='),\n        ],\n        adGuardAnnoyances: [\n            '.hs-sosyal',\n            '#cookieconsentdiv',\n            'div[class^=\"app_gdpr\"]',\n            '.as-oil',\n            '[data-cypress=\"soft-push-notification-modal\"]',\n        ],\n        adGuardBase: [\n            '.BetterJsPopOverlay',\n            fromB64('I2FkXzMwMFgyNTA='),\n            fromB64('I2Jhbm5lcmZsb2F0MjI='),\n            fromB64('I2NhbXBhaWduLWJhbm5lcg=='),\n            fromB64('I0FkLUNvbnRlbnQ='),\n        ],\n        adGuardChinese: [\n            fromB64('LlppX2FkX2FfSA=='),\n            fromB64('YVtocmVmKj0iLmh0aGJldDM0LmNvbSJd'),\n            '#widget-quan',\n            fromB64('YVtocmVmKj0iLzg0OTkyMDIwLnh5eiJd'),\n            fromB64('YVtocmVmKj0iLjE5NTZobC5jb20vIl0='),\n        ],\n        adGuardFrench: [\n            '#pavePub',\n            fromB64('LmFkLWRlc2t0b3AtcmVjdGFuZ2xl'),\n            '.mobile_adhesion',\n            '.widgetadv',\n            fromB64('LmFkc19iYW4='),\n        ],\n        adGuardGerman: ['aside[data-portal-id=\"leaderboard\"]'],\n        adGuardJapanese: [\n            '#kauli_yad_1',\n            fromB64('YVtocmVmXj0iaHR0cDovL2FkMi50cmFmZmljZ2F0ZS5uZXQvIl0='),\n            fromB64('Ll9wb3BJbl9pbmZpbml0ZV9hZA=='),\n            fromB64('LmFkZ29vZ2xl'),\n            fromB64('Ll9faXNib29zdFJldHVybkFk'),\n        ],\n        adGuardMobile: [\n            fromB64('YW1wLWF1dG8tYWRz'),\n            fromB64('LmFtcF9hZA=='),\n            'amp-embed[type=\"24smi\"]',\n            '#mgid_iframe1',\n            fromB64('I2FkX2ludmlld19hcmVh'),\n        ],\n        adGuardRussian: [\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZC5sZXRtZWFkcy5jb20vIl0='),\n            fromB64('LnJlY2xhbWE='),\n            'div[id^=\"smi2adblock\"]',\n            fromB64('ZGl2W2lkXj0iQWRGb3hfYmFubmVyXyJd'),\n            '#psyduckpockeball',\n        ],\n        adGuardSocial: [\n            fromB64('YVtocmVmXj0iLy93d3cuc3R1bWJsZXVwb24uY29tL3N1Ym1pdD91cmw9Il0='),\n            fromB64('YVtocmVmXj0iLy90ZWxlZ3JhbS5tZS9zaGFyZS91cmw/Il0='),\n            '.etsy-tweet',\n            '#inlineShare',\n            '.popup-social',\n        ],\n        adGuardSpanishPortuguese: ['#barraPublicidade', '#Publicidade', '#publiEspecial', '#queTooltip', '.cnt-publi'],\n        adGuardTrackingProtection: [\n            '#qoo-counter',\n            fromB64('YVtocmVmXj0iaHR0cDovL2NsaWNrLmhvdGxvZy5ydS8iXQ=='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2hpdGNvdW50ZXIucnUvdG9wL3N0YXQucGhwIl0='),\n            fromB64('YVtocmVmXj0iaHR0cDovL3RvcC5tYWlsLnJ1L2p1bXAiXQ=='),\n            '#top100counter',\n        ],\n        adGuardTurkish: [\n            '#backkapat',\n            fromB64('I3Jla2xhbWk='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2Fkc2Vydi5vbnRlay5jb20udHIvIl0='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2l6bGVuemkuY29tL2NhbXBhaWduLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cDovL3d3dy5pbnN0YWxsYWRzLm5ldC8iXQ=='),\n        ],\n        bulgarian: [fromB64('dGQjZnJlZW5ldF90YWJsZV9hZHM='), '#ea_intext_div', '.lapni-pop-over', '#xenium_hot_offers'],\n        easyList: [\n            '.yb-floorad',\n            fromB64('LndpZGdldF9wb19hZHNfd2lkZ2V0'),\n            fromB64('LnRyYWZmaWNqdW5reS1hZA=='),\n            '.textad_headline',\n            fromB64('LnNwb25zb3JlZC10ZXh0LWxpbmtz'),\n        ],\n        easyListChina: [\n            fromB64('LmFwcGd1aWRlLXdyYXBbb25jbGljayo9ImJjZWJvcy5jb20iXQ=='),\n            fromB64('LmZyb250cGFnZUFkdk0='),\n            '#taotaole',\n            '#aafoot.top_box',\n            '.cfa_popup',\n        ],\n        easyListCookie: [\n            '.ezmob-footer',\n            '.cc-CookieWarning',\n            '[data-cookie-number]',\n            fromB64('LmF3LWNvb2tpZS1iYW5uZXI='),\n            '.sygnal24-gdpr-modal-wrap',\n        ],\n        easyListCzechSlovak: [\n            '#onlajny-stickers',\n            fromB64('I3Jla2xhbW5pLWJveA=='),\n            fromB64('LnJla2xhbWEtbWVnYWJvYXJk'),\n            '.sklik',\n            fromB64('W2lkXj0ic2tsaWtSZWtsYW1hIl0='),\n        ],\n        easyListDutch: [\n            fromB64('I2FkdmVydGVudGll'),\n            fromB64('I3ZpcEFkbWFya3RCYW5uZXJCbG9jaw=='),\n            '.adstekst',\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly94bHR1YmUubmwvY2xpY2svIl0='),\n            '#semilo-lrectangle',\n        ],\n        easyListGermany: [\n            '#SSpotIMPopSlider',\n            fromB64('LnNwb25zb3JsaW5rZ3J1ZW4='),\n            fromB64('I3dlcmJ1bmdza3k='),\n            fromB64('I3Jla2xhbWUtcmVjaHRzLW1pdHRl'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9iZDc0Mi5jb20vIl0='),\n        ],\n        easyListItaly: [\n            fromB64('LmJveF9hZHZfYW5udW5jaQ=='),\n            '.sb-box-pubbliredazionale',\n            fromB64('YVtocmVmXj0iaHR0cDovL2FmZmlsaWF6aW9uaWFkcy5zbmFpLml0LyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZHNlcnZlci5odG1sLml0LyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZmZpbGlhemlvbmlhZHMuc25haS5pdC8iXQ=='),\n        ],\n        easyListLithuania: [\n            fromB64('LnJla2xhbW9zX3RhcnBhcw=='),\n            fromB64('LnJla2xhbW9zX251b3JvZG9z'),\n            fromB64('aW1nW2FsdD0iUmVrbGFtaW5pcyBza3lkZWxpcyJd'),\n            fromB64('aW1nW2FsdD0iRGVkaWt1b3RpLmx0IHNlcnZlcmlhaSJd'),\n            fromB64('aW1nW2FsdD0iSG9zdGluZ2FzIFNlcnZlcmlhaS5sdCJd'),\n        ],\n        estonian: [fromB64('QVtocmVmKj0iaHR0cDovL3BheTRyZXN1bHRzMjQuZXUiXQ==')],\n        fanboyAnnoyances: ['#ac-lre-player', '.navigate-to-top', '#subscribe_popup', '.newsletter_holder', '#back-top'],\n        fanboyAntiFacebook: ['.util-bar-module-firefly-visible'],\n        fanboyEnhancedTrackers: [\n            '.open.pushModal',\n            '#issuem-leaky-paywall-articles-zero-remaining-nag',\n            '#sovrn_container',\n            'div[class$=\"-hide\"][zoompage-fontsize][style=\"display: block;\"]',\n            '.BlockNag__Card',\n        ],\n        fanboySocial: ['#FollowUs', '#meteored_share', '#social_follow', '.article-sharer', '.community__social-desc'],\n        frellwitSwedish: [\n            fromB64('YVtocmVmKj0iY2FzaW5vcHJvLnNlIl1bdGFyZ2V0PSJfYmxhbmsiXQ=='),\n            fromB64('YVtocmVmKj0iZG9rdG9yLXNlLm9uZWxpbmsubWUiXQ=='),\n            'article.category-samarbete',\n            fromB64('ZGl2LmhvbGlkQWRz'),\n            'ul.adsmodern',\n        ],\n        greekAdBlock: [\n            fromB64('QVtocmVmKj0iYWRtYW4ub3RlbmV0LmdyL2NsaWNrPyJd'),\n            fromB64('QVtocmVmKj0iaHR0cDovL2F4aWFiYW5uZXJzLmV4b2R1cy5nci8iXQ=='),\n            fromB64('QVtocmVmKj0iaHR0cDovL2ludGVyYWN0aXZlLmZvcnRobmV0LmdyL2NsaWNrPyJd'),\n            'DIV.agores300',\n            'TABLE.advright',\n        ],\n        hungarian: [\n            '#cemp_doboz',\n            '.optimonk-iframe-container',\n            fromB64('LmFkX19tYWlu'),\n            fromB64('W2NsYXNzKj0iR29vZ2xlQWRzIl0='),\n            '#hirdetesek_box',\n        ],\n        iDontCareAboutCookies: [\n            '.alert-info[data-block-track*=\"CookieNotice\"]',\n            '.ModuleTemplateCookieIndicator',\n            '.o--cookies--container',\n            '#cookies-policy-sticky',\n            '#stickyCookieBar',\n        ],\n        icelandicAbp: [fromB64('QVtocmVmXj0iL2ZyYW1ld29yay9yZXNvdXJjZXMvZm9ybXMvYWRzLmFzcHgiXQ==')],\n        latvian: [\n            fromB64('YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiAxMjBweDsgaGVpZ2h0O' +\n                'iA0MHB4OyBvdmVyZmxvdzogaGlkZGVuOyBwb3NpdGlvbjogcmVsYXRpdmU7Il0='),\n            fromB64('YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6I' +\n                'DMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ=='),\n        ],\n        listKr: [\n            fromB64('YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0='),\n            fromB64('I2xpdmVyZUFkV3JhcHBlcg=='),\n            fromB64('YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=='),\n            fromB64('aW5zLmZhc3R2aWV3LWFk'),\n            '.revenue_unit_item.dable',\n        ],\n        listeAr: [\n            fromB64('LmdlbWluaUxCMUFk'),\n            '.right-and-left-sponsers',\n            fromB64('YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=='),\n            fromB64('YVtocmVmKj0iYm9vcmFxLm9yZyJd'),\n            fromB64('YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd'),\n        ],\n        listeFr: [\n            fromB64('YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=='),\n            fromB64('I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=='),\n            fromB64('YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0='),\n            '.site-pub-interstitiel',\n            'div[id^=\"crt-\"][data-criteo-id]',\n        ],\n        officialPolish: [\n            '#ceneo-placeholder-ceneo-12',\n            fromB64('W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=='),\n            fromB64('YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=='),\n            fromB64('ZGl2I3NrYXBpZWNfYWQ='),\n        ],\n        ro: [\n            fromB64('YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0='),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd'),\n            'a[href^=\"/url/\"]',\n        ],\n        ruAd: [\n            fromB64('YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd'),\n            fromB64('YVtocmVmKj0iLy91dGltZy5ydS8iXQ=='),\n            fromB64('YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0='),\n            '#pgeldiz',\n            '.yandex-rtb-block',\n        ],\n        thaiAds: [\n            'a[href*=macau-uta-popup]',\n            fromB64('I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=='),\n            fromB64('LmFkczMwMHM='),\n            '.bumq',\n            '.img-kosana',\n        ],\n        webAnnoyancesUltralist: [\n            '#mod-social-share-2',\n            '#social-tools',\n            fromB64('LmN0cGwtZnVsbGJhbm5lcg=='),\n            '.zergnet-recommend',\n            '.yt.btn-link.btn-md.btn',\n        ],\n    };\n}\n/**\n * The order of the returned array means nothing (it's always sorted alphabetically).\n *\n * Notice that the source is slightly unstable.\n * Safari provides a 2-taps way to disable all content blockers on a page temporarily.\n * Also content blockers can be disabled permanently for a domain, but it requires 4 taps.\n * So empty array shouldn't be treated as \"no blockers\", it should be treated as \"no signal\".\n * If you are a website owner, don't make your visitors want to disable content blockers.\n */\nfunction getDomBlockers(_a) {\n    var _b = _a === void 0 ? {} : _a, debug = _b.debug;\n    return __awaiter(this, void 0, void 0, function () {\n        var filters, filterNames, allSelectors, blockedSelectors, activeBlockers;\n        var _c;\n        return __generator(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    if (!isApplicable()) {\n                        return [2 /*return*/, undefined];\n                    }\n                    filters = getFilters();\n                    filterNames = Object.keys(filters);\n                    allSelectors = (_c = []).concat.apply(_c, filterNames.map(function (filterName) { return filters[filterName]; }));\n                    return [4 /*yield*/, getBlockedSelectors(allSelectors)];\n                case 1:\n                    blockedSelectors = _d.sent();\n                    if (debug) {\n                        printDebug(filters, blockedSelectors);\n                    }\n                    activeBlockers = filterNames.filter(function (filterName) {\n                        var selectors = filters[filterName];\n                        var blockedCount = countTruthy(selectors.map(function (selector) { return blockedSelectors[selector]; }));\n                        return blockedCount > selectors.length * 0.6;\n                    });\n                    activeBlockers.sort();\n                    return [2 /*return*/, activeBlockers];\n            }\n        });\n    });\n}\nfunction isApplicable() {\n    // Safari (desktop and mobile) and all Android browsers keep content blockers in both regular and private mode\n    return isWebKit() || isAndroid();\n}\nfunction getBlockedSelectors(selectors) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function () {\n        var d, root, elements, blockedSelectors, i, element, holder, i;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    d = document;\n                    root = d.createElement('div');\n                    elements = new Array(selectors.length);\n                    blockedSelectors = {} // Set() isn't used just in case somebody need older browser support\n                    ;\n                    forceShow(root);\n                    // First create all elements that can be blocked. If the DOM steps below are done in a single cycle,\n                    // browser will alternate tree modification and layout reading, that is very slow.\n                    for (i = 0; i < selectors.length; ++i) {\n                        element = selectorToElement(selectors[i]);\n                        if (element.tagName === 'DIALOG') {\n                            element.show();\n                        }\n                        holder = d.createElement('div') // Protects from unwanted effects of `+` and `~` selectors of filters\n                        ;\n                        forceShow(holder);\n                        holder.appendChild(element);\n                        root.appendChild(holder);\n                        elements[i] = element;\n                    }\n                    _b.label = 1;\n                case 1:\n                    if (!!d.body) return [3 /*break*/, 3];\n                    return [4 /*yield*/, wait(50)];\n                case 2:\n                    _b.sent();\n                    return [3 /*break*/, 1];\n                case 3:\n                    d.body.appendChild(root);\n                    try {\n                        // Then check which of the elements are blocked\n                        for (i = 0; i < selectors.length; ++i) {\n                            if (!elements[i].offsetParent) {\n                                blockedSelectors[selectors[i]] = true;\n                            }\n                        }\n                    }\n                    finally {\n                        // Then remove the elements\n                        (_a = root.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(root);\n                    }\n                    return [2 /*return*/, blockedSelectors];\n            }\n        });\n    });\n}\nfunction forceShow(element) {\n    element.style.setProperty('visibility', 'hidden', 'important');\n    element.style.setProperty('display', 'block', 'important');\n}\nfunction printDebug(filters, blockedSelectors) {\n    var message = 'DOM blockers debug:\\n```';\n    for (var _i = 0, _a = Object.keys(filters); _i < _a.length; _i++) {\n        var filterName = _a[_i];\n        message += \"\\n\".concat(filterName, \":\");\n        for (var _b = 0, _c = filters[filterName]; _b < _c.length; _b++) {\n            var selector = _c[_b];\n            message += \"\\n  \".concat(blockedSelectors[selector] ? '🚫' : '➡️', \" \").concat(selector);\n        }\n    }\n    // console.log is ok here because it's under a debug clause\n    // eslint-disable-next-line no-console\n    console.log(\"\".concat(message, \"\\n```\"));\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/color-gamut\n */\nfunction getColorGamut() {\n    // rec2020 includes p3 and p3 includes srgb\n    for (var _i = 0, _a = ['rec2020', 'p3', 'srgb']; _i < _a.length; _i++) {\n        var gamut = _a[_i];\n        if (matchMedia(\"(color-gamut: \".concat(gamut, \")\")).matches) {\n            return gamut;\n        }\n    }\n    return undefined;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/inverted-colors\n */\nfunction areColorsInverted() {\n    if (doesMatch$5('inverted')) {\n        return true;\n    }\n    if (doesMatch$5('none')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$5(value) {\n    return matchMedia(\"(inverted-colors: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/forced-colors\n */\nfunction areColorsForced() {\n    if (doesMatch$4('active')) {\n        return true;\n    }\n    if (doesMatch$4('none')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$4(value) {\n    return matchMedia(\"(forced-colors: \".concat(value, \")\")).matches;\n}\n\nvar maxValueToCheck = 100;\n/**\n * If the display is monochrome (e.g. black&white), the value will be ≥0 and will mean the number of bits per pixel.\n * If the display is not monochrome, the returned value will be 0.\n * If the browser doesn't support this feature, the returned value will be undefined.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/monochrome\n */\nfunction getMonochromeDepth() {\n    if (!matchMedia('(min-monochrome: 0)').matches) {\n        // The media feature isn't supported by the browser\n        return undefined;\n    }\n    // A variation of binary search algorithm can be used here.\n    // But since expected values are very small (≤10), there is no sense in adding the complexity.\n    for (var i = 0; i <= maxValueToCheck; ++i) {\n        if (matchMedia(\"(max-monochrome: \".concat(i, \")\")).matches) {\n            return i;\n        }\n    }\n    throw new Error('Too high value');\n}\n\n/**\n * @see https://www.w3.org/TR/mediaqueries-5/#prefers-contrast\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-contrast\n */\nfunction getContrastPreference() {\n    if (doesMatch$3('no-preference')) {\n        return 0 /* ContrastPreference.None */;\n    }\n    // The sources contradict on the keywords. Probably 'high' and 'low' will never be implemented.\n    // Need to check it when all browsers implement the feature.\n    if (doesMatch$3('high') || doesMatch$3('more')) {\n        return 1 /* ContrastPreference.More */;\n    }\n    if (doesMatch$3('low') || doesMatch$3('less')) {\n        return -1 /* ContrastPreference.Less */;\n    }\n    if (doesMatch$3('forced')) {\n        return 10 /* ContrastPreference.ForcedColors */;\n    }\n    return undefined;\n}\nfunction doesMatch$3(value) {\n    return matchMedia(\"(prefers-contrast: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-motion\n */\nfunction isMotionReduced() {\n    if (doesMatch$2('reduce')) {\n        return true;\n    }\n    if (doesMatch$2('no-preference')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$2(value) {\n    return matchMedia(\"(prefers-reduced-motion: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-transparency\n */\nfunction isTransparencyReduced() {\n    if (doesMatch$1('reduce')) {\n        return true;\n    }\n    if (doesMatch$1('no-preference')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$1(value) {\n    return matchMedia(\"(prefers-reduced-transparency: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://www.w3.org/TR/mediaqueries-5/#dynamic-range\n */\nfunction isHDR() {\n    if (doesMatch('high')) {\n        return true;\n    }\n    if (doesMatch('standard')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch(value) {\n    return matchMedia(\"(dynamic-range: \".concat(value, \")\")).matches;\n}\n\nvar M = Math; // To reduce the minified code size\nvar fallbackFn = function () { return 0; };\n/**\n * @see https://gitlab.torproject.org/legacy/trac/-/issues/13018\n * @see https://bugzilla.mozilla.org/show_bug.cgi?id=531915\n */\nfunction getMathFingerprint() {\n    // Native operations\n    var acos = M.acos || fallbackFn;\n    var acosh = M.acosh || fallbackFn;\n    var asin = M.asin || fallbackFn;\n    var asinh = M.asinh || fallbackFn;\n    var atanh = M.atanh || fallbackFn;\n    var atan = M.atan || fallbackFn;\n    var sin = M.sin || fallbackFn;\n    var sinh = M.sinh || fallbackFn;\n    var cos = M.cos || fallbackFn;\n    var cosh = M.cosh || fallbackFn;\n    var tan = M.tan || fallbackFn;\n    var tanh = M.tanh || fallbackFn;\n    var exp = M.exp || fallbackFn;\n    var expm1 = M.expm1 || fallbackFn;\n    var log1p = M.log1p || fallbackFn;\n    // Operation polyfills\n    var powPI = function (value) { return M.pow(M.PI, value); };\n    var acoshPf = function (value) { return M.log(value + M.sqrt(value * value - 1)); };\n    var asinhPf = function (value) { return M.log(value + M.sqrt(value * value + 1)); };\n    var atanhPf = function (value) { return M.log((1 + value) / (1 - value)) / 2; };\n    var sinhPf = function (value) { return M.exp(value) - 1 / M.exp(value) / 2; };\n    var coshPf = function (value) { return (M.exp(value) + 1 / M.exp(value)) / 2; };\n    var expm1Pf = function (value) { return M.exp(value) - 1; };\n    var tanhPf = function (value) { return (M.exp(2 * value) - 1) / (M.exp(2 * value) + 1); };\n    var log1pPf = function (value) { return M.log(1 + value); };\n    // Note: constant values are empirical\n    return {\n        acos: acos(0.123124234234234242),\n        acosh: acosh(1e308),\n        acoshPf: acoshPf(1e154),\n        asin: asin(0.123124234234234242),\n        asinh: asinh(1),\n        asinhPf: asinhPf(1),\n        atanh: atanh(0.5),\n        atanhPf: atanhPf(0.5),\n        atan: atan(0.5),\n        sin: sin(-1e300),\n        sinh: sinh(1),\n        sinhPf: sinhPf(1),\n        cos: cos(10.000000000123),\n        cosh: cosh(1),\n        coshPf: coshPf(1),\n        tan: tan(-1e300),\n        tanh: tanh(1),\n        tanhPf: tanhPf(1),\n        exp: exp(1),\n        expm1: expm1(1),\n        expm1Pf: expm1Pf(1),\n        log1p: log1p(10),\n        log1pPf: log1pPf(10),\n        powPI: powPI(-100),\n    };\n}\n\n/**\n * We use m or w because these two characters take up the maximum width.\n * Also there are a couple of ligatures.\n */\nvar defaultText = 'mmMwWLliI0fiflO&1';\n/**\n * Settings of text blocks to measure. The keys are random but persistent words.\n */\nvar presets = {\n    /**\n     * The default font. User can change it in desktop Chrome, desktop Firefox, IE 11,\n     * Android Chrome (but only when the size is ≥ than the default) and Android Firefox.\n     */\n    default: [],\n    /** OS font on macOS. User can change its size and weight. Applies after Safari restart. */\n    apple: [{ font: '-apple-system-body' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    serif: [{ fontFamily: 'serif' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    sans: [{ fontFamily: 'sans-serif' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    mono: [{ fontFamily: 'monospace' }],\n    /**\n     * Check the smallest allowed font size. User can change it in desktop Chrome, desktop Firefox and desktop Safari.\n     * The height can be 0 in Chrome on a retina display.\n     */\n    min: [{ fontSize: '1px' }],\n    /** Tells one OS from another in desktop Chrome. */\n    system: [{ fontFamily: 'system-ui' }],\n};\n/**\n * The result is a dictionary of the width of the text samples.\n * Heights aren't included because they give no extra entropy and are unstable.\n *\n * The result is very stable in IE 11, Edge 18 and Safari 14.\n * The result changes when the OS pixel density changes in Chromium 87. The real pixel density is required to solve,\n * but seems like it's impossible: https://stackoverflow.com/q/1713771/1118709.\n * The \"min\" and the \"mono\" (only on Windows) value may change when the page is zoomed in Firefox 87.\n */\nfunction getFontPreferences() {\n    return withNaturalFonts(function (document, container) {\n        var elements = {};\n        var sizes = {};\n        // First create all elements to measure. If the DOM steps below are done in a single cycle,\n        // browser will alternate tree modification and layout reading, that is very slow.\n        for (var _i = 0, _a = Object.keys(presets); _i < _a.length; _i++) {\n            var key = _a[_i];\n            var _b = presets[key], _c = _b[0], style = _c === void 0 ? {} : _c, _d = _b[1], text = _d === void 0 ? defaultText : _d;\n            var element = document.createElement('span');\n            element.textContent = text;\n            element.style.whiteSpace = 'nowrap';\n            for (var _e = 0, _f = Object.keys(style); _e < _f.length; _e++) {\n                var name_1 = _f[_e];\n                var value = style[name_1];\n                if (value !== undefined) {\n                    element.style[name_1] = value;\n                }\n            }\n            elements[key] = element;\n            container.append(document.createElement('br'), element);\n        }\n        // Then measure the created elements\n        for (var _g = 0, _h = Object.keys(presets); _g < _h.length; _g++) {\n            var key = _h[_g];\n            sizes[key] = elements[key].getBoundingClientRect().width;\n        }\n        return sizes;\n    });\n}\n/**\n * Creates a DOM environment that provides the most natural font available, including Android OS font.\n * Measurements of the elements are zoom-independent.\n * Don't put a content to measure inside an absolutely positioned element.\n */\nfunction withNaturalFonts(action, containerWidthPx) {\n    if (containerWidthPx === void 0) { containerWidthPx = 4000; }\n    /*\n     * Requirements for Android Chrome to apply the system font size to a text inside an iframe:\n     * - The iframe mustn't have a `display: none;` style;\n     * - The text mustn't be positioned absolutely;\n     * - The text block must be wide enough.\n     *   2560px on some devices in portrait orientation for the biggest font size option (32px);\n     * - There must be much enough text to form a few lines (I don't know the exact numbers);\n     * - The text must have the `text-size-adjust: none` style. Otherwise the text will scale in \"Desktop site\" mode;\n     *\n     * Requirements for Android Firefox to apply the system font size to a text inside an iframe:\n     * - The iframe document must have a header: `<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />`.\n     *   The only way to set it is to use the `srcdoc` attribute of the iframe;\n     * - The iframe content must get loaded before adding extra content with JavaScript;\n     *\n     * https://example.com as the iframe target always inherits Android font settings so it can be used as a reference.\n     *\n     * Observations on how page zoom affects the measurements:\n     * - macOS Safari 11.1, 12.1, 13.1, 14.0: zoom reset + offsetWidth = 100% reliable;\n     * - macOS Safari 11.1, 12.1, 13.1, 14.0: zoom reset + getBoundingClientRect = 100% reliable;\n     * - macOS Safari 14.0: offsetWidth = 5% fluctuation;\n     * - macOS Safari 14.0: getBoundingClientRect = 5% fluctuation;\n     * - iOS Safari 9, 10, 11.0, 12.0: haven't found a way to zoom a page (pinch doesn't change layout);\n     * - iOS Safari 13.1, 14.0: zoom reset + offsetWidth = 100% reliable;\n     * - iOS Safari 13.1, 14.0: zoom reset + getBoundingClientRect = 100% reliable;\n     * - iOS Safari 14.0: offsetWidth = 100% reliable;\n     * - iOS Safari 14.0: getBoundingClientRect = 100% reliable;\n     * - Chrome 42, 65, 80, 87: zoom 1/devicePixelRatio + offsetWidth = 1px fluctuation;\n     * - Chrome 42, 65, 80, 87: zoom 1/devicePixelRatio + getBoundingClientRect = 100% reliable;\n     * - Chrome 87: offsetWidth = 1px fluctuation;\n     * - Chrome 87: getBoundingClientRect = 0.7px fluctuation;\n     * - Firefox 48, 51: offsetWidth = 10% fluctuation;\n     * - Firefox 48, 51: getBoundingClientRect = 10% fluctuation;\n     * - Firefox 52, 53, 57, 62, 66, 67, 68, 71, 75, 80, 84: offsetWidth = width 100% reliable, height 10% fluctuation;\n     * - Firefox 52, 53, 57, 62, 66, 67, 68, 71, 75, 80, 84: getBoundingClientRect = width 100% reliable, height 10%\n     *   fluctuation;\n     * - Android Chrome 86: haven't found a way to zoom a page (pinch doesn't change layout);\n     * - Android Firefox 84: font size in accessibility settings changes all the CSS sizes, but offsetWidth and\n     *   getBoundingClientRect keep measuring with regular units, so the size reflects the font size setting and doesn't\n     *   fluctuate;\n     * - IE 11, Edge 18: zoom 1/devicePixelRatio + offsetWidth = 100% reliable;\n     * - IE 11, Edge 18: zoom 1/devicePixelRatio + getBoundingClientRect = reflects the zoom level;\n     * - IE 11, Edge 18: offsetWidth = 100% reliable;\n     * - IE 11, Edge 18: getBoundingClientRect = 100% reliable;\n     */\n    return withIframe(function (_, iframeWindow) {\n        var iframeDocument = iframeWindow.document;\n        var iframeBody = iframeDocument.body;\n        var bodyStyle = iframeBody.style;\n        bodyStyle.width = \"\".concat(containerWidthPx, \"px\");\n        bodyStyle.webkitTextSizeAdjust = bodyStyle.textSizeAdjust = 'none';\n        // See the big comment above\n        if (isChromium()) {\n            iframeBody.style.zoom = \"\".concat(1 / iframeWindow.devicePixelRatio);\n        }\n        else if (isWebKit()) {\n            iframeBody.style.zoom = 'reset';\n        }\n        // See the big comment above\n        var linesOfText = iframeDocument.createElement('div');\n        linesOfText.textContent = __spreadArray([], Array((containerWidthPx / 20) << 0), true).map(function () { return 'word'; }).join(' ');\n        iframeBody.appendChild(linesOfText);\n        return action(iframeDocument, iframeBody);\n    }, '<!doctype html><html><head><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">');\n}\n\nfunction isPdfViewerEnabled() {\n    return navigator.pdfViewerEnabled;\n}\n\n/**\n * Unlike most other architectures, on x86/x86-64 when floating-point instructions\n * have no NaN arguments, but produce NaN output, the output NaN has sign bit set.\n * We use it to distinguish x86/x86-64 from other architectures, by doing subtraction\n * of two infinities (must produce NaN per IEEE 754 standard).\n *\n * See https://codebrowser.bddppq.com/pytorch/pytorch/third_party/XNNPACK/src/init.c.html#79\n */\nfunction getArchitecture() {\n    var f = new Float32Array(1);\n    var u8 = new Uint8Array(f.buffer);\n    f[0] = Infinity;\n    f[0] = f[0] - f[0];\n    return u8[3];\n}\n\n/**\n * The return type is a union instead of the enum, because it's too challenging to embed the const enum into another\n * project. Turning it into a union is a simple and an elegant solution.\n */\nfunction getApplePayState() {\n    var ApplePaySession = window.ApplePaySession;\n    if (typeof (ApplePaySession === null || ApplePaySession === void 0 ? void 0 : ApplePaySession.canMakePayments) !== 'function') {\n        return -1 /* ApplePayState.NoAPI */;\n    }\n    if (willPrintConsoleError()) {\n        return -3 /* ApplePayState.NotAvailableInFrame */;\n    }\n    try {\n        return ApplePaySession.canMakePayments() ? 1 /* ApplePayState.Enabled */ : 0 /* ApplePayState.Disabled */;\n    }\n    catch (error) {\n        return getStateFromError(error);\n    }\n}\n/**\n * Starting from Safari 15 calling `ApplePaySession.canMakePayments()` produces this error message when FingerprintJS\n * runs in an iframe with a cross-origin parent page, and the iframe on that page has no allow=\"payment *\" attribute:\n *   Feature policy 'Payment' check failed for element with origin 'https://example.com' and allow attribute ''.\n * This function checks whether the error message is expected.\n *\n * We check for cross-origin parents, which is prone to false-positive results. Instead, we should check for allowed\n * feature/permission, but we can't because none of these API works in Safari yet:\n *   navigator.permissions.query({ name: ‘payment' })\n *   navigator.permissions.query({ name: ‘payment-handler' })\n *   document.featurePolicy\n */\nvar willPrintConsoleError = isAnyParentCrossOrigin;\nfunction getStateFromError(error) {\n    // See full expected error messages in the test\n    if (error instanceof Error && error.name === 'InvalidAccessError' && /\\bfrom\\b.*\\binsecure\\b/i.test(error.message)) {\n        return -2 /* ApplePayState.NotAvailableInInsecureContext */;\n    }\n    throw error;\n}\n\n/**\n * Checks whether the Safari's Privacy Preserving Ad Measurement setting is on.\n * The setting is on when the value is not undefined.\n * A.k.a. private click measurement, privacy-preserving ad attribution.\n *\n * Unfortunately, it doesn't work in mobile Safari.\n * Probably, it will start working in mobile Safari or stop working in desktop Safari later.\n * We've found no way to detect the setting state in mobile Safari. Help wanted.\n *\n * @see https://webkit.org/blog/11529/introducing-private-click-measurement-pcm/\n * @see https://developer.apple.com/videos/play/wwdc2021/10033\n */\nfunction getPrivateClickMeasurement() {\n    var _a;\n    var link = document.createElement('a');\n    var sourceId = (_a = link.attributionSourceId) !== null && _a !== void 0 ? _a : link.attributionsourceid;\n    return sourceId === undefined ? undefined : String(sourceId);\n}\n\n/** WebGl context is not available */\nvar STATUS_NO_GL_CONTEXT = -1;\n/** WebGL context `getParameter` method is not a function */\nvar STATUS_GET_PARAMETER_NOT_A_FUNCTION = -2;\nvar validContextParameters = new Set([\n    10752, 2849, 2884, 2885, 2886, 2928, 2929, 2930, 2931, 2932, 2960, 2961, 2962, 2963, 2964, 2965, 2966, 2967, 2968,\n    2978, 3024, 3042, 3088, 3089, 3106, 3107, 32773, 32777, 32777, 32823, 32824, 32936, 32937, 32938, 32939, 32968, 32969,\n    32970, 32971, 3317, 33170, 3333, 3379, 3386, 33901, 33902, 34016, 34024, 34076, 3408, 3410, 3411, 3412, 3413, 3414,\n    3415, 34467, 34816, 34817, 34818, 34819, 34877, 34921, 34930, 35660, 35661, 35724, 35738, 35739, 36003, 36004, 36005,\n    36347, 36348, 36349, 37440, 37441, 37443, 7936, 7937, 7938,\n    // SAMPLE_ALPHA_TO_COVERAGE (32926) and SAMPLE_COVERAGE (32928) are excluded because they trigger a console warning\n    // in IE, Chrome ≤ 59 and Safari ≤ 13 and give no entropy.\n]);\nvar validExtensionParams = new Set([\n    34047,\n    35723,\n    36063,\n    34852,\n    34853,\n    34854,\n    34229,\n    36392,\n    36795,\n    38449, // MAX_VIEWS_OVR\n]);\nvar shaderTypes = ['FRAGMENT_SHADER', 'VERTEX_SHADER'];\nvar precisionTypes = ['LOW_FLOAT', 'MEDIUM_FLOAT', 'HIGH_FLOAT', 'LOW_INT', 'MEDIUM_INT', 'HIGH_INT'];\nvar rendererInfoExtensionName = 'WEBGL_debug_renderer_info';\nvar polygonModeExtensionName = 'WEBGL_polygon_mode';\n/**\n * Gets the basic and simple WebGL parameters\n */\nfunction getWebGlBasics(_a) {\n    var _b, _c, _d, _e, _f, _g;\n    var cache = _a.cache;\n    var gl = getWebGLContext(cache);\n    if (!gl) {\n        return STATUS_NO_GL_CONTEXT;\n    }\n    if (!isValidParameterGetter(gl)) {\n        return STATUS_GET_PARAMETER_NOT_A_FUNCTION;\n    }\n    var debugExtension = shouldAvoidDebugRendererInfo() ? null : gl.getExtension(rendererInfoExtensionName);\n    return {\n        version: ((_b = gl.getParameter(gl.VERSION)) === null || _b === void 0 ? void 0 : _b.toString()) || '',\n        vendor: ((_c = gl.getParameter(gl.VENDOR)) === null || _c === void 0 ? void 0 : _c.toString()) || '',\n        vendorUnmasked: debugExtension ? (_d = gl.getParameter(debugExtension.UNMASKED_VENDOR_WEBGL)) === null || _d === void 0 ? void 0 : _d.toString() : '',\n        renderer: ((_e = gl.getParameter(gl.RENDERER)) === null || _e === void 0 ? void 0 : _e.toString()) || '',\n        rendererUnmasked: debugExtension ? (_f = gl.getParameter(debugExtension.UNMASKED_RENDERER_WEBGL)) === null || _f === void 0 ? void 0 : _f.toString() : '',\n        shadingLanguageVersion: ((_g = gl.getParameter(gl.SHADING_LANGUAGE_VERSION)) === null || _g === void 0 ? void 0 : _g.toString()) || '',\n    };\n}\n/**\n * Gets the advanced and massive WebGL parameters and extensions\n */\nfunction getWebGlExtensions(_a) {\n    var cache = _a.cache;\n    var gl = getWebGLContext(cache);\n    if (!gl) {\n        return STATUS_NO_GL_CONTEXT;\n    }\n    if (!isValidParameterGetter(gl)) {\n        return STATUS_GET_PARAMETER_NOT_A_FUNCTION;\n    }\n    var extensions = gl.getSupportedExtensions();\n    var contextAttributes = gl.getContextAttributes();\n    var unsupportedExtensions = [];\n    // Features\n    var attributes = [];\n    var parameters = [];\n    var extensionParameters = [];\n    var shaderPrecisions = [];\n    // Context attributes\n    if (contextAttributes) {\n        for (var _i = 0, _b = Object.keys(contextAttributes); _i < _b.length; _i++) {\n            var attributeName = _b[_i];\n            attributes.push(\"\".concat(attributeName, \"=\").concat(contextAttributes[attributeName]));\n        }\n    }\n    // Context parameters\n    var constants = getConstantsFromPrototype(gl);\n    for (var _c = 0, constants_1 = constants; _c < constants_1.length; _c++) {\n        var constant = constants_1[_c];\n        var code = gl[constant];\n        parameters.push(\"\".concat(constant, \"=\").concat(code).concat(validContextParameters.has(code) ? \"=\".concat(gl.getParameter(code)) : ''));\n    }\n    // Extension parameters\n    if (extensions) {\n        for (var _d = 0, extensions_1 = extensions; _d < extensions_1.length; _d++) {\n            var name_1 = extensions_1[_d];\n            if ((name_1 === rendererInfoExtensionName && shouldAvoidDebugRendererInfo()) ||\n                (name_1 === polygonModeExtensionName && shouldAvoidPolygonModeExtensions())) {\n                continue;\n            }\n            var extension = gl.getExtension(name_1);\n            if (!extension) {\n                unsupportedExtensions.push(name_1);\n                continue;\n            }\n            for (var _e = 0, _f = getConstantsFromPrototype(extension); _e < _f.length; _e++) {\n                var constant = _f[_e];\n                var code = extension[constant];\n                extensionParameters.push(\"\".concat(constant, \"=\").concat(code).concat(validExtensionParams.has(code) ? \"=\".concat(gl.getParameter(code)) : ''));\n            }\n        }\n    }\n    // Shader precision\n    for (var _g = 0, shaderTypes_1 = shaderTypes; _g < shaderTypes_1.length; _g++) {\n        var shaderType = shaderTypes_1[_g];\n        for (var _h = 0, precisionTypes_1 = precisionTypes; _h < precisionTypes_1.length; _h++) {\n            var precisionType = precisionTypes_1[_h];\n            var shaderPrecision = getShaderPrecision(gl, shaderType, precisionType);\n            shaderPrecisions.push(\"\".concat(shaderType, \".\").concat(precisionType, \"=\").concat(shaderPrecision.join(',')));\n        }\n    }\n    // Postprocess\n    extensionParameters.sort();\n    parameters.sort();\n    return {\n        contextAttributes: attributes,\n        parameters: parameters,\n        shaderPrecisions: shaderPrecisions,\n        extensions: extensions,\n        extensionParameters: extensionParameters,\n        unsupportedExtensions: unsupportedExtensions,\n    };\n}\n/**\n * This function usually takes the most time to execute in all the sources, therefore we cache its result.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getWebGLContext(cache) {\n    if (cache.webgl) {\n        return cache.webgl.context;\n    }\n    var canvas = document.createElement('canvas');\n    var context;\n    canvas.addEventListener('webglCreateContextError', function () { return (context = undefined); });\n    for (var _i = 0, _a = ['webgl', 'experimental-webgl']; _i < _a.length; _i++) {\n        var type = _a[_i];\n        try {\n            context = canvas.getContext(type);\n        }\n        catch (_b) {\n            // Ok, continue\n        }\n        if (context) {\n            break;\n        }\n    }\n    cache.webgl = { context: context };\n    return context;\n}\n/**\n * https://developer.mozilla.org/en-US/docs/Web/API/WebGLShaderPrecisionFormat\n * https://developer.mozilla.org/en-US/docs/Web/API/WebGLRenderingContext/getShaderPrecisionFormat\n * https://www.khronos.org/registry/webgl/specs/latest/1.0/#5.12\n */\nfunction getShaderPrecision(gl, shaderType, precisionType) {\n    var shaderPrecision = gl.getShaderPrecisionFormat(gl[shaderType], gl[precisionType]);\n    return shaderPrecision ? [shaderPrecision.rangeMin, shaderPrecision.rangeMax, shaderPrecision.precision] : [];\n}\nfunction getConstantsFromPrototype(obj) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    var keys = Object.keys(obj.__proto__);\n    return keys.filter(isConstantLike);\n}\nfunction isConstantLike(key) {\n    return typeof key === 'string' && !key.match(/[^A-Z0-9_x]/);\n}\n/**\n * Some browsers print a console warning when the WEBGL_debug_renderer_info extension is requested.\n * JS Agent aims to avoid printing messages to console, so we avoid this extension in that browsers.\n */\nfunction shouldAvoidDebugRendererInfo() {\n    return isGecko();\n}\n/**\n * Some browsers print a console warning when the WEBGL_polygon_mode extension is requested.\n * JS Agent aims to avoid printing messages to console, so we avoid this extension in that browsers.\n */\nfunction shouldAvoidPolygonModeExtensions() {\n    return isChromium() || isWebKit();\n}\n/**\n * Some unknown browsers have no `getParameter` method\n */\nfunction isValidParameterGetter(gl) {\n    return typeof gl.getParameter === 'function';\n}\n\nfunction getAudioContextBaseLatency() {\n    var _a;\n    // The signal emits warning in Chrome and Firefox, therefore it is enabled on Safari where it doesn't produce warning\n    // and on Android where it's less visible\n    var isAllowedPlatform = isAndroid() || isWebKit();\n    if (!isAllowedPlatform) {\n        return -2 /* SpecialFingerprint.Disabled */;\n    }\n    if (!window.AudioContext) {\n        return -1 /* SpecialFingerprint.NotSupported */;\n    }\n    return (_a = new AudioContext().baseLatency) !== null && _a !== void 0 ? _a : -1 /* SpecialFingerprint.NotSupported */;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/resolvedOptions\n *\n * The return type is a union instead of a const enum due to the difficulty of embedding const enums in other projects.\n * This makes integration simpler and more elegant.\n */\nfunction getDateTimeLocale() {\n    if (!window.Intl) {\n        return -1 /* Status.IntlAPINotSupported */;\n    }\n    var DateTimeFormat = window.Intl.DateTimeFormat;\n    if (!DateTimeFormat) {\n        return -2 /* Status.DateTimeFormatNotSupported */;\n    }\n    var locale = DateTimeFormat().resolvedOptions().locale;\n    if (!locale && locale !== '') {\n        return -3 /* Status.LocaleNotAvailable */;\n    }\n    return locale;\n}\n\n/**\n * The list of entropy sources used to make visitor identifiers.\n *\n * This value isn't restricted by Semantic Versioning, i.e. it may be changed without bumping minor or major version of\n * this package.\n *\n * Note: Rollup and Webpack are smart enough to remove unused properties of this object during tree-shaking, so there is\n * no need to export the sources individually.\n */\nvar sources = {\n    // READ FIRST:\n    // See https://github.com/fingerprintjs/fingerprintjs/blob/master/contributing.md#how-to-add-an-entropy-source\n    // to learn how entropy source works and how to make your own.\n    // The sources run in this exact order.\n    // The asynchronous sources are at the start to run in parallel with other sources.\n    fonts: getFonts,\n    domBlockers: getDomBlockers,\n    fontPreferences: getFontPreferences,\n    audio: getAudioFingerprint,\n    screenFrame: getScreenFrame,\n    canvas: getCanvasFingerprint,\n    osCpu: getOsCpu,\n    languages: getLanguages,\n    colorDepth: getColorDepth,\n    deviceMemory: getDeviceMemory,\n    screenResolution: getScreenResolution,\n    hardwareConcurrency: getHardwareConcurrency,\n    timezone: getTimezone,\n    sessionStorage: getSessionStorage,\n    localStorage: getLocalStorage,\n    indexedDB: getIndexedDB,\n    openDatabase: getOpenDatabase,\n    cpuClass: getCpuClass,\n    platform: getPlatform,\n    plugins: getPlugins,\n    touchSupport: getTouchSupport,\n    vendor: getVendor,\n    vendorFlavors: getVendorFlavors,\n    cookiesEnabled: areCookiesEnabled,\n    colorGamut: getColorGamut,\n    invertedColors: areColorsInverted,\n    forcedColors: areColorsForced,\n    monochrome: getMonochromeDepth,\n    contrast: getContrastPreference,\n    reducedMotion: isMotionReduced,\n    reducedTransparency: isTransparencyReduced,\n    hdr: isHDR,\n    math: getMathFingerprint,\n    pdfViewerEnabled: isPdfViewerEnabled,\n    architecture: getArchitecture,\n    applePay: getApplePayState,\n    privateClickMeasurement: getPrivateClickMeasurement,\n    audioBaseLatency: getAudioContextBaseLatency,\n    dateTimeLocale: getDateTimeLocale,\n    // Some sources can affect other sources (e.g. WebGL can affect canvas), so it's important to run these sources\n    // after other sources.\n    webGlBasics: getWebGlBasics,\n    webGlExtensions: getWebGlExtensions,\n};\n/**\n * Loads the built-in entropy sources.\n * Returns a function that collects the entropy components to make the visitor identifier.\n */\nfunction loadBuiltinSources(options) {\n    return loadSources(sources, options, []);\n}\n\nvar commentTemplate = '$ if upgrade to Pro: https://fpjs.dev/pro';\nfunction getConfidence(components) {\n    var openConfidenceScore = getOpenConfidenceScore(components);\n    var proConfidenceScore = deriveProConfidenceScore(openConfidenceScore);\n    return { score: openConfidenceScore, comment: commentTemplate.replace(/\\$/g, \"\".concat(proConfidenceScore)) };\n}\nfunction getOpenConfidenceScore(components) {\n    // In order to calculate the true probability of the visitor identifier being correct, we need to know the number of\n    // website visitors (the higher the number, the less the probability because the fingerprint entropy is limited).\n    // JS agent doesn't know the number of visitors, so we can only do an approximate assessment.\n    if (isAndroid()) {\n        return 0.4;\n    }\n    // Safari (mobile and desktop)\n    if (isWebKit()) {\n        return isDesktopWebKit() && !(isWebKit616OrNewer() && isSafariWebKit()) ? 0.5 : 0.3;\n    }\n    var platform = 'value' in components.platform ? components.platform.value : '';\n    // Windows\n    if (/^Win/.test(platform)) {\n        // The score is greater than on macOS because of the higher variety of devices running Windows.\n        // Chrome provides more entropy than Firefox according too\n        // https://netmarketshare.com/browser-market-share.aspx?options=%7B%22filter%22%3A%7B%22%24and%22%3A%5B%7B%22platform%22%3A%7B%22%24in%22%3A%5B%22Windows%22%5D%7D%7D%5D%7D%2C%22dateLabel%22%3A%22Trend%22%2C%22attributes%22%3A%22share%22%2C%22group%22%3A%22browser%22%2C%22sort%22%3A%7B%22share%22%3A-1%7D%2C%22id%22%3A%22browsersDesktop%22%2C%22dateInterval%22%3A%22Monthly%22%2C%22dateStart%22%3A%222019-11%22%2C%22dateEnd%22%3A%222020-10%22%2C%22segments%22%3A%22-1000%22%7D\n        // So we assign the same score to them.\n        return 0.6;\n    }\n    // macOS\n    if (/^Mac/.test(platform)) {\n        // Chrome provides more entropy than Safari and Safari provides more entropy than Firefox.\n        // Chrome is more popular than Safari and Safari is more popular than Firefox according to\n        // https://netmarketshare.com/browser-market-share.aspx?options=%7B%22filter%22%3A%7B%22%24and%22%3A%5B%7B%22platform%22%3A%7B%22%24in%22%3A%5B%22Mac%20OS%22%5D%7D%7D%5D%7D%2C%22dateLabel%22%3A%22Trend%22%2C%22attributes%22%3A%22share%22%2C%22group%22%3A%22browser%22%2C%22sort%22%3A%7B%22share%22%3A-1%7D%2C%22id%22%3A%22browsersDesktop%22%2C%22dateInterval%22%3A%22Monthly%22%2C%22dateStart%22%3A%222019-11%22%2C%22dateEnd%22%3A%222020-10%22%2C%22segments%22%3A%22-1000%22%7D\n        // So we assign the same score to them.\n        return 0.5;\n    }\n    // Another platform, e.g. a desktop Linux. It's rare, so it should be pretty unique.\n    return 0.7;\n}\nfunction deriveProConfidenceScore(openConfidenceScore) {\n    return round(0.99 + 0.01 * openConfidenceScore, 0.0001);\n}\n\nfunction componentsToCanonicalString(components) {\n    var result = '';\n    for (var _i = 0, _a = Object.keys(components).sort(); _i < _a.length; _i++) {\n        var componentKey = _a[_i];\n        var component = components[componentKey];\n        var value = 'error' in component ? 'error' : JSON.stringify(component.value);\n        result += \"\".concat(result ? '|' : '').concat(componentKey.replace(/([:|\\\\])/g, '\\\\$1'), \":\").concat(value);\n    }\n    return result;\n}\nfunction componentsToDebugString(components) {\n    return JSON.stringify(components, function (_key, value) {\n        if (value instanceof Error) {\n            return errorToObject(value);\n        }\n        return value;\n    }, 2);\n}\nfunction hashComponents(components) {\n    return x64hash128(componentsToCanonicalString(components));\n}\n/**\n * Makes a GetResult implementation that calculates the visitor id hash on demand.\n * Designed for optimisation.\n */\nfunction makeLazyGetResult(components) {\n    var visitorIdCache;\n    // This function runs very fast, so there is no need to make it lazy\n    var confidence = getConfidence(components);\n    // A plain class isn't used because its getters and setters aren't enumerable.\n    return {\n        get visitorId() {\n            if (visitorIdCache === undefined) {\n                visitorIdCache = hashComponents(this.components);\n            }\n            return visitorIdCache;\n        },\n        set visitorId(visitorId) {\n            visitorIdCache = visitorId;\n        },\n        confidence: confidence,\n        components: components,\n        version: version,\n    };\n}\n/**\n * A delay is required to ensure consistent entropy components.\n * See https://github.com/fingerprintjs/fingerprintjs/issues/254\n * and https://github.com/fingerprintjs/fingerprintjs/issues/307\n * and https://github.com/fingerprintjs/fingerprintjs/commit/945633e7c5f67ae38eb0fea37349712f0e669b18\n */\nfunction prepareForSources(delayFallback) {\n    if (delayFallback === void 0) { delayFallback = 50; }\n    // A proper deadline is unknown. Let it be twice the fallback timeout so that both cases have the same average time.\n    return requestIdleCallbackIfAvailable(delayFallback, delayFallback * 2);\n}\n/**\n * The function isn't exported from the index file to not allow to call it without `load()`.\n * The hiding gives more freedom for future non-breaking updates.\n *\n * A factory function is used instead of a class to shorten the attribute names in the minified code.\n * Native private class fields could've been used, but TypeScript doesn't allow them with `\"target\": \"es5\"`.\n */\nfunction makeAgent(getComponents, debug) {\n    var creationTime = Date.now();\n    return {\n        get: function (options) {\n            return __awaiter(this, void 0, void 0, function () {\n                var startTime, components, result;\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            startTime = Date.now();\n                            return [4 /*yield*/, getComponents()];\n                        case 1:\n                            components = _a.sent();\n                            result = makeLazyGetResult(components);\n                            if (debug || (options === null || options === void 0 ? void 0 : options.debug)) {\n                                // console.log is ok here because it's under a debug clause\n                                // eslint-disable-next-line no-console\n                                console.log(\"Copy the text below to get the debug data:\\n\\n```\\nversion: \".concat(result.version, \"\\nuserAgent: \").concat(navigator.userAgent, \"\\ntimeBetweenLoadAndGet: \").concat(startTime - creationTime, \"\\nvisitorId: \").concat(result.visitorId, \"\\ncomponents: \").concat(componentsToDebugString(components), \"\\n```\"));\n                            }\n                            return [2 /*return*/, result];\n                    }\n                });\n            });\n        },\n    };\n}\n/**\n * Sends an unpersonalized AJAX request to collect installation statistics\n */\nfunction monitor() {\n    // The FingerprintJS CDN (https://github.com/fingerprintjs/cdn) replaces `window.__fpjs_d_m` with `true`\n    if (window.__fpjs_d_m || Math.random() >= 0.001) {\n        return;\n    }\n    try {\n        var request = new XMLHttpRequest();\n        request.open('get', \"https://m1.openfpcdn.io/fingerprintjs/v\".concat(version, \"/npm-monitoring\"), true);\n        request.send();\n    }\n    catch (error) {\n        // console.error is ok here because it's an unexpected error handler\n        // eslint-disable-next-line no-console\n        console.error(error);\n    }\n}\n/**\n * Builds an instance of Agent and waits a delay required for a proper operation.\n */\nfunction load(options) {\n    var _a;\n    if (options === void 0) { options = {}; }\n    return __awaiter(this, void 0, void 0, function () {\n        var delayFallback, debug, getComponents;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    if ((_a = options.monitoring) !== null && _a !== void 0 ? _a : true) {\n                        monitor();\n                    }\n                    delayFallback = options.delayFallback, debug = options.debug;\n                    return [4 /*yield*/, prepareForSources(delayFallback)];\n                case 1:\n                    _b.sent();\n                    getComponents = loadBuiltinSources({ cache: {}, debug: debug });\n                    return [2 /*return*/, makeAgent(getComponents, debug)];\n            }\n        });\n    });\n}\n\n// The default export is a syntax sugar (`import * as FP from '...' → import FP from '...'`).\n// It should contain all the public exported values.\nvar index = { load: load, hashComponents: hashComponents, componentsToDebugString: componentsToDebugString };\n// The exports below are for private usage. They may change unexpectedly. Use them at your own risk.\n/** Not documented, out of Semantic Versioning, usage is at your own risk */\nvar murmurX64Hash128 = x64hash128;\n\nexport { componentsToDebugString, index as default, getFullscreenElement, getUnstableAudioFingerprint, getUnstableCanvasFingerprint, getUnstableScreenFrame, getUnstableScreenResolution, getWebGLContext, hashComponents, isAndroid, isChromium, isDesktopWebKit, isEdgeHTML, isGecko, isSamsungInternet, isTrident, isWebKit, load, loadSources, murmurX64Hash128, prepareForSources, sources, transformSource, withIframe };\n", "import FingerprintJS from '@fingerprintjs/fingerprintjs';\nexport async function getDeviceId() {\n    const fp = await FingerprintJS.load();\n    const result = await fp.get();\n    return result.visitorId; // Unique Device ID\n}\nexport async function fetchWithDeviceId(input, init) {\n    const deviceId = await getDeviceId();\n    // Convert Headers instance to a plain object if needed\n    let headers;\n    if (init?.headers instanceof Headers) {\n        headers = new Headers();\n        init.headers.forEach((value, key) => {\n            headers.set(key, value);\n        });\n    }\n    else {\n        headers = new Headers(init?.headers || {});\n    }\n    // Set the custom header\n    headers.set(\"X-Device-Id\", deviceId);\n    const modifiedInit = {\n        ...init,\n        headers,\n        credentials: \"include\", // Ensure credentials are sent if needed\n    };\n    return fetch(input, modifiedInit);\n}\nexport async function fetchWithDeviceIdandApiKey(input, init = {}, apiKey) {\n    const deviceId = await getDeviceId(); // Lấy deviceId\n    // Khởi tạo headers và đảm bảo giữ nguyên headers cũ nếu có\n    const headers = new Headers(init.headers);\n    headers.set(\"X-Device-Id\", deviceId);\n    headers.set(\"X-Api-Key\", apiKey); // Gửi API key trong request\n    // Tạo request mới với headers cập nhật\n    const modifiedInit = {\n        ...init,\n        headers,\n        credentials: \"include\", // Giữ cookies nếu cần\n    };\n    try {\n        const response = await fetch(input, modifiedInit);\n        return response;\n    }\n    catch (error) {\n        console.error('Fetch error:', error);\n        throw error;\n    }\n}\n", "import { environment } from \"../environments/environment\";\nimport { fetchWithDeviceId } from \"../utils/deviceUtils\";\nimport FingerprintJS from '@fingerprintjs/fingerprintjs';\nconst apiUrl = environment.apiUrl;\nconst publicKey = atob(environment.publicKey);\nexport class CryptoService {\n    constructor() {\n        this.keyPair = null;\n        this.encryptionKeyPair = null;\n    }\n    async gra() {\n        this.keyPair = await crypto.subtle.generateKey({\n            name: \"RSA-OAEP\",\n            modulusLength: 2048,\n            publicExponent: new Uint8Array([1, 0, 1]),\n            hash: \"SHA-256\"\n        }, true, [\"encrypt\", \"decrypt\"]);\n        const publicKey = await crypto.subtle.exportKey(\"spki\", this.keyPair.publicKey);\n        const privateKey = await crypto.subtle.exportKey(\"pkcs8\", this.keyPair.privateKey);\n        return {\n            publicKey: this.arrayBufferToPEM(publicKey, \"PUBLIC KEY\"),\n            privateKey: this.arrayBufferToPEM(privateKey, \"PRIVATE KEY\")\n        };\n    }\n    async ga() {\n        return await crypto.subtle.generateKey({ name: 'AES-GCM', length: 256 }, true, ['encrypt', 'decrypt']);\n    }\n    async ea(aesKey, data) {\n        const encoder = new TextEncoder();\n        const dataBuffer = encoder.encode(data);\n        const iv = crypto.getRandomValues(new Uint8Array(12));\n        const encryptedData = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, aesKey, dataBuffer);\n        return { encryptedData, iv };\n    }\n    async irpu(pem) {\n        const binaryDer = this.pemToArrayBuffer(pem);\n        return await crypto.subtle.importKey('spki', binaryDer, { name: 'RSA-OAEP', hash: 'SHA-256' }, true, ['encrypt']);\n    }\n    async irpr(pem) {\n        const binaryDer = this.pemToArrayBuffer(pem);\n        return await crypto.subtle.importKey('pkcs8', binaryDer, { name: 'RSA-OAEP', hash: 'SHA-256' }, true, ['decrypt']);\n    }\n    async era(publicKey, aesKey) {\n        const exportedAESKey = await crypto.subtle.exportKey('raw', aesKey);\n        return await crypto.subtle.encrypt({ name: 'RSA-OAEP' }, publicKey, exportedAESKey);\n    }\n    async dra(privateKey, encryptedData) {\n        return await crypto.subtle.decrypt({ name: 'RSA-OAEP' }, privateKey, encryptedData);\n    }\n    async he(publicKeyPem, data) {\n        const aesKey = await this.ga();\n        const { encryptedData, iv } = await this.ea(aesKey, data);\n        const publicKey = await this.irpu(publicKeyPem);\n        const encryptedAESKey = await this.era(publicKey, aesKey);\n        const combinedData = new Uint8Array(encryptedAESKey.byteLength + iv.byteLength + encryptedData.byteLength);\n        combinedData.set(new Uint8Array(encryptedAESKey), 0);\n        combinedData.set(iv, encryptedAESKey.byteLength);\n        combinedData.set(new Uint8Array(encryptedData), encryptedAESKey.byteLength + iv.byteLength);\n        return btoa(String.fromCharCode(...combinedData));\n    }\n    async hd(privateKeyBem, encryptedText) {\n        try {\n            const combinedData = Uint8Array.from(atob(encryptedText), c => c.charCodeAt(0));\n            const encryptedAesKey = combinedData.slice(0, 256);\n            const encryptedData = combinedData.slice(256, combinedData.length);\n            const privateKey = await this.irpr(privateKeyBem);\n            const aesKeyBuffer = await this.dra(privateKey, encryptedAesKey);\n            const decryptedData = await this.da(aesKeyBuffer, encryptedData);\n            return decryptedData;\n        }\n        catch (error) {\n            console.error(\"Error during decryption:\", error);\n            throw new Error(\"Decryption failed\");\n        }\n    }\n    bts(buffer) {\n        return btoa(String.fromCharCode(...new Uint8Array(buffer)));\n    }\n    async da(aesKeyBuffer, encryptedData) {\n        try {\n            const aesKey = await crypto.subtle.importKey(\"raw\", aesKeyBuffer, { name: \"AES-GCM\" }, false, [\"decrypt\"]);\n            const iv = encryptedData.slice(0, 12);\n            const tag = encryptedData.slice(12, 28);\n            const cipherText = encryptedData.slice(28);\n            const encryptedBuffer = new Uint8Array([...cipherText, ...tag]);\n            const decryptedBuffer = await crypto.subtle.decrypt({ name: \"AES-GCM\", iv: iv }, aesKey, encryptedBuffer);\n            return new TextDecoder().decode(decryptedBuffer);\n        }\n        catch (error) {\n            throw new Error(\"AES-GCM Decryption failed\");\n        }\n    }\n    async encrypt(publicKey, plainText) {\n        const key = await this.importPublicKey(publicKey);\n        const encryptedData = await crypto.subtle.encrypt({\n            name: \"RSA-OAEP\"\n        }, key, new TextEncoder().encode(plainText));\n        return this.arrayBufferToBase64(encryptedData);\n    }\n    async decrypt(privateKey, encryptedText) {\n        const key = await this.importPrivateKey(privateKey);\n        const decryptedData = await crypto.subtle.decrypt({\n            name: \"RSA-OAEP\"\n        }, key, this.base64ToArrayBuffer(encryptedText));\n        return new TextDecoder().decode(decryptedData);\n    }\n    async importPublicKey(pem) {\n        return crypto.subtle.importKey(\"spki\", this.pemToArrayBuffer(pem), {\n            name: \"RSA-OAEP\",\n            hash: \"SHA-256\"\n        }, true, [\"encrypt\"]);\n    }\n    async importPrivateKey(pem) {\n        return crypto.subtle.importKey(\"pkcs8\", this.pemToArrayBuffer(pem), {\n            name: \"RSA-OAEP\",\n            hash: \"SHA-256\"\n        }, true, [\"decrypt\"]);\n    }\n    arrayBufferToPEM(buffer, type) {\n        const base64 = this.arrayBufferToBase64(buffer);\n        const pem = `-----BEGIN ${type}-----\\n${base64.match(/.{1,64}/g)?.join('\\n')}\\n-----END ${type}-----`;\n        return pem;\n    }\n    arrayBufferToBase64(buffer) {\n        let binary = '';\n        const bytes = new Uint8Array(buffer);\n        const len = bytes.byteLength;\n        for (let i = 0; i < len; i++) {\n            binary += String.fromCharCode(bytes[i]);\n        }\n        return window.btoa(binary);\n    }\n    base64ToArrayBuffer(base64) {\n        const binaryString = window.atob(base64);\n        const len = binaryString.length;\n        const bytes = new Uint8Array(len);\n        for (let i = 0; i < len; i++) {\n            bytes[i] = binaryString.charCodeAt(i);\n        }\n        return bytes.buffer;\n    }\n    pemToArrayBuffer(pem) {\n        const base64 = pem.replace(/-----BEGIN [A-Z ]+-----|-----END [A-Z ]+-----|\\n/g, '');\n        return this.base64ToArrayBuffer(base64);\n    }\n    async gr() {\n        this.encryptionKeyPair = await this.gra();\n        const signingKeyPair = await crypto.subtle.generateKey({ name: \"RSASSA-PKCS1-v1_5\", modulusLength: 2048, publicExponent: new Uint8Array([0x01, 0x00, 0x01]), hash: \"SHA-256\" }, true, [\"sign\", \"verify\"]);\n        const ep = this.textToBase64(this.encryptionKeyPair.publicKey);\n        const spBuffer = await crypto.subtle.exportKey(\"spki\", signingKeyPair.publicKey);\n        const sp = btoa(String.fromCharCode(...new Uint8Array(spBuffer)));\n        const s = crypto.randomUUID();\n        const xdi = await this.gdi();\n        const encoder = new TextEncoder();\n        const sBuffer = encoder.encode(s + xdi);\n        const signatureBuffer = await crypto.subtle.sign({ name: \"RSASSA-PKCS1-v1_5\" }, signingKeyPair.privateKey, sBuffer);\n        const ss = btoa(String.fromCharCode(...new Uint8Array(signatureBuffer)));\n        return { ep, sp, ss, s };\n    }\n    textToBase64(text) {\n        return btoa(unescape(encodeURIComponent(text)));\n    }\n    sc(name, value, minutes) {\n        const date = new Date();\n        date.setTime(date.getTime() + (minutes * 60 * 1000));\n        const expires = \"expires=\" + date.toUTCString();\n        document.cookie = name + \"=\" + value + \";\" + expires + \";path=/\";\n    }\n    gc(name) {\n        const nameEQ = name + \"=\";\n        const ca = document.cookie.split(';');\n        for (let i = 0; i < ca.length; i++) {\n            let c = ca[i];\n            while (c.charAt(0) === ' ')\n                c = c.substring(1, c.length);\n            if (c.indexOf(nameEQ) === 0)\n                return c.substring(nameEQ.length, c.length);\n        }\n        return null;\n    }\n    //remove cookie\n    rc(name) {\n        document.cookie = name + '=; Max-Age=-99999999;';\n    }\n    //remove all cookies\n    ra() {\n        const cookies = document.cookie.split(\";\");\n        for (let i = 0; i < cookies.length; i++) {\n            const cookie = cookies[i];\n            const eqPos = cookie.indexOf(\"=\");\n            const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;\n            document.cookie = name + \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n        }\n    }\n    async spu() {\n        const { ep, sp, ss, s } = await this.gr();\n        const request = {\n            ep: ep,\n            sp: sp,\n            ss: ss,\n            s: s\n        };\n        const requestJson = JSON.stringify(request);\n        const encryptedData = await this.he(publicKey, requestJson);\n        const requestEncrypt = {\n            EncryptData: encryptedData\n        };\n        try {\n            const response = await fetchWithDeviceId(apiUrl + '/api/Crypto/dr', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestEncrypt)\n            });\n            if (!response.ok) {\n                throw new Error('Network response was not ok');\n            }\n            const responseData = await response.json();\n            if (responseData && responseData.resultObj && responseData.resultObj.encryptedData) {\n                this.sc('s', responseData.resultObj.encryptedData, 5);\n                const privateKeyBase64String = this.textToBase64(this.encryptionKeyPair.privateKey);\n                this.sc('c', privateKeyBase64String, 5);\n            }\n            else {\n                console.error('Invalid response from server:', responseData);\n            }\n        }\n        catch (error) {\n            console.error('Error in spu:', error);\n        }\n    }\n    async dsk() {\n        var c = this.gc('c');\n        var s = this.gc('s');\n        if (!c || !s) {\n            return \"\";\n        }\n        var cText = atob(c);\n        var serverKey = await this.hd(cText, s);\n        return serverKey;\n    }\n    async eda(data) {\n        var s = await this.dsk();\n        var sText = atob(s);\n        if (!s) {\n            return \"\";\n        }\n        var encryptedData = await this.he(sText, data);\n        return encryptedData;\n    }\n    async dda(encryptedData) {\n        var c = this.gc('c');\n        if (!c) {\n            return \"\";\n        }\n        var cText = atob(c);\n        var decryptedData = await this.hd(cText, encryptedData);\n        return decryptedData;\n    }\n    async csi() {\n        try {\n            const response = await fetchWithDeviceId(apiUrl + '/api/Crypto/check-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: null\n            });\n            if (!response.ok) {\n                throw new Error('Network response was not ok');\n            }\n            const responseData = await response.json();\n            console.log(responseData);\n        }\n        catch (error) {\n            console.error('Error in csi:', error);\n        }\n    }\n    async iih() {\n        if (!this.ch()) {\n            await this.spu();\n        }\n    }\n    ch() {\n        if (this.gc('s') && this.gc('c')) {\n            return true;\n        }\n        return false;\n    }\n    async wk() {\n        let retries = 10;\n        while (!this.gc('s') && retries > 0) {\n            await new Promise(resolve => setTimeout(resolve, 200));\n            retries--;\n        }\n    }\n    async gdi() {\n        const fp = await FingerprintJS.load();\n        const result = await fp.get();\n        return result.visitorId; // Device ID duy nhất\n    }\n}\n", "import { environment } from \"../environments/environment\";\nimport { fetchWithDeviceIdandApiKey } from \"../utils/deviceUtils\";\nconst apiUrl = environment.apiUrl;\nexport class FlightService {\n    async request(endpoint, request, useDeviceId = true, apiKey) {\n        try {\n            const fetchFn = useDeviceId ? fetchWithDeviceIdandApiKey : fetch;\n            const response = await fetchFn(`${apiUrl}/api/Library/${endpoint}`, {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' },\n                body: JSON.stringify(request)\n            }, apiKey);\n            if (!response.ok) {\n                throw response;\n            }\n            return await response.json();\n        }\n        catch (error) {\n            throw error;\n        }\n    }\n    SearchTrip(request, api) { return this.request('SearchTrip', request, true, api); }\n    PriceAncillary(request, api) { return this.request('PriceAncillary', request, true, api); }\n    FareRules(request, language) { return this.request('../FareRules/get-fare-rules/' + language, request, false, ''); }\n    AvailableTrip(request, api) { return this.request('AvailableTrip', request, true, api); }\n    RequestTrip(request, api) { return this.request('RequestTrip', request, true, api); }\n    RePayment(request, api) { return this.request('RePayment', request, true, api); }\n}\n", "/**\n * Chuyển đổi đối tượng Date thành chuỗi định dạng yyyy-MM-dd\n * @param date - Đ<PERSON>i tượng Date cần chuyển đổi\n * @returns Chuỗi định dạng yyyy-MM-dd\n */\nexport const formatDate = (date) => {\n    const year = date.getFullYear();\n    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Thêm số 0 ở đầu nếu cần\n    const day = date.getDate().toString().padStart(2, '0'); // Thêm số 0 ở đầu nếu cần\n    return `${year}-${month}-${day}`; // Định dạng yyyy-MM-dd\n};\nexport function formatDateTo_ddMMyyyy(date, language) {\n    if (!date || date === undefined) {\n        return null;\n    }\n    var date1 = new Date(date);\n    if (language === 'vi') {\n        // Tr<PERSON> về dạng dd/MM/yyyy\n        return date1.toLocaleDateString('vi-VN', {\n            day: '2-digit',\n            month: '2-digit',\n            year: 'numeric'\n        });\n    }\n    // Trả về dạng 11 Jul, 2025\n    const day = date1.getDate().toString().padStart(2, '0');\n    const month = date1.toLocaleString('en-US', { month: 'short' }); // Jul\n    const year = date1.getFullYear();\n    return `${day} ${month}, ${year}`;\n}\nexport function getDurationByArray(legs) {\n    if (legs == null)\n        return '';\n    var duration = 0;\n    var departure = new Date(legs[0].DepartureDate);\n    var arrival = new Date(legs[legs.length - 1].ArrivalDate);\n    duration = arrival.getTime() - departure.getTime();\n    var hours = Math.floor(duration / 3600000);\n    var minutes = Math.floor((duration % 3600000) / 60000);\n    return hours.toString().padStart(2, '0') + 'h' + minutes.toString().padStart(2, '0');\n}\nexport function formatDateToString(date, language) {\n    if (!date)\n        return null;\n    let day, month, year;\n    // Trường hợp là đối tượng Date\n    if (date instanceof Date) {\n        day = date.getDate();\n        month = date.getMonth() + 1;\n        year = date.getFullYear();\n    }\n    // Trường hợp là object có day/month/year\n    else if (typeof date === 'object' && ('day' in date || 'month' in date || 'year' in date)) {\n        day = date.day || 1;\n        month = date.month || 1;\n        year = date.year || 2000;\n    }\n    // Trường hợp là string có thể parse được\n    else if (typeof date === 'string') {\n        const parsed = new Date(date);\n        if (isNaN(parsed.getTime()))\n            return null;\n        day = parsed.getDate();\n        month = parsed.getMonth() + 1;\n        year = parsed.getFullYear();\n    }\n    else {\n        return null;\n    }\n    const dd = day.toString().padStart(2, '0');\n    const mm = month.toString().padStart(2, '0');\n    const yyyy = year.toString();\n    // Trả về theo ngôn ngữ\n    return language === 'vi' ? `${dd}/${mm}/${yyyy}` : `${mm}/${dd}/${yyyy}`;\n}\nexport function validatePhone(phone) {\n    return phone.match(/^[0-9]{6,12}$/) ? true : false;\n}\nexport function validateEmail(email) {\n    return email.match(/^([\\w.%+-]+)@([\\w-]+\\.)+([\\w]{2,})$/i) ? true : false;\n}\nexport function getTimeFromDateTime(dateTime) {\n    const date = new Date(dateTime);\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n}\nexport function convertDurationToHour(duration) {\n    const hours = Math.floor(duration / 60).toString().padStart(2, '0');\n    const minutes = (duration % 60).toString().padStart(2, '0');\n    return `${hours}h${minutes}`;\n}\nexport function getDuration(leg) {\n    if (leg == null)\n        return '';\n    var departure = new Date(leg.DepartureDate);\n    var arrival = new Date(leg.ArrivalDate);\n    var duration = arrival.getTime() - departure.getTime();\n    var hours = Math.floor(duration / 3600000);\n    var minutes = Math.floor((duration % 3600000) / 60000);\n    return hours.toString().padStart(2, '0') + 'h' + minutes.toString().padStart(2, '0');\n}\nexport function formatddMMyyyy(date) {\n    if (date == null)\n        return '';\n    var dateObj = new Date(date);\n    return dateObj.getDate().toString().padStart(2, '0') + '/' + ((dateObj.getMonth() + 1).toString().padStart(2, '0')) + '/' + dateObj.getFullYear();\n}\nexport function getDayInWeek(date) {\n    const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];\n    const dateObj = new Date(date);\n    return days[dateObj.getDay()];\n}\nexport function getFlights(legs) {\n    return legs?.map(leg => leg.OperatingAirlines + leg.FlightNumber).join(' - ');\n}\nexport function getDirect(legs, language) {\n    return legs.length > 1 ? (language === 'vi' ? \"Nhiều chặng\" : \"Multiple stops\") : (language === 'vi' ? \"Bay thẳng\" : \"Direct flight\");\n}\nexport function getDurarionLeg(leg) {\n    var departure = new Date(leg.DepartureDate);\n    var arrival = new Date(leg.ArrivalDate);\n    var duration = (arrival.getTime() - departure.getTime()) / 60000;\n    return convertDurationToHour(duration);\n}\nexport function getPassengerDescription(searchTripRequest, paxType, language) {\n    if (!searchTripRequest) {\n        return '';\n    }\n    switch (paxType) {\n        case 'ADT':\n            return `${searchTripRequest?.Adult} x ${language === 'vi' ? 'Người lớn' : 'Adult'}`;\n        case 'CHD':\n            return `${searchTripRequest?.Child} x ${language === 'vi' ? 'Trẻ em' : 'Child'}`;\n        case 'INF':\n            return `${searchTripRequest?.Infant} x ${language === 'vi' ? 'Em bé' : 'Infant'}`;\n        default:\n            return '';\n    }\n}\nexport function getPassengerDescriptionV2(paxType, ADT = 0, CHD = 0, INF = 0, language) {\n    switch (paxType) {\n        case 'ADT':\n            return `${ADT} x ${language === 'vi' ? 'Người lớn' : 'Adult'}`;\n        case 'CHD':\n            return `${CHD} x ${language === 'vi' ? 'Trẻ em' : 'Child'}`;\n        case 'INF':\n            return `${INF} x ${language === 'vi' ? 'Em bé' : 'Infant'}`;\n        default:\n            return '';\n    }\n}\nexport function getTypePassenger(passenger, passengers, language) {\n    var type = '';\n    switch (passenger.type) {\n        case 'adult':\n            type = language === 'vi' ? 'Người lớn' : 'Adult';\n            break;\n        case 'child':\n            type = language === 'vi' ? 'Trẻ em' : 'Child';\n            break;\n        case 'infant':\n            type = language === 'vi' ? 'Em bé' : 'Infant';\n            break;\n    }\n    //get index of passenger in type\n    var indexPassenger = passengers.filter(p => p.type === passenger.type).indexOf(passenger);\n    var result = `${type} ${indexPassenger + 1}`;\n    return result;\n}\nexport function formatNumber(value, convertedVND, language) {\n    if (value === null || value === undefined)\n        return '';\n    const result = language === 'vi' ? value : value / convertedVND;\n    if (language === 'vi' || convertedVND === 1) {\n        return Math.round(result).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, '.');\n    }\n    else {\n        const [integerPart, decimalPart] = result.toFixed(2).split('.');\n        const formattedInteger = integerPart.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n        return `${formattedInteger}.${decimalPart}`;\n    }\n}\nexport function range(start, end) {\n    if (start > end) {\n        return [];\n    }\n    return Array(end - start + 1).fill(0).map((_, idx) => start + idx);\n}\nexport function getDayOfWeek(date, language) {\n    const dayOfWeek = new Date(date).getDay();\n    return language === 'vi' ? ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'][dayOfWeek] : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayOfWeek];\n}\nexport function formatDate_ddMM(date, language) {\n    const day = date.getDate().toString().padStart(2, '0');\n    if (language === 'vi') {\n        const monthStr = (date.getMonth() + 1).toString().padStart(2, '0');\n        return `${day}/${monthStr}`;\n    }\n    const monthStr = date.toLocaleString('en', { month: 'short' });\n    return `${day} ${monthStr}`;\n}\nexport function formatDateS(dateString, language) {\n    var date = new Date(dateString);\n    return formatDateTypeDate(date, language);\n}\nexport function formatDateTypeDate(date, language) {\n    const daysOfWeek = language === 'vi'\n        ? [\"Chủ nhật\", \"Thứ hai\", \"Thứ ba\", \"Thứ tư\", \"Thứ năm\", \"Thứ sáu\", \"Thứ bảy\"]\n        : [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"];\n    const dayOfWeek = daysOfWeek[date.getDay()];\n    if (!dayOfWeek) {\n        return '';\n    }\n    const day = date.getDate().toString().padStart(2, '0');\n    const year = date.getFullYear();\n    if (language === 'vi') {\n        const monthStr = (date.getMonth() + 1).toString().padStart(2, '0');\n        return `${dayOfWeek}, ${day}/${monthStr}/${year}`;\n    }\n    // Format for English: Thursday, 31 July 2023\n    const monthStr = date.toLocaleString('en', { month: 'long' });\n    return `${dayOfWeek}, ${day} ${monthStr} ${year}`;\n}\nexport function getFareType(bookingInfos) {\n    return bookingInfos.map(bookingInfo => bookingInfo.FareType || bookingInfo.CabinName).join(' - ');\n}\nexport function debounce(func, wait) {\n    let timeout;\n    return (...args) => {\n        clearTimeout(timeout);\n        timeout = window.setTimeout(() => func(...args), wait);\n    };\n}\n", "import { html } from \"lit\";\nimport { convertDurationToHour, formatDateTo_ddMMyyyy, formatDateToString, formatddMMyyyy, formatNumber, getDayInWeek, getDuration, getDurationByArray, getTimeFromDateTime } from \"../../utils/dateUtils\";\nimport { environment } from \"../../environments/environment\";\nconst apiUrl = environment.apiUrl;\nexport const TripResultTemplate = (autoFillOrderCode, uri_searchBox, language, _isLoading, _isNotValid, _orderAvailable, _orderDetails, _inforAirports, _PaymentNote, __NoteModel, _currencySymbol, _convertedVND, rePayment, handleLanguageChange, showLanguageSelect) => html `\r\n ${_isLoading ? html `\r\n    <div class=\"static\" *ngIf=\"isLoading\">\r\n        <div class=\"loader-container\">\r\n            <span class=\"loader\"></span>\r\n            <img src=\"${apiUrl}/assets/img/background/trip_loading2.gif\"/>\r\n            <span class=\"loadidng-vertical  bg-gradient-to-br from-nmt-600 to-nmt-400 shadow-lg shadow-white\">${language === 'vi' ? `Đang kiểm tra chuyến bay, vui lòng chờ trong giây lát...` : `Checking flight, please wait a moment...`}</span>\r\n        </div>\r\n    </div>\r\n    ` : \"\"}\r\n\r\n    <div class=\"w-full bg-gray-100  relative min-h-screen  max-md:pb-24\">\r\n    <div class=\"container mx-auto px-4 py-8 max-w-7xl\">\r\n        <section class=\"text-gray-600 body-font\">\r\n            <div class=\"container md:px-5   mx-auto\">\r\n            ${_isLoading ? html `\r\n            <div class=\"flex flex-col text-center w-full md:mb-10 \">\r\n                    <h1 class=\"inline-block mb-2 text-3xl font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                        ${language === 'vi' ? `Đang tìm đơn hàng của bạn` : `Finding your order`}\r\n                    </h1>\r\n                    <p class=\"text-gray-500 dark:text-gray-400\">${language === 'vi' ? `Vui lòng chờ trong giây lát...` : `Please wait a moment...`}</p>\r\n                </div>\r\n            ` : html `\r\n            ${_isNotValid ? html `\r\n            <div>\r\n                    <div class=\"pt-4 pb-8\">\r\n                        <div\r\n                            class=\"w-full max-w-4xl mx-auto max-md:overflow-x-scroll max-md:overflow-y-hidden max-md:pb-[10px]\">\r\n                            <div class=\"flex items-center justify-center max-md:space-x-2\">\r\n                                <div class=\"flex items-center\">\r\n                                    <div class=\"relative group\">\r\n                                        <div\r\n                                            class=\"flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white  cursor-pointer\">\r\n                                            <a href=\"/${uri_searchBox}\"\r\n                                                class=\"flex items-center gap-3 transition-transform duration-300 group-hover:scale-105\">\r\n                                                <div\r\n                                                    class=\"rounded-lg p-2 transition-colors duration-300 bg-nmt-500\">\r\n                                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                        viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                        stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                        class=\"lucide lucide-search w-5 h-5 text-white\">\r\n                                                        <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\r\n                                                        <path d=\"m21 21-4.3-4.3\"></path>\r\n                                                    </svg>\r\n                                                </div><span\r\n                                                    class=\"font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden\">\r\n                                                    ${language === 'vi' ? `Tìm kiếm` : `Search`}\r\n                                                </span>\r\n            </a>\r\n\r\n                                        </div>\r\n                                    </div>\r\n                                    <div\r\n                                        class=\"md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400 max-md:hidden\">\r\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"\r\n                                            stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                            class=\"lucide lucide-chevron-right w-5 h-5\">\r\n                                            <path d=\"m9 18 6-6-6-6\"></path>\r\n                                        </svg>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"flex items-center\">\r\n                                    <div class=\"relative group\">\r\n                                        <div\r\n                                            class=\"flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed\">\r\n                                            <div\r\n                                                class=\"flex items-center gap-3 transition-transform duration-300 group-hover:scale-105\">\r\n                                                <div\r\n                                                    class=\"rounded-lg p-2 transition-colors duration-300 bg-nmt-500\">\r\n                                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                        viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                        stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                        class=\"lucide lucide-ticket w-5 h-5 text-white\">\r\n                                                        <path\r\n                                                            d=\"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z\">\r\n                                                        </path>\r\n                                                        <path d=\"M13 5v2\"></path>\r\n                                                        <path d=\"M13 17v2\"></path>\r\n                                                        <path d=\"M13 11v2\"></path>\r\n                                                    </svg>\r\n                                                </div><span\r\n                                                    class=\"font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden\">${language === 'vi' ? `Chọn vé` : `Select ticket`}</span>\r\n                                            </div>\r\n\r\n                                        </div>\r\n                                    </div>\r\n                                    <div\r\n                                        class=\"md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400 max-md:hidden\">\r\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"\r\n                                            stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                            class=\"lucide lucide-chevron-right w-5 h-5\">\r\n                                            <path d=\"m9 18 6-6-6-6\"></path>\r\n                                        </svg>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"flex items-center\">\r\n                                    <div class=\"relative group\">\r\n                                        <div\r\n                                            class=\"flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed\">\r\n                                            <div\r\n                                                class=\"flex items-center gap-3 transition-transform duration-300 group-hover:scale-105\">\r\n                                                <div\r\n                                                    class=\"rounded-lg p-2 transition-colors duration-300 bg-nmt-500\">\r\n                                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                        viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                        stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                        class=\"lucide lucide-user w-5 h-5 text-white\">\r\n                                                        <path d=\"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\"></path>\r\n                                                        <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\r\n                                                    </svg>\r\n                                                </div><span\r\n                                                    class=\"font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600  max-md:hidden\">${language === 'vi' ? `Thông tin` : `Information`}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div\r\n                                        class=\"md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400  max-md:hidden\">\r\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"\r\n                                            stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                            class=\"lucide lucide-chevron-right w-5 h-5\">\r\n                                            <path d=\"m9 18 6-6-6-6\"></path>\r\n                                        </svg>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"flex items-center\">\r\n                                    <div class=\"relative group\">\r\n                                        <div\r\n                                            class=\"flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed\">\r\n                                            <div\r\n                                                class=\"flex items-center gap-3 transition-transform duration-300 group-hover:scale-105\">\r\n                                                <div\r\n                                                    class=\"rounded-lg p-2 transition-colors duration-300 bg-nmt-500\">\r\n                                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                        viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                        stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                        class=\"lucide lucide-credit-card w-5 h-5 text-white\">\r\n                                                        <rect width=\"20\" height=\"14\" x=\"2\" y=\"5\" rx=\"2\"></rect>\r\n                                                        <line x1=\"2\" x2=\"22\" y1=\"10\" y2=\"10\"></line>\r\n                                                    </svg>\r\n                                                </div>\r\n                                                <span\r\n                                                    class=\"font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600 max-md:hidden\">${language === 'vi' ? `Thanh toán` : `Payment`}</span>\r\n                                            </div>\r\n\r\n                                        </div>\r\n                                    </div>\r\n                                    <div\r\n                                        class=\"md:mx-4 mx-1.5 p-1 rounded-full transition-colors duration-300 bg-gray-200 text-gray-400  max-md:hidden\">\r\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"\r\n                                            stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                            class=\"lucide lucide-chevron-right w-5 h-5\">\r\n                                            <path d=\"m9 18 6-6-6-6\"></path>\r\n                                        </svg>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"flex items-center\">\r\n                                    <div class=\"relative group\">\r\n                                        <div\r\n                                            class=\"flex items-center h-12 md:px-6 px-2  rounded-lg transition-all duration-300 ease-in-out border-2 border-nmt-500 bg-white cursor-not-allowed\">\r\n                                            <div\r\n                                                class=\"flex items-center gap-3 transition-transform duration-300 group-hover:scale-105\">\r\n                                                <div\r\n                                                    class=\"rounded-lg p-2 transition-colors duration-300 bg-nmt-500\">\r\n                                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\"\r\n                                                        viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                        stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                        class=\"lucide lucide-credit-card w-5 h-5 text-white\">\r\n                                                        <path d=\"M20 6 9 17l-5-5\"></path>\r\n                                                    </svg>\r\n                                                </div><span\r\n                                                    class=\"font-medium tracking-wide whitespace-nowrap text-[15px] text-nmt-600\">${language === 'vi' ? `Hoàn tất` : `Complete`}</span>\r\n                                            </div>\r\n                                            <div class=\"absolute -bottom-[5px] left-1/2 transform -translate-x-1/2\">\r\n                                                <div class=\"w-2.5 h-2.5 rounded-full bg-nmt-500 ring-4 ring-white\">\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid md:grid-cols-5 grid-cols-1 md:gap-8 mt-6\">\r\n                    <div class=\"col-span-3\">\r\n                        <div class=\"w-full overflow-x-auto\">\r\n                            <div class=\"flex justify-between items-center mb-2\">\r\n                                <h1 class=\"inline-block  text-xl font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                ${language === 'vi' ? `Chi Tiết Đơn Hàng` : `Order Details`}</h1>\r\n\r\n                                <div class=\"flex justify-end items-center  \">\r\n                                    ${showLanguageSelect ? html `\r\n                                    <select id=\"language\" \r\n                                        class=\" text-sm  bg-gray-200 rounded-lg px-2 py-1  border-none focus:outline-none text-gray-600 \"\r\n                                        .value=${language}\r\n                                        @change=${(e) => handleLanguageChange(e.target.value)}\r\n                                    >\r\n                                        <option style=\"background-color: #f0f0f0; color: black;\" value=\"en\">English</option>\r\n                                        <option style=\"background-color: #f0f0f0; color: black;\" value=\"vi\">Tiếng Việt</option>\r\n                                    </select>\r\n                                    ` : ''}\r\n                                </div>\r\n                            </div>\r\n                            \r\n                            <div class=\"relative overflow-x-auto shadow border border-gray-100 rounded-lg\">\r\n                                <table\r\n                                    class=\"w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 shadow\">\r\n                                    <tbody>\r\n                                        <tr class=\"bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700\">\r\n                                            <td scope=\"row\"\r\n                                                class=\"md:ps-6 ps-2 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white\">\r\n                                                ${language === 'vi' ? `Mã đơn hàng` : `Order code`}\r\n                                            </td>\r\n                                            <td class=\"md:px-6 px-2 py-2\">\r\n                                                <span\r\n                                                    class=\"font-extrabold text-lg text-nmt-500\">${_orderAvailable?.OrderCode}</span>\r\n                                            </td>\r\n                                        </tr>\r\n                                        <tr class=\"bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700\">\r\n                                            <td scope=\"row\"\r\n                                                class=\"md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white\">\r\n                                                ${language === 'vi' ? `Ngày đặt` : `Order date`}\r\n                                            </td>\r\n                                            <td class=\"md:px-6 px-2 py-2\">\r\n                                                <span\r\n                                                    class=\"text-base text-gray-700 dark:text-gray-400\">${_orderAvailable?.TimeCreate}</span>\r\n                                            </td>\r\n                                        </tr>\r\n                                        <tr class=\"bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700\">\r\n                                            <td scope=\"row\"\r\n                                                class=\"md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white\">\r\n                                                ${language === 'vi' ? `Tình trạng` : `Status`}\r\n                                            </td>\r\n                                            <td class=\"md:px-6 px-2 py-2\">\r\n                                                <span\r\n                                                    class=\"text-base text-gray-700 dark:text-gray-400 flex items-center flex-row gap-2\">\r\n                                                    ${_orderAvailable?.Status === 0 ? html `\r\n                                                    <span class=\"text-red-600\">\r\n                                                        ${language === 'vi' ? `Chờ thanh toán` : `Pending payment`}</span>\r\n                                                    <button @click=${rePayment}\r\n                                                        class=\"h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-lg text-sm md:px-6 px-3 md:py-3 py-1.5 text-center me-2\">\r\n                                                        ${language === 'vi' ? `Thanh toán ngay` : `Pay now`}\r\n                                                    </button>\r\n                                                    ` : _orderAvailable?.Status === 1 ? html `\r\n                                                    <span class=\"text-green-600\">${language === 'vi' ? `Đã thanh toán` : `Paid`}</span>\r\n                                                    ` : _orderAvailable?.Status === -1 ? html `\r\n                                                    <span class=\"text-red-600\">${language === 'vi' ? `Đã hủy` : `Cancelled`}</span>\r\n                                                    ` : ''}\r\n                                                </span>\r\n                                            </td>\r\n                                        </tr>\r\n                                        <tr class=\"bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700\">\r\n                                            <td scope=\"row\"\r\n                                                class=\"md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white\">\r\n                                                ${language === 'vi' ? `Giá trị đơn hàng` : `Order value`}\r\n                                            </td>\r\n                                            <td class=\"md:px-6 px-2 py-2\">\r\n                                                <span class=\"font-extrabold text-lg text-nmt-500\">${formatNumber(_orderDetails?.totalPrice, _convertedVND, language)} ${_currencySymbol}</span>\r\n                                            </td>\r\n                                        </tr>\r\n                                        <tr class=\"bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700\">\r\n                                            <td scope=\"row\"\r\n                                                class=\"md:ps-6 ps-2 py-2 font-medium text-gray-900 md:whitespace-nowrap dark:text-white\">\r\n                                                ${language === 'vi' ? `Hình thức thanh toán` : `Payment method`}\r\n                                            </td>\r\n                                            <td class=\"md:px-6 px-2 py-2\">\r\n                                            ${_orderAvailable?.PaymentMethod.includes('bank-transfer') ? html `\r\n                                            <div class=\"flex-1\">\r\n                                                    <label for=\"bank-transfer\"\r\n                                                        class=\"items-center  cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\"\r\n                                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                            stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                            stroke-linejoin=\"round\"\r\n                                                            class=\"mr-2 h-5 w-5 text-nmt-600 inline-flex\">\r\n                                                            <rect width=\"20\" height=\"16\" x=\"2\" y=\"6\" rx=\"2\"></rect>\r\n                                                            <path d=\"M12 3v10\"></path>\r\n                                                            <path d=\"m5 9 7-4 7 4\"></path>\r\n                                                        </svg>\r\n                                                        ${language === 'vi' ? `Chuyển Khoản Ngân Hàng` : `Bank Transfer`}\r\n                                                    </label>\r\n                                                    <p class=\"text-sm text-gray-500 mt-1\">${language === 'vi' ? `Thanh toán trực tiếp từ tài khoản ngân hàng của bạn` : `Pay directly from your bank account`}</p>\r\n\r\n                                                    <div class=\"mt-4 space-y-4 pt-3 border-t border-nmt-100\">\r\n                                                        <div class=\"bg-nmt-100 p-3 rounded-md text-sm\">\r\n                                                            <p class=\"font-medium\">${language === 'vi' ? `Thông tin chuyển khoản` : `Transfer information`}</p>\r\n                                                        </div>\r\n\r\n                                                        <div class=\"space-y-3\">\r\n                                                            <div class=\"grid grid-cols-3 gap-2 text-sm\">\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? `Chủ Tài Khoản:` : `Account Holder:`}</div>\r\n                                                                <div class=\"col-span-2 font-medium\">\r\n                                                                    ${_PaymentNote?.banksInfo[0]?.accountHolder}\r\n                                                                </div>\r\n\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? `Ngân hàng:` : `Bank:`}</div>\r\n                                                                <div class=\"col-span-2 font-medium\"> ${_PaymentNote?.banksInfo[0]?.bankName} </div>\r\n\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? `Chi nhánh:` : `Branch:`}</div>\r\n                                                                <div class=\"col-span-2 font-medium\"> ${_PaymentNote?.banksInfo[0]?.branch}</div>\r\n\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? `Số tài khoản:` : `Account Number:`}</div>\r\n                                                                <div class=\"col-span-2 font-medium\"> ${_PaymentNote?.banksInfo[0]?.accountNumber}</div>\r\n\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? `Nội dung CK:` : `Transfer Content:`}</div>\r\n                                                                <div class=\"col-span-2 font-medium\">${_PaymentNote?.transferContent} ${autoFillOrderCode ? _orderAvailable?.OrderCode : ''}</div>\r\n                                                                \r\n                                                                ${_PaymentNote?.banksInfo[0]?.qrImageUrl ? html `\r\n                                                                <div class=\"text-gray-600\">${language === 'vi' ? 'QR Code:' : 'QR Code:'}</div>\r\n                                                                <div class=\"col-span-2 font-medium\">\r\n                                                                    <img src=\"${_PaymentNote?.banksInfo[0]?.qrImageUrl}\" alt=\"${_PaymentNote?.banksInfo[0]?.bankName}\" class=\"h-36 rounded-md bg-gray-100 shadow-md\" />\r\n                                                                </div>\r\n                                                                ` : ''}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div\r\n                                                            class=\"bg-nmt-50 border border-nmt-200 p-3 rounded-md mt-4\">\r\n                                                            <p class=\"text-sm font-medium text-nmt-800\">${language === 'vi' ? `Hướng dẫn xác nhận thanh toán:` : `Payment confirmation instructions:`}</p>\r\n                                                            <p class=\"text-sm mt-2\"> ${_PaymentNote?.note}</p>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            ` : _orderAvailable?.PaymentMethod.includes('cash') ? html `\r\n                                            <div class=\"flex-1\">\r\n                                                    <label for=\"cash\"\r\n                                                        class=\"items-center cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\"\r\n                                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                            stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                            stroke-linejoin=\"round\"\r\n                                                            class=\"mr-2 h-5 w-5 text-nmt-600 inline-flex\">\r\n                                                            <rect width=\"20\" height=\"12\" x=\"2\" y=\"6\" rx=\"2\"></rect>\r\n                                                            <circle cx=\"12\" cy=\"12\" r=\"2\"></circle>\r\n                                                            <path d=\"M6 12h.01M18 12h.01\"></path>\r\n                                                        </svg>\r\n                                                        ${language === 'vi' ? `Thanh Toán Tiền Mặt` : `Cash Payment`}\r\n                                                    </label>\r\n                                                    <p class=\"text-sm text-gray-500 mt-1\">${language === 'vi' ? `Thanh toán trực tiếp bằng tiền mặt tại quầy` : `Pay in cash at the counter`}</p>\r\n\r\n                                                    <div class=\"mt-4 space-y-4 pt-3 border-t border-nmt-100\">\r\n                                                        <div class=\"bg-nmt-100 p-3 rounded-md text-sm\">\r\n                                                            <p>${language === 'vi' ? `Thông tin thanh toán tiền mặt:` : `Cash payment information:`}</p>\r\n                                                        </div>\r\n\r\n                                                        <div class=\"space-y-3\">\r\n                                                            <div class=\"flex items-start gap-2\">\r\n                                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\"\r\n                                                                    height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\"\r\n                                                                    stroke=\"currentColor\" stroke-width=\"2\"\r\n                                                                    stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                                    class=\"mt-0.5 text-nmt-600 min-w-5\">\r\n                                                                    <path\r\n                                                                        d=\"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z\">\r\n                                                                    </path>\r\n                                                                    <circle cx=\"12\" cy=\"10\" r=\"3\"></circle>\r\n                                                                </svg>\r\n                                                                <div>\r\n                                                                    <p class=\"font-medium\">${language === 'vi' ? `Địa điểm thanh toán:` : `Payment location:`}</p>\r\n                                                                    <p class=\"text-sm text-gray-600\">${language === 'vi' ? `Quầy vé tại văn phòng đại lý của chúng tôi:` : `Ticket counter at our agency's office:`}\r\n                                                                         <span class=\"font-medium text-gray-800\">\r\n                                                                        ${_PaymentNote?.paymentAddress}\r\n                                                                    </span>\r\n                                                                    </p>\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                            <div class=\"flex items-start gap-2\">\r\n                                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\"\r\n                                                                    height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\"\r\n                                                                    stroke=\"currentColor\" stroke-width=\"2\"\r\n                                                                    stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                                    class=\"mt-0.5 text-nmt-600\">\r\n                                                                    <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n                                                                    <polyline points=\"12 6 12 12 16 14\"></polyline>\r\n                                                                </svg>\r\n                                                                <div>\r\n                                                                    <p class=\"font-medium\">${language === 'vi' ? `Thời gian:` : `Time:`}</p>\r\n                                                                    <ul class=\"list-disc pl-6 space-y-1\">\r\n                                                                        <li>${_PaymentNote?.paymentDeadline}</li>\r\n                                                                        <li>${_PaymentNote?.workingHours}</li>\r\n                                                                    </ul>\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                            <div class=\"flex items-start gap-2\">\r\n                                                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\"\r\n                                                                    height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\"\r\n                                                                    stroke=\"currentColor\" stroke-width=\"2\"\r\n                                                                    stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                                                                    class=\"mt-0.5 text-nmt-600\">\r\n                                                                    <rect width=\"18\" height=\"18\" x=\"3\" y=\"3\" rx=\"2\"\r\n                                                                        ry=\"2\">\r\n                                                                    </rect>\r\n                                                                    <line x1=\"9\" x2=\"15\" y1=\"9\" y2=\"9\"></line>\r\n                                                                    <line x1=\"9\" x2=\"15\" y1=\"15\" y2=\"15\"></line>\r\n                                                                </svg>\r\n                                                                <div>\r\n                                                                    <p class=\"font-medium\">${language === 'vi' ? `Giấy tờ cần mang theo:` : `Documents to bring:`}</p>\r\n                                                                    <p class=\"text-sm text-gray-600\">\r\n                                                                    ${_PaymentNote?.note}\r\n                                                                    </p>\r\n                                                                </div>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            ` : _orderAvailable?.PaymentMethod?.includes('e-wallet') ? html `\r\n                                                <div class=\"flex-1\">\r\n                                                    <label for=\"e-wallet\"\r\n                                                        class=\"items-center cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\"\r\n                                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                            stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                            stroke-linejoin=\"round\"\r\n                                                            class=\"mr-2 h-5 w-5 text-nmt-600 inline-flex\">\r\n                                                            <rect width=\"20\" height=\"12\" x=\"2\" y=\"6\" rx=\"2\"></rect>\r\n                                                            <circle cx=\"12\" cy=\"12\" r=\"2\"></circle>\r\n                                                            <path d=\"M6 12h.01M18 12h.01\"></path>\r\n                                                        </svg>\r\n                                                        ${language === 'vi' ? `Thanh Toán Ví Điện Tử` : `E-Wallet Payment`}\r\n                                                    </label>\r\n                                                    <p class=\"text-sm text-gray-500 mt-1\">${language === 'vi' ? `Thanh toán trực tuyến bằng ví điện tử ${_orderAvailable?.PaymentMethod?.replace('e-wallet', '')}` : `Pay online using your e-wallet ${_orderAvailable?.PaymentMethod?.replace('e-wallet', '')}`}</p>\r\n                                                </div>\r\n                                            ` : _orderAvailable?.PaymentMethod === 'credit-card' ? html `\r\n                                            <div class=\"flex-1\">\r\n                                                    <label for=\"credit-card\"\r\n                                                        class=\"items-center cursor-pointer inline-block mb-2 text-base font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\"\r\n                                                            viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\"\r\n                                                            stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                            stroke-linejoin=\"round\"\r\n                                                            class=\"mr-2 h-5 w-5 text-nmt-600 inline-flex\">\r\n                                                            <rect width=\"20\" height=\"14\" x=\"2\" y=\"5\" rx=\"2\"></rect>\r\n                                                            <line x1=\"2\" x2=\"22\" y1=\"10\" y2=\"10\"></line>\r\n                                                        </svg>\r\n                                                        ${language === 'vi' ? `Thanh Toán Thẻ Tín Dụng` : `Credit Card Payment`}\r\n                                                    </label>\r\n                                                    <p class=\"text-sm text-gray-500 mt-1\">${language === 'vi' ? `Thanh toán trực tuyến bằng thẻ tín dụng` : `Pay online using credit card`}</p>\r\n                                                </div>\r\n                                            ` : ``}\r\n                                            </td>\r\n                                        </tr>\r\n                                    </tbody>\r\n                                </table>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"mt-10\">\r\n                            <h1\r\n                                class=\"inline-block mb-2 text-xl font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                ${language === 'vi' ? `Thông Tin Liên Hệ` : `Contact Information`}</h1>\r\n                            <div class=\"flex flex-col space-y-2 w-fit\">\r\n                                <h2\r\n                                    class=\"gap-2  text-base tracking-tight text-gray-600  dark:text-white grid grid-cols-2\">\r\n                                    <span class=\"font-bold  whitespace-nowrap\">${language === 'vi' ? `Họ và tên` : `Full name`} </span>\r\n                                    <span class=\" text-gray-500 dark:text-white font-semibold\">:\r\n                                        ${_orderAvailable?.CustomerName}</span>\r\n                                </h2>\r\n                                <h2\r\n                                    class=\"gap-2  text-base tracking-tight text-gray-600  dark:text-white grid grid-cols-2\">\r\n                                    <span class=\"font-bold  whitespace-nowrap\">${language === 'vi' ? `Số điện thoại` : `Phone number`} </span>\r\n                                    <span class=\" text-gray-500 dark:text-white font-semibold\">:\r\n                                        ${_orderAvailable?.PhoneNumber}</span>\r\n                                </h2>\r\n                                <h2\r\n                                    class=\"gap-2  text-base tracking-tight text-gray-600  dark:text-white grid grid-cols-2\">\r\n                                    <span class=\"font-bold  whitespace-nowrap\">${language === 'vi' ? `Email` : `Email`} </span>\r\n                                    <span class=\" text-gray-500 dark:text-white font-semibold inline-flex\">:\r\n                                        ${_orderAvailable?.Email}</span>\r\n                                </h2>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"w-full overflow-x-auto mt-10\">\r\n                            <h1\r\n                                class=\"inline-block mb-2 text-xl font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                                ${language === 'vi' ? `Danh Sách Khách` : `Passenger List`}</h1>\r\n                            <div class=\"relative overflow-x-auto shadow dark:shadow-lg sm:rounded-lg\">\r\n                                <table class=\"w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400\">\r\n                                    <thead\r\n                                        class=\"text-xs text-gray-700 uppercase bg-gray-200 dark:bg-gray-800 dark:text-gray-400\">\r\n                                        <tr>\r\n                                            <th scope=\"col\" class=\"px-6 py-3\">\r\n                                                ${language === 'vi' ? `Họ và tên` : `Full name`}\r\n                                            </th>\r\n                                            <th scope=\"col\" class=\"px-6 py-3\">\r\n                                                ${language === 'vi' ? `Ngày sinh` : `Date of birth`}\r\n                                            </th>\r\n                                            <th scope=\"col\" class=\"px-6 py-3\">\r\n                                                ${language === 'vi' ? `Giới tính` : `Gender`}\r\n                                            </th>\r\n                                        </tr>\r\n                                    </thead>\r\n                                    <tbody>\r\n                                    ${_orderDetails?.paxList.map((pax) => html `\r\n                                    <tr\r\n                                            class=\"odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\">\r\n                                            <td scope=\"row\"\r\n                                                class=\"md:px-6 px-2 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white\">\r\n                                                <div>\r\n                                                    ${pax?.fullname}\r\n                                                </div>\r\n                                                ${pax?.withInfant ? html `\r\n                                                <div>\r\n                                                    <span class=\"text-xs text-red-500 \">\r\n                                                        * ${language === 'vi' ? `Em bé` : `Infant`}: ${pax?.withInfant?.fullname} - ${pax?.withInfant?.birthday.day + '/' + pax?.withInfant?.birthday.month + '/' + pax?.withInfant?.birthday.year} - \r\n                                                    ${pax?.withInfant?.gender === 'MSTR' ? language === 'vi' ? `Bé trai` : `Boy` : pax?.withInfant?.gender === 'MISS' ? language === 'vi' ? `Bé gái` : `Girl` : language === 'vi' ? `Khác` : `Other`}\r\n                                                    \r\n                                                </div>\r\n                                                ` : ``}\r\n                                                <div>\r\n                                                ${pax.baggages.map((baggage) => html `\r\n                                                ${baggage?.SsrCode ? html `\r\n                                                <div class=\"text-xs text-gray-500 dark:text-gray-400\">${baggage?.type} - ${baggage?.WeightBag} KG</div>\r\n                                                ` : ``}\r\n                                                `)}\r\n                                                </div>\r\n                                            </td>\r\n                                            <td class=\"md:px-6 px-2 py-2\">\r\n                                                ${formatDateToString(pax?.birthday, language)}\r\n                                            </td>\r\n                                            <td class=\"md:px-6 px-2 py-2\">\r\n                                                ${pax.gender === 'MR' ? language === 'vi' ? `Nam` : `Male` : pax.gender === 'MRS' ? language === 'vi' ? `Nữ` : `Female` : language === 'vi' ? `Khác` : `Other`}\r\n                                            </td>\r\n                                        </tr>\r\n                                    `)}\r\n                                        \r\n                                    </tbody>\r\n                                </table>\r\n                            </div>\r\n\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-span-2 relative max-md:mt-4\">\r\n                        <div class=\"sticky top-24\">\r\n                            <div class=\"border-2 border-dashed border-nmt-500 rounded-lg md:p-4 p-2\">\r\n                                <h1\r\n                                    class=\"w-full inline-block text-center text-xl font-extrabold tracking-tight text-nmt-500  dark:text-white\">\r\n                                    ${language === 'vi' ? `THÔNG TIN CHUYẾN BAY` : `FLIGHT INFORMATION`}</h1>\r\n                                <div>\r\n                                    <h2\r\n                                        class=\"mt-4 flex gap-2  text-base tracking-tight text-gray-600  dark:text-white\">\r\n                                        <span class=\"font-extrabold\">${language === 'vi' ? `Điểm Khởi Hành:` : `Departure point:`}</span> <strong\r\n                                            class=\"text-gray-500 dark:text-white font-semibold\">\r\n                                            ${_inforAirports[_orderAvailable?.Depart]?.cityName}\r\n                                            (${_orderAvailable?.Depart})\r\n                                        </strong>\r\n                                    </h2>\r\n                                    <h2 class=\"mt-2  gap-2  text-base tracking-tight text-gray-600  dark:text-white\">\r\n                                        <span class=\"font-extrabold  whitespace-nowrap\">${language === 'vi' ? `Điểm Đến:` : `Arrival point:`} </span>\r\n                                        <span class=\" text-gray-500 dark:text-white font-semibold\">\r\n\r\n                                            ${_inforAirports[_orderAvailable?.Arrival]?.cityName}\r\n                                            (${_orderAvailable?.Arrival})</span>\r\n                                    </h2>\r\n                                    <h2\r\n                                        class=\"mt-2 flex gap-2  text-base tracking-tight text-gray-600  dark:text-white\">\r\n                                        <span class=\"font-extrabold inline-flex whitespace-nowrap\">${language === 'vi' ? `Ngày khởi hành:` : `Departure date:`}</span>\r\n                                        <span class=\"inline-flex text-gray-500 dark:text-white font-semibold\">\r\n                                            ${_orderAvailable?.DepartDate}\r\n                                        </span>\r\n                                    </h2>\r\n                                    ${_orderAvailable?.ReturnDate ? html `\r\n                                    <h2 class=\"mt-2 flex gap-2  text-base tracking-tight text-gray-600  dark:text-white\">\r\n                                        <span class=\"font-extrabold inline-flex whitespace-nowrap\">${language === 'vi' ? `Ngày trở về:` : `Return date:`}</span>\r\n                                        <span class=\"inline-flex text-gray-500 dark:text-white font-semibold\">\r\n                                            ${_orderAvailable?.ReturnDate}\r\n                                        </span>\r\n                                    </h2>\r\n                                    ` : ``}\r\n                                    \r\n                                    <h2\r\n                                        class=\"mt-2 flex flex-col gap-2  text-base tracking-tight text-gray-600  dark:text-white\">\r\n                                        <span class=\"font-extrabold inline-flex whitespace-nowrap\">${language === 'vi' ? `Chi tiết chuyến bay:` : `Flight details:`}</span>\r\n                                        ${_orderDetails.full?.InventoriesSelected.length > 0 ? html `\r\n                                        <div class=\"w-full space-y-10\">\r\n                                        ${_orderDetails.full?.InventoriesSelected.map((itinerarySelected, index) => html `\r\n                                        <div class=\"w-full bg-gray-100 my-4 \">\r\n                                                <!-- start flight infor -->\r\n                                                <div class=\"bg-white rounded-e-lg rounded-bl-lg \">\r\n                                                    <div class=\"py-[2px] flex gap-2\">\r\n                                                        <button\r\n                                                            class=\"h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-nmt-600 to-nmt-300 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-nmt-300 dark:focus:ring-nmt-800 rounded-md text-sm px-2 py-1 text-center me-2\">\r\n                                                            <span>\r\n                                                            ${_orderDetails.full?.InventoriesSelected.length > 1 ? html `${index % 2 === 1 ? language === 'vi' ? `Chiều về` : `Return` : language === 'vi' ? `Chiều đi` : `Departure`}` : ``}\r\n                                                            </span>\r\n                                                        </button>\r\n\r\n                                                        <span>\r\n                                                            ${formatDateTo_ddMMyyyy(itinerarySelected.segment.Legs[0]?.DepartureDate, language)}\r\n                                                            |\r\n                                                            ${language === 'vi' ? `Thời gian bay` : `Flight time`}\r\n                                                            ${getDurationByArray(itinerarySelected.segment.Legs)}\r\n                                                        </span>\r\n                                                    </div>\r\n                                                    <div class=\"w-full\">\r\n                                                    ${itinerarySelected.segment.Legs.map((leg, $index) => html `\r\n                                                    ${index > 0 ? html `\r\n                                                    <div\r\n                                                            class=\"relative flex flex-1 flex-row items-center justify-start min-h-[50px] w-full  before:content-[''] before:absolute before:border-l-[4px] before:border-dotted before:border-[#dadfe6] before:h-[30%] before:w-0 before:start-[68px] before:top-0 before:inline-block after:content-[''] after:absolute after:border-l-[4px] after:border-dotted after:border-[#dadfe6] after:h-[30%] after:w-0 after:start-[68px] after:top-[70%] after:inline-block\">\r\n                                                            <div class=\"flex py-4 ps-[80px] w-full\">\r\n                                                                <div\r\n                                                                    class=\"flex flex-row items-center justify-start w-full\">\r\n                                                                    <div\r\n                                                                        class=\"w-full text-sm py-2 px-1 bg-gray-100 rounded-lg \">\r\n                                                                        ${language === 'vi' ? `Trung chuyển tại` : `Transit at`}\r\n                                                                        ${_inforAirports[leg.DepartureCode]?.cityName}\r\n                                                                        ${convertDurationToHour(itinerarySelected.segment.Legs[$index].StopTime)}\r\n\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </div>\r\n                                                            <!-- icon -->\r\n                                                            <div\r\n                                                                class=\"absolute inline-block start-[62.5px] top-[calc(50%-8px)]\">\r\n                                                                <svg class=\"w-4 h-4 text-[#acb4bf] dark:text-white\"\r\n                                                                    aria-hidden=\"true\"\r\n                                                                    xmlns=\"http://www.w3.org/2000/svg\" width=\"24\"\r\n                                                                    height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                                                    <path stroke=\"currentColor\" stroke-linecap=\"round\"\r\n                                                                        stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                                                                        d=\"M17.651 7.65a7.131 7.131 0 0 0-12.68 3.15M18.001 4v4h-4m-7.652 8.35a7.13 7.13 0 0 0 12.68-3.15M6 20v-4h4\" />\r\n                                                                </svg>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    ` : ``}\r\n                                                    <div class=\"flex flex-col items-start overflow-hidden relative\">\r\n                                                            <div>\r\n                                                                <div class=\"flex flex-row items-center justify-start\">\r\n                                                                    <div\r\n                                                                        class=\"flex flex-col items-center text-[#0f294d] text-[16px]  leading-[24px] me-[43px] whitespace-nowrap w-[45px] font-extrabold  \">\r\n                                                                        <span>\r\n                                                                            ${getTimeFromDateTime(leg?.DepartureDate)}\r\n                                                                        </span>\r\n                                                                    </div>\r\n                                                                    <span\r\n                                                                        class=\"text-[#0f294d] text-[16px] font-semibold leading-[24px]\">\r\n                                                                        <span>(${leg?.DepartureCode})</span>\r\n                                                                        ${_inforAirports[leg?.DepartureCode]?.name}\r\n                                                                    </span>\r\n                                                                </div>\r\n\r\n                                                                <!-- class=\"flex flex-1 flex-row items-center justify-start min-h-[50px] w-full  before:content-[''] before:absolute before:bg-[#dadfe6] before:rounded-[2px] before:w-[4px] before:start-[68px] before:inline-block before:h-[32%] before:top-[5%] after:content-[''] after:absolute after:bg-[#dadfe6] after:rounded-[2px] after:w-[4px] after:h-[35%] after:start-[68px] after:bottom-[5%] after:inline-block\"> -->\r\n                                                                <div\r\n                                                                    class=\"flex flex-1 flex-row items-center justify-start min-h-[50px] w-full  before:content-[''] before:absolute before:bg-[#dadfe6] before:rounded-[2px] before:w-[4px] before:start-[68px] before:inline-block before:h-[80%] before:top-[10%] \">\r\n                                                                    <div\r\n                                                                        class=\"flex flex-row-reverse flex-wrap-reverse items-center justify-end w-[97px] overflow-hidden \">\r\n                                                                        <div\r\n                                                                            class=\"flex items-center justify-center bg-white  h-[24px]\">\r\n                                                                            <img src=\"${apiUrl}/assets/img/airlines/${leg?.OperatingAirlines}.png\"\r\n                                                                                class=\"h-[22px] pe-7 max-w-[86px]\">\r\n                                                                        </div>\r\n                                                                    </div>\r\n\r\n                                                                    <div class=\"flex md:py-4 py-3\">\r\n                                                                        <div\r\n                                                                            class=\"flex flex-row items-center justify-start\">\r\n                                                                            <span\r\n                                                                                class=\"md:text-sm text-xs text-[#8592a6]\">\r\n                                                                                ${leg?.OperatingAirlinesName}${leg?.OperatingAirlines + leg?.FlightNumber}${itinerarySelected.inventorySelected?.BookingInfos[$index]?.FareType || itinerarySelected.inventorySelected?.BookingInfos[$index]?.CabinName}\r\n                                                                            </span>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n\r\n                                                                <div class=\"flex flex-row items-center justify-start\">\r\n                                                                    <div\r\n                                                                        class=\"flex flex-col items-center text-[#0f294d] font-extrabold text-[16px]  leading-[24px] me-[43px] whitespace-nowrap w-[45px]\">\r\n                                                                        <span>${getTimeFromDateTime(leg?.ArrivalDate)}</span>\r\n                                                                    </div>\r\n                                                                    <span\r\n                                                                        class=\"text-nmt-600 text-[16px] font-semibold leading-[24px]\">\r\n                                                                        <span>(${leg?.ArrivalCode})</span>\r\n                                                                        ${_inforAirports[leg?.ArrivalCode]?.name}\r\n                                                                    </span>\r\n                                                                </div>\r\n                                                            </div>\r\n                                                        </div>\r\n\r\n                                                    `)}\r\n                                                    </div>\r\n                                                </div>\r\n                                                <!-- end flight infor -->\r\n                                            </div>\r\n                                        `)}\r\n                                        </div>\r\n                                        ` : html ``}\r\n                                    </h2>\r\n\r\n\r\n                                    <h2\r\n                                        class=\"mt-2  gap-2 text-center  text-base tracking-tight text-nmt-500  dark:text-white font-extrabold  whitespace-nowrap\">\r\n                                        --------OOOOO-------\r\n                                    </h2>\r\n                                </div>\r\n                            </div>\r\n\r\n\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                ${_orderDetails.full?.InventoriesSelected.length > 0 ? html `\r\n                <div class=\"w-full space-y-10 my-8\">\r\n                ${_orderDetails.full?.InventoriesSelected.map((itinerarySelected, index) => html `\r\n                ${itinerarySelected.segment.Legs.map((leg, $index) => html `\r\n                <div class=\"space-y-4  bg-card mb-8  \">\r\n                        <div class=\"max-md:overflow-x-scroll w-auto h-max  max-md:overflow-y-hidden max-md:pb-2\">\r\n                            <div class=\"md:w-full w-max m-auto \">\r\n                                <div class=\"grid grid-cols-10 rounded-lg  relative \">\r\n                                    <div class=\"col-span-4 shadow-lg  relative rounded-s-lg \">\r\n                                        <div\r\n                                            class=\"w-full h-[37px] flex justify-between items-center  md:px-8 px-4 py-1 bg-gradient-to-l from-nmt-600 via-nmt-600 to-nmt-300 rounded-tl-lg\">\r\n                                            <span class=\"text-white md:text-lg text-base font-extrabold line-clamp-1\">\r\n                                                <svg class=\"fill-white w-6 h-6 inline-block\"\r\n                                                    xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 640 512\">\r\n                                                    <path\r\n                                                        d=\"M381 114.9L186.1 41.8c-16.7-6.2-35.2-5.3-51.1 2.7L89.1 67.4C78 73 77.2 88.5 87.6 95.2l146.9 94.5L136 240 77.8 214.1c-8.7-3.9-18.8-3.7-27.3 .6L18.3 230.8c-9.3 4.7-11.8 16.8-5 24.7l73.1 85.3c6.1 7.1 15 11.2 24.3 11.2l137.7 0c5 0 9.9-1.2 14.3-3.4L535.6 212.2c46.5-23.3 82.5-63.3 100.8-112C645.9 75 627.2 48 600.2 48l-57.4 0c-20.2 0-40.2 4.8-58.2 14L381 114.9zM0 480c0 17.7 14.3 32 32 32l576 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 448c-17.7 0-32 14.3-32 32z\" />\r\n                                                </svg>\r\n                                                <span class=\"ms-2\">${_inforAirports[leg.DepartureCode]?.cityName} - ${_inforAirports[leg.ArrivalCode]?.cityName}</span>\r\n                                            </span>\r\n                                        </div>\r\n                                        <div\r\n                                            class=\" flex flex-col justify-start items-center text-gray-800 bg-white  rounded-bl-lg pb-4\">\r\n                                            <div class=\"w-full h-12 flex justify-center items-center mt-4\">\r\n                                                <img src=\"${apiUrl}/assets/img/airlines/${leg.OperatingAirlines}.png\" class=\"h-full w-auto\">\r\n                                            </div>\r\n                                            <div\r\n                                                class=\"w-full flex flex-col justify-start items-start mt-2 space-y-2 md:px-8 px-4\">\r\n                                                <div class=\"flex flex-col \">\r\n                                                    <span class=\"text-xs font-semibold text-gray-700\">${language === 'vi' ? `HÃNG CHUYÊN CHỞ/ CARRIER` : `CARRIER`}</span>\r\n                                                    <span\r\n                                                        class=\"text-base font-bold uppercase\">${leg.OperatingAirlinesName}</span>\r\n                                                </div>\r\n\r\n                                                <div class=\"flex flex-col \">\r\n                                                    <span class=\"text-xs font-semibold text-gray-700\">${language === 'vi' ? `SỐ HIỆU/ FLIGHT` : `FLIGHT`}</span>\r\n                                                    <span class=\"text-base font-bold\">\r\n                                                        ${leg.OperatingAirlines + leg.FlightNumber}\r\n                                                    </span>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"col-span-6 relative  flex flex-row rounded-br-lg\">\r\n                                        <div class=\"w-3.5 min-w-3.5 h-full m-auto  bg-transparent z-20 \">\r\n                                            <div class=\"w-3.5 h-3 bg-nmt-600 mask-top-circle-cut\">\r\n                                            </div>\r\n                                            <div\r\n                                                class=\"w-3.5 h-[calc(100%-1.5rem)] bg-white  flex justify-center items-center relative z-10\">\r\n                                                <div\r\n                                                    style=\"background-image: linear-gradient(to bottom, #fff 50%, #ea580c 50%) !important; width: 1px; height: 100%; position: relative; background-size: 100% 10px;\">\r\n                                                    <div\r\n                                                        class=\"absolute md:top-0 -top-[1px] w-6 -left-2 md:h-[25px] h-[26px] bg-nmt-600\">\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div class=\"w-3.5 h-3 bg-white mask-bottom-circle-cut\">\r\n                                            </div>\r\n                                        </div>\r\n                                        <div\r\n                                            class=\"w-full h-full shadow-lg flex justify-between items-center cursor-pointer relative z-10 rounded-br-lg\">\r\n                                            <div\r\n                                                class=\"w-full h-full text-center cursor-pointer bg-white rounded-br-lg flex flex-col \">\r\n                                                <div class=\"w-full  text-center cursor-pointer\">\r\n                                                    <div\r\n                                                        class=\"w-full flex justify-end items-center h-[37px]  md:px-8 px-4 py-1  bg-gradient-to-l from-nmt-700 via-nmt-700 to-nmt-600 rounded-tr-lg\">\r\n                                                        <span class=\"text-white md:text-lg text-base font-extrabold\">\r\n                                                            (${getDayInWeek(leg.DepartureDate)}) - ${formatddMMyyyy(leg.DepartureDate)}\r\n                                                        </span>\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div\r\n                                                    class=\"flex justify-center items-center w-full h-full px-4 gap-2 my-4\">\r\n                                                    <div class=\"flex flex-col justify-start items-start\">\r\n                                                        <strong class=\"text-3xl font-extrabold text-nmt-600\">\r\n                                                            ${getTimeFromDateTime(leg.DepartureDate)}\r\n                                                        </strong>\r\n                                                        <strong\r\n                                                            class=\"md:text-base text-sm font-semibold text-gray-600\">\r\n                                                            ${formatddMMyyyy(leg.DepartureDate)}\r\n                                                        </strong>\r\n                                                        <strong class=\"text-lg font-bold text-gray-800 text-nowrap\">\r\n                                                            ${leg.DepartureCode + ' - ' + _inforAirports[leg.DepartureCode]?.cityName}\r\n                                                        </strong>\r\n                                                        <strong class=\"md:text-base text-sm font-semibold text-gray-600\">\r\n                                                            ${language === 'vi' ? `Nhà ga:` : `Terminal:`} ${leg.DepartureTerminal || '-'}\r\n                                                        </strong>\r\n                                                    </div>\r\n                                                    <div class=\"w-full flex-col justify-center items-center\">\r\n                                                        <div class=\"w-full text-lg text-center font-semibold -mb-2\">\r\n                                                            ${leg.Equipment}\r\n                                                        </div>\r\n                                                        <div class=\"w-full flex justify-center items-center md:px-6\">\r\n                                                            <div class=\"w-full h-[3px] rounded-full bg-nmt-600 \">\r\n                                                            </div>\r\n                                                            <svg xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                                class=\"w-6 h-6 fill-nmt-600 inline-block ml-[1px]\"\r\n                                                                viewBox=\"0 0 576 512\">\r\n                                                                <path\r\n                                                                    d=\"M482.3 192c34.2 0 93.7 29 93.7 64c0 36-59.5 64-93.7 64l-116.6 0L265.2 495.9c-5.7 10-16.3 16.1-27.8 16.1l-56.2 0c-10.6 0-18.3-10.2-15.4-20.4l49-171.6L112 320 68.8 377.6c-3 4-7.8 6.4-12.8 6.4l-42 0c-7.8 0-14-6.3-14-14c0-1.3 .2-2.6 .5-3.9L32 256 .5 145.9c-.4-1.3-.5-2.6-.5-3.9c0-7.8 6.3-14 14-14l42 0c5 0 9.8 2.4 12.8 6.4L112 192l102.9 0-49-171.6C162.9 10.2 170.6 0 181.2 0l56.2 0c11.5 0 22.1 6.2 27.8 16.1L365.7 192l116.6 0z\" />\r\n                                                            </svg>\r\n                                                        </div>\r\n                                                        <div class=\"w-full text-lg text-center font-semibold -mt-2\">\r\n                                                            ${getDuration(leg)}\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div class=\"flex flex-col justify-end items-end\">\r\n                                                        <strong class=\"text-3xl font-bold \">\r\n                                                            ${getTimeFromDateTime(leg.ArrivalDate)}\r\n                                                        </strong>\r\n                                                        <strong\r\n                                                            class=\"md:text-base text-sm font-semibold text-gray-700\">\r\n                                                            ${formatddMMyyyy(leg.ArrivalDate)}\r\n                                                        </strong>\r\n                                                        <strong class=\"text-lg  font-bold text-gray-800 text-nowrap\">\r\n                                                            ${_inforAirports[leg.ArrivalCode]?.cityName + ' - ' + leg.ArrivalCode}\r\n                                                        </strong>\r\n                                                        <strong class=\"md:text-base text-sm font-semibold text-gray-700\">\r\n                                                            ${language === 'vi' ? `Nhà ga:` : `Terminal:`} ${leg.ArrivalTerminal || '-'}\r\n                                                        </strong>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <div class=\"w-full rounded-br-lg\">\r\n                                                    <div\r\n                                                        style=\"width: 100%; height: 1px; background-image: linear-gradient(to right, transparent 50%, #fb7740 50%); background-size: 10px 100%;\">\r\n                                                    </div>\r\n                                                    <div\r\n                                                        class=\"w-full rounded-br-lg flex justify-between items-center px-4 pb-2\">\r\n                                                        <span>\r\n                                                            <span>${language === 'vi' ? `Hành lý xách tay:` : `Carry-on baggage:`} </span>\r\n                                                            ${itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandBaggage > 1 && itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandWeightBag !== 0 ? html `\r\n                                                            <strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandBaggage}x</strong>\r\n                                                            ` : ``}\r\n                                                            <strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.HandWeightBag}KG</strong>\r\n                                                        </span>\r\n                                                        <span>\r\n                                                            <span>${language === 'vi' ? `Hành lý ký gửi:` : `Checked baggage:`} </span>\r\n                                                            ${itinerarySelected.inventorySelected?.BookingInfos[$index]?.BagPieces > 1 && itinerarySelected.inventorySelected?.BookingInfos[$index]?.WeightBag !== 0 ? html `\r\n                                                            <strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.BagPieces}x</strong>\r\n                                                            ` : ``}\r\n                                                            ${itinerarySelected.inventorySelected?.BookingInfos[$index]?.WeightBag === 0 ? html `\r\n                                                            <span>${language === 'vi' ? `Không bao gồm` : `Not included`}</span>\r\n                                                            ` : html `<strong>${itinerarySelected.inventorySelected?.BookingInfos[$index]?.WeightBag}KG</strong>`}\r\n                                                        </span>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                    <!-- end bottom -->\r\n\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                `)}\r\n                `)}\r\n                </div>\r\n                ` : html ``}\r\n\r\n                <div class=\" bg-white border shadow-md rounded-lg overflow-hidden\">\r\n\r\n                    <div class=\"px-6 pt-6 space-y-2 \">\r\n                        <div class=\"space-y-2 text-sm\">\r\n                            <h2 class=\"text-lg font-semibold text-nmt-700 border-b border-nmt-200 pb-2\">${language === 'vi' ? `Thời gian check-in` : `Check-in time`}</h2>\r\n                            <ul class=\"list-disc pl-5 space-y-1\">\r\n                                <li>${language === 'vi' ? `Quý khách vui lòng tới sân bay trước` : `Please arrive at the airport before`} \r\n                                    <span class=\"font-medium text-nmt-600\">${__NoteModel?.TimeCheckIn?.Domestic}</span> \r\n                                    (${language === 'vi' ? `nội địa` : `domestic`}), \r\n                                    hoặc <span class=\"font-medium text-nmt-600\">${__NoteModel?.TimeCheckIn?.International}</span> \r\n                                    (${language === 'vi' ? `quốc tế` : `international`}) \r\n                                    ${language === 'vi' ? `để làm thủ tục check-in` : `to complete check-in procedures`}.</li>\r\n                            </ul>\r\n                        </div>\r\n                        <div class=\"space-y-2\">\r\n                            <h2 class=\"text-lg font-semibold text-nmt-700 border-b border-nmt-200 pb-2\">${language === 'vi' ? `Giấy tờ tùy thân` : `Identity documents`}</h2>\r\n                            <ul class=\"list-disc pl-5 space-y-1 text-sm\">\r\n                                ${__NoteModel?.IdentityDocuments?.map((identityDoc) => html `<li>${identityDoc.value}</li>`)}\r\n                            </ul>\r\n                        </div>\r\n                        <div class=\"space-y-2\">\r\n                            <h2 class=\"text-lg font-semibold text-nmt-700 border-b border-nmt-200 pb-2\">${language === 'vi' ? `Quy định đặc biệt` : `Special rules`}</h2>\r\n                            <ul class=\"list-disc pl-5 space-y-2 text-sm\">\r\n                            ${__NoteModel?.SpecialRules?.map((specialRule) => html `<li>${specialRule.value}</li>`)}\r\n                            </ul>\r\n                        </div>\r\n                        <p class=\"text-nmt-800 font-bold text-lg text-center pb-4\">${language === 'vi' ? `CHÚC QUÝ KHÁCH CÓ CHUYẾN BAY TỐT ĐẸP!` : `HAVE A GOOD TRIP!`}</p>\r\n\r\n                    </div>\r\n    \r\n                </div>\r\n            ` : html `            <div class=\"flex flex-col text-center w-full md:mb-10 \">\r\n                    <h1 class=\"inline-block mb-2 text-3xl font-extrabold tracking-tight text-gray-900 dark:text-white\">\r\n                        ${language === 'vi' ? `KHÔNG TÌM THẤY ĐƠN HÀNG` : `ORDER NOT FOUND`}\r\n                        <div class=\"italic text-lg font-normal\">${language === 'vi' ? `(Vui lòng kiểm tra lại mã đơn hàng)` : `(Please check the order code)`}</div>\r\n                    </h1>\r\n                </div>\r\n            `}                \r\n            `}\r\n            </div>\r\n        </section>\r\n    </div>\r\n</div>\r\n`;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t=t=>(e,o)=>{void 0!==o?o.addInitializer((()=>{customElements.define(t,e)})):customElements.define(t,e)};export{t as customElement};\n//# sourceMappingURL=custom-element.js.map\n", "import{defaultConverter as t,notEqual as e}from\"../reactive-element.js\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */const o={attribute:!0,type:String,converter:t,reflect:!1,hasChanged:e},r=(t=o,e,r)=>{const{kind:n,metadata:i}=r;let s=globalThis.litPropertyMetadata.get(i);if(void 0===s&&globalThis.litPropertyMetadata.set(i,s=new Map),s.set(r.name,t),\"accessor\"===n){const{name:o}=r;return{set(r){const n=e.get.call(this);e.set.call(this,r),this.requestUpdate(o,n,t)},init(e){return void 0!==e&&this.P(o,void 0,t),e}}}if(\"setter\"===n){const{name:o}=r;return function(r){const n=this[o];e.call(this,r),this.requestUpdate(o,n,t)}}throw Error(\"Unsupported decorator location: \"+n)};function n(t){return(e,o)=>\"object\"==typeof o?r(t,e,o):((t,e,o)=>{const r=e.hasOwnProperty(o);return e.constructor.createProperty(o,r?{...t,wrapped:!0}:t),r?Object.getOwnPropertyDescriptor(e,o):void 0})(t,e,o)}export{n as property,r as standardProperty};\n//# sourceMappingURL=property.js.map\n", "import{property as t}from\"./property.js\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */function r(r){return t({...r,state:!0,attribute:!1})}export{r as state};\n//# sourceMappingURL=state.js.map\n", "import { environment } from \"../environments/environment\";\nimport { fetchWithDeviceIdandApiKey } from \"../utils/deviceUtils\";\nconst apiUrl = environment.apiUrl;\nexport const getAirportInfoByCode = async (airportsCode, language, apiKey) => {\n    const requestBody = {\n        airportsCode: airportsCode.join(';'),\n        language: language\n    };\n    try {\n        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/airport-info`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(requestBody)\n        }, apiKey);\n        if (!response.ok) {\n            throw response;\n        }\n        return await response.json();\n    }\n    catch (error) {\n        throw error;\n    }\n};\nexport const phones = async () => {\n    const response = await fetch(`${apiUrl}/api/World/phones`, {\n        method: 'GET'\n    });\n    return response.json();\n};\nexport const getAirportsDefault = async (language, apiKey) => {\n    const requestBody = {\n        language: language\n    };\n    try {\n        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/airports-default`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(requestBody)\n        }, apiKey);\n        if (!response.ok) {\n            throw response;\n        }\n        return await response.json();\n    }\n    catch (error) {\n        throw error;\n    }\n};\nexport const getFeatures = async (features, apiKey) => {\n    try {\n        const response = await fetchWithDeviceIdandApiKey(`${apiUrl}/api/Library/feature/${features}`, {\n            method: 'GET',\n            headers: { 'Content-Type': 'application/json' },\n        }, apiKey);\n        if (!response.ok) {\n            throw response;\n        }\n        return await response.json();\n    }\n    catch (error) {\n        throw error;\n    }\n};\nexport const searchAirport = async (request) => {\n    const requestBody = JSON.stringify(request);\n    const response = await fetch(`${apiUrl}/api/World/flight/airport-search`, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: requestBody\n    });\n    return response.json();\n};\n", "export const colors = {\n    inherit: 'inherit',\n    current: 'currentColor',\n    transparent: 'transparent',\n    black: '#000',\n    white: '#fff',\n    slate: {\n        '50': '#f8fafc',\n        '100': '#f1f5f9',\n        '200': '#e2e8f0',\n        '300': '#cbd5e1',\n        '400': '#94a3b8',\n        '500': '#64748b',\n        '600': '#475569',\n        '700': '#334155',\n        '800': '#1e293b',\n        '900': '#0f172a',\n        '950': '#020617'\n    },\n    gray: {\n        '50': '#f9fafb',\n        '100': '#f3f4f6',\n        '200': '#e5e7eb',\n        '300': '#d1d5db',\n        '400': '#9ca3af',\n        '500': '#6b7280',\n        '600': '#4b5563',\n        '700': '#374151',\n        '800': '#1f2937',\n        '900': '#111827',\n        '950': '#030712'\n    },\n    zinc: {\n        '50': '#fafafa',\n        '100': '#f4f4f5',\n        '200': '#e4e4e7',\n        '300': '#d4d4d8',\n        '400': '#a1a1aa',\n        '500': '#71717a',\n        '600': '#52525b',\n        '700': '#3f3f46',\n        '800': '#27272a',\n        '900': '#18181b',\n        '950': '#09090b'\n    },\n    neutral: {\n        '50': '#fafafa',\n        '100': '#f5f5f5',\n        '200': '#e5e5e5',\n        '300': '#d4d4d4',\n        '400': '#a3a3a3',\n        '500': '#737373',\n        '600': '#525252',\n        '700': '#404040',\n        '800': '#262626',\n        '900': '#171717',\n        '950': '#0a0a0a'\n    },\n    stone: {\n        '50': '#fafaf9',\n        '100': '#f5f5f4',\n        '200': '#e7e5e4',\n        '300': '#d6d3d1',\n        '400': '#a8a29e',\n        '500': '#78716c',\n        '600': '#57534e',\n        '700': '#44403c',\n        '800': '#292524',\n        '900': '#1c1917',\n        '950': '#0c0a09',\n    },\n    red: {\n        '50': '#fef2f2',\n        '100': '#fee2e2',\n        '200': '#fecaca',\n        '300': '#fca5a5',\n        '400': '#f87171',\n        '500': '#ef4444',\n        '600': '#dc2626',\n        '700': '#b91c1c',\n        '800': '#991b1b',\n        '900': '#7f1d1d',\n        '950': '#450a0a'\n    },\n    orange: {\n        '50': '#fff7ed',\n        '100': '#ffedd5',\n        '200': '#fed7aa',\n        '300': '#fdba74',\n        '400': '#fb923c',\n        '500': '#f97316',\n        '600': '#ea580c',\n        '700': '#c2410c',\n        '800': '#9a3412',\n        '900': '#7c2d12',\n        '950': '#431407'\n    },\n    amber: {\n        '50': '#fffbeb',\n        '100': '#fef3c7',\n        '200': '#fde68a',\n        '300': '#fcd34d',\n        '400': '#fbbf24',\n        '500': '#f59e0b',\n        '600': '#d97706',\n        '700': '#b45309',\n        '800': '#92400e',\n        '900': '#78350f',\n        '950': '#451a03'\n    },\n    yellow: {\n        '50': '#fefce8',\n        '100': '#fef9c3',\n        '200': '#fef08a',\n        '300': '#fde047',\n        '400': '#facc15',\n        '500': '#eab308',\n        '600': '#ca8a04',\n        '700': '#a16207',\n        '800': '#854d0e',\n        '900': '#713f12',\n        '950': '#422006'\n    },\n    lime: {\n        '50': '#f7fee7',\n        '100': '#ecfccb',\n        '200': '#d9f99d',\n        '300': '#bef264',\n        '400': '#a3e635',\n        '500': '#84cc16',\n        '600': '#65a30d',\n        '700': '#4d7c0f',\n        '800': '#3f6212',\n        '900': '#365314',\n        '950': '#1a2e05',\n    },\n    green: {\n        '50': '#f0fdf4',\n        '100': '#dcfce7',\n        '200': '#bbf7d0',\n        '300': '#86efac',\n        '400': '#4ade80',\n        '500': '#22c55e',\n        '600': '#16a34a',\n        '700': '#15803d',\n        '800': '#166534',\n        '900': '#14532d',\n        '950': '#052e16'\n    },\n    emerald: {\n        '50': '#ecfdf5',\n        '100': '#d1fae5',\n        '200': '#a7f3d0',\n        '300': '#6ee7b7',\n        '400': '#34d399',\n        '500': '#10b981',\n        '600': '#059669',\n        '700': '#047857',\n        '800': '#065f46',\n        '900': '#064e3b',\n        '950': '#022c22'\n    },\n    teal: {\n        '50': '#f0fdfa',\n        '100': '#ccfbf1',\n        '200': '#99f6e4',\n        '300': '#5eead4',\n        '400': '#2dd4bf',\n        '500': '#14b8a6',\n        '600': '#0d9488',\n        '700': '#0f766e',\n        '800': '#115e59',\n        '900': '#134e4a',\n        '950': '#042f2e'\n    },\n    cyan: {\n        '50': '#ecfeff',\n        '100': '#cffafe',\n        '200': '#a5f3fc',\n        '300': '#67e8f9',\n        '400': '#22d3ee',\n        '500': '#06b6d4',\n        '600': '#0891b2',\n        '700': '#0e7490',\n        '800': '#155e75',\n        '900': '#164e63',\n        '950': '#083344'\n    },\n    sky: {\n        '50': '#f0f9ff',\n        '100': '#e0f2fe',\n        '200': '#bae6fd',\n        '300': '#7dd3fc',\n        '400': '#38bdf8',\n        '500': '#0ea5e9',\n        '600': '#0284c7',\n        '700': '#0369a1',\n        '800': '#075985',\n        '900': '#0c4a6e',\n        '950': '#082f49'\n    },\n    blue: {\n        '50': '#eff6ff',\n        '100': '#dbeafe',\n        '200': '#bfdbfe',\n        '300': '#93c5fd',\n        '400': '#60a5fa',\n        '500': '#3b82f6',\n        '600': '#2563eb',\n        '700': '#1d4ed8',\n        '800': '#1e40af',\n        '900': '#1e3a8a',\n        '950': '#172554'\n    },\n    indigo: {\n        '50': '#eef2ff',\n        '100': '#e0e7ff',\n        '200': '#c7d2fe',\n        '300': '#a5b4fc',\n        '400': '#818cf8',\n        '500': '#6366f1',\n        '600': '#4f46e5',\n        '700': '#4338ca',\n        '800': '#3730a3',\n        '900': '#312e81',\n        '950': '#1e1b4b'\n    },\n    violet: {\n        '50': '#f5f3ff',\n        '100': '#ede9fe',\n        '200': '#ddd6fe',\n        '300': '#c4b5fd',\n        '400': '#a78bfa',\n        '500': '#8b5cf6',\n        '600': '#7c3aed',\n        '700': '#6d28d9',\n        '800': '#5b21b6',\n        '900': '#4c1d95',\n        '950': '#2e1065'\n    },\n    purple: {\n        '50': '#faf5ff',\n        '100': '#f3e8ff',\n        '200': '#e9d5ff',\n        '300': '#d8b4fe',\n        '400': '#c084fc',\n        '500': '#a855f7',\n        '600': '#9333ea',\n        '700': '#7e22ce',\n        '800': '#6b21a8',\n        '900': '#581c87',\n        '950': '#3b0764'\n    },\n    fuchsia: {\n        '50': '#fdf4ff',\n        '100': '#fae8ff',\n        '200': '#f5d0fe',\n        '300': '#f0abfc',\n        '400': '#e879f9',\n        '500': '#d946ef',\n        '600': '#c026d3',\n        '700': '#a21caf',\n        '800': '#86198f',\n        '900': '#701a75',\n        '950': '#4a044e',\n    },\n    pink: {\n        '50': '#fdf2f8',\n        '100': '#fce7f3',\n        '200': '#fbcfe8',\n        '300': '#f9a8d4',\n        '400': '#f472b6',\n        '500': '#ec4899',\n        '600': '#db2777',\n        '700': '#be185d',\n        '800': '#9d174d',\n        '900': '#831843',\n        '950': '#500724'\n    },\n    rose: {\n        '50': '#fff1f2',\n        '100': '#ffe4e6',\n        '200': '#fecdd3',\n        '300': '#fda4af',\n        '400': '#fb7185',\n        '500': '#f43f5e',\n        '600': '#e11d48',\n        '700': '#be123c',\n        '800': '#9f1239',\n        '900': '#881337',\n        '950': '#4c0519'\n    }\n};\n", "import { colors } from \"../interface/DefaultColors\";\n/**\n * Thi<PERSON><PERSON> lập các biến CSS đại diện cho màu \"nmt\"\n * @param baseColor Tên màu trong Tailwind (vd: 'blue', 'rose') hoặc mã màu hex (vd: '#3b82f6')\n */\nexport function setnmtColors(baseColor) {\n    console.log('baseColor', baseColor);\n    // Nếu baseColor là object chứa các giá trị màu đã được tính toán sẵn\n    try {\n        const parsed = JSON.parse(baseColor);\n        if (typeof parsed === 'object') {\n            const root = document.documentElement;\n            Object.entries(parsed).forEach(([key, value]) => {\n                root.style.setProperty(`--color-nmt-${key}`, value);\n            });\n            return;\n        }\n    }\n    catch (e) {\n    }\n    // Lấy màu hex từ tên màu Tailwind hoặc chuỗi hex\n    const getHexColor = (baseColor) => {\n        // Kiểm tra xem baseColor có phải là mã hex không\n        if (baseColor.startsWith(\"#\")) {\n            return baseColor;\n        }\n        // Kiểm tra xem baseColor có phải là tên màu trong Tailwind không\n        const color = colors[baseColor];\n        if (color) {\n            // Lấy màu chính (500) từ danh sách màu\n            return color[\"500\"];\n        }\n        // Nếu không phải tên màu hợp lệ, trả về màu mặc định (màu chính của Tailwind)\n        // return colors.blue[\"500\"]; // Màu xanh dương mặc định\n        return colors.orange[\"500\"]; // Màu cam mặc định\n    };\n    // Làm sáng màu\n    const lighten = (hex, percent) => {\n        const num = parseInt(hex.replace(\"#\", \"\"), 16);\n        const amt = Math.round(2.55 * percent);\n        const r = Math.min(255, Math.max(0, (num >> 16) + amt));\n        const g = Math.min(255, Math.max(0, ((num >> 8) & 0xff) + amt));\n        const b = Math.min(255, Math.max(0, (num & 0xff) + amt));\n        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;\n    };\n    // Làm tối màu\n    const darken = (hex, percent) => {\n        const num = parseInt(hex.replace(\"#\", \"\"), 16);\n        const amt = Math.round(2.55 * percent);\n        const r = Math.min(255, Math.max(0, (num >> 16) - amt));\n        const g = Math.min(255, Math.max(0, ((num >> 8) & 0xff) - amt));\n        const b = Math.min(255, Math.max(0, (num & 0xff) - amt));\n        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;\n    };\n    // Lấy màu gốc\n    const hexColor = getHexColor(baseColor);\n    // Các sắc độ màu\n    const shades = {\n        \"50\": lighten(hexColor, 50),\n        \"100\": lighten(hexColor, 40),\n        \"200\": lighten(hexColor, 30),\n        \"300\": lighten(hexColor, 20),\n        \"400\": lighten(hexColor, 10),\n        \"500\": hexColor,\n        \"600\": darken(hexColor, 10),\n        \"700\": darken(hexColor, 20),\n        \"800\": darken(hexColor, 30),\n        \"900\": darken(hexColor, 40),\n        \"950\": darken(hexColor, 50),\n    };\n    // Lấy root element\n    const root = document.documentElement;\n    // Gán vào biến CSS\n    Object.entries(shades).forEach(([key, value]) => {\n        root.style.setProperty(`--color-nmt-${key}`, value);\n    });\n}\n", "import { __decorate, __metadata } from \"tslib\";\nimport { css, LitElement, unsafeCSS } from \"lit\";\nimport { CryptoService } from \"../../services/CryptoService\";\nimport { FlightService } from \"../../services/FlightService\";\nimport { TripResultTemplate } from \"./trip-result-template\";\nimport { customElement, property, state } from \"lit/decorators.js\";\nimport { getAirportInfoByCode } from \"../../services/WorldServices\";\nimport { setnmtColors } from \"../../services/ColorService\";\nimport styles from '../../styles/styles.css';\nconst cryptoService = new CryptoService();\nconst flightService = new FlightService();\nlet TripResult = class TripResult extends LitElement {\n    static { this.styles = [\n        unsafeCSS(styles),\n        css `\r\n        :host {\r\n          font-family: var(--nmt-font, 'Roboto', sans-serif);\r\n        }`\n    ]; }\n    get language() {\n        return this._language;\n    }\n    set language(value) {\n        const oldValue = this._language;\n        // Chỉ kiểm tra URL nếu autoLanguageParam được bật\n        if (this.autoLanguageParam) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const languageParam = urlParams.get('language');\n            if (languageParam && languageParam !== this._language) {\n                // URL có language parameter - luôn ưu tiên URL\n                this._language = languageParam;\n                console.log('Language overridden from URL parameter:', this._language);\n            }\n            else {\n                // URL không có language parameter - sử dụng giá trị được set\n                this._language = value;\n                console.log('Language set from property:', this._language);\n                // Tự động thêm vào URL nếu chưa có\n                if (!this._hasCheckedURL) {\n                    this.updateURLWithLanguage();\n                    this._hasCheckedURL = true;\n                }\n            }\n        }\n        else {\n            // Không sử dụng URL parameter, chỉ set giá trị trực tiếp\n            this._language = value;\n            console.log('Language set from property (autoLanguageParam disabled):', this._language);\n        }\n        this.requestUpdate('language', oldValue);\n    }\n    get currencySymbolAv() {\n        return this.convertedVND === 1 || this.language === 'vi' ? '₫' : this.currencySymbol;\n    }\n    constructor(_cryptoService, _flightService) {\n        super();\n        this._cryptoService = _cryptoService;\n        this._flightService = _flightService;\n        this.autoFillOrderCode = false;\n        this.mode = \"online\";\n        this.googleFontsUrl = \"\";\n        this.font = \"\";\n        this.urlRePayment = 'TripRePayment';\n        this.ApiKey = '';\n        this.color = \"\";\n        this.uri_searchBox = \"\";\n        this.showLanguageSelect = false;\n        this.autoLanguageParam = false; // Bật/tắt tính năng tự động thêm language param\n        this._language = \"vi\";\n        this._hasCheckedURL = false; // Flag để tránh kiểm tra URL nhiều lần\n        this._ApiKey = '';\n        this._isLoading = false;\n        this._isNotValid = false;\n        this._orderAvailable = null;\n        this._orderDetails = null;\n        this._inforAirports = [];\n        this._PaymentNote = null;\n        this._NoteModel = null;\n        this.displayMode = 'total';\n        this.convertedVND = 1;\n        this.currencySymbol = '₫';\n        this.request = {\n            OrderCode: '',\n            PhoneCustomer: '',\n            EmailCustomer: ''\n        };\n        this._cryptoService = cryptoService;\n        this._flightService = flightService;\n    }\n    connectedCallback() {\n        super.connectedCallback();\n        this._ApiKey = this.ApiKey;\n        this.removeAttribute(\"ApiKey\");\n        // Kiểm tra URL ngay khi component được kết nối, trường hợp không có attribute language\n        this.checkLanguageFromURL();\n    }\n    checkLanguageFromURL() {\n        // Chỉ kiểm tra URL nếu autoLanguageParam được bật\n        if (!this.autoLanguageParam) {\n            console.log('autoLanguageParam disabled, skipping URL check');\n            return;\n        }\n        const urlParams = new URLSearchParams(window.location.search);\n        const languageParam = urlParams.get('language');\n        if (languageParam) {\n            // URL có language parameter - set giá trị từ URL\n            this._language = languageParam;\n            console.log('Language initialized from URL parameter:', this._language);\n            this.requestUpdate('language');\n        }\n        else if (!this._hasCheckedURL) {\n            // URL không có language parameter - tự động thêm vào URL với giá trị mặc định\n            this.updateURLWithLanguage();\n            this._hasCheckedURL = true;\n        }\n    }\n    updateURLWithLanguage() {\n        const currentUrl = new URL(window.location.href);\n        const params = new URLSearchParams(currentUrl.search);\n        // Thêm hoặc cập nhật parameter language\n        params.set('language', this._language);\n        // Cập nhật URL mà không reload trang\n        const newUrl = `${currentUrl.pathname}?${params.toString()}`;\n        window.history.replaceState({}, '', newUrl);\n        console.log('URL updated with language parameter:', newUrl);\n    }\n    async firstUpdated(_changedProperties) {\n        super.firstUpdated(_changedProperties);\n        await this.getRequest();\n        if (this.color !== \"\") {\n            setnmtColors(this.color);\n            this.requestUpdate();\n        }\n        console.log(this.googleFontsUrl);\n        // Handle Google Fonts\n        if (this.googleFontsUrl) {\n            const googleFontsLink = document.createElement('link');\n            googleFontsLink.rel = 'stylesheet';\n            googleFontsLink.href = this.googleFontsUrl;\n            document.head.appendChild(googleFontsLink);\n        }\n        else {\n            // Default font if no Google Fonts URL provided\n            const googleFontsLink = document.createElement('link');\n            googleFontsLink.rel = 'stylesheet';\n            googleFontsLink.href = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap';\n            document.head.appendChild(googleFontsLink);\n        }\n        console.log('font', this.font);\n        if (this.font !== \"\") {\n            const root = document.documentElement;\n            root.style.setProperty('--nmt-font', this.font);\n        }\n    }\n    updated(_changedProperties) {\n        super.updated(_changedProperties);\n    }\n    async getRequest() {\n        var params = new URLSearchParams(window.location.search);\n        this.request = {\n            OrderCode: params.get('OrderCode') || '',\n            PhoneCustomer: params.get('PhoneCustomer') || '',\n            EmailCustomer: params.get('EmailCustomer') || ''\n        };\n        if (this.request.OrderCode && this.request.PhoneCustomer && this.request.EmailCustomer) {\n            await this.AvailableTrip(this.request);\n        }\n    }\n    async AvailableTrip(request) {\n        if (!this._cryptoService.ch()) {\n            await this._cryptoService.spu();\n        }\n        this.CallAvailableTrip(request);\n    }\n    async RequestEncrypt(data) {\n        const encryptedData = await this._cryptoService.eda(JSON.stringify(data));\n        return {\n            EncryptData: encryptedData\n        };\n    }\n    formatPassenger() {\n        var indexInfant = 0;\n        this._orderDetails?.paxList.forEach((pax, index) => {\n            if (pax.type == 'infant') {\n                //get pax adult index same index infant\n                var paxAdult = this._orderDetails.paxList.find((pax) => pax.type == 'adult' && pax.index == indexInfant);\n                if (paxAdult) {\n                    paxAdult.withInfant = pax;\n                    //remove pax infant\n                    this._orderDetails.paxList.splice(index, 1);\n                }\n                indexInfant++;\n            }\n            else {\n                pax.index = index;\n            }\n        });\n    }\n    async CallAvailableTrip(request) {\n        this._isLoading = true;\n        var payloadsEncrypted = await this.RequestEncrypt(request);\n        try {\n            const res = await this._flightService.AvailableTrip(payloadsEncrypted, this._ApiKey);\n            const resDecrypted = await this._cryptoService.dda(res.resultObj);\n            var resJson = JSON.parse(resDecrypted);\n            if (resJson.IsSuccessed) {\n                var noteData = JSON.parse(resJson.ResultObj.Note);\n                this._orderDetails = noteData;\n                this._isNotValid = true;\n                this._orderAvailable = resJson.ResultObj;\n                this._PaymentNote = JSON.parse(resJson.ResultObj.PaymentNote);\n                this._NoteModel = JSON.parse(resJson.ResultObj.NoteResult);\n                console.log(this._PaymentNote);\n                this.formatPassenger();\n                await this.getInforAirports();\n            }\n        }\n        catch (error) {\n            if (error.status === 403) {\n                this._cryptoService.ra();\n                await this._cryptoService.spu();\n                await this.CallAvailableTrip(request);\n            }\n        }\n        finally {\n            this._isLoading = false;\n        }\n    }\n    async getInforAirports() {\n        var airportsCode = [];\n        this._orderDetails.full?.InventoriesSelected.forEach((inventory) => {\n            inventory.segment.Legs.forEach((leg) => {\n                if (!airportsCode.includes(leg.DepartureCode)) {\n                    airportsCode.push(leg.DepartureCode);\n                }\n                if (!airportsCode.includes(leg.ArrivalCode)) {\n                    airportsCode.push(leg.ArrivalCode);\n                }\n            });\n        });\n        try {\n            var res = await getAirportInfoByCode(airportsCode, this.language || 'vi', this._ApiKey);\n            if (res.isSuccessed) {\n                this._inforAirports = res.resultObj;\n                this.displayMode = res.feature.displayMode || 'total';\n                const currencyObj = typeof res.feature.currency === 'string' ? JSON.parse(res.feature.currency) : res.feature.currency;\n                this.currencySymbol = currencyObj.symbol || '₫';\n                this.convertedVND = currencyObj.convertedVND || 1;\n            }\n            console.log('mode', this.mode);\n            if (this.mode === \"online\") {\n                if (res.feature?.color) {\n                    this.color = res.feature.color;\n                    if (this.color !== \"\") {\n                        setnmtColors(this.color);\n                        this.requestUpdate();\n                    }\n                }\n            }\n        }\n        catch (error) {\n            console.error(error);\n        }\n    }\n    rePayment() {\n        const baseUrl = window.location.origin; // Lấy domain hiện tại\n        const params = new URLSearchParams({\n            OrderCode: this.request.OrderCode,\n            PhoneCustomer: this.request.PhoneCustomer,\n            EmailCustomer: this.request.EmailCustomer\n        });\n        // Chỉ thêm language parameter nếu autoLanguageParam được bật\n        if (this.autoLanguageParam) {\n            params.append('language', this.language);\n        }\n        window.location.href = `${baseUrl}/${this.urlRePayment}?${params.toString()}`;\n    }\n    handleLanguageChange(newLang) {\n        this.language = newLang;\n        this.getInforAirports();\n        // Tự động cập nhật URL với language mới\n        this.updateURLWithLanguage();\n        this.requestUpdate();\n    }\n    render() {\n        return TripResultTemplate(this.autoFillOrderCode, this.uri_searchBox, this.language, this._isLoading, this._isNotValid, this._orderAvailable, this._orderDetails, this._inforAirports, this._PaymentNote, this._NoteModel, this.currencySymbolAv, this.convertedVND, this.rePayment.bind(this), this.handleLanguageChange.bind(this), this.showLanguageSelect // truyền thuộc tính mới\n        );\n    }\n};\n__decorate([\n    property({ type: Boolean }),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"autoFillOrderCode\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"mode\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"googleFontsUrl\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"font\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", String)\n], TripResult.prototype, \"urlRePayment\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"ApiKey\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"color\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"uri_searchBox\", void 0);\n__decorate([\n    property({ type: Boolean }),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"showLanguageSelect\", void 0);\n__decorate([\n    property({ type: Boolean }),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"autoLanguageParam\", void 0);\n__decorate([\n    property({ type: String }),\n    __metadata(\"design:type\", String),\n    __metadata(\"design:paramtypes\", [String])\n], TripResult.prototype, \"language\", null);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripResult.prototype, \"_ApiKey\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripResult.prototype, \"_isLoading\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Boolean)\n], TripResult.prototype, \"_isNotValid\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"_orderAvailable\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"_orderDetails\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Array)\n], TripResult.prototype, \"_inforAirports\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"_PaymentNote\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"_NoteModel\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripResult.prototype, \"displayMode\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Number)\n], TripResult.prototype, \"convertedVND\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", String)\n], TripResult.prototype, \"currencySymbol\", void 0);\n__decorate([\n    state(),\n    __metadata(\"design:type\", Object)\n], TripResult.prototype, \"request\", void 0);\nTripResult = __decorate([\n    customElement(\"trip-result\"),\n    __metadata(\"design:paramtypes\", [CryptoService,\n        FlightService])\n], TripResult);\nexport { TripResult };\n"], "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "this", "__decorate", "decorators", "target", "key", "desc", "d", "c", "r", "getOwnPropertyDescriptor", "Reflect", "decorate", "defineProperty", "__metadata", "metadataKey", "metadataValue", "metadata", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "value", "step", "next", "e", "rejected", "throw", "result", "done", "then", "__generator", "body", "f", "y", "_", "label", "sent", "trys", "ops", "g", "create", "Iterator", "verb", "return", "Symbol", "iterator", "v", "op", "TypeError", "pop", "push", "__spread<PERSON><PERSON>y", "to", "from", "pack", "ar", "l", "Array", "slice", "concat", "globalThis", "ShadowRoot", "ShadyCSS", "nativeShadow", "Document", "CSSStyleSheet", "o", "WeakMap", "n$3", "constructor", "_$cssResult$", "Error", "cssText", "styleSheet", "let", "get", "replaceSync", "set", "toString", "cssRules", "is", "getOwnPropertyNames", "h", "getOwnPropertySymbols", "getPrototypeOf", "a", "trustedTypes", "emptyScript", "reactiveElementPolyfillSupport", "u", "toAttribute", "Boolean", "JSON", "stringify", "fromAttribute", "Number", "parse", "attribute", "type", "String", "converter", "reflect", "has<PERSON><PERSON>ed", "litPropertyMetadata", "b", "HTMLElement", "addInitializer", "_$Ei", "observedAttributes", "finalize", "_$Eh", "keys", "createProperty", "state", "elementProperties", "noAccessor", "getPropertyDescriptor", "requestUpdate", "configurable", "enumerable", "getPropertyOptions", "Map", "finalized", "properties", "_$Eu", "elementStyles", "finalizeStyles", "styles", "isArray", "Set", "flat", "reverse", "unshift", "toLowerCase", "super", "_$Ep", "isUpdatePending", "hasUpdated", "_$Em", "_$Ev", "_$ES", "enableUpdating", "_$AL", "_$E_", "for<PERSON>ach", "addController", "_$EO", "add", "renderRoot", "isConnected", "hostConnected", "removeController", "delete", "size", "createRenderRoot", "shadowRoot", "attachShadow", "shadowRootOptions", "adoptedStyleSheets", "map", "document", "createElement", "litNonce", "setAttribute", "textContent", "append<PERSON><PERSON><PERSON>", "connectedCallback", "disconnectedCallback", "hostDisconnected", "attributeChangedCallback", "_$AK", "_$EC", "removeAttribute", "_$ET", "has", "_$Ej", "scheduleUpdate", "performUpdate", "wrapped", "shouldUpdate", "willUpdate", "hostUpdate", "update", "_$EU", "_$AE", "hostUpdated", "firstUpdated", "updated", "updateComplete", "getUpdateComplete", "mode", "ReactiveElement", "reactiveElementVersions", "createPolicy", "createHTML", "Math", "random", "toFixed", "createComment", "m", "RegExp", "$", "x", "_$litType$", "strings", "values", "T", "for", "E", "A", "C", "createTreeWalker", "V", "lastIndex", "exec", "test", "startsWith", "N", "parts", "el", "currentNode", "content", "<PERSON><PERSON><PERSON><PERSON>", "replaceWith", "childNodes", "nextNode", "nodeType", "hasAttributes", "getAttributeNames", "endsWith", "getAttribute", "split", "index", "name", "ctor", "H", "I", "L", "k", "tagName", "append", "data", "indexOf", "innerHTML", "S", "_$Co", "_$Cl", "_$litDirective$", "_$AO", "_$AT", "_$AS", "M$2", "_$AV", "_$AN", "_$AD", "_$AM", "parentNode", "_$AU", "creationScope", "importNode", "R", "nextS<PERSON>ling", "z", "_$AI", "_$Cv", "_$AH", "_$AA", "_$AB", "options", "startNode", "endNode", "_$AR", "O", "insertBefore", "createTextNode", "_$AC", "M", "_$AP", "remove", "setConnected", "element", "fill", "j", "toggleAttribute", "capture", "once", "passive", "removeEventListener", "addEventListener", "handleEvent", "host", "litHtmlPolyfillSupport", "B", "litHtmlVersions", "renderBefore", "_$litPart$", "r$2", "renderOptions", "_$Do", "render", "_$litElement$", "litElementHydrateSupport", "LitElement", "litElementPolyfillSupport", "litElementVersions", "_0x44b8eb", "_0x26c6", "_0x6eca", "_0x4689a6", "_0x4a7220", "_0x141bb3", "_0x6ecae4", "_0x26c651", "_0x47d498", "_0xe86f83", "_0x41fce9", "parseInt", "shift", "_0x50a904", "environment", "wait", "durationMs", "resolveWith", "setTimeout", "isPromise", "awaitIfAsync", "action", "callback", "returnedValue", "error", "mapWithBreaks", "items", "loopReleaseInterval", "results", "lastLoopReleaseTime", "now", "_a", "Date", "channel", "MessageChannel", "port1", "onmessage", "port2", "postMessage", "suppressUnhandledRejectionWarning", "promise", "undefined", "toInt", "toFloat", "parseFloat", "replaceNaN", "replacement", "isNaN", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "sum", "round", "base", "abs", "counterBase", "x64Add", "m0", "n0", "o0", "o1", "o2", "o3", "x64Multiply", "m1", "m2", "m3", "n1", "n2", "n3", "x64Rotl", "bits", "x64LeftShift", "x64Xor", "F1", "F2", "x64Fmix", "shifted", "C1", "C2", "M$1", "N1", "N2", "x64hash128", "input", "seed", "getUTF8Bytes", "Uint8Array", "charCode", "charCodeAt", "TextEncoder", "encode", "remainder", "bytes", "h1", "h2", "k1", "k2", "val", "loadSources", "sources", "sourceOptions", "excludeSources", "includedSources", "filter", "sourceKey", "excludes", "haystack", "needle", "includes", "sourceGettersPromise", "loadSource", "source", "sourceLoadPromise", "resolveLoad", "loadStartTime", "bind", "loadArgs", "_i", "loadResult", "loadDuration", "isFinalResultLoaded", "duration", "resolveGet", "getStartTime", "getArgs", "finalizeSource", "componentPromises", "componentArray", "components", "sourceGetter", "all", "isTrident", "w", "window", "navigator", "isChromium", "vendor", "isWebKit", "isDesktopWebKit", "isSafariWebKit", "isFunctionNative", "func", "print", "browser", "isGecko", "_b", "documentElement", "style", "isWebKit616OrNewer", "CSS", "HTMLButtonElement", "supports", "exitFullscreen", "msExitFullscreen", "mozCancelFullScreen", "webkitExitFullscreen", "isAndroid", "isItChromium", "isItGecko", "Audio", "appVersion", "makeInnerError", "withIframe", "initialHtml", "domPollInterval", "_c", "iframe", "_d", "_resolve", "_reject", "isComplete", "onload", "onerror", "checkReadyState", "setProperty", "position", "top", "left", "visibility", "srcdoc", "src", "contentWindow", "readyState", "<PERSON><PERSON><PERSON><PERSON>", "selectorToElement", "selector", "parseSimpleCssSelector", "errorMessage", "tagMatch", "attributes", "tag", "partsRegex", "addAttribute", "match", "part", "attributeMatch", "name_1", "join", "addStyleString", "name_2", "property", "priority", "baseFonts", "fontList", "canvasToString", "canvas", "toDataURL", "screenFrameBackup", "screenFrameSizeTimeoutId", "getUnstableScreenFrame", "_this", "watchScreenFrame", "checkScreenFrame", "frameSize", "getCurrentScreenFrame", "isFrameSizeNull", "getFullscreenElement", "fullscreenElement", "msFullscreenElement", "mozFullScreenElement", "webkitFullscreenElement", "screen", "availTop", "width", "availWidth", "availLeft", "height", "availHeight", "getBlockedSelectors", "selectors", "root", "elements", "blockedSelectors", "holder", "forceShow", "show", "offsetParent", "doesMatch$5", "matchMedia", "matches", "doesMatch$4", "doesMatch$3", "doesMatch$2", "doesMatch$1", "doesMatch", "fallbackFn", "presets", "default", "apple", "font", "serif", "fontFamily", "sans", "mono", "min", "fontSize", "system", "willPrintConsoleError", "isAnyParentCrossOrigin", "currentWindow", "parentWindow", "parent", "location", "origin", "validContextParameters", "validExtensionParams", "shaderTypes", "precisionTypes", "getWebGLContext", "cache", "webgl", "context", "getContext", "getShaderPrecision", "gl", "shaderType", "precisionType", "shaderPrecision", "getShaderPrecisionFormat", "rangeMin", "rangeMax", "precision", "getConstantsFromPrototype", "obj", "__proto__", "isConstantLike", "shouldAvoidDebugRendererInfo", "isValidParameterGetter", "getParameter", "fonts", "getFonts", "spansContainer", "defaultWidth", "defaultHeight", "createSpan", "createSpanWithFonts", "initializeFontsSpans", "isFontAvailable", "baseFontsSpans", "fontsSpans", "span", "fontToDetect", "baseFont", "spans", "fontList_1", "fontSpans", "some", "baseFontIndex", "offsetWidth", "offsetHeight", "domBlockers", "getDomBlockers", "debug", "filters", "filterNames", "isApplicable", "getFilters", "fromB64", "atob", "abpIndo", "abpvn", "adBlockFinland", "ad<PERSON><PERSON><PERSON><PERSON><PERSON>", "adBlockWarningRemoval", "adGuardAnnoyances", "adGuardBase", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adGuardJapanese", "adGuardMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adGuard<PERSON><PERSON><PERSON>", "adGuardSpanishPortuguese", "adGuardTrackingProtection", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bulgarian", "easyList", "easyListChina", "easyList<PERSON><PERSON><PERSON>", "easyListCzechSlovak", "easyList<PERSON>ut<PERSON>", "easyListGermany", "easyListItaly", "easyListLithuania", "estonian", "fanboyAnnoyances", "fanboyAntiFacebook", "fanboyEnhancedTrackers", "fanboySocial", "frellwitSwedish", "greekAdBlock", "hungarian", "iDontCareAboutCookies", "icelandicAbp", "latvian", "listKr", "listeAr", "listeFr", "officialPolish", "ro", "ruAd", "thaiAds", "webAnnoyancesUltralist", "filterName", "printDebug", "activeBlockers", "sort", "fontPreferences", "getFontPreferences", "withNaturalFonts", "containerWidthPx", "iframeWindow", "linesOfText", "iframeDocument", "iframeBody", "bodyStyle", "webkitTextSizeAdjust", "textSizeAdjust", "zoom", "devicePixelRatio", "container", "sizes", "text", "whiteSpace", "_e", "_f", "_g", "_h", "getBoundingClientRect", "audio", "getAudioFingerprint", "doesBrowserPerformAntifingerprinting$1", "isSamsungInternet", "audioPrototype", "visualViewport", "Image", "isChromium122OrNewer", "URLPattern", "WebGLRenderingContext", "getUnstableAudioFingerprint", "renderPromise", "finishRendering", "fingerprintPromise", "AudioContext", "OfflineAudioContext", "webkitOfflineAudioContext", "doesBrowserSuspendAudioContext", "isWebKit606OrNewer", "oscillator", "createOscillator", "frequency", "compressor", "createDynamicsCompressor", "threshold", "knee", "ratio", "attack", "release", "connect", "destination", "start", "startRenderingAudio", "isFinalized", "renderTryCount", "startedRunningAt", "startRunningTimeout", "oncomplete", "event", "<PERSON><PERSON><PERSON><PERSON>", "tryRender", "renderingPromise", "startRendering", "hidden", "buffer", "getHash", "signal", "hash", "getChannelData", "subarray", "screenFrame", "getScreenFrame", "screenFrameGetter", "processSize", "sideSize", "getCanvasFingerprint", "getUnstableCanvasFingerprint", "skipImages", "geometry", "winding", "makeCanvasContext", "isSupported", "doesSupportWinding", "rect", "isPointInPath", "renderImages", "renderTextImage", "textBaseline", "fillStyle", "fillRect", "printedText", "fromCharCode", "fillText", "textImage1", "renderGeometryImage", "globalCompositeOperation", "color", "beginPath", "arc", "PI", "closePath", "doesBrowserPerformAntifingerprinting", "osCpu", "getOsCpu", "oscpu", "languages", "getLanguages", "language", "userLanguage", "browserLanguage", "systemLanguage", "isChromium86OrNewer", "Intl", "colorDepth", "getColorDepth", "deviceMemory", "getDeviceMemory", "screenResolution", "getScreenResolution", "getUnstableScreenResolution", "parseDimension", "dimensions", "hardwareConcurrency", "getHardwareConcurrency", "timezone", "getTimezone", "DateTimeFormat", "resolvedOptions", "timeZone", "offset", "getTimezoneOffset", "currentYear", "getFullYear", "max", "sessionStorage", "getSessionStorage", "localStorage", "getLocalStorage", "indexedDB", "getIndexedDB", "isEdgeHTML", "openDatabase", "getOpenDatabase", "cpuClass", "getCpuClass", "platform", "getPlatform", "isIPad", "screenRatio", "Element", "webkitRequestFullscreen", "plugins", "getPlugins", "rawPlugins", "plugin", "mimeTypes", "mimeType", "suffixes", "description", "touchSupport", "getTouchSupport", "touchEvent", "maxTouchPoints", "msMaxTouchPoints", "createEvent", "touchStart", "get<PERSON>endor", "vendorFlavors", "getVendorFlavors", "flavors", "cookiesEnabled", "areCookiesEnabled", "cookie", "colorGamut", "getColorGamut", "gamut", "invertedColors", "areColorsInverted", "forcedColors", "areColorsForced", "monochrome", "getMonochromeDepth", "contrast", "getContrastPreference", "reducedMotion", "isMotionReduced", "reducedTransparency", "isTransparencyReduced", "hdr", "isHDR", "math", "getMathFingerprint", "acos", "acosh", "asin", "asinh", "atanh", "atan", "sin", "sinh", "cos", "cosh", "tan", "tanh", "exp", "expm1", "log1p", "acoshPf", "log", "sqrt", "asinhPf", "atanhPf", "sinhPf", "coshPf", "tanhPf", "expm1Pf", "log1pPf", "powPI", "pow", "pdfViewerEnabled", "isPdfViewerEnabled", "architecture", "getArchitecture", "Float32Array", "u8", "Infinity", "applePay", "getApplePayState", "ApplePaySession", "canMakePayments", "getStateFromError", "message", "privateClickMeasurement", "getPrivateClickMeasurement", "link", "sourceId", "attributionSourceId", "attributionsourceid", "audioBaseLatency", "getAudioContextBaseLatency", "baseLatency", "dateTimeLocale", "getDateTimeLocale", "locale", "webGlBasics", "getWebGlBasics", "debugExtension", "getExtension", "version", "VERSION", "VENDOR", "vendorUnmasked", "UNMASKED_VENDOR_WEBGL", "renderer", "RENDERER", "rendererUnmasked", "UNMASKED_RENDERER_WEBGL", "shadingLanguageVersion", "SHADING_LANGUAGE_VERSION", "webGlExtensions", "getWebGlExtensions", "extensions", "getSupportedExtensions", "contextAttributes", "getContextAttributes", "unsupportedExtensions", "parameters", "extensionParameters", "shaderPrecisions", "attributeName", "constants_1", "code", "constant", "extensions_1", "extension", "shaderTypes_1", "precisionTypes_1", "loadBuiltinSources", "getConfidence", "proConfidenceScore", "deriveProConfidenceScore", "openConfidenceScore", "getOpenConfidenceScore", "score", "comment", "replace", "hashComponents", "componentsToCanonicalString", "componentKey", "component", "prepareForSources", "<PERSON><PERSON><PERSON><PERSON>", "requestIdleCallbackIfAvailable", "fallbackTimeout", "deadlineTimeout", "requestIdleCallback", "timeout", "makeAgent", "getComponents", "makeLazyGetResult", "visitorIdCache", "confidence", "visitorId", "load", "monitoring", "monitor", "__fpjs_d_m", "request", "XMLHttpRequest", "open", "send", "componentsToDebugString", "_key", "errorToObject", "stack", "getDeviceId", "_getDeviceId", "_asyncToGenerator", "_regeneratorRuntime", "_callee", "_context", "FingerprintJS", "fp", "fetchWithDeviceId", "_x", "_x2", "_fetchWithDeviceId", "_callee2", "init", "headers", "modifiedInit", "_context2", "deviceId", "Headers", "_objectSpread", "fetch", "_0xe68c", "_0x1e1cb8", "fetchWithDeviceIdandApiKey", "_x3", "_fetchWithDeviceIdandApiKey", "_callee3", "<PERSON><PERSON><PERSON><PERSON>", "response", "_args3", "_context3", "_0x1dbba5", "_0x42b3ef", "_0x4cdc", "_0x2d3088", "_0x51705c", "_0x7010ae", "_0x7010", "_0x4cdc63", "_0x34bb00", "apiUrl", "public<PERSON>ey", "CryptoService", "_gdi", "_wk", "_iih", "_csi", "_dda", "_eda", "_dsk", "_spu", "_gr", "_importPrivateKey", "_importPublicKey", "_decrypt", "_encrypt", "_da", "_hd", "_he", "_dra", "_era", "_irpr", "_irpu", "_ea", "_ga", "_gra", "_createClass", "_classCallCheck", "privateKey", "crypto", "_0x377375", "_0x35bdc6", "aes<PERSON>ey", "encoder", "iv", "encryptedData", "_0x16bcc9", "_0x2a0ac5", "dataBuffer", "_callee4", "pem", "binaryDer", "_context4", "irpu", "_callee5", "_context5", "_0x3ff6ad", "pemToA<PERSON>y<PERSON><PERSON>er", "_x4", "_callee6", "exportedAESKey", "_context6", "_x5", "_x6", "era", "_callee7", "_context7", "_x7", "_x8", "_callee8", "publicKeyPem", "encryptedAESKey", "combinedData", "_context8", "ga", "ea", "_yield$this$ea", "btoa", "_toConsumableArray", "_x9", "_x10", "he", "_callee9", "privateKeyBem", "encryptedText", "encryptedAesKey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decryptedData", "_context9", "_0x1acbc0", "_0x13f4f6", "da", "_x11", "_x12", "hd", "_callee10", "encryptedBuffer", "decryptedBuffer", "_context10", "cipherText", "TextDecoder", "_x13", "_x14", "_callee11", "plainText", "_context11", "_x15", "_x16", "_callee12", "_context12", "importPrivateKey", "base64ToArrayBuffer", "_x17", "_x18", "_callee13", "_context13", "_0x2828de", "_x19", "_callee14", "_context14", "_0x328099", "_0x46af55", "_x20", "_base64$match", "base64", "binary", "len", "binaryString", "_callee15", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ep", "sp", "signature<PERSON><PERSON>er", "ss", "_context15", "_0x5e5a19", "_0x432001", "gra", "spB<PERSON>er", "xdi", "s<PERSON><PERSON><PERSON>", "unescape", "encodeURIComponent", "minutes", "date", "expires", "nameEQ", "ca", "cookies", "eqPos", "_callee16", "_yield$this$gr", "requestEncrypt", "responseData", "privateKeyBase64String", "_context16", "gr", "requestJson", "sc", "textToBase64", "_callee17", "server<PERSON>ey", "_context17", "gc", "cText", "_callee18", "sText", "_context18", "_0x3010a4", "_0x14cff4", "_x21", "_callee19", "_context19", "_x22", "dda", "_callee20", "_context20", "_callee21", "_context21", "_0x1c45ed", "ch", "_callee22", "retries", "_context22", "_0x404d1f", "_0x454be", "_callee23", "_context23", "_0x80c11b", "_0xfeb85a", "_0x4957", "_0x26d274", "_0x534df5", "_0x3a2a45", "_0x3a2a", "_0x49574e", "_0x45f591", "FlightService", "_request2", "endpoint", "fetchFn", "_args", "_0x535131", "_0x987f50", "useDeviceId", "api", "_0x51f04c", "_0xe3cb40", "_0x330078", "_0x2f2fea", "getTimeFromDateTime", "dateTime", "hours", "formatddMMyyyy", "date<PERSON><PERSON>j", "TripResultTemplate", "autoFillOrderCode", "uri_searchBox", "_isLoading", "_isNotValid", "_orderAvailable", "_orderDetails", "_inforAirports", "_PaymentNote", "__<PERSON><PERSON><PERSON><PERSON>", "_currencySymbol", "_convertedVND", "rePayment", "handleLanguageChange", "showLanguageSelect", "html", "_templateObject", "_taggedTemplateLiteral", "_templateObject2", "_templateObject3", "_templateObject4", "_templateObject5", "_templateObject6", "_templateObject7", "_templateObject8", "_templateObject9", "formatNumber", "convertedVND", "integerPart", "_result$toFixed$split2", "_slicedToArray", "decimalPart", "formattedInteger", "_templateObject10", "_PaymentNote$banksInf", "_PaymentNote$banksInf2", "_PaymentNote$banksInf3", "_PaymentNote$banksInf4", "_PaymentNote$banksInf5", "_templateObject11", "_PaymentNote$banksInf6", "_PaymentNote$banksInf7", "_templateObject12", "_orderAvailable$Payme", "_templateObject13", "_orderAvailable$Payme2", "_orderAvailable$Payme3", "_templateObject14", "pax", "_pax$withInfant6", "_templateObject15", "_templateObject16", "_pax$withInfant", "_pax$withInfant2", "_pax$withInfant3", "_pax$withInfant4", "_pax$withInfant5", "baggage", "_templateObject17", "_templateObject18", "formatDateToString", "year", "_0x9ad2eb", "_0xe219", "day", "month", "_typeof", "parsed", "dd", "mm", "yyyy", "_inforAirports$_order", "_inforAirports$_order2", "_templateObject19", "_orderDetails$full", "_templateObject20", "_orderDetails$full2", "itinerarySelected", "_orderDetails$full3", "_itinerarySelected$se", "_templateObject21", "_templateObject22", "formatDateTo_ddMMyyyy", "_0x292d91", "date1", "getDurationByArray", "legs", "departure", "arrival", "leg", "$index", "_inforAirports$leg$De", "_itinerarySelected$in2", "_templateObject23", "_templateObject24", "convertDurationToHour", "padStart", "_inforAirports$leg$De2", "_itinerarySelected$in", "_inforAirports$leg$Ar", "_templateObject25", "_orderDetails$full4", "_templateObject26", "_orderDetails$full5", "_templateObject27", "_inforAirports$leg$De3", "_itinerarySelected$in11", "_templateObject28", "_inforAirports$leg$Ar2", "getDayInWeek", "days", "_inforAirports$leg$De4", "getDuration", "_inforAirports$leg$Ar3", "_itinerarySelected$in3", "_itinerarySelected$in4", "_templateObject29", "_itinerarySelected$in5", "_itinerarySelected$in6", "_itinerarySelected$in7", "_itinerarySelected$in8", "_templateObject30", "_itinerarySelected$in9", "_itinerarySelected$in10", "_templateObject31", "_templateObject32", "_templateObject33", "_NoteModel$TimeCheck", "_NoteModel$TimeCheck2", "_NoteModel$IdentityD", "identityDoc", "_templateObject34", "_NoteModel$SpecialRu", "specialRule", "_templateObject35", "_templateObject36", "kind", "getAirportInfoByCode", "_ref", "airportsCode", "requestBody", "_0x479f3b", "_0x52172f", "_ref2", "_0x3564b2", "_0x510c", "_ref3", "_0x457fe", "_0x608ca5", "_ref4", "features", "_0x426f35", "_0x40c2d4", "_0x38ef70", "_0x4895c8", "_0x454268", "_0x1b5c", "_0x123f", "_0x1eb677", "_0x2528b3", "_0x4a6e1a", "_0x123f81", "_0x1b5c0c", "_0x4b163c", "_0x5690d9", "_0x40046a", "_0x4e7457", "colors", "setnmtColors", "baseColor", "lighten", "hex", "percent", "num", "amt", "darken", "shades", "hexColor", "cryptoService", "flightService", "TripResult", "_TripResult", "_getInforAirports", "_CallAvailableTrip", "_RequestEncrypt", "_AvailableTrip", "_getRequest", "_firstUpdated", "_cryptoService", "_flightService", "_callSuper", "_inherits", "languageParam", "oldValue", "autoLanguageParam", "URLSearchParams", "urlParams", "_language", "_0x4f4a0c", "_0x3bc669", "_superPropGet", "_<PERSON><PERSON><PERSON><PERSON>", "currentUrl", "URL", "params", "newUrl", "_changedProperties", "_googleFontsLink", "_0x2df7aa", "_0x4d93da", "googleFontsLink", "googleFontsUrl", "PhoneCustomer", "_0x20c7a3", "_0x218cc8", "AvailableTrip", "_0x400bcc", "_0x310812", "eda", "RequestEncrypt", "_this$_orderDetails", "_this2", "indexInfant", "paxAdult", "payloadsEncrypted", "res", "res<PERSON>son", "noteData", "_0xecdb1a", "_0x59028b", "resDecrypted", "_NoteModel", "getInforAirports", "ra", "_res$feature", "_0x399012", "_0x5379", "_this$_orderDetails$f", "full", "inventory", "currencyObj", "baseUrl", "EmailCustomer", "urlRePayment", "newLang", "unsafeCSS", "css", "customElements", "define", "customElement"], "mappings": "ijXA+BO,IAAIA,SAAW,WAQlB,OAPAA,SAAWC,OAAOC,QAAU,SAAkBC,GAC1C,IAAK,IAAIC,EAAGC,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAE5C,IAAK,IAAII,KADTL,EAAIG,UAAUF,GACOJ,OAAOS,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,IAE9E,OAAON,CACV,GACeU,MAAMC,KAAMP,UAChC,EAcO,SAASQ,WAAWC,EAAYC,EAAQC,EAAKC,GAChD,IAA2HC,EAAvHC,EAAId,UAAUC,OAAQc,EAAID,EAAI,EAAIJ,EAAkB,OAATE,EAAgBA,EAAOlB,OAAOsB,yBAAyBN,EAAQC,GAAOC,EACrH,GAAuB,iBAAZK,SAAoD,mBAArBA,QAAQC,SAAyBH,EAAIE,QAAQC,SAAST,EAAYC,EAAQC,EAAKC,QACpH,IAAK,IAAId,EAAIW,EAAWR,OAAS,EAAQ,GAALH,EAAQA,KAASe,EAAIJ,EAAWX,MAAIiB,GAAKD,EAAI,EAAID,EAAEE,GAAS,EAAJD,EAAQD,EAAEH,EAAQC,EAAKI,GAAKF,EAAEH,EAAQC,KAASI,GAChJ,OAAW,EAAJD,GAASC,GAAKrB,OAAOyB,eAAeT,EAAQC,EAAKI,GAAIA,CAChE,CAmDO,SAASK,WAAWC,EAAaC,GACpC,GAAuB,iBAAZL,SAAoD,mBAArBA,QAAQM,SAAyB,OAAON,QAAQM,SAASF,EAAaC,EACpH,CAEO,SAASE,UAAUC,EAASC,EAAYC,EAAGC,GAE9C,OAAO,IAAKD,EAAAA,GAAUE,UAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUC,GAAS,IAAMC,EAAKN,EAAUO,KAAKF,IAAW,MAAOG,GAAKL,EAAOK,GAAO,CAC3F,SAASC,EAASJ,GAAS,IAAMC,EAAKN,EAAiBU,MAAEL,IAAW,MAAOG,GAAKL,EAAOK,GAAO,CAC9F,SAASF,EAAKK,GAJlB,IAAeN,EAIaM,EAAOC,KAAOV,EAAQS,EAAON,SAJ1CA,EAIyDM,EAAON,iBAJ/BN,EAAIM,EAAQ,IAAIN,GAAE,SAAUG,GAAWA,EAAQG,EAAO,KAIhBQ,KAAKT,EAAWK,EAAY,CAC9GH,GAAMN,EAAYA,EAAUtB,MAAMmB,EAASC,GAAc,KAAKS,OACtE,GACA,CAEO,SAASO,YAAYjB,EAASkB,GACjC,IAAsGC,EAAGC,EAAGjD,EAAxGkD,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPpD,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAG,EAAIqD,KAAM,GAAIC,IAAK,IAAeC,EAAIzD,OAAO0D,QAA4B,mBAAbC,SAA0BA,SAAW3D,QAAQS,WACtL,OAAOgD,EAAEhB,KAAOmB,EAAK,GAAIH,EAASb,MAAIgB,EAAK,GAAIH,EAAUI,OAAID,EAAK,GAAsB,mBAAXE,SAA0BL,EAAEK,OAAOC,UAAY,WAAa,OAAOlD,IAAO,GAAG4C,EAC1J,SAASG,EAAKvD,GAAK,OAAO,SAAU2D,GAAYxB,IAClCyB,EADuC,CAAC5D,EAAG2D,GAErD,GAAId,EAAG,MAAM,IAAIgB,UAAU,mCAC3B,KAA8Bd,EAAvBK,GAAaQ,EAAPR,EAAI,GAAiB,EAAKL,GAAG,IACtC,GAAIF,EAAI,EAAGC,IAAMjD,EAAY,EAAR+D,EAAG,GAASd,EAAUU,OAAII,EAAG,GAAKd,EAASP,SAAO1C,EAAIiD,EAAUU,SAAM3D,EAAES,KAAKwC,GAAI,GAAKA,EAAEV,SAAWvC,EAAIA,EAAES,KAAKwC,EAAGc,EAAG,KAAKnB,KAAM,OAAO5C,EAE3J,OADIiD,EAAI,GAAMc,EAAH/D,EAAQ,CAAS,EAAR+D,EAAG,GAAQ/D,EAAEqC,OACzB0B,GAAG,IACP,KAAK,EAAG,KAAK,EAAG/D,EAAI+D,EAAI,MACxB,KAAK,EAAc,OAAXb,EAAEC,QAAgB,CAAEd,MAAO0B,EAAG,GAAInB,MAAM,GAChD,KAAK,EAAGM,EAAEC,QAASF,EAAIc,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKb,EAAEI,IAAIW,MAAOf,EAAEG,KAAKY,MAAO,SACxC,QACI,KAAkBjE,EAAe,GAA3BA,EAAIkD,EAAEG,MAAYhD,QAAcL,EAAEA,EAAEK,OAAS,MAAkB,IAAV0D,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEb,EAAI,EAAG,QAAW,CAC5G,GAAc,IAAVa,EAAG,MAAc/D,GAAM+D,EAAG,GAAK/D,EAAE,IAAM+D,EAAG,GAAK/D,EAAE,IAAQkD,EAAEC,MAAQY,EAAG,QAC1E,GAAc,IAAVA,EAAG,IAAYb,EAAEC,MAAQnD,EAAE,GAAMkD,EAAEC,MAAQnD,EAAE,GAAIA,EAAI+D,MAAzD,CACA,KAAI/D,GAAKkD,EAAEC,MAAQnD,EAAE,IAArB,CACIA,EAAE,IAAIkD,EAAEI,IAAIW,MAChBf,EAAEG,KAAKY,MAAO,QAFqD,CAAxCf,EAAEC,MAAQnD,EAAE,GAAIkD,EAAEI,IAAIY,KAAKH,EADe,EAK7EA,EAAKhB,EAAKtC,KAAKoB,EAASqB,EAC8B,CAAxD,MAAOV,GAAKuB,EAAK,CAAC,EAAGvB,GAAIS,EAAI,CAAE,CAAW,QAAED,EAAIhD,EAAI,CAAI,CAC1D,GAAY,EAAR+D,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE1B,MAAO0B,EAAG,GAAKA,EAAG,QAAK,EAAQnB,MAAM,EArBf,CAAG,CAuBtE,CA+DO,SAASuB,cAAcC,EAAIC,EAAMC,GACpC,GAAIA,GAA6B,IAArBlE,UAAUC,OAAc,IAAK,IAA4BkE,EAAxBrE,EAAI,EAAGsE,EAAIH,EAAKhE,OAAYH,EAAIsE,EAAGtE,KACxEqE,GAAQrE,KAAKmE,KACRE,EAAAA,GAASE,MAAMlE,UAAUmE,MAAMjE,KAAK4D,EAAM,EAAGnE,IAC/CA,GAAKmE,EAAKnE,IAGrB,OAAOkE,EAAGO,OAAOJ,GAAME,MAAMlE,UAAUmE,MAAMjE,KAAK4D,GACtD,CCxNA,IAAMrE,IAAE4E,WAAWpC,IAAExC,IAAE6E,kBAAa,IAAS7E,IAAE8E,UAAU9E,IAAE8E,SAASC,eAAe,uBAAuBC,SAASzE,WAAW,YAAY0E,cAAc1E,UAAUN,IAAE2D,SAASsB,IAAE,IAAIC,QAAQC,IAAQC,MAAAA,WAAAA,CAAYrF,EAAEwC,EAAE0C,GAAG,GAAGvE,KAAK2E,cAAa,EAAGJ,IAAIjF,IAAE,MAAMsF,MAAM,qEAAqE5E,KAAK6E,QAAQxF,EAAEW,KAAKX,EAAEwC,CAAC,CAACiD,cAAAA,GAAiBC,IAAI1F,EAAEW,KAAKuE,EAAE,IAAuC1C,EAAjCvC,EAAEU,KAAKX,EAAwJ,OAAnJwC,UAAG,IAASxC,QAAoD,KAAZA,GAA/BwC,OAAE,IAASvC,GAAG,IAAIA,EAAEI,QAAa6E,IAAES,IAAI1F,GAAaD,MAAKW,KAAKuE,EAAElF,EAAE,IAAIiF,eAAeW,YAAYjF,KAAK6E,SAAShD,IAAG0C,IAAEW,IAAI5F,EAAED,GAAWA,CAAC,CAAC8F,QAAAA,GAAW,OAAOnF,KAAK6E,OAAO,GAAQrE,IAAEnB,GAAG,IAAIG,IAAE,iBAAiBH,EAAEA,EAAEA,EAAE,QAAG,EAAOC,KAAmlBiB,IAAEsB,IAAExC,GAAGA,EAAEA,IAAGA,GAAAA,aAAaiF,cAAc,CAAKS,IAAIlD,EAAE,GAAG,IAAI,IAAMvC,KAA2CD,EAApC+F,SAASvD,GAAGvC,EAAEuF,QAAQ,OAAOrE,IAAEqB,EAAM,CAAExC,OAAAA,ICAlzCgG,GAAG9F,IAAEqB,eAAeiB,IAAEpB,yBAAyBD,IAAE8E,oBAAoBC,IAAEC,sBAAsBjB,IAAEkB,eAAejG,KAAGL,OAAOuG,IAAEzB,WAAW1D,IAAEmF,IAAEC,aAAa9B,IAAEtD,IAAEA,IAAEqF,YAAY,GAAGjG,IAAE+F,IAAEG,+BAA+BvF,IAAE,CAACjB,EAAEC,IAAID,EAAEyG,IAAE,CAACC,WAAAA,CAAY1G,EAAEC,GAAG,OAAOA,GAAG,KAAK0G,QAAQ3G,EAAEA,EAAEwE,IAAE,KAAK,MAAM,KAAK1E,OAAO,KAAK2E,MAAMzE,EAAE,MAAMA,EAAEA,EAAE4G,KAAKC,UAAU7G,GAAG,OAAOA,CAAC,EAAE8G,aAAAA,CAAc9G,EAAEC,GAAGyF,IAAIxF,EAAEF,EAAE,OAAOC,GAAG,KAAK0G,QAAQzG,EAAE,OAAOF,EAAE,MAAM,KAAK+G,OAAO7G,EAAE,OAAOF,EAAE,KAAK+G,OAAO/G,GAAG,MAAM,KAAKF,OAAO,KAAK2E,MAAM,IAAIvE,EAAE0G,KAAKI,MAAMhH,EAAkB,CAAf,MAAMA,GAAGE,EAAE,IAAI,EAAE,OAAOA,CAAC,GAAG8C,IAAE,CAAChD,EAAEC,KAAKC,IAAEF,EAAEC,GAAGgD,IAAE,CAACgE,WAAU,EAAGC,KAAKC,OAAOC,UAAUX,IAAEY,SAAQ,EAAGC,WAAWtE,KAAGY,OAAOjC,WAAWiC,OAAO,YAAYyC,IAAEkB,sBAAsB,IAAIpC,QAAAA,MAAcqC,UAAUC,YAAYC,qBAAsB1H,CAAAA,GAAGW,KAAKgH,QAAQhH,KAAK6D,IAAI,IAAIN,KAAKlE,EAAE,CAAC4H,6BAAAA,GAAgC,OAAOjH,KAAKkH,WAAWlH,KAAKmH,MAAM,IAAInH,KAAKmH,KAAKC,OAAO,CAACC,qBAAAA,CAAsBhI,EAAEC,EAAEgD,KAAG,IAAyG9B,EAAtGlB,EAAEgI,QAAQhI,EAAEgH,WAAU,GAAItG,KAAKgH,OAAOhH,KAAKuH,kBAAkBrC,IAAI7F,EAAEC,GAAIA,EAAEkI,aAAkBjI,EAAE0D,cAA6C,KAApCzC,EAAER,KAAKyH,sBAAsBpI,EAAEE,EAAED,KAAeuC,IAAE7B,KAAKJ,UAAUP,EAAEmB,GAAG,CAACiH,6BAA6BpI,EAAEC,EAAEC,GAAG,IAAMyF,IAAInD,EAAEqD,IAAIK,GAAG/E,IAAER,KAAKJ,UAAUP,IAAI,CAAC2F,GAAAA,GAAM,OAAOhF,KAAKV,EAAE,EAAE4F,GAAAA,CAAI7F,GAAGW,KAAKV,GAAGD,CAAC,GAAG,MAAM,CAAC2F,GAAAA,GAAM,OAAOnD,GAAG/B,KAAKE,KAAK,EAAEkF,GAAAA,CAAI5F,GAAG,IAAMkB,EAAEqB,GAAG/B,KAAKE,MAAMuF,EAAEzF,KAAKE,KAAKV,GAAGU,KAAK0H,cAAcrI,EAAEmB,EAAEjB,EAAE,EAAEoI,cAAa,EAAGC,YAAW,EAAG,CAACC,yBAA0BxI,CAAAA,GAAG,OAAOW,KAAKuH,kBAAkBvC,IAAI3F,IAAIiD,GAAC,CAAC0E,WAAAA,GAAc,IAA4D3H,EAAzDW,KAAKH,eAAeS,IAAE,yBAAmCjB,EAAEG,IAAEQ,OAAQkH,gBAAW,IAAS7H,EAAEwE,IAAI7D,KAAK6D,EAAE,IAAIxE,EAAEwE,IAAI7D,KAAKuH,kBAAkB,IAAIO,IAAIzI,EAAEkI,mBAAkB,CAACL,eAAkBA,GAAA,IAAGlH,KAAKH,eAAeS,IAAE,cAAzB,CAA8C,GAAGN,KAAK+H,WAAU,EAAG/H,KAAKgH,OAAOhH,KAAKH,eAAeS,IAAE,eAAe,CAAC,IAAMjB,EAAEW,KAAKgI,WAAW1I,EAAE,IAAIiG,IAAElG,MAAMkF,IAAElF,IAAI,IAAI,IAAME,KAAKD,EAAEU,KAAKqH,eAAe9H,EAAEF,EAAEE,GAAG,CAAC,IAAMF,EAAEW,KAAKiD,OAAOjC,UAAU,GAAG,OAAO3B,EAAE,CAAC,IAAMC,EAAEsH,oBAAoB5B,IAAI3F,GAAG,QAAG,IAASC,EAAE,IAAI,IAAMD,EAAEE,KAAKD,EAAEU,KAAKuH,kBAAkBrC,IAAI7F,EAAEE,EAAE,CAACS,KAAKmH,KAAK,IAAIW,IAAI,IAAI,IAAMzI,EAAEC,KAAKU,KAAKuH,kBAAkB,CAAC,IAAMhI,EAAES,KAAKiI,KAAK5I,EAAEC,QAAG,IAASC,GAAGS,KAAKmH,KAAKjC,IAAI3F,EAAEF,EAAE,CAACW,KAAKkI,cAAclI,KAAKmI,eAAenI,KAAKoI,OAA5c,CAAmd,CAACD,qBAAsB7I,CAAAA,GAAG,IAAMC,EAAE,GAAG,GAAGuE,MAAMuE,QAAQ/I,GAAG,CAAC,IAAMuC,EAAE,IAAIyG,IAAIhJ,EAAEiJ,KAAK,KAAKC,WAAW,IAAI,IAAMlJ,KAAKuC,EAAEtC,EAAEkJ,QAAQpJ,IAAEC,GAAG,WAAM,IAASA,GAAGC,EAAEgE,KAAKlE,IAAEC,IAAI,OAAOC,CAAC,CAAC0I,WAAAA,CAAY5I,EAAEC,GAAuB,OAAM,KAApBC,EAAED,EAAEgH,gBAAuB,EAAO,iBAAiB/G,EAAEA,EAAE,iBAAiBF,EAAEA,EAAEqJ,mBAAc,CAAM,CAAChE,WAAAA,GAAciE,QAAQ3I,KAAK4I,UAAK,EAAO5I,KAAK6I,iBAAgB,EAAG7I,KAAK8I,YAAW,EAAG9I,KAAK+I,KAAK,KAAK/I,KAAKgJ,MAAM,CAACA,IAAAA,GAAOhJ,KAAKiJ,KAAK,IAAI3H,SAASjC,GAAGW,KAAKkJ,eAAe7J,IAAIW,KAAKmJ,KAAK,IAAIrB,IAAI9H,KAAKoJ,OAAOpJ,KAAK0H,gBAAgB1H,KAAK0E,YAAYb,GAAGwF,SAAShK,GAAGA,EAAEW,OAAO,CAACsJ,aAAAA,CAAcjK,IAAIW,KAAKuJ,OAAO,IAAIjB,KAAKkB,IAAInK,QAAG,IAASW,KAAKyJ,YAAYzJ,KAAK0J,aAAarK,EAAEsK,iBAAiB,CAACC,gBAAAA,CAAiBvK,GAAGW,KAAKuJ,MAAMM,OAAOxK,EAAE,CAAC+J,IAAAA,GAAO,IAA+D7J,EAAzDF,EAAE,IAAIyI,IAAyC,IAAUvI,KAA7CS,KAAK0E,YAAY6C,kBAAmCH,OAAOpH,KAAKH,eAAeN,KAAKF,EAAE6F,IAAI3F,EAAES,KAAKT,WAAWS,KAAKT,IAAW,EAAPF,EAAEyK,OAAS9J,KAAK4I,KAAKvJ,EAAE,CAAC0K,gBAAAA,GAAmB,IAAM1K,EAAEW,KAAKgK,YAAYhK,KAAKiK,aAAajK,KAAK0E,YAAYwF,mBAAmB,MDAhiE,EAAC5K,EAAEiF,KAAK,GAAG1C,IAAEvC,EAAE6K,mBAAmB5F,EAAE6F,KAAK/K,GAAGA,aAAaiF,cAAcjF,EAAEA,EAAEyF,kBAAkB,IAAI,IAAMjD,KAAK0C,EAAE,CAAC,IAAMA,EAAE8F,SAASC,cAAc,SAAS9K,EAAEH,IAAEkL,cAAS,IAAS/K,GAAG+E,EAAEiG,aAAa,QAAQhL,GAAG+E,EAAEkG,YAAY5I,EAAEgD,QAAQvF,EAAEoL,YAAYnG,EAAE,GCAqzDjF,CAAED,EAAEW,KAAK0E,YAAYwD,eAAe7I,CAAC,CAACsL,iBAAAA,GAAoB3K,KAAKyJ,aAAazJ,KAAK+J,mBAAmB/J,KAAKkJ,gBAAe,GAAIlJ,KAAKuJ,MAAMF,SAAShK,GAAGA,EAAEsK,mBAAmB,CAACT,cAAAA,CAAe7J,GAAAA,CAAIuL,oBAAAA,GAAuB5K,KAAKuJ,MAAMF,SAAShK,GAAGA,EAAEwL,sBAAsB,CAACC,wBAAAA,CAAyBzL,EAAEC,EAAEC,GAAGS,KAAK+K,KAAK1L,EAAEE,EAAE,CAACyL,IAAAA,CAAK3L,EAAEC,GAAG,IAAMC,EAAES,KAAK0E,YAAY6C,kBAAkBvC,IAAI3F,GAAGwC,EAAE7B,KAAK0E,YAAYuD,KAAK5I,EAAEE,QAAM,IAASsC,IAAG,IAAKtC,EAAEmH,UAAelG,QAAG,IAASjB,EAAEkH,WAAWV,YAAYxG,EAAEkH,UAAUX,KAAGC,YAAYzG,EAAEC,EAAEgH,MAAMvG,KAAK+I,KAAK1J,EAAE,MAAMmB,EAAER,KAAKiL,gBAAgBpJ,GAAG7B,KAAKwK,aAAa3I,EAAErB,GAAGR,KAAK+I,KAAK,KAAK,CAACgC,IAAAA,CAAK1L,EAAEC,GAAG,IAAMC,EAAES,KAAK0E,YAAY7C,EAAEtC,EAAE4H,KAAKnC,IAAI3F,GAAG,QAAG,IAASwC,GAAG7B,KAAK+I,OAAOlH,EAAE,CAAC,IAAMxC,EAAEE,EAAEsI,mBAAmBhG,GAAGrB,EAAE,mBAAmBnB,EAAEoH,UAAU,CAACN,cAAc9G,EAAEoH,gBAAW,IAASpH,EAAEoH,WAAWN,cAAc9G,EAAEoH,UAAUX,IAAE9F,KAAK+I,KAAKlH,EAAE7B,KAAK6B,GAAGrB,EAAE2F,cAAc7G,EAAED,EAAEkH,MAAMvG,KAAK+I,KAAK,IAAI,CAAC,CAACrB,aAAAA,CAAcrI,EAAEC,EAAEC,GAAG,QAAG,IAASF,EAAE,CAAC,MAAGE,IAAIS,KAAK0E,YAAYmD,mBAAmBxI,IAAOsH,YAAYtE,KAAGrC,KAAKX,GAAGC,GAAG,OAAOU,KAAKoB,EAAE/B,EAAEC,EAAEC,EAAE,EAAC,IAAKS,KAAK6I,kBAAkB7I,KAAKiJ,KAAKjJ,KAAKkL,OAAO,CAAC9J,CAAAA,CAAE/B,EAAEC,EAAEC,GAAGS,KAAKmJ,KAAKgC,IAAI9L,IAAIW,KAAKmJ,KAAKjE,IAAI7F,EAAEC,IAAG,IAAKC,EAAEmH,SAAS1G,KAAK+I,OAAO1J,IAAIW,KAAKoL,OAAO,IAAI9C,KAAKkB,IAAInK,EAAE,CAAC6L,aAAalL,KAAK6I,iBAAgB,EAAG,UAAU7I,KAAKiJ,IAA+B,CAA1B,MAAM5J,GAAGiC,QAAQE,OAAOnC,EAAE,CAAC,IAAMA,EAAEW,KAAKqL,iBAAiB,OAAO,MAAMhM,SAASA,GAAGW,KAAK6I,eAAe,CAACwC,cAAAA,GAAiB,OAAOrL,KAAKsL,eAAe,CAACA,aAAAA,GAAgB,GAAItL,KAAK6I,gBAAT,CAAgC,IAAI7I,KAAK8I,WAAW,CAAC,GAAG9I,KAAKyJ,aAAazJ,KAAK+J,mBAAmB/J,KAAK4I,KAAK,CAAC,IAAI,IAAMvJ,EAAEC,KAAKU,KAAK4I,KAAK5I,KAAKX,GAAGC,EAAEU,KAAK4I,UAAK,CAAM,CAAC,IAAMvJ,EAAEW,KAAK0E,YAAY6C,kBAAkB,GAAU,EAAPlI,EAAEyK,KAAO,IAAI,IAAMxK,EAAEC,KAAKF,GAAI,IAAGE,EAAEgM,SAASvL,KAAKmJ,KAAKgC,IAAI7L,SAAI,IAASU,KAAKV,IAAIU,KAAKoB,EAAE9B,EAAEU,KAAKV,GAAGC,EAAE,CAACwF,IAAI1F,GAAE,EAASC,EAAEU,KAAKmJ,KAAK,KAAI9J,EAAEW,KAAKwL,aAAalM,KAAMU,KAAKyL,WAAWnM,GAAGU,KAAKuJ,MAAMF,SAAShK,GAAGA,EAAEqM,iBAAiB1L,KAAK2L,OAAOrM,IAAIU,KAAK4L,MAAwC,CAAjC,MAAMtM,GAAG,MAAMD,GAAE,EAAGW,KAAK4L,OAAOtM,CAAC,CAACD,GAAGW,KAAK6L,KAAKvM,EAAtd,CAAwd,CAACmM,UAAAA,CAAWpM,GAAIwM,CAAAA,IAAAA,CAAKxM,GAAGW,KAAKuJ,MAAMF,SAAShK,GAAGA,EAAEyM,kBAAkB9L,KAAK8I,aAAa9I,KAAK8I,YAAW,EAAG9I,KAAK+L,aAAa1M,IAAIW,KAAKgM,QAAQ3M,EAAE,CAACuM,IAAAA,GAAO5L,KAAKmJ,KAAK,IAAIrB,IAAI9H,KAAK6I,iBAAgB,CAAE,CAACoD,qBAAqB,OAAOjM,KAAKkM,mBAAmB,CAACA,iBAAAA,GAAoB,OAAOlM,KAAKiJ,IAAI,CAACuC,YAAAA,CAAanM,GAAG,OAAQ,CAAA,CAACsM,MAAAA,CAAOtM,GAAGW,KAAKoL,OAAOpL,KAAKoL,KAAK/B,SAAShK,GAAGW,KAAKgL,KAAK3L,EAAEW,KAAKX,MAAMW,KAAK4L,MAAM,CAACI,OAAAA,CAAQ3M,GAAI0M,CAAAA,YAAAA,CAAa1M,GAAI,EAACwH,EAAEqB,cAAc,GAAGrB,EAAEqD,kBAAkB,CAACiC,KAAK,QAAQtF,EAAEvG,IAAE,sBAAsB,IAAIwH,IAAIjB,EAAEvG,IAAE,cAAc,IAAIwH,IAAInI,MAAI,CAACyM,gBAAgBvF,KAAKnB,IAAE2G,0BAA0B,IAAI9I,KAAK,SCA56K,IAAClE,IAAE4E,WAAW1E,IAAEF,IAAEsG,aAAarG,EAAEC,IAAEA,IAAE+M,aAAa,WAAW,CAACC,WAAWlN,GAAGA,SAAI,EAAOwC,EAAE,QAAQ0D,EAAAA,OAASiH,KAAKC,SAASC,QAAQ,GAAG3I,MAAM,MAAMQ,IAAE,IAAIgB,EAAE/F,IAAM+E,IAAAA,OAAK/D,IAAE6J,SAASxG,EAAE,IAAIrD,IAAEmM,cAAc,IAAIpM,EAAElB,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,mBAAmBA,EAAEqG,EAAE5B,MAAMuE,QAA2D/H,EAAE,cAAc+B,EAAE,sDAAsDc,EAAE,OAAOZ,EAAE,KAAKqK,EAAEC,OAAAA,KAAYvM,sBAAsBA,MAAMA,uCAAuC,KAAKX,EAAE,KAAKiD,EAAE,KAAKkK,EAAE,qCAAwFC,EAAjD1N,IAAG,CAACE,KAAKD,KAAK,CAAC0N,WAAW3N,EAAE4N,QAAQ1N,EAAE2N,OAAO5N,IAAMgD,CAAE,GAAiB6K,EAAElK,OAAOmK,IAAI,gBAAgBC,EAAEpK,OAAOmK,IAAI,eAAeE,EAAE,IAAI9I,QAAQ+I,EAAE/M,IAAEgN,iBAAiBhN,IAAE,KAAK,SAASY,EAAE/B,EAAEE,GAAG,GAAImG,EAAErG,IAAKA,EAAEQ,eAAe,OAAqD,YAAO,IAASP,EAAEA,EAAEiN,WAAWhN,GAAGA,EAAhF,MAAMqF,MAAM,iCAAqE,CAAC,IAAM6I,EAAE,CAACpO,EAAEE,KAAK,IAA4BiB,EAAtBlB,EAAED,EAAEK,OAAO,EAAE6E,EAAE,GAASV,EAAE,IAAItE,EAAE,QAAQ,IAAIA,EAAE,SAAS,GAAGgB,EAAE8B,EAAE,IAAI0C,IAAIxF,EAAE,EAAEA,EAAED,EAAEC,IAAI,CAAC,IAAiBmG,EAAEI,EAAbxG,EAAED,EAAEE,GAAWe,KAAKgC,EAAE,EAAE,KAAKA,EAAEhD,EAAEI,SAASa,EAAEmN,UAAUpL,EAAc,QAAZwD,EAAEvF,EAAEoN,KAAKrO,MAAcgD,EAAE/B,EAAEmN,UAAUnN,IAAI8B,EAAE,QAAQyD,EAAE,GAAGvF,EAAE4C,OAAE,IAAS2C,EAAE,GAAGvF,EAAEgC,OAAE,IAASuD,EAAE,IAAIgH,EAAEc,KAAK9H,EAAE,MAAMtF,EAAEqM,OAAO,KAAK/G,EAAE,GAAG,MAAMvF,EAAEqM,QAAG,IAAS9G,EAAE,KAAKvF,EAAEqM,GAAGrM,IAAIqM,EAAE,MAAM9G,EAAE,IAAIvF,EAAEC,GAAG6B,EAAE/B,GAAE,QAAI,IAASwF,EAAE,GAAGxF,MAAMA,EAAEC,EAAEmN,UAAU5H,EAAE,GAAGpG,OAAOgG,EAAEI,EAAE,GAAGvF,OAAE,IAASuF,EAAE,GAAG8G,EAAE,MAAM9G,EAAE,GAAGlD,EAAEjD,GAAGY,IAAIqC,GAAGrC,IAAIZ,EAAEY,EAAEqM,EAAErM,IAAI4C,GAAG5C,IAAIgC,EAAEhC,EAAE8B,GAAG9B,EAAEqM,EAAEpM,OAAE,GAAQ,IAAMuM,EAAExM,IAAIqM,GAAGvN,EAAEE,EAAE,GAAGsO,WAAW,MAAM,IAAI,GAAGhK,GAAGtD,IAAI8B,EAAE/C,EAAEE,IAAK,GAAHc,GAAMiE,EAAEhB,KAAKmC,GAAGpG,EAAEyE,MAAM,EAAEzD,GAAGuB,EAAEvC,EAAEyE,MAAMzD,GAAGiF,EAAEwH,GAAGzN,EAAEiG,QAAQjF,EAAEf,EAAEwN,EAAE,CAAC,MAAM,CAAC3L,EAAE/B,EAAEwE,GAAGxE,EAAEC,IAAI,QAAQ,IAAIC,EAAE,SAAS,IAAIA,EAAE,UAAU,KAAKgF,EAAE,EAAA,MAAQuJ,EAAEpJ,WAAAA,EAAauI,QAAQ5N,EAAE2N,WAAW1N,GAAGE,GAAGuF,IAAIvE,EAAER,KAAK+N,MAAM,GAAGhJ,IAAIxE,EAAE,EAAEmF,EAAE,EAAE,IAAMI,EAAEzG,EAAEK,OAAO,EAAEY,EAAEN,KAAK+N,OAAO1L,EAAEc,GAAGsK,EAAEpO,EAAEC,GAAG,GAAGU,KAAKgO,GAAGF,EAAExD,cAAcjI,EAAE7C,GAAG+N,EAAEU,YAAYjO,KAAKgO,GAAGE,QAAQ,IAAI5O,GAAG,IAAIA,EAAE,CAAC,IAAMD,EAAEW,KAAKgO,GAAGE,QAAQC,WAAW9O,EAAE+O,eAAe/O,EAAEgP,WAAW,CAAC,KAAK,QAAQ7N,EAAE+M,EAAEe,aAAahO,EAAEZ,OAAOoG,GAAG,CAAC,GAAG,IAAItF,EAAE+N,SAAS,CAAC,GAAG/N,EAAEgO,gBAAgB,IAAI,IAAMnP,KAAKmB,EAAEiO,oBAAoB,GAAGpP,EAAEqP,SAAS7M,GAAG,CAAC,IAAMtC,EAAE4D,EAAEuC,KAAKpG,EAAEkB,EAAEmO,aAAatP,GAAGuP,MAAMrJ,GAAG1D,EAAE,eAAe8L,KAAKpO,GAAGe,EAAEiD,KAAK,CAACgD,KAAK,EAAEsI,MAAMtO,EAAEuO,KAAKjN,EAAE,GAAGoL,QAAQ3N,EAAEyP,KAAK,MAAMlN,EAAE,GAAGmN,EAAE,MAAMnN,EAAE,GAAGoN,EAAE,MAAMpN,EAAE,GAAGqN,EAAEC,IAAI3O,EAAEyK,gBAAgB5L,EAAE,MAAMA,EAAEwO,WAAWtI,KAAKjF,EAAEiD,KAAK,CAACgD,KAAK,EAAEsI,MAAMtO,IAAIC,EAAEyK,gBAAgB5L,IAAI,GAAGyN,EAAEc,KAAKpN,EAAE4O,SAAS,CAAC,IAAM/P,EAAEmB,EAAEiK,YAAYmE,MAAMrJ,GAAGjG,EAAED,EAAEK,OAAO,EAAE,GAAK,EAAFJ,EAAI,CAACkB,EAAEiK,YAAYlL,IAAEA,IAAEqG,YAAY,GAAG,IAAIb,IAAIxF,EAAE,EAAEA,EAAED,EAAEC,IAAIiB,EAAE6O,OAAOhQ,EAAEE,GAAGsE,KAAK0J,EAAEe,WAAWhO,EAAEiD,KAAK,CAACgD,KAAK,EAAEsI,QAAQtO,IAAIC,EAAE6O,OAAOhQ,EAAEC,GAAGuE,IAAI,CAAC,CAAC,MAAM,GAAG,IAAIrD,EAAE+N,SAAS,GAAG/N,EAAE8O,OAAO/K,IAAEjE,EAAEiD,KAAK,CAACgD,KAAK,EAAEsI,MAAMtO,QAAQ,CAACwE,IAAI1F,GAAE,EAAG,MAAK,KAAMA,EAAEmB,EAAE8O,KAAKC,QAAQhK,EAAElG,EAAE,KAAKiB,EAAEiD,KAAK,CAACgD,KAAK,EAAEsI,MAAMtO,IAAIlB,GAAGkG,EAAE7F,OAAO,CAAC,CAACa,GAAG,CAAC,CAAC+J,oBAAAA,CAAqBjL,EAAEE,GAAG,IAAMD,EAAEkB,IAAE8J,cAAc,YAAY,OAAOhL,EAAEkQ,UAAUnQ,EAAEC,CAAC,EAAE,SAASmQ,EAAEpQ,EAAEE,EAAED,EAAED,EAAEwC,GAAG,GAAGtC,IAAI4N,EAAP,CAAkBpI,IAAIQ,OAAE,IAAS1D,EAAEvC,EAAEoQ,OAAO7N,GAAGvC,EAAEqQ,KAAK,IAAMpL,EAAEhE,EAAEhB,QAAG,EAAOA,EAAEqQ,gBAAuBrK,GAAGb,cAAcH,IAAIgB,GAAGsK,QAAO,QAAI,IAAStL,EAAEgB,OAAE,GAAQA,EAAE,IAAIhB,EAAElF,IAAKyQ,KAAKzQ,EAAEC,EAAEuC,QAAI,IAASA,GAAGvC,EAAEoQ,OAAO,IAAI7N,GAAG0D,EAAEjG,EAAEqQ,KAAKpK,QAAG,IAASA,IAAIhG,EAAEkQ,EAAEpQ,EAAEkG,EAAEwK,KAAK1Q,EAAEE,EAAE2N,QAAQ3H,EAAE1D,GAApP,CAA2E,OAA6KtC,CAAC,CAAAwF,IAAAiL,UAAStL,WAAAA,CAAYrF,EAAEE,GAAGS,KAAKiQ,KAAK,GAAGjQ,KAAKkQ,UAAK,EAAOlQ,KAAKmQ,KAAK9Q,EAAEW,KAAKoQ,KAAK7Q,CAAC,CAAC8Q,cAAAA,GAAiB,OAAOrQ,KAAKoQ,KAAKC,UAAU,CAACC,QAAAA,GAAW,OAAOtQ,KAAKoQ,KAAKE,IAAI,CAACxK,CAAAA,CAAEzG,GAAG,IAAM2O,IAAIE,QAAQ3O,GAAGwO,MAAMzO,GAAGU,KAAKmQ,KAAKtO,GAAGxC,GAAGkR,eAAe/P,KAAGgQ,WAAWjR,GAAE,GAAIgO,EAAEU,YAAYpM,EAAEkD,IAAIQ,EAAEgI,EAAEe,WAAW/J,EAAE,EAAE/E,EAAE,EAAEqE,EAAEvE,EAAE,GAAG,UAAK,IAASuE,GAAG,CAAC,GAAGU,IAAIV,EAAEgL,MAAM,CAAC9J,IAAIxF,EAAE,IAAIsE,EAAE0C,KAAKhH,EAAE,IAAIkR,EAAElL,EAAEA,EAAEmL,YAAY1Q,KAAKX,GAAG,IAAIwE,EAAE0C,KAAKhH,EAAE,IAAIsE,EAAEkL,KAAKxJ,EAAE1B,EAAEiL,KAAKjL,EAAEoJ,QAAQjN,KAAKX,GAAG,IAAIwE,EAAE0C,OAAOhH,EAAE,IAAIoR,EAAEpL,EAAEvF,KAAKX,IAAIW,KAAKiQ,KAAK1M,KAAKhE,GAAGsE,EAAEvE,IAAIE,EAAE,CAAC+E,IAAIV,GAAGgL,QAAQtJ,EAAEgI,EAAEe,WAAW/J,IAAI,CAAC,OAAOgJ,EAAEU,YAAYzN,IAAEqB,CAAC,CAAClC,CAAAA,CAAEN,GAAG0F,IAAIxF,EAAE,EAAE,IAAI,IAAMD,KAAKU,KAAKiQ,cAAc3Q,SAAI,IAASA,EAAE2N,SAAS3N,EAAEsR,KAAKvR,EAAEC,EAAEC,GAAGA,GAAGD,EAAE2N,QAAQvN,OAAO,GAAGJ,EAAEsR,KAAKvR,EAAEE,KAAKA,GAAG,SAAQkR,EAAEH,QAAAA,GAAW,OAAOtQ,KAAKoQ,MAAME,MAAMtQ,KAAK6Q,IAAI,CAACnM,WAAAA,CAAYrF,EAAEE,EAAED,EAAEuC,GAAG7B,KAAKuG,KAAK,EAAEvG,KAAK8Q,KAAKzD,EAAErN,KAAKkQ,UAAK,EAAOlQ,KAAK+Q,KAAK1R,EAAEW,KAAKgR,KAAKzR,EAAES,KAAKoQ,KAAK9Q,EAAEU,KAAKiR,QAAQpP,EAAE7B,KAAK6Q,KAAKhP,GAAG6H,cAAa,CAAE,CAAC2G,cAAAA,GAAiBtL,IAAI1F,EAAEW,KAAK+Q,KAAKV,WAAW,IAAM9Q,EAAES,KAAKoQ,KAAK,YAAO,IAAS7Q,GAAG,KAAKF,GAAGkP,SAAahP,EAAE8Q,WAAYhR,CAAC,CAAC6R,gBAAgB,OAAOlR,KAAK+Q,IAAI,CAACI,WAAAA,GAAc,OAAOnR,KAAKgR,IAAI,CAACJ,IAAAA,CAAKvR,EAAEE,EAAES,MAAMX,EAAEoQ,EAAEzP,KAAKX,EAAEE,GAAGgB,EAAElB,GAAGA,IAAIgO,GAAG,MAAMhO,GAAG,KAAKA,GAAGW,KAAK8Q,OAAOzD,GAAGrN,KAAKoR,OAAOpR,KAAK8Q,KAAKzD,GAAGhO,IAAIW,KAAK8Q,MAAMzR,IAAI8N,GAAGnN,KAAKuC,EAAElD,QAAG,IAASA,EAAE2N,WAAWhN,KAAK8M,EAAEzN,QAAG,IAASA,EAAEkP,SAASvO,KAAKmN,EAAE9N,GAA1zHA,IAAGqG,EAAErG,IAAI,mBAAmBA,IAAI4D,OAAOC,UAAsxH4C,CAAEzG,GAAGW,KAAKmP,EAAE9P,GAAGW,KAAKuC,EAAElD,EAAE,CAACgS,CAAAA,CAAEhS,GAAG,OAAOW,KAAK+Q,KAAKV,WAAWiB,aAAajS,EAAEW,KAAKgR,KAAK,CAAC7D,CAAAA,CAAE9N,GAAGW,KAAK8Q,OAAOzR,IAAIW,KAAKoR,OAAOpR,KAAK8Q,KAAK9Q,KAAKqR,EAAEhS,GAAG,CAACkD,CAAAA,CAAElD,GAAGW,KAAK8Q,OAAOzD,GAAG9M,EAAEP,KAAK8Q,MAAM9Q,KAAK+Q,KAAKL,YAAYpB,KAAKjQ,EAAEW,KAAKmN,EAAE3M,IAAE+Q,eAAelS,IAAIW,KAAK8Q,KAAKzR,CAAC,CAACyN,CAAAA,CAAEzN,GAAG,IAAM6N,OAAO3N,EAAEyN,WAAW1N,GAAGD,EAAEwC,EAAE,iBAAiBvC,EAAEU,KAAKwR,KAAKnS,SAAI,IAASC,EAAE0O,KAAK1O,EAAE0O,GAAGF,EAAExD,cAAclJ,EAAE9B,EAAEiG,EAAEjG,EAAEiG,EAAE,IAAIvF,KAAKiR,UAAU3R,GAAG,GAAGU,KAAK8Q,MAAMX,OAAOtO,EAAE7B,KAAK8Q,KAAKnR,EAAEJ,OAAO,CAAC,IAAMF,EAAE,IAAIoS,IAAE5P,EAAE7B,MAAMV,EAAED,EAAEyG,EAAE9F,KAAKiR,SAAS5R,EAAEM,EAAEJ,GAAGS,KAAKmN,EAAE7N,GAAGU,KAAK8Q,KAAKzR,CAAC,CAAC,CAACmS,IAAAA,CAAKnS,GAAG0F,IAAIxF,EAAE+N,EAAEtI,IAAI3F,EAAE4N,SAAS,YAAO,IAAS1N,GAAG+N,EAAEpI,IAAI7F,EAAE4N,QAAQ1N,EAAE,IAAIuO,EAAEzO,IAAIE,CAAC,CAAC4P,CAAAA,CAAE9P,GAAGqG,EAAE1F,KAAK8Q,QAAQ9Q,KAAK8Q,KAAK,GAAG9Q,KAAKoR,QAAQ,IAAsC7L,EAAhChG,EAAES,KAAK8Q,KAAK/L,IAAIzF,EAAEuC,EAAE,EAAE,IAAU0D,KAAKlG,EAAEwC,IAAItC,EAAEG,OAAOH,EAAEgE,KAAKjE,EAAE,IAAImR,EAAEzQ,KAAKqR,EAAExN,KAAK7D,KAAKqR,EAAExN,KAAK7D,KAAKA,KAAKiR,UAAU3R,EAAEC,EAAEsC,GAAGvC,EAAEsR,KAAKrL,GAAG1D,IAAIA,EAAEtC,EAAEG,SAASM,KAAKoR,KAAK9R,GAAGA,EAAE0R,KAAKN,YAAY7O,GAAGtC,EAAEG,OAAOmC,EAAE,CAACuP,IAAAA,CAAK/R,EAAEW,KAAK+Q,KAAKL,YAAYnR,GAAG,IAAIS,KAAK0R,QAAO,GAAG,EAAGnS,GAAGF,GAAGA,IAAIW,KAAKgR,MAAM,CAAC,IAAMzR,EAAEF,EAAEqR,YAAYrR,EAAEsS,SAAStS,EAAEE,CAAC,CAAC,CAACqS,YAAAA,CAAavS,QAAG,IAASW,KAAKoQ,OAAOpQ,KAAK6Q,KAAKxR,EAAEW,KAAK0R,OAAOrS,GAAG,QAAQ8P,EAAEC,WAAAA,GAAc,OAAOpP,KAAK6R,QAAQzC,OAAO,CAACkB,QAAWA,GAAA,OAAOtQ,KAAKoQ,KAAKE,IAAI,CAAC5L,WAAAA,CAAYrF,EAAEE,EAAED,EAAEuC,EAAE0D,GAAGvF,KAAKuG,KAAK,EAAEvG,KAAK8Q,KAAKzD,EAAErN,KAAKkQ,UAAK,EAAOlQ,KAAK6R,QAAQxS,EAAEW,KAAK8O,KAAKvP,EAAES,KAAKoQ,KAAKvO,EAAE7B,KAAKiR,QAAQ1L,EAAW,EAATjG,EAAEI,QAAU,KAAKJ,EAAE,IAAI,KAAKA,EAAE,IAAIU,KAAK8Q,KAAKhN,MAAMxE,EAAEI,OAAO,GAAGoS,KAAK,IAAItL,QAAQxG,KAAKiN,QAAQ3N,GAAGU,KAAK8Q,KAAKzD,CAAC,CAACuD,IAAAA,CAAKvR,EAAEE,EAAES,KAAKV,EAAEuC,GAAG,IAAM0D,EAAEvF,KAAKiN,QAAQlI,IAAIR,GAAE,EAAG,QAAG,IAASgB,EAAElG,EAAEoQ,EAAEzP,KAAKX,EAAEE,EAAE,IAAGgF,GAAGhE,EAAElB,IAAIA,IAAIW,KAAK8Q,MAAMzR,IAAI8N,KAAMnN,KAAK8Q,KAAKzR,OAAO,CAAC,IAAcG,EAAEgB,EAAVqB,EAAExC,EAAU,IAAIA,EAAEkG,EAAE,GAAG/F,EAAE,EAAEA,EAAE+F,EAAE7F,OAAO,EAAEF,KAAIgB,EAAEiP,EAAEzP,KAAK6B,EAAEvC,EAAEE,GAAGD,EAAEC,MAAO2N,IAAI3M,EAAER,KAAK8Q,KAAKtR,IAAI+E,KAAKhE,EAAEC,IAAIA,IAAIR,KAAK8Q,KAAKtR,GAAGgB,IAAI6M,EAAEhO,EAAEgO,EAAEhO,IAAIgO,IAAIhO,IAAImB,GAAG,IAAI+E,EAAE/F,EAAE,IAAIQ,KAAK8Q,KAAKtR,GAAGgB,CAAC,CAAC+D,IAAI1C,GAAG7B,KAAK+R,EAAE1S,EAAE,CAAC0S,CAAAA,CAAE1S,GAAGA,IAAIgO,EAAErN,KAAK6R,QAAQ5G,gBAAgBjL,KAAK8O,MAAM9O,KAAK6R,QAAQrH,aAAaxK,KAAK8O,KAAKzP,GAAG,GAAG,EAAQ2P,MAAAA,UAAUG,EAAEzK,WAAAA,GAAciE,SAASlJ,WAAWO,KAAKuG,KAAK,CAAC,CAACwL,CAAAA,CAAE1S,GAAGW,KAAK6R,QAAQ7R,KAAK8O,MAAMzP,IAAIgO,OAAE,EAAOhO,CAAC,QAAQ4P,UAAUE,EAAEzK,WAAAA,GAAciE,SAASlJ,WAAWO,KAAKuG,KAAK,CAAC,CAACwL,CAAAA,CAAE1S,GAAGW,KAAK6R,QAAQG,gBAAgBhS,KAAK8O,OAAOzP,GAAGA,IAAIgO,EAAE,EAAC,MAAO6B,UAAUC,EAAEzK,WAAAA,CAAYrF,EAAEE,EAAED,EAAEuC,EAAE0D,GAAGoD,MAAMtJ,EAAEE,EAAED,EAAEuC,EAAE0D,GAAGvF,KAAKuG,KAAK,CAAC,CAACqK,IAAAA,CAAKvR,EAAEE,EAAES,MAAM,IAAqD6B,EAA8E0D,GAA/HlG,EAAEoQ,EAAEzP,KAAKX,EAAEE,EAAE,IAAI8N,KAAKF,IAAe7N,EAAEU,KAAK8Q,KAAKjP,EAAExC,IAAIgO,GAAG/N,IAAI+N,GAAGhO,EAAE4S,UAAU3S,EAAE2S,SAAS5S,EAAE6S,OAAO5S,EAAE4S,MAAM7S,EAAE8S,UAAU7S,EAAE6S,QAAQ5M,EAAElG,IAAIgO,IAAI/N,IAAI+N,GAAGxL,GAAGA,GAAG7B,KAAK6R,QAAQO,oBAAoBpS,KAAK8O,KAAK9O,KAAKV,GAAGiG,GAAGvF,KAAK6R,QAAQQ,iBAAiBrS,KAAK8O,KAAK9O,KAAKX,GAAGW,KAAK8Q,KAAKzR,EAAC,CAACiT,WAAAA,CAAYjT,GAAG,mBAAmBW,KAAK8Q,KAAK9Q,KAAK8Q,KAAKhR,KAAKE,KAAKiR,SAASsB,MAAMvS,KAAK6R,QAAQxS,GAAGW,KAAK8Q,KAAKwB,YAAYjT,EAAE,EAAC,MAAOsR,EAAEjM,WAAAA,CAAYrF,EAAEE,EAAED,GAAGU,KAAK6R,QAAQxS,EAAEW,KAAKuG,KAAK,EAAEvG,KAAKkQ,UAAK,EAAOlQ,KAAKoQ,KAAK7Q,EAAES,KAAKiR,QAAQ3R,CAAC,CAACgR,QAAWA,GAAA,OAAOtQ,KAAKoQ,KAAKE,IAAI,CAACM,IAAAA,CAAKvR,GAAGoQ,EAAEzP,KAAKX,EAAE,EAAO,IAA6D0S,EAAE1S,IAAEmT,uBAA6EC,GAAtDV,IAAIjE,EAAE2C,IAAIpR,IAAEqT,kBAAkB,IAAInP,KAAK,SAAiB,CAAClE,EAAEE,EAAED,KAAK,IAAMuC,EAAEvC,GAAGqT,cAAcpT,EAAEwF,IAAIQ,EAAE1D,EAAE+Q,WAAW,QAAG,IAASrN,EAAE,CAAC,IAAMlG,EAAEC,GAAGqT,cAAc,KAAK9Q,EAAE+Q,WAAWrN,EAAE,IAAIkL,EAAElR,EAAE+R,aAAazN,IAAIxE,GAAGA,OAAE,EAAOC,GAAG,CAAA,EAAG,CAAC,OAAOiG,EAAEqL,KAAKvR,GAAGkG,ICAz6NsN,kBAAgBxT,EAAEqF,WAAAA,GAAciE,SAASlJ,WAAWO,KAAK8S,cAAc,CAACP,KAAKvS,MAAMA,KAAK+S,UAAK,CAAM,CAAChJ,gBAAAA,GAAmB,IAAM1K,EAAEsJ,MAAMoB,mBAAmB,OAAO/J,KAAK8S,cAAcH,eAAetT,EAAE8O,WAAW9O,CAAC,CAACsM,MAAAA,CAAOtM,GAAG,IAAMC,EAAEU,KAAKgT,SAAShT,KAAK8I,aAAa9I,KAAK8S,cAAcpJ,YAAY1J,KAAK0J,aAAaf,MAAMgD,OAAOtM,GAAGW,KAAK+S,KAAKlR,EAAEvC,EAAEU,KAAKyJ,WAAWzJ,KAAK8S,cAAc,CAACnI,iBAAAA,GAAoBhC,MAAMgC,oBAAoB3K,KAAK+S,MAAMnB,cAAa,EAAG,CAAChH,oBAAAA,GAAuBjC,MAAMiC,uBAAuB5K,KAAK+S,MAAMnB,cAAa,EAAG,CAACoB,MAAAA,GAAS,OAAO1T,CAAC,GAAmGC,GAAjGiB,IAAEyS,eAAc,EAAGzS,IAAauH,WAAE,EAAG9D,WAAWiP,2BAA2B,CAACC,WAAW3S,MAAYyD,WAAWmP,2BAA0B7T,IAAI,CAAC4T,WAAW3S,OAA0DyD,WAAWoP,qBAAqB,IAAI9P,KAAK,SCLhyB,IAAA+P,UAAAC,QAAA,SAAAC,UAAA,IAAAC,EAAA,CAAA,+BAAA,gBAAA,gBAAA,aAAA,gBAAA,umBAAA,eAAA,cAAA,gBAAA,cAAA,OAAAD,QAAA,WAAA,OAAAC,CAAA,IAAA,CAAA,SAAAF,QAAAG,EAAAC,GAAA,IAAAC,EAAAJ,UAAA,OAAAD,QAAA,SAAAM,EAAAC,GAAA,OAAAF,EAAAC,GAAA,IAAA,GAAAH,EAAAC,EAAA,OAAA,IAAA,IAAAI,EAAAR,QAAAS,EAAAR,YAAA,IAAA,GAAA,SAAAS,SAAAF,EAAA,MAAAE,SAAAF,EAAA,MAAA,GAAAE,SAAAF,EAAA,MAAA,GAAAE,SAAAF,EAAA,MAAA,EAAAE,SAAAF,EAAA,MAAA,IAAAE,SAAAF,EAAA,MAAA,GAAAE,SAAAF,EAAA,MAAA,GAAAE,SAAAF,EAAA,MAAA,EAAA,MAAAC,EAAAzQ,KAAAyQ,EAAAE,QAAA,CAAA,MAAAC,GAAAH,EAAAzQ,KAAAyQ,EAAAE,QAAA,CAAA,KAAO,IAAME,YAAc,gCAAA,+BCgB3B,SAASC,KAAKC,EAAYC,GACtB,OAAO,IAAIjT,SAAQ,SAAUC,GAAW,OAAOiT,WAAWjT,EAAS+S,EAAYC,KACnF,CA4BA,SAASE,UAAU/S,GACf,QAASA,GAA+B,mBAAfA,EAAMQ,IACnC,CAcA,SAASwS,aAAaC,EAAQC,GAC1B,IACI,IAAIC,EAAgBF,IAChBF,UAAUI,GACVA,EAAc3S,MAAK,SAAUF,GAAU,OAAO4S,GAAS,EAAM5S,EAAQ,IAAI,SAAU8S,GAAS,OAAOF,GAAS,EAAOE,MAGnHF,GAAS,EAAMC,EAK3B,CAFI,MAAOC,GACHF,GAAS,EAAOE,EACxB,CACA,CAMA,SAASC,cAAcC,EAAOJ,EAAUK,GAEpC,YAD4B,IAAxBA,IAAkCA,EAAsB,IACrDhU,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAIkV,EAASC,EAAqB5V,EAAG6V,EACrC,OAAOjT,YAAYnC,MAAM,SAAUqV,GAC/B,OAAQA,EAAG7S,OACP,KAAK,EACD0S,EAAUpR,MAAMkR,EAAMtV,QACtByV,EAAsBG,KAAKF,MAC3B7V,EAAI,EACJ8V,EAAG7S,MAAQ,EACf,KAAK,EACD,OAAMjD,EAAIyV,EAAMtV,QAChBwV,EAAQ3V,GAAKqV,EAASI,EAAMzV,GAAIA,GAChC6V,EAAME,KAAKF,MACED,EAAsBF,GAA7BG,GACND,EAAsBC,EACf,CAAC,EAvEjB,IAAI9T,SAAQ,SAAUC,GACzB,IAAIgU,EAAU,IAAIC,eAClBD,EAAQE,MAAMC,UAAY,WAAc,OAAOnU,GAAY,EAC3DgU,EAAQI,MAAMC,YAAY,KAClC,MAiEoF,CAAC,EAAa,IAH9C,CAAC,EAAa,GAMlD,KAAK,EACDP,EAAG5S,OACH4S,EAAG7S,MAAQ,EACf,KAAK,EAED,QADEjD,EACK,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,EAAc2V,GAE9C,GACA,GACA,CAQA,SAASW,kCAAkCC,GAEvC,OADAA,EAAQ5T,UAAK6T,GAAW,eACjBD,CACX,CAyBA,SAASE,MAAMtU,GACX,OAAOuS,SAASvS,EACpB,CAIA,SAASuU,QAAQvU,GACb,OAAOwU,WAAWxU,EACtB,CACA,SAASyU,WAAWzU,EAAO0U,GACvB,MAAwB,iBAAV1U,GAAsB2U,MAAM3U,GAAS0U,EAAc1U,CACrE,CACA,SAAS4U,YAAYpJ,GACjB,OAAOA,EAAOqJ,QAAO,SAAUC,EAAK9U,GAAS,OAAO8U,GAAO9U,EAAQ,EAAI,EAAG,GAAI,EAClF,CACA,SAAS+U,MAAM/U,EAAOgV,GAElB,YADa,IAATA,IAAmBA,EAAO,GACR,GAAlBlK,KAAKmK,IAAID,GACFlK,KAAKiK,MAAM/U,EAAQgV,GAAQA,GAK9BE,EAAc,EAAIF,EACflK,KAAKiK,MAAM/U,EAAQkV,GAAeA,EAEjD,CAyEA,SAASC,OAAOjK,EAAGpN,GACf,IAAIsX,EAAKlK,EAAE,KAAO,GACdmK,EAAKvX,EAAE,KAAO,GACdwX,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAEjCD,IADAC,IAHwE,MAAPvK,EAAE,KACK,MAAPpN,EAAE,OAGtD,GACb2X,GAAM,MAENF,IADAC,IAN+CtK,EAAE,KAAO,KACTpN,EAAE,KAAO,OAM3C,GACb0X,GAAM,MAMNtK,EAAE,KAJFoK,IADAC,IATkC,MAAPrK,EAAE,KACK,MAAPpN,EAAE,OAShB,KAEPsX,EAAKC,GACL,QACQ,IAHdE,GAAM,OAINrK,EAAE,GAAMsK,GAAM,GAAMC,CACxB,CAKA,SAASC,YAAYxK,EAAGpN,GACpB,IAAIsX,EAAKlK,EAAE,KAAO,GAAIyK,EAAY,MAAPzK,EAAE,GAAa0K,EAAK1K,EAAE,KAAO,GAAI2K,EAAY,MAAP3K,EAAE,GAC/DmK,EAAKvX,EAAE,KAAO,GAAIgY,EAAY,MAAPhY,EAAE,GAAaiY,EAAKjY,EAAE,KAAO,GACpDwX,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAEjCD,IADAC,GAAMI,GAFsDG,EAAY,MAAPlY,EAAE,OAGtD,GACb2X,GAAM,MAKNF,IAJAC,GAAMI,EAAKI,KACE,MAEbR,GADM,MAANA,GACMK,EAAKE,KACE,IACbP,GAAM,MAYNtK,EAAE,KAVFoK,IADAC,GAAMI,EAAKK,KACE,MAEbT,GADM,MAANA,GACMK,EAAKG,KACE,MAEbR,GADM,MAANA,GACMM,EAAKC,KACE,KAEPV,EAAKY,EAAKL,EAAKI,EAAKH,EAAKE,EAAKD,EAAKR,GACnC,QACQ,IAHdE,GAAM,OAINrK,EAAE,GAAMsK,GAAM,GAAMC,CACxB,CAKA,SAASQ,QAAQ/K,EAAGgL,GAChB,IAAId,EAAKlK,EAAE,GAEE,KADbgL,GAAQ,KAEJhL,EAAE,GAAKA,EAAE,GACTA,EAAE,GAAKkK,GAEFc,EAAO,IACZhL,EAAE,GAAMkK,GAAMc,EAAShL,EAAE,KAAQ,GAAKgL,EACtChL,EAAE,GAAMA,EAAE,IAAMgL,EAASd,IAAQ,GAAKc,IAItChL,EAAE,GAAMA,EAAE,KADVgL,GAAQ,IACiBd,IAAQ,GAAKc,EACtChL,EAAE,GAAMkK,GAAMc,EAAShL,EAAE,KAAQ,GAAKgL,EAE9C,CAKA,SAASC,aAAajL,EAAGgL,GAER,IADbA,GAAQ,MAICA,EAAO,IACZhL,EAAE,GAAKA,EAAE,KAAQ,GAAKgL,EACtBhL,EAAE,GAAKA,EAAE,IAAMgL,IAGfhL,EAAE,GAAKA,EAAE,IAAOgL,EAAO,GACvBhL,EAAE,GAAK,GAEf,CAKA,SAASkL,OAAOlL,EAAGpN,GACfoN,EAAE,IAAMpN,EAAE,GACVoN,EAAE,IAAMpN,EAAE,EACd,CACA,IAAIuY,GAAK,CAAC,WAAY,YAClBC,GAAK,CAAC,WAAY,WAMtB,SAASC,QAAQ1S,GACb,IAAI2S,EAAU,CAAC,EAAG3S,EAAE,KAAO,GAC3BuS,OAAOvS,EAAG2S,GACVd,YAAY7R,EAAGwS,IACfG,EAAQ,GAAK3S,EAAE,KAAO,EACtBuS,OAAOvS,EAAG2S,GACVd,YAAY7R,EAAGyS,IACfE,EAAQ,GAAK3S,EAAE,KAAO,EACtBuS,OAAOvS,EAAG2S,EACd,CACA,IAAIC,GAAK,CAAC,WAAY,WAClBC,GAAK,CAAC,WAAY,WAClBC,IAAM,CAAC,EAAG,GACVC,GAAK,CAAC,EAAG,YACTC,GAAK,CAAC,EAAG,WAQb,SAASC,WAAWC,EAAOC,GAWvB,IAVA,IAAItY,EArJR,SAASuY,aAAaF,GAIlB,IADA,IAAIzW,EAAS,IAAI4W,WAAWH,EAAM/Y,QACzBH,EAAI,EAAGA,EAAIkZ,EAAM/Y,OAAQH,IAAK,CAEnC,IAAIsZ,EAAWJ,EAAMK,WAAWvZ,GAEhC,GAAe,IAAXsZ,EACA,OAAA,IAAWE,aAAcC,OAAOP,GAEpCzW,EAAOzC,GAAKsZ,CACpB,CACI,OAAO7W,CACX,CAuIc2W,CAAaF,GAGnBQ,GADAvZ,EAAS,CAAC,EAAGU,EAAIV,SACE,GAAK,GACxBwZ,EAAQxZ,EAAO,GAAKuZ,EACpBE,EAAK,CAAC,EAJVT,EAAOA,GAAQ,GAKXU,EAAK,CAAC,EAAGV,GACTW,EAAK,CAAC,EAAG,GACTC,EAAK,CAAC,EAAG,GAER/Z,EAAI,EAAGA,EAAI2Z,EAAO3Z,GAAQ,GAC3B8Z,EAAG,GAAKjZ,EAAIb,EAAI,GAAMa,EAAIb,EAAI,IAAM,EAAMa,EAAIb,EAAI,IAAM,GAAOa,EAAIb,EAAI,IAAM,GAC7E8Z,EAAG,GAAKjZ,EAAIb,GAAMa,EAAIb,EAAI,IAAM,EAAMa,EAAIb,EAAI,IAAM,GAAOa,EAAIb,EAAI,IAAM,GACzE+Z,EAAG,GAAKlZ,EAAIb,EAAI,IAAOa,EAAIb,EAAI,KAAO,EAAMa,EAAIb,EAAI,KAAO,GAAOa,EAAIb,EAAI,KAAO,GACjF+Z,EAAG,GAAKlZ,EAAIb,EAAI,GAAMa,EAAIb,EAAI,IAAM,EAAMa,EAAIb,EAAI,KAAO,GAAOa,EAAIb,EAAI,KAAO,GAC/E6X,YAAYiC,EAAIlB,IAChBR,QAAQ0B,EAAI,IACZjC,YAAYiC,EAAIjB,IAChBN,OAAOqB,EAAIE,GACX1B,QAAQwB,EAAI,IACZtC,OAAOsC,EAAIC,GACXhC,YAAY+B,EAAId,KAChBxB,OAAOsC,EAAIb,IACXlB,YAAYkC,EAAIlB,IAChBT,QAAQ2B,EAAI,IACZlC,YAAYkC,EAAInB,IAChBL,OAAOsB,EAAIE,GACX3B,QAAQyB,EAAI,IACZvC,OAAOuC,EAAID,GACX/B,YAAYgC,EAAIf,KAChBxB,OAAOuC,EAAIb,IAEfc,EAAG,GAAK,EAERC,EADAD,EAAG,GAAK,GACA,EAER,IAAIE,EAAM,CADVD,EAAG,GAAK,EACM,GACd,OAAQL,GACJ,KAAK,GACDM,EAAI,GAAKnZ,EAAIb,EAAI,IACjBsY,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAKnZ,EAAIb,EAAI,IACjBsY,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAKnZ,EAAIb,EAAI,IACjBsY,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAKnZ,EAAIb,EAAI,IACjBsY,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAKnZ,EAAIb,EAAI,IACjBsY,aAAa0B,EAAK,IAClBzB,OAAOwB,EAAIC,GAEf,KAAK,GACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,GAClBzB,OAAOwB,EAAIC,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBuY,OAAOwB,EAAIC,GACXnC,YAAYkC,EAAIlB,IAChBT,QAAQ2B,EAAI,IACZlC,YAAYkC,EAAInB,IAChBL,OAAOsB,EAAIE,GAEf,KAAK,EACDC,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,IAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,EAAI,GACjBsY,aAAa0B,EAAK,GAClBzB,OAAOuB,EAAIE,GAEf,KAAK,EACDA,EAAI,GAAKnZ,EAAIb,GACbuY,OAAOuB,EAAIE,GACXnC,YAAYiC,EAAIlB,IAChBR,QAAQ0B,EAAI,IACZjC,YAAYiC,EAAIjB,IAChBN,OAAOqB,EAAIE,GAWnB,OARAvB,OAAOqB,EAAIzZ,GACXoY,OAAOsB,EAAI1Z,GACXmX,OAAOsC,EAAIC,GACXvC,OAAOuC,EAAID,GACXlB,QAAQkB,GACRlB,QAAQmB,GACRvC,OAAOsC,EAAIC,GACXvC,OAAOuC,EAAID,IACF,YAAcA,EAAG,KAAO,GAAGhU,SAAS,KAAKpB,WAC7C,YAAcoV,EAAG,KAAO,GAAGhU,SAAS,KAAKpB,WACzC,YAAcqV,EAAG,KAAO,GAAGjU,SAAS,KAAKpB,WACzC,YAAcqV,EAAG,KAAO,GAAGjU,SAAS,KAAKpB,SAClD,CA6EA,SAASyV,YAAYC,EAASC,EAAeC,EAAgB1E,GACzD,IAAI2E,EAAkBza,OAAOiI,KAAKqS,GAASI,QAAO,SAAUC,GAAa,OAzb7E,SAASC,SAASC,EAAUC,GACxB,OAZJ,SAASC,SAASF,EAAUC,GACxB,IAAK,IAAI1a,EAAI,EAAGsE,EAAImW,EAASta,OAAQH,EAAIsE,IAAKtE,EAC1C,GAAIya,EAASza,KAAO0a,EAChB,OAAO,EAGf,OAAO,CACX,CAKYC,CAASF,EAAUC,EAC/B,CAuboFF,CAASJ,EAAgBG,EAAW,IAGhHK,EAAuBtE,kCAAkCd,cAAc6E,GAAiB,SAAUE,GAAa,OA1DvH,SAASM,WAAWC,EAAQX,GACxB,IAAIY,EAAoBzE,kCAAkC,IAAIvU,SAAQ,SAAUiZ,GAC5E,IAAIC,EAAgBlF,KAAKF,MAGzBV,aAAa2F,EAAOI,KAAK,KAAMf,IAAgB,WAE3C,IADA,IAAIgB,EAAW,GACNC,EAAK,EAAGA,EAAKlb,UAAUC,OAAQib,IACpCD,EAASC,GAAMlb,UAAUkb,GAE7B,IAKIC,EALAC,EAAevF,KAAKF,MAAQoF,EAEhC,OAAKE,EAAS,GArB1B,SAASI,oBAAoBF,GACzB,MAA6B,mBAAfA,CAClB,CAwBgBE,CAFAF,EAAaF,EAAS,IAGfH,GAAY,WAAc,OAAU7Y,MAAOkZ,EAAYG,SAAUF,WAG5EN,GAAY,WACR,OAAO,IAAIjZ,SAAQ,SAAU0Z,GACzB,IAAIC,EAAe3F,KAAKF,MACxBV,aAAakG,GAAY,WAErB,IADA,IAAIM,EAAU,GACLP,EAAK,EAAGA,EAAKlb,UAAUC,OAAQib,IACpCO,EAAQP,GAAMlb,UAAUkb,GAE5B,IAAII,EAAWF,EAAevF,KAAKF,MAAQ6F,EAE3C,IAAKC,EAAQ,GACT,OAAOF,EAAW,CAAElG,MAAOoG,EAAQ,GAAIH,SAAUA,IAGrDC,EAAW,CAAEtZ,MAAOwZ,EAAQ,GAAIH,SAAUA,GAClE,GACA,GACA,IAzBuBR,GAAY,WAAc,MAAQ,CAAEzF,MAAO4F,EAAS,GAAIK,SAAUF,KA0BzF,GACA,KACI,OAAO,WACH,OAAOP,EAAkBpY,MAAK,SAAUiZ,GAAkB,OAAOA,MACpE,CACL,CAc8Hf,CAAWX,EAAQK,GAAYJ,EAAiB,GAAEzE,IAC5K,OAAO,WACH,OAAOhU,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAmBob,EAAmBC,EAAgBC,EAAYzM,EAClE,OAAO1M,YAAYnC,MAAM,SAAUqV,GAC/B,OAAQA,EAAG7S,OACP,KAAK,EAAG,MAAO,CAAC,EAAa2X,GAC7B,KAAK,EAED,MAAO,CAAC,EAAapF,cADLM,EAAG5S,QAC+B,SAAU8Y,GAAgB,OAAO1F,kCAAkC0F,IAAkB,GAAEtG,IAC7I,KAAK,EAED,OADAmG,EAAoB/F,EAAG5S,OAChB,CAAC,EAAanB,QAAQka,IAAIJ,IAGrC,KAAK,EAGD,IAFAC,EAAiBhG,EAAG5S,OACpB6Y,EAAa,GACRzM,EAAQ,EAAGA,EAAQ+K,EAAgBla,SAAUmP,EAC9CyM,EAAW1B,EAAgB/K,IAAUwM,EAAexM,GAExD,MAAO,CAAC,EAAcyM,GAE9C,GACA,GACK,CACL,CAuCA,SAASG,YACL,IAAIC,EAAIC,OACJnc,EAAIoc,UAER,OAMM,GANEtF,YAAY,CAChB,gBAAiBoF,EACjB,mBAAoBA,EACpB,gBAAiBA,EACjB,qBAAsBlc,EACtB,qBAAsBA,GAE9B,CAoBA,SAASqc,aAEL,IAAIH,EAAIC,OACJnc,EAAIoc,UACR,OAQM,GAREtF,YAAY,CAChB,4BAA6B9W,EAC7B,2BAA4BA,EACW,KAAtCA,EAAEsc,QAAU,IAAIvM,QAAQ,UACzB,oCAAqCmM,EACrC,mBAAoBA,EACpB,sBAAuBA,EACvB,wBAAyBA,GAEjC,CAQA,SAASK,WAEL,IAAIL,EAAIC,OAER,OAOM,GAPErF,YAAY,CAChB,kBAAmBoF,EACnB,sBAAuBA,EACvB,YAAaA,EACiB,IAL1BE,UAKFE,OAAOvM,QAAQ,SACjB,aAAcmM,EACd,oBAAqBA,GAE7B,CAQA,SAASM,kBAEL,IAAIN,EAAIC,OACJ7U,EAAc4U,EAAE5U,YAAazC,EAAWqX,EAAErX,SAC9C,OAOM,GAPEiS,YAAY,CAChB,WAAYoF,IACV,iBAAkBA,KAClB,eAAgBA,KAChB,gBAAiBA,GACnB5U,KAAiB,mBAAoBA,EAAYlH,WACjDyE,GAAY,uBAAwBA,EAASzE,WAErD,CAOA,SAASqc,iBAKL,IAAIP,EAAIC,OACR,OAzOJ,SAASO,iBAAiBC,GACtB,MAAO,yCAAyCvO,KAAKpH,OAAO2V,GAChE,CAyOID,CAAiBR,EAAEU,QAEO,8BAAtB5V,OAAOkV,EAAEW,QACjB,CAOA,SAASC,UACL,IAAQC,EACJb,EAAIC,OAER,OAOM,GAPErF,YAAY,CAChB,YAAasF,UACb,kBAAoB,OAACW,EAAK,OAAClH,EAAKhL,SAASmS,sBAA6C,EAASnH,EAAGoH,OAAmCF,EAAK,IAC1I,0BAA2Bb,EAC3B,oBAAqBA,EACrB,uBAAwBA,EACxB,6BAA8BA,GAEtC,CAqDA,SAASgB,qBACL,IAAIhB,EAAIC,OACJnc,EAAIoc,UACJe,EAAMjB,EAAEiB,IAAKC,EAAoBlB,EAAEkB,kBACvC,OAMM,GANEtG,YAAY,GACd,sBAAuB9W,GACzBod,GAAqB,YAAaA,EAAkBhd,UACpD,wBAAyB8b,EACzBiB,EAAIE,SAAS,mCACbF,EAAIE,SAAS,+BAErB,CAmCA,SAASC,iBACL,IAAIxc,EAAI+J,SAER,OAAQ/J,EAAEwc,gBAAkBxc,EAAEyc,kBAAoBzc,EAAE0c,qBAAuB1c,EAAE2c,sBAAsBnd,KAAKQ,EAC5G,CAOA,SAAS4c,YACL,IAAIC,EAAetB,aACfuB,EAAYd,UACZZ,EAAIC,OACJnc,EAAIoc,UACJrb,EAAI,aAGR,OAAI4c,EAQM,GAPE7G,YAAY,GACd,iBAAkBoF,GAIpBlc,EAAEe,IAAM,iBAAkBf,EAAEe,KAC1B,WAAY,IAAI8c,WAGjBD,GACkG,GAAhG9G,YAAY,CAAC,wBAAyBoF,EAAG,gBAAiBA,EAAG,WAAW9N,KAAKpO,EAAE8d,aAO9F,CAgLA,SAASC,eAAezO,GACpB,IAAIgG,EAAQ,IAAIlQ,MAAMkK,GAEtB,OADAgG,EAAMhG,KAAOA,EACNgG,CACX,CAYA,SAAS0I,WAAW7I,EAAQ8I,EAAaC,GACrC,IAAYC,EAEZ,YADwB,IAApBD,IAA8BA,EAAkB,IAC7Czc,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAIM,EAAGsd,EACP,OAAOzb,YAAYnC,MAAM,SAAU6d,GAC/B,OAAQA,EAAGrb,OACP,KAAK,EACDlC,EAAI+J,SACJwT,EAAGrb,MAAQ,EACf,KAAK,EACD,OAAMlC,EAAE8B,KAAa,CAAC,EAAa,GAC5B,CAAC,EAAaiS,KAAKqJ,IAC9B,KAAK,EAED,OADAG,EAAGpb,OACI,CAAC,EAAa,GACzB,KAAK,EACDmb,EAAStd,EAAEgK,cAAc,UACzBuT,EAAGrb,MAAQ,EACf,KAAK,EAED,OADAqb,EAAGnb,KAAKa,KAAK,CAAC,EAAK,CAAA,GAAI,KAChB,CAAC,EAAa,IAAIjC,SAAQ,SAAUwc,EAAUC,GAC7C,IAAIC,GAAa,EACbzc,EAAU,WACVyc,GAAa,EACbF,GACH,EAOGrB,GAFJmB,EAAOK,OAAS1c,EAChBqc,EAAOM,QALM,SAAUpJ,GACnBkJ,GAAa,EACbD,EAAQjJ,EACX,EAGW8I,EAAOnB,OAgBf0B,GAfJ1B,EAAM2B,YAAY,UAAW,QAAS,aACtC3B,EAAM4B,SAAW,WACjB5B,EAAM6B,IAAM,IACZ7B,EAAM8B,KAAO,IACb9B,EAAM+B,WAAa,SACff,GAAe,WAAYG,EAC3BA,EAAOa,OAAShB,EAGhBG,EAAOc,IAAM,cAEjBpe,EAAE8B,KAAKsI,YAAYkT,GAIG,WAClB,IAAQrB,EAIJyB,IAK6I,cAA5I,OAACzB,EAAK,OAAClH,EAAKuI,EAAOe,oBAA2C,EAAStJ,EAAGhL,eAAsC,EAASkS,EAAGqC,YAC7Hrd,IAGAiT,WAAW2J,EAAiB,IAEnC,GACDA,GAC5B,KACgB,KAAK,EACDN,EAAGpb,OACHob,EAAGrb,MAAQ,EACf,KAAK,EACD,OAAO,OAAC+Z,EAAK,OAAClH,EAAKuI,EAAOe,oBAA2C,EAAStJ,EAAGhL,WAA+CkS,EAAGna,KAAc,CAAC,EAAa,GACxJ,CAAC,EAAaiS,KAAKqJ,IAC9B,KAAK,EAED,OADAG,EAAGpb,OACI,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,EAAakS,EAAOiJ,EAAQA,EAAOe,gBACnD,KAAK,EAAG,MAAO,CAAC,EAAcd,EAAGpb,QACjC,KAAK,GAED,OADA,OAACkb,EAAKC,EAAOvN,aAAiDsN,EAAGkB,YAAYjB,GACtE,CAAC,GACZ,KAAK,GAAI,MAAO,CAAC,GAEjC,GACA,GACA,CAKA,SAASkB,kBAAkBC,GACnB1J,EAn/BR,SAAS2J,uBAAuBD,GAW5B,IAVA,IAAQxC,EACJ0C,EAAe,sBAAsBjb,OAAO+a,EAAU,KACtDG,EAAW,sBAAsBvR,KAAKoR,GAEtCI,GADAC,EAAMF,EAAS,SAAMnJ,EACR,CACbsJ,GAAAA,EAAa,0BACbC,EAAe,SAAUxQ,EAAMpN,GAC/Byd,EAAWrQ,GAAQqQ,EAAWrQ,IAAS,GACvCqQ,EAAWrQ,GAAMvL,KAAK7B,EACzB,IACQ,CACL,IAAI6d,EAAQF,EAAW1R,KAAKuR,EAAS,IACrC,IAAKK,EACD,MAEJ,IAAIC,EAAOD,EAAM,GACjB,OAAQC,EAAK,IACT,IAAK,IACDF,EAAa,QAASE,EAAKzb,MAAM,IACjC,MACJ,IAAK,IACDub,EAAa,KAAME,EAAKzb,MAAM,IAC9B,MACJ,IAAK,IACD,IAAI0b,EAAiB,yDAAyD9R,KAAK6R,GACnF,IAAIC,EAIA,MAAM,IAAI7a,MAAMqa,GAHhBK,EAAaG,EAAe,GAAI,OAAClD,EAAK,OAAClH,EAAKoK,EAAe,IAAgCpK,EAAKoK,EAAe,IAAgClD,EAAK,IAKxJ,MAEJ,QACI,MAAM,IAAI3X,MAAMqa,GAEhC,CACI,MAAO,CAACG,EAAKD,EACjB,CA48BaH,CAAuBD,GAEhC,IAFA,IAA2CK,EAAM/J,EAAG,GAAI8J,EAAa9J,EAAG,GACpExD,EAAUxH,SAASC,cAAc8U,MAAAA,EAAiCA,EAAM,OACnEzE,EAAK,EAAG4B,EAAKpd,OAAOiI,KAAK+X,GAAaxE,EAAK4B,EAAG7c,OAAQib,IAAM,CACjE,IAAI+E,EAASnD,EAAG5B,GACZjZ,EAAQyd,EAAWO,GAAQC,KAAK,KAGrB,UAAXD,EACAE,eAAe/N,EAAQ4K,MAAO/a,GAG9BmQ,EAAQrH,aAAakV,EAAQhe,EAEzC,CACI,OAAOmQ,CACX,CAIA,SAAS+N,eAAenD,EAAOpC,GAG3B,IAAK,IAAIM,EAAK,EAAGtF,EAAKgF,EAAOzL,MAAM,KAAM+L,EAAKtF,EAAG3V,OAAQib,IAAM,CAC3D,IAGQkF,EAAmBne,EAHvBoe,EAAWzK,EAAGsF,IACd4E,EAAQ,8CAA8C5R,KAAKmS,MAEvDD,EAASN,EAAM,GAAI7d,EAAQ6d,EAAM,GAAIQ,EAAWR,EAAM,GAC1D9C,EAAM2B,YAAYyB,EAAQne,EAAOqe,GAAY,IAEzD,CACA,CA6BA,IAKIC,UAAY,CAAC,YAAa,aAAc,SACxCC,SAAW,CAEX,kBACA,WACA,YACA,qBACA,mBACA,mBACA,mBACA,SACA,2BACA,UACA,UACA,iBACA,YACA,YACA,kBACA,eACA,eACA,SACA,YACA,OACA,mBACA,iBACA,gBACA,aACA,gBACA,aACA,gBACA,cACA,QACA,YACA,aACA,yBACA,eACA,WACA,aACA,UACA,YACA,mBACA,aACA,mBACA,WACA,WACA,YACA,iBACA,SACA,SACA,cACA,iBACA,aACA,uBACA,SACA,YA0NJ,SAASC,eAAeC,GACpB,OAAOA,EAAOC,WAClB,CAyGA,IAGIC,kBACAC,yBA8BJ,SAASC,yBACL,IAAIC,EAAQxgB,KAEZ,OAzBJ,SAASygB,mBACL,IAGIC,OAH6B3K,IAA7BuK,2BAGAI,EAAmB,WACnB,IAAIC,EAAYC,wBAEZN,yBADAO,gBAAgBF,GACWnM,WAAWkM,EAnBnB,WAsBnBL,kBAAoBM,EAG3B,IAEL,CASIF,GACO,WAAc,OAAOxf,UAAUuf,OAAO,OAAQ,GAAQ,WACzD,IAAIG,EACJ,OAAOxe,YAAYnC,MAAM,SAAUqV,GAC/B,OAAQA,EAAG7S,OACP,KAAK,EAED,OAAKqe,gBADLF,EAAYC,yBAERP,kBACO,CAAC,EAAc7c,cAAc,GAAI6c,mBAAmB,IA7yBnF,SAASS,uBACL,IAAIxgB,EAAI+J,SACR,OAAO/J,EAAEygB,mBAAqBzgB,EAAE0gB,qBAAuB1gB,EAAE2gB,sBAAwB3gB,EAAE4gB,yBAA2B,IAClH,CA4yByBJ,GAIE,CAAC,EAAahE,kBAJe,CAAC,EAAa,GAJV,CAAC,EAAa,GAS1D,KAAK,EAIDzH,EAAG5S,OACHke,EAAYC,wBACZvL,EAAG7S,MAAQ,EACf,KAAK,EAID,OAHKqe,gBAAgBF,KACjBN,kBAAoBM,GAEjB,CAAC,EAAcA,GAE1C,GACK,GAAI,CACT,CA8BA,SAASC,wBACL,IAAIthB,EAAI6hB,OAMR,MAAO,CACHhL,WAAWF,QAAQ3W,EAAE8hB,UAAW,MAChCjL,WAAWF,QAAQ3W,EAAE+hB,OAASpL,QAAQ3W,EAAEgiB,YAAcnL,WAAWF,QAAQ3W,EAAEiiB,WAAY,GAAI,MAC3FpL,WAAWF,QAAQ3W,EAAEkiB,QAAUvL,QAAQ3W,EAAEmiB,aAAetL,WAAWF,QAAQ3W,EAAE8hB,UAAW,GAAI,MAC5FjL,WAAWF,QAAQ3W,EAAEiiB,WAAY,MAEzC,CACA,SAASV,gBAAgBF,GACrB,IAAK,IAAIphB,EAAI,EAAGA,EAAI,IAAKA,EACrB,GAAIohB,EAAUphB,GACV,OAAO,EAGf,OAAO,CACX,CAweA,SAASmiB,oBAAoBC,GACzB,IAAItM,EACJ,OAAOpU,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAIM,EAAGshB,EAAMC,EAAUC,EAAqBjQ,EAASkQ,EAAQxiB,EAC7D,OAAO4C,YAAYnC,MAAM,SAAUuc,GAC/B,OAAQA,EAAG/Z,OACP,KAAK,EASD,IARAlC,EAAI+J,SACJuX,EAAOthB,EAAEgK,cAAc,OACvBuX,EAAW,IAAI/d,MAAM6d,EAAUjiB,QAC/BoiB,EAAmB,GAEnBE,UAAUJ,GAGLriB,EAAI,EAAGA,EAAIoiB,EAAUjiB,SAAUH,EAER,YADxBsS,EAAUiN,kBAAkB6C,EAAUpiB,KAC1B6P,SACRyC,EAAQoQ,OAIZD,UAFAD,EAASzhB,EAAEgK,cAAc,QAGzByX,EAAOrX,YAAYmH,GACnB+P,EAAKlX,YAAYqX,GACjBF,EAAStiB,GAAKsS,EAElB0K,EAAG/Z,MAAQ,EACf,KAAK,EACD,OAAMlC,EAAE8B,KAAa,CAAC,EAAa,GAC5B,CAAC,EAAaiS,KAAK,KAC9B,KAAK,EAED,OADAkI,EAAG9Z,OACI,CAAC,EAAa,GACzB,KAAK,EACDnC,EAAE8B,KAAKsI,YAAYkX,GACnB,IAEI,IAAKriB,EAAI,EAAGA,EAAIoiB,EAAUjiB,SAAUH,EAC3BsiB,EAAStiB,GAAG2iB,eACbJ,EAAiBH,EAAUpiB,KAAM,EAOjE,CAH4B,QAEJ,OAAC8V,EAAKuM,EAAKvR,aAAiDgF,EAAGwJ,YAAY+C,EACnG,CACoB,MAAO,CAAC,EAAcE,GAE1C,GACA,GACA,CACA,SAASE,UAAUnQ,GACfA,EAAQ4K,MAAM2B,YAAY,aAAc,SAAU,aAClDvM,EAAQ4K,MAAM2B,YAAY,UAAW,QAAS,YAClD,CA0CA,SAAS+D,YAAYzgB,GACjB,OAAO0gB,WAAW,qBAAqBpe,OAAOtC,EAAO,MAAM2gB,OAC/D,CAcA,SAASC,YAAY5gB,GACjB,OAAO0gB,WAAW,mBAAmBpe,OAAOtC,EAAO,MAAM2gB,OAC7D,CA8CA,SAASE,YAAY7gB,GACjB,OAAO0gB,WAAW,sBAAsBpe,OAAOtC,EAAO,MAAM2gB,OAChE,CAcA,SAASG,YAAY9gB,GACjB,OAAO0gB,WAAW,4BAA4Bpe,OAAOtC,EAAO,MAAM2gB,OACtE,CAcA,SAASI,YAAY/gB,GACjB,OAAO0gB,WAAW,kCAAkCpe,OAAOtC,EAAO,MAAM2gB,OAC5E,CAcA,SAASK,UAAUhhB,GACf,OAAO0gB,WAAW,mBAAmBpe,OAAOtC,EAAO,MAAM2gB,OAC7D,CAEA,IAAI5Q,EAAIjF,KACJmW,WAAa,WAAc,OAAO,CAAI,EAiE1C,IAIIC,QAAU,CAKVC,QAAS,GAETC,MAAO,CAAC,CAAEC,KAAM,uBAEhBC,MAAO,CAAC,CAAEC,WAAY,UAEtBC,KAAM,CAAC,CAAED,WAAY,eAErBE,KAAM,CAAC,CAAEF,WAAY,cAKrBG,IAAK,CAAC,CAAEC,SAAU,QAElBC,OAAQ,CAAC,CAAEL,WAAY,eAoK3B,IAAIM,sBA38CJ,SAASC,yBAEL,IADA,IAAIC,EAAgB9H,SACX,CACL,IAAI+H,EAAeD,EAAcE,OACjC,IAAKD,GAAgBA,IAAiBD,EAClC,OAAO,EAEX,IACI,GAAIC,EAAaE,SAASC,SAAWJ,EAAcG,SAASC,OACxD,OAAO,CASvB,CANQ,MAAO/O,GAEH,GAAIA,aAAiBlQ,OAAwB,kBAAfkQ,EAAMhG,KAChC,OAAO,EAEX,MAAMgG,CAClB,CACQ2O,EAAgBC,CACxB,CACA,EAm9CA,IAGII,uBAAyB,IAAIxb,IAAI,CACjC,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAC7G,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAChH,MAAO,MAAO,KAAM,MAAO,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAC9G,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAC/G,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,OAItDyb,qBAAuB,IAAIzb,IAAI,CAC/B,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,QAEA0b,YAAc,CAAC,kBAAmB,iBAClCC,eAAiB,CAAC,YAAa,eAAgB,aAAc,UAAW,aAAc,YA2G1F,SAASC,gBAAgBC,GACrB,GAAIA,EAAMC,MACN,OAAOD,EAAMC,MAAMC,QAEvB,IACIA,EADAlE,EAAS9V,SAASC,cAAc,UAEpC6V,EAAO9N,iBAAiB,2BAA2B,WAAc,OAAQgS,OAAUtO,KACnF,IAAK,IAAI4E,EAAK,EAAGtF,EAAK,CAAC,QAAS,sBAAuBsF,EAAKtF,EAAG3V,OAAQib,IAAM,CACzE,IAAIpU,EAAO8O,EAAGsF,GACd,IACI0J,EAAUlE,EAAOmE,WAAW/d,EAIxC,CAFQ,MAAOgW,IAGP,GAAI8H,EACA,KAEZ,CAEI,OADAF,EAAMC,MAAQ,CAAEC,QAASA,GAClBA,CACX,CAMA,SAASE,mBAAmBC,EAAIC,EAAYC,GAExC,OADIC,EAAkBH,EAAGI,yBAAyBJ,EAAGC,GAAaD,EAAGE,KAC5C,CAACC,EAAgBE,SAAUF,EAAgBG,SAAUH,EAAgBI,WAAa,EAC/G,CACA,SAASC,0BAA0BC,GAG/B,OADW9lB,OAAOiI,KAAK6d,EAAIC,WACfrL,OAAOsL,eACvB,CACA,SAASA,eAAe/kB,GACpB,MAAsB,iBAARA,IAAqBA,EAAImf,MAAM,cACjD,CAKA,SAAS6F,+BACL,OAAO9I,SACX,CAWA,SAAS+I,uBAAuBb,GAC5B,MAAkC,mBAApBA,EAAGc,YACrB,CA8CA,IAAI7L,QAAU,CAMV8L,MAnoDJ,SAASC,WACL,IAAIhF,EAAQxgB,KAIZ,OAAOwd,YAAW,SAAUjb,EAAG8S,GAC3B,IAAIhL,EAAWgL,EAAGhL,SAClB,OAAOpJ,UAAUuf,OAAO,OAAQ,GAAQ,WACpC,IAAIuB,EAAQ0D,EAAgBC,EAAcC,EAAeC,EAAYC,EAA+CC,EAAsBC,EAAiBC,EAAgBC,EAAYpX,EACvL,OAAO1M,YAAYnC,MAAM,SAAUuc,GA+C/B,KA9CAwF,EAAS1X,EAASjI,MACXqa,MAAM4G,SAvEd,QAwECoC,EAAiBpb,EAASC,cAAc,QACzBmS,MAAM2B,YAAY,aAAc,SAAU,aACzDsH,EAAe,GACfC,EAAgB,CAAA,EAChBC,EAAa,SAAU3C,GACnB,IAAIiD,EAAO7b,EAASC,cAAc,QAC9BmS,EAAQyJ,EAAKzJ,MAOjB,OANAA,EAAM4B,SAAW,WACjB5B,EAAM6B,IAAM,IACZ7B,EAAM8B,KAAO,IACb9B,EAAMwG,WAAaA,EACnBiD,EAAKzb,YArFR,gBAsFGgb,EAAe/a,YAAYwb,GACpBA,CACV,EACDL,EAAsB,SAAUM,EAAcC,GAC1C,OAAOR,EAAW,IAAI5hB,OAAOmiB,EAAc,MAAMniB,OAAOoiB,GAC3D,EAIDN,EAAuB,WAMnB,IAJA,IAAIO,EAAQ,GAIH1L,EAAK,EAAG2L,EAAarG,SAAUtF,EAAK2L,EAAW5mB,OAAQib,KAHxCoI,IACpBsD,EAAMtD,GAAQ/C,UAAU5V,KAAI,SAAUgc,GAAY,OAAOP,EAAoB9C,EAAMqD,EAAU,MAGlFE,EAAW3L,IAG1B,OAAO0L,CACV,EACDN,EAAkB,SAAUQ,GACxB,OAAOvG,UAAUwG,MAAK,SAAUJ,EAAUK,GACtC,OAAOF,EAAUE,GAAeC,cAAgBhB,EAAaU,IACzDG,EAAUE,GAAeE,eAAiBhB,EAAcS,EACpF,GACiB,EACDJ,EApBWhG,UAAU5V,IAAIwb,GAqBzBK,EAAaH,IAEb/D,EAAOrX,YAAY+a,GAEd5W,EAAQ,EAAGA,EAAQmR,UAAUtgB,OAAQmP,IACtC6W,EAAa1F,UAAUnR,IAAUmX,EAAenX,GAAO6X,YACvDf,EAAc3F,UAAUnR,IAAUmX,EAAenX,GAAO8X,aAG5D,MAAO,CAAC,EAAc1G,SAASpG,QAAO,SAAUkJ,GAAQ,OAAOgD,EAAgBE,EAAWlD,GAAS,IACnH,GACA,GACA,GACA,EAmkDI6D,YAvwBJ,SAASC,eAAexR,GACpB,IAAkCyR,QAAlB,IAAPzR,EAAgB,CAAA,EAAKA,GAAeyR,MAC7C,OAAO7lB,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAI+mB,EAASC,EAA2BlF,EACpCnE,EACJ,OAAOxb,YAAYnC,MAAM,SAAU6d,GAC/B,OAAQA,EAAGrb,OACP,KAAK,EACD,OAuBpB,SAASykB,eAEL,OAAOlL,YAAcmB,WACzB,CA1ByB+J,IAGLF,EA7RpB,SAASG,aACL,IAAIC,EAAUC,KACd,MAAO,CACHC,QAAS,CACL,kBACA,mBACA,wBACA,wBACAF,EAAQ,yBAEZG,MAAO,CAAC,YAAa,iBAAkBH,EAAQ,oBAAqB,2BAA4B,UAChGI,eAAgB,CACZ,cACAJ,EAAQ,oBACR,aACAA,EAAQ,wCACRA,EAAQ,yDAEZK,eAAgB,CACZ,oBACA,QACA,uBACA,YACAL,EAAQ,qDAEZM,sBAAuB,CACnB,oBACA,kBACA,qBACAN,EAAQ,4BACRA,EAAQ,qBAEZO,kBAAmB,CACf,aACA,oBACA,yBACA,UACA,iDAEJC,YAAa,CACT,sBACAR,EAAQ,oBACRA,EAAQ,wBACRA,EAAQ,4BACRA,EAAQ,qBAEZS,eAAgB,CACZT,EAAQ,oBACRA,EAAQ,oCACR,eACAA,EAAQ,oCACRA,EAAQ,qCAEZU,cAAe,CACX,WACAV,EAAQ,gCACR,mBACA,aACAA,EAAQ,iBAEZW,cAAe,CAAC,uCAChBC,gBAAiB,CACb,eACAZ,EAAQ,wDACRA,EAAQ,gCACRA,EAAQ,gBACRA,EAAQ,6BAEZa,cAAe,CACXb,EAAQ,oBACRA,EAAQ,gBACR,0BACA,gBACAA,EAAQ,yBAEZc,eAAgB,CACZd,EAAQ,oDACRA,EAAQ,gBACR,yBACAA,EAAQ,oCACR,qBAEJe,cAAe,CACXf,EAAQ,gEACRA,EAAQ,oDACR,cACA,eACA,iBAEJgB,yBAA0B,CAAC,oBAAqB,eAAgB,iBAAkB,cAAe,cACjGC,0BAA2B,CACvB,eACAjB,EAAQ,oDACRA,EAAQ,gEACRA,EAAQ,oDACR,kBAEJkB,eAAgB,CACZ,aACAlB,EAAQ,gBACRA,EAAQ,wDACRA,EAAQ,wDACRA,EAAQ,yDAEZmB,UAAW,CAACnB,EAAQ,gCAAiC,iBAAkB,kBAAmB,sBAC1FoB,SAAU,CACN,cACApB,EAAQ,gCACRA,EAAQ,4BACR,mBACAA,EAAQ,iCAEZqB,cAAe,CACXrB,EAAQ,wDACRA,EAAQ,wBACR,YACA,kBACA,cAEJsB,eAAgB,CACZ,gBACA,oBACA,uBACAtB,EAAQ,4BACR,6BAEJuB,oBAAqB,CACjB,oBACAvB,EAAQ,wBACRA,EAAQ,4BACR,SACAA,EAAQ,iCAEZwB,cAAe,CACXxB,EAAQ,oBACRA,EAAQ,oCACR,YACAA,EAAQ,oDACR,sBAEJyB,gBAAiB,CACb,oBACAzB,EAAQ,4BACRA,EAAQ,oBACRA,EAAQ,gCACRA,EAAQ,6CAEZ0B,cAAe,CACX1B,EAAQ,4BACR,4BACAA,EAAQ,4DACRA,EAAQ,oDACRA,EAAQ,iEAEZ2B,kBAAmB,CACf3B,EAAQ,4BACRA,EAAQ,4BACRA,EAAQ,4CACRA,EAAQ,gDACRA,EAAQ,iDAEZ4B,SAAU,CAAC5B,EAAQ,qDACnB6B,iBAAkB,CAAC,iBAAkB,mBAAoB,mBAAoB,qBAAsB,aACnGC,mBAAoB,CAAC,oCACrBC,uBAAwB,CACpB,kBACA,oDACA,mBACA,kEACA,mBAEJC,aAAc,CAAC,YAAa,kBAAmB,iBAAkB,kBAAmB,2BACpFC,gBAAiB,CACbjC,EAAQ,4DACRA,EAAQ,gDACR,6BACAA,EAAQ,oBACR,gBAEJkC,aAAc,CACVlC,EAAQ,gDACRA,EAAQ,4DACRA,EAAQ,oEACR,gBACA,kBAEJmC,UAAW,CACP,cACA,6BACAnC,EAAQ,gBACRA,EAAQ,gCACR,mBAEJoC,sBAAuB,CACnB,gDACA,iCACA,yBACA,yBACA,oBAEJC,aAAc,CAACrC,EAAQ,qEACvBsC,QAAS,CACLtC,EAAQ,4KAERA,EAAQ,6KAGZuC,OAAQ,CACJvC,EAAQ,gDACRA,EAAQ,4BACRA,EAAQ,gDACRA,EAAQ,wBACR,4BAEJwC,QAAS,CACLxC,EAAQ,oBACR,2BACAA,EAAQ,oCACRA,EAAQ,gCACRA,EAAQ,yDAEZyC,QAAS,CACLzC,EAAQ,oDACRA,EAAQ,oCACRA,EAAQ,gDACR,yBACA,mCAEJ0C,eAAgB,CACZ,8BACA1C,EAAQ,gDACRA,EAAQ,wEACRA,EAAQ,gEACRA,EAAQ,yBAEZ2C,GAAI,CACA3C,EAAQ,4DACRA,EAAQ,oEACRA,EAAQ,4EACRA,EAAQ,oDACR,oBAEJ4C,KAAM,CACF5C,EAAQ,oCACRA,EAAQ,oCACRA,EAAQ,wCACR,WACA,qBAEJ6C,QAAS,CACL,2BACA7C,EAAQ,oDACRA,EAAQ,gBACR,QACA,eAEJ8C,uBAAwB,CACpB,sBACA,gBACA9C,EAAQ,4BACR,qBACA,2BAGZ,CAqB8BD,GACVF,EAAc7nB,OAAOiI,KAAK2f,GAEnB,CAAC,EAAarF,qBADL/D,EAAK,IAAI3Z,OAAOjE,MAAM4d,EAAIqJ,EAAY5c,KAAI,SAAU8f,GAAc,OAAOnD,EAAQmD,EAAY,QAJlG,CAAC,OAAcnU,GAM9B,KAAK,EAWD,OAVA+L,EAAmBjE,EAAGpb,OAClBqkB,GA2ExB,SAASqD,WAAWpD,EAASjF,GAEzB,IADA,IACSnH,EAAK,EAAGtF,EAAKlW,OAAOiI,KAAK2f,GAAUpM,EAAKtF,EAAG3V,OAAQib,IAAM,CAC9D,IAAIuP,EAAa7U,EAAGsF,GACT,KAAK3W,OAAOkmB,EAAY,KACnC,IAAK,IAAI3N,EAAK,EAAGoB,EAAKoJ,EAAQmD,GAAa3N,EAAKoB,EAAGje,OAAQ6c,IAAM,CAC7D,IAAIwC,EAAWpB,EAAGpB,GACP,OAAOvY,OAAO8d,EAAiB/C,GAAY,KAAO,KAAM,KAAK/a,OAAO+a,EAC3F,CACA,CAIA,CAvFwBoL,CAAWpD,EAASjF,IAExBsI,EAAiBpD,EAAYnN,QAAO,SAAUqQ,GAG1C,OADmB5T,aADfqL,EAAYoF,EAAQmD,IACiB9f,KAAI,SAAU2U,GAAY,OAAO+C,EAAiB/C,EAAY,KAC9D,GAAnB4C,EAAUjiB,MACxD,KACmC2qB,OACR,CAAC,EAAcD,GAE1C,GACA,GACA,EA0uBIE,gBA1aJ,SAASC,qBACL,OAkCJ,SAASC,iBAAiB7V,EAAQ8V,GA8C9B,YA7CyB,IAArBA,IAA+BA,EAAmB,KA6C/CjN,YAAW,SAAUjb,EAAGmoB,GAC3B,IAaIC,EAbAC,EAAiBF,EAAargB,SAC9BwgB,EAAaD,EAAexoB,KAehC,OAHIuoB,IAXAG,EAAYD,EAAWpO,OACjB4E,MAAQ,GAAGrd,OAAOymB,EAAkB,MAC9CK,EAAUC,qBAAuBD,EAAUE,eAAiB,OAExDnP,aACAgP,EAAWpO,MAAMwO,KAAO,GAAGjnB,OAAO,EAAI0mB,EAAaQ,kBAE9CnP,aACL8O,EAAWpO,MAAMwO,KAAO,SAGVL,EAAetgB,cAAc,SACnCG,YAAcjH,cAAc,GAAIM,MAAO2mB,EAAmB,GAAO,IAAI,GAAMrgB,KAAI,WAAc,MAAO,UAAWuV,KAAK,KAChIkL,EAAWngB,YAAYigB,GAChBhW,EAAOiW,EAAgBC,EACjC,GAAE,kGACP,CAnGWL,EAAiB,SAAUngB,EAAU8gB,GAKxC,IAJA,IAAItJ,EAAW,GACXuJ,EAAQ,CAAA,EAGHzQ,EAAK,EAAGtF,EAAKlW,OAAOiI,KAAKwb,SAAUjI,EAAKtF,EAAG3V,OAAQib,IAAM,CAC9D,IACoEkD,EADhEzd,EAAMiV,EAAGsF,GACsB8B,OAAe,KAA3BkB,GAAnBpB,EAAKqG,QAAQxiB,IAAc,IAA4B,GAAKud,EAAgB0N,OAAc,KAA1BxN,EAAKtB,EAAG,IA3CtE,oBA2C+GsB,EACjHhM,EAAUxH,EAASC,cAAc,QACrCuH,EAAQpH,YAAc4gB,EACtBxZ,EAAQ4K,MAAM6O,WAAa,SAC3B,IAAK,IAAIC,EAAK,EAAGC,EAAKrsB,OAAOiI,KAAKqV,GAAQ8O,EAAKC,EAAG9rB,OAAQ6rB,IAAM,CAC5D,IAAI7L,EAAS8L,EAAGD,GACZ7pB,EAAQ+a,EAAMiD,QACJ3J,IAAVrU,IACAmQ,EAAQ4K,MAAMiD,GAAUhe,EAE5C,CACYmgB,EAASzhB,GAAOyR,EAChBsZ,EAAU9b,OAAOhF,EAASC,cAAc,MAAOuH,EAC3D,CAEQ,IAAK,IAAI4Z,EAAK,EAAGC,EAAKvsB,OAAOiI,KAAKwb,SAAU6I,EAAKC,EAAGhsB,OAAQ+rB,IAExDL,EAAMhrB,EADIsrB,EAAGD,IACA5J,EAASzhB,GAAKurB,wBAAwBtK,MAEvD,OAAO+J,CACf,GACA,EA8YIQ,MA7/DJ,SAASC,sBACL,OA8DJ,SAASC,yCACL,OAEC/P,YAAcW,sBAAwBT,kBAElCJ,cA1FT,SAASkQ,oBAEL,IAAIvsB,EAAIoc,UACJF,EAAIC,OACJqQ,EAAiB3O,MAAMzd,UAE3B,OAMM,GANE0W,YAAY,CAChB,cAAe0V,EACf,mBAAoBA,EACpB,kBAAmBxsB,GAJnBysB,EAAiBvQ,EAAEuQ,iBAKD,aAAcA,EAChC,uBAAwBC,MAAMtsB,WAEtC,CA6EyBmsB,IAvNzB,SAASI,uBAEL,IAAIzQ,EAAIC,OACJyQ,EAAa1Q,EAAE0Q,WACnB,OAKM,GALE9V,YAAY,CAChB,UAAWhO,IAAI1I,UACf,aAAc8b,EACd0Q,GAAc,oBAAqBA,EAAWxsB,UAC9C,SAAUysB,sBAAsBzsB,WAExC,CA6MgDusB,EAChD,CApEQL,IACS,EAUjB,SAASQ,8BACL,IA2BIjX,EAAmCkX,EAAuBC,EAE1DC,EA7BA/Q,EAAIC,OACJ+Q,EAAehR,EAAEiR,qBAAuBjR,EAAEkR,0BAC9C,OAAKF,EAyCT,SAASG,iCAEL,OAAO9Q,aAAeC,oBA5L1B,SAAS8Q,qBAEL,IAAIpR,EAAIC,OACR,OAKM,GALErF,YAAY,CAChB,gBAAiBoF,EACjB,8BAA+BA,EAC/B,uBAAwBA,EACxB,uBAAwBA,GAEhC,CAmLgDoR,EAChD,CArCQD,IACS,IAKTE,GADA1I,EAAU,IAAIqI,EAAa,EADb,IAC6B,QACtBM,oBACdzmB,KAAO,WAClBwmB,EAAWE,UAAUvrB,MAAQ,KACzBwrB,EAAa7I,EAAQ8I,4BACdC,UAAU1rB,OAAW,GAChCwrB,EAAWG,KAAK3rB,MAAQ,GACxBwrB,EAAWI,MAAM5rB,MAAQ,GACzBwrB,EAAWK,OAAO7rB,MAAQ,EAC1BwrB,EAAWM,QAAQ9rB,MAAQ,IAC3BqrB,EAAWU,QAAQP,GACnBA,EAAWO,QAAQpJ,EAAQqJ,aAC3BX,EAAWY,MAAM,GACsBpB,GAAnClX,EAkCR,SAASuY,oBAAoBvJ,GACzB,IAIInd,EAAW,aA0Df,MAAO,CAzDa,IAAI5F,SAAQ,SAAUC,EAASC,GAC/C,IAAIqsB,GAAc,EACdC,EAAiB,EACjBC,EAAmB,EAEnBC,GADJ3J,EAAQ4J,WAAa,SAAUC,GAAS,OAAO3sB,EAAQ2sB,EAAMC,eAAkB,EACrD,WACtB3Z,YAAW,WAAc,OAAOhT,EAAO+b,eAAe,WAA2C,GAAE/Q,KAAK4W,IATtF,IAS+G2K,EAR7G,IAQwJzY,KAAKF,OACpL,GACGgZ,EAAY,WACZ,IACI,IAAIC,EAAmBhK,EAAQiK,iBAM/B,OAJI7Z,UAAU4Z,IAEVxY,kCAAkCwY,GAE9BhK,EAAQ/c,OACZ,IAAK,UACDymB,EAAmBzY,KAAKF,MACpByY,GACAG,IAEJ,MAIJ,IAAK,YAKI3jB,SAASkkB,QACVT,IAEAD,GAvCA,GAuCeC,EACftsB,EAAO+b,eAAe,cAGtB/I,WAAW4Z,EA1CZ,KAiD3B,CAFY,MAAOtZ,GACHtT,EAAOsT,EACvB,CACS,EACDsZ,IACAlnB,EAAW,WACF2mB,IACDA,GAAc,EACS,EAAnBE,GACAC,IAGX,CACT,IAC2B9mB,EAC3B,CAlGa0mB,CAAoBvJ,IAA6B,GAAImI,EAAkBnX,EAAG,GAE/EoX,EAAqB5W,kCAAkC0W,EAAcrqB,MAAK,SAAUssB,GAAU,OAiGtG,SAASC,QAAQC,GAEb,IADA,IAAIC,EAAO,EACFpvB,EAAI,EAAGA,EAAImvB,EAAOhvB,SAAUH,EACjCovB,GAAQniB,KAAKmK,IAAI+X,EAAOnvB,IAE5B,OAAOovB,CACX,CAvG6GF,CAAQD,EAAOI,eAAe,GAAGC,SAjBtH,MAiB+I,IAAI,SAAU/Z,GAC7K,GAAmB,YAAfA,EAAMhG,MAAkE,cAAfgG,EAAMhG,KAC/D,OAAS,EAEb,MAAMgG,CACd,KACW,WAEH,OADA0X,IACOC,CACV,IAnCY,CAoCjB,CAhDWH,EACX,EAy/DIwC,YAzvCJ,SAASC,iBACL,IAIIC,EAJAxO,EAAQxgB,KACZ,OAAI+b,YAAcW,sBAAwBT,iBAC/B,WAAc,OAAO3a,QAAQC,aAAQwU,EAAa,GAEzDiZ,EAAoBzO,yBACjB,WAAc,OAAOtf,UAAUuf,OAAO,OAAQ,GAAQ,WACzD,IAAIG,EAAWsO,EACf,OAAO9sB,YAAYnC,MAAM,SAAUqV,GAC/B,OAAQA,EAAG7S,OACP,KAAK,EAAG,MAAO,CAAC,EAAawsB,KAC7B,KAAK,EAKD,OAJArO,EAAYtL,EAAG5S,OAIR,CAAC,EAAc,EAHtBwsB,EAAc,SAAUC,GAAY,OAAqB,OAAbA,EAAoB,KAAOzY,MAAMyY,EAxFzE,GAwFyG,GAG1EvO,EAAU,IAAKsO,EAAYtO,EAAU,IAAKsO,EAAYtO,EAAU,IAAKsO,EAAYtO,EAAU,MAElJ,GACK,GAAI,EACT,EAsuCIR,OAliDJ,SAASgP,uBACL,OAQJ,SAASC,6BAA6BC,GAClC,IAEIC,EACAjE,EAFAkE,GAAU,EAGVhT,EAeR,SAASiT,oBACL,IAAIrP,EAAS9V,SAASC,cAAc,UAGpC,OAFA6V,EAAOkB,MAAQ,EACflB,EAAOqB,OAAS,EACT,CAACrB,EAAQA,EAAOmE,WAAW,MACtC,CApBakL,GAAqBrP,EAAS5D,EAAG,GAAI8H,EAAU9H,EAAG,GAa3D,OAQJ,SAASkT,YAAYtP,EAAQkE,GACzB,SAAUA,IAAWlE,EAAOC,UAChC,CAtBSqP,CAAYtP,EAAQkE,IAIrBkL,EAmBR,SAASG,mBAAmBrL,GAKxB,OAFAA,EAAQsL,KAAK,EAAG,EAAG,GAAI,IACvBtL,EAAQsL,KAAK,EAAG,EAAG,EAAG,IACdtL,EAAQuL,cAAc,EAAG,EAAG,UACxC,CAzBkBF,CAAmBrL,GACzBgL,EACAC,EAAWjE,EAAO,WAGkBiE,GAApCja,EAqBZ,SAASwa,aAAa1P,EAAQkE,IAiB9B,SAASyL,gBAAgB3P,EAAQkE,GAE7BlE,EAAOkB,MAAQ,IACflB,EAAOqB,OAAS,GAChB6C,EAAQ0L,aAAe,aACvB1L,EAAQ2L,UAAY,OACpB3L,EAAQ4L,SAAS,IAAK,EAAG,GAAI,IAC7B5L,EAAQ2L,UAAY,OAGpB3L,EAAQtB,KAAO,yBAOXmN,EAAc,qBAAqBlsB,OAAOwC,OAAO2pB,aAAa,MAAO,QACzE9L,EAAQ+L,SAASF,EAAa,EAAG,IACjC7L,EAAQ2L,UAAY,yBACpB3L,EAAQtB,KAAO,aACfsB,EAAQ+L,SAASF,EAAa,EAAG,GACrC,CAtCIJ,CAAgB3P,EAAQkE,GACxB,IAAIgM,EAAanQ,eAAeC,GAIhC,OAAIkQ,IAHanQ,eAAeC,GAIrB,CAAC,WAAuC,aAiCvD,SAASmQ,oBAAoBnQ,EAAQkE,GAEjClE,EAAOkB,MAAQ,IACflB,EAAOqB,OAAS,IAIhB6C,EAAQkM,yBAA2B,WACnC,IAAK,IAAI5V,EAAK,EAAGtF,EAAK,CAClB,CAAC,OAAQ,GAAI,IACb,CAAC,OAAQ,GAAI,IACb,CAAC,OAAQ,GAAI,KACdsF,EAAKtF,EAAG3V,OAAQib,IAAM,CACrB,IAAiB6V,GAAbjU,EAAKlH,EAAGsF,IAAgB,GAAI5N,EAAIwP,EAAG,GAAIja,EAAIia,EAAG,GAClD8H,EAAQ2L,UAAYQ,EACpBnM,EAAQoM,YACRpM,EAAQqM,IAAI3jB,EAAGzK,EAAG,GAAI,EAAa,EAAVkK,KAAKmkB,IAAQ,GACtCtM,EAAQuM,YACRvM,EAAQvS,MAChB,CAIIuS,EAAQ2L,UAAY,OACpB3L,EAAQqM,IAAI,GAAI,GAAI,GAAI,EAAa,EAAVlkB,KAAKmkB,IAAQ,GACxCtM,EAAQqM,IAAI,GAAI,GAAI,GAAI,EAAa,EAAVlkB,KAAKmkB,IAAQ,GACxCtM,EAAQvS,KAAK,UACjB,CAtDIwe,CAAoBnQ,EAAQkE,GAErB,CADanE,eAAeC,GACZkQ,GAC3B,CArCiBR,CAAa1P,EAAQkE,IAAwB,GAAIgH,EAAOhW,EAAG,KARpEia,EAAWjE,EAAO,cAWf,CAAEkE,QAASA,EAASD,SAAUA,EAAUjE,KAAMA,EACzD,CA3BW+D,CAsHX,SAASyB,uCAEL,OAAO9U,YAAcW,sBAAwBT,gBACjD,CAzHwC4U,GACxC,EAiiDIC,MAv4CJ,SAASC,WACL,OAAOnV,UAAUoV,KACrB,EAs4CIC,UAp4CJ,SAASC,eACL,IAAI1xB,EAAIoc,UACJ5Z,EAAS,GACTmvB,EAAW3xB,EAAE2xB,UAAY3xB,EAAE4xB,cAAgB5xB,EAAE6xB,iBAAmB7xB,EAAE8xB,eAiBtE,YAhBiBvb,IAAbob,GACAnvB,EAAOuB,KAAK,CAAC4tB,IAEbrtB,MAAMuE,QAAQ7I,EAAEyxB,WAGVpV,cAxyBd,SAAS0V,sBAEL,IAAI7V,EAAIC,OACR,OAKM,GALErF,YAAY,GACd,uBAAwBoF,GAC1B,yBAA0BA,EAC1B,GAAKA,EAAE8V,MAAS,gBAChB,GAAK9V,EAAEhb,SAAY,oBAE3B,CA+xB8B6wB,IAClBvvB,EAAOuB,KAAK/D,EAAEyxB,WAGU,iBAAhBzxB,EAAEyxB,YACVA,EAAYzxB,EAAEyxB,YAEdjvB,EAAOuB,KAAK0tB,EAAUriB,MAAM,MAG7B5M,CACX,EAg3CIyvB,WA92CJ,SAASC,gBACL,OAAO/V,OAAOwF,OAAOsQ,UACzB,EA62CIE,aA32CJ,SAASC,kBAEL,OAAOzb,WAAWF,QAAQ2F,UAAU+V,mBAAe5b,EACvD,EAy2CI8b,iBAl2CJ,SAASC,sBACL,KAAI/V,YAAcW,sBAAwBT,kBAG1C,OAQJ,SAAS8V,8BAKgB,SAAjBC,EAA2BtwB,GAAS,OAAOyU,WAAWH,MAAMtU,GAAQ,KAAQ,CAJhF,IAAIpC,EAAI6hB,OAKJ8Q,EAAa,CAACD,EAAe1yB,EAAE+hB,OAAQ2Q,EAAe1yB,EAAEkiB,SAE5D,OADAyQ,EAAW5H,OAAO7hB,UACXypB,CACX,CAjBWF,EACX,EA81CIG,oBAptCJ,SAASC,yBAEL,OAAOhc,WAAWH,MAAM4F,UAAUsW,0BAAsBnc,EAC5D,EAktCIqc,SAhtCJ,SAASC,cACL,IACIC,EAAiB,OAACjd,EAAKsG,OAAO6V,WAAkC,EAASnc,EAAGid,eAChF,OAAIA,IACIF,GAAW,IAAIE,GAAiBC,kBAAkBC,UAE3CJ,GAKXK,GAGR,SAASC,oBACL,IAAIC,GAAc,IAAIrd,MAAOsd,cAK7B,OAAOpmB,KAAKqmB,IAEZ5c,QAAQ,IAAIX,KAAKqd,EAAa,EAAG,GAAGD,qBAAsBzc,QAAQ,IAAIX,KAAKqd,EAAa,EAAG,GAAGD,qBAClG,CAZkBA,GACP,MAAM1uB,OAAiB,GAAVyuB,EAAc,IAAM,IAAIzuB,OAAOyuB,GACvD,EAosCIK,eAxrCJ,SAASC,oBACL,IACI,QAASpX,OAAOmX,cAKxB,CAHI,MAAOhe,GAEH,OAAO,CACf,CACA,EAirCIke,aA9qCJ,SAASC,kBACL,IACI,QAAStX,OAAOqX,YAKxB,CAHI,MAAOnxB,GAEH,OAAO,CACf,CACA,EAuqCIqxB,UArqCJ,SAASC,eAGL,IAAI1X,cApnCR,SAAS2X,aAEL,IAAI1X,EAAIC,OACJnc,EAAIoc,UACR,OAA6G,GAArGtF,YAAY,CAAC,wBAAyBoF,EAAG,aAAcA,EAAG,gBAAiBlc,EAAG,eAAgBA,MACjGic,WACT,CA8mCuB2X,GAGnB,IACI,QAASzX,OAAOuX,SAKxB,CAHI,MAAOrxB,GAEH,OAAO,CACf,CACA,EAypCIwxB,aAvpCJ,SAASC,kBACL,QAAS3X,OAAO0X,YACpB,EAspCIE,SAppCJ,SAASC,cACL,OAAO5X,UAAU2X,QACrB,EAmpCIE,SAjpCJ,SAASC,cAEL,IAAID,EAAW7X,UAAU6X,SAKzB,MAAiB,aAAbA,GACI1X,aAAeC,kBAp+B3B,SAAS2X,SAOL,IAIIC,EAJJ,MAA2B,SAAvBhY,UAAU6X,WAIVG,GADAt0B,EAAI6hB,QACYE,MAAQ/hB,EAAEkiB,OASxB,GARElL,YAAY,CAEhB,gBAAiBqF,SAEfkY,QAAQj0B,UAAUk0B,wBAGN,IAAdF,GAAsBA,EAAc,OAE5C,CAg9BmBD,GAAW,OAAS,SAG5BF,CACX,EAqoCIM,QAnlDJ,SAASC,aACL,IAAIC,EAAarY,UAAUmY,QAC3B,GAAKE,EAAL,CAKA,IAFA,IAAIF,EAAU,GAELx0B,EAAI,EAAGA,EAAI00B,EAAWv0B,SAAUH,EAAG,CACxC,IAAI20B,EAASD,EAAW10B,GACxB,GAAK20B,EAAL,CAIA,IADA,IAAIC,EAAY,GACPpiB,EAAI,EAAGA,EAAImiB,EAAOx0B,SAAUqS,EAAG,CACpC,IAAIqiB,EAAWF,EAAOniB,GACtBoiB,EAAU5wB,KAAK,CACXgD,KAAM6tB,EAAS7tB,KACf8tB,SAAUD,EAASC,UAEnC,CACQN,EAAQxwB,KAAK,CACTuL,KAAMolB,EAAOplB,KACbwlB,YAAaJ,EAAOI,YACpBH,UAAWA,GAZvB,CAcA,CACI,OAAOJ,CAtBX,CAuBA,EAyjDIQ,aA96CJ,SAASC,kBACL,IAEIC,EAFAj1B,EAAIoc,UACJ8Y,EAAiB,OAEI3e,IAArBvW,EAAEk1B,eACFA,EAAiB1e,MAAMxW,EAAEk1B,qBAEG3e,IAAvBvW,EAAEm1B,mBACPD,EAAiBl1B,EAAEm1B,kBAEvB,IACItqB,SAASuqB,YAAY,cACrBH,GAAa,CAIrB,CAFI,MAAOpf,GACHof,GAAa,CACrB,CAEI,MAAO,CACHC,eAAgBA,EAChBD,WAAYA,EACZI,WAJa,iBAAkBlZ,OAMvC,EAw5CIG,OAroCJ,SAASgZ,YACL,OAAOlZ,UAAUE,QAAU,EAC/B,EAooCIiZ,cA9nCJ,SAASC,mBAEL,IADA,IAAIC,EAAU,GACLta,EAAK,EAAGtF,EAAK,CAElB,SAEA,SAEA,UACA,WAEA,SAEA,OACA,SAEA,cAEA,qCACA,SAEA,OAEA,YAEA,QACA,cAEA,gBAGDsF,EAAKtF,EAAG3V,OAAQib,IAAM,CACrB,IAAIva,EAAMiV,EAAGsF,GACTjZ,EAAQia,OAAOvb,GACfsB,GAA0B,iBAAVA,GAChBuzB,EAAQ1xB,KAAKnD,EAEzB,CACI,OAAO60B,EAAQ5K,MACnB,EAwlCI6K,eA/kCJ,SAASC,oBACL,IAAI70B,EAAI+J,SAQR,IAEI/J,EAAE80B,OAAS,iCACX,IAAIpzB,GAA8C,IAArC1B,EAAE80B,OAAO7lB,QAAQ,eAG9B,OADAjP,EAAE80B,OAAS,uEACJpzB,CAIf,CAFI,MAAOH,GACH,OAAO,CACf,CACA,EA4jCIwzB,WAhrBJ,SAASC,gBAEL,IAAK,IAAI3a,EAAK,EAAGtF,EAAK,CAAC,UAAW,KAAM,QAASsF,EAAKtF,EAAG3V,OAAQib,IAAM,CACnE,IAAI4a,EAAQlgB,EAAGsF,GACf,GAAIyH,WAAW,iBAAiBpe,OAAOuxB,EAAO,MAAMlT,QAChD,OAAOkT,CAEnB,CAEA,EAwqBIC,eAnqBJ,SAASC,oBACL,QAAItT,YAAY,cAGZA,YAAY,cAAhB,CAIJ,EA4pBIuT,aAppBJ,SAASC,kBACL,QAAIrT,YAAY,YAGZA,YAAY,cAAhB,CAIJ,EA6oBIsT,WAhoBJ,SAASC,qBACL,GAAKzT,WAAW,uBAAuBC,QAAvC,CAMA,IAAK,IAAI9iB,EAAI,EAAGA,GAfE,MAesBA,EACpC,GAAI6iB,WAAW,oBAAoBpe,OAAOzE,EAAG,MAAM8iB,QAC/C,OAAO9iB,EAGf,MAAM,IAAIqF,MAAM,iBARpB,CASA,EAonBIkxB,SA9mBJ,SAASC,wBACL,OAAIxT,YAAY,iBACL,EAIPA,YAAY,SAAWA,YAAY,QAC5B,EAEPA,YAAY,QAAUA,YAAY,SACzB,EAETA,YAAY,UACL,QADX,CAIJ,EA+lBIyT,cAvlBJ,SAASC,kBACL,QAAIzT,YAAY,YAGZA,YAAY,uBAAhB,CAIJ,EAglBI0T,oBAxkBJ,SAASC,wBACL,QAAI1T,YAAY,YAGZA,YAAY,uBAAhB,CAIJ,EAikBI2T,IAzjBJ,SAASC,QACL,QAAI3T,UAAU,UAGVA,UAAU,kBAAd,CAIJ,EAkjBI4T,KAviBJ,SAASC,qBAEL,IAAIC,EAAO/kB,EAAE+kB,MAAQ7T,WACjB8T,EAAQhlB,EAAEglB,OAAS9T,WACnB+T,EAAOjlB,EAAEilB,MAAQ/T,WACjBgU,EAAQllB,EAAEklB,OAAShU,WACnBiU,EAAQnlB,EAAEmlB,OAASjU,WACnBkU,EAAOplB,EAAEolB,MAAQlU,WACjBmU,EAAMrlB,EAAEqlB,KAAOnU,WACfoU,EAAOtlB,EAAEslB,MAAQpU,WACjBqU,EAAMvlB,EAAEulB,KAAOrU,WACfsU,EAAOxlB,EAAEwlB,MAAQtU,WACjBuU,EAAMzlB,EAAEylB,KAAOvU,WACfwU,EAAO1lB,EAAE0lB,MAAQxU,WACjByU,EAAM3lB,EAAE2lB,KAAOzU,WACf0U,EAAQ5lB,EAAE4lB,OAAS1U,WACnB2U,EAAQ7lB,EAAE6lB,OAAS3U,WAYvB,MAAO,CACH6T,KAAMA,EAAK,oBACXC,MAAOA,EAAM,OACbc,SAZoB71B,EAYH,MAZmB+P,EAAE+lB,IAAI91B,EAAQ+P,EAAEgmB,KAAK/1B,EAAQA,EAAQ,KAazEg1B,KAAMA,EAAK,oBACXC,MAAOA,EAAM,GACbe,SAdoBh2B,EAcH,EAdmB+P,EAAE+lB,IAAI91B,EAAQ+P,EAAEgmB,KAAK/1B,EAAQA,EAAQ,KAezEk1B,MAAOA,EAAM,IACbe,SAfoBj2B,EAeH,GAfmB+P,EAAE+lB,KAAK,EAAI91B,IAAU,EAAIA,IAAU,GAgBvEm1B,KAAMA,EAAK,IACXC,IAAKA,UACLC,KAAMA,EAAK,GACXa,QAlBmBl2B,EAkBJ,EAlBoB+P,EAAE2lB,IAAI11B,GAAS,EAAI+P,EAAE2lB,IAAI11B,GAAS,GAmBrEs1B,IAAKA,EAAI,iBACTC,KAAMA,EAAK,GACXY,QApBmBn2B,EAoBJ,GApBqB+P,EAAE2lB,IAAI11B,GAAS,EAAI+P,EAAE2lB,IAAI11B,IAAU,GAqBvEw1B,IAAKA,UACLC,KAAMA,EAAK,GACXW,QArBmBp2B,EAqBJ,GArBqB+P,EAAE2lB,IAAI,EAAI11B,GAAS,IAAM+P,EAAE2lB,IAAI,EAAI11B,GAAS,IAsBhF01B,IAAKA,EAAI,GACTC,MAAOA,EAAM,GACbU,QAzBoCtmB,EAAE2lB,IAyBrB,GAzBkC,EA0BnDE,MAAOA,EAAM,IACbU,QAzBoCvmB,EAAE+lB,IAAI,IA0B1CS,MAlCkCxmB,EAAEymB,IAAIzmB,EAAEkf,SAoClD,EAkfIwH,iBAnWJ,SAASC,qBACL,OAAOxc,UAAUuc,gBACrB,EAkWIE,aAxVJ,SAASC,kBACL,IAAIj2B,EAAI,IAAIk2B,aAAa,GACrBC,EAAK,IAAI5f,WAAWvW,EAAEmsB,QAG1B,OAFAnsB,EAAE,GAAKo2B,IACPp2B,EAAE,GAAKA,EAAE,GAAKA,EAAE,GACTm2B,EAAG,EACd,EAmVIE,SA7UJ,SAASC,mBACL,IAAIC,EAAkBjd,OAAOid,gBAC7B,GAAmH,mBAAvGA,MAAAA,OAAyD,EAASA,EAAgBC,iBAC1F,OAAS,EAEb,GAAItV,wBACA,OAAS,EAEb,IACI,OAAOqV,EAAgBC,kBAAoB,EAAgC,CAInF,CAFI,MAAO/jB,GACH,OAgBR,SAASgkB,kBAAkBhkB,GAEvB,GAAIA,aAAiBlQ,OAAwB,uBAAfkQ,EAAMhG,MAAiC,0BAA0BlB,KAAKkH,EAAMikB,SACtG,OAAS,EAEb,MAAMjkB,CACV,CAtBegkB,CAAkBhkB,EACjC,CACA,EAgUIkkB,wBA9RJ,SAASC,6BACL,IACIC,EAAO7uB,SAASC,cAAc,KAC9B6uB,EAAW,OAAC9jB,EAAK6jB,EAAKE,qBAAiD/jB,EAAK6jB,EAAKG,oBACrF,YAAoBtjB,IAAbojB,OAAyBpjB,EAAYvP,OAAO2yB,EACvD,EA0RIG,iBAvFJ,SAASC,6BACL,IAAIlkB,EAIJ,OADwB6H,aAAenB,WAIlCJ,OAAO+Q,cAGL,OAACrX,GAAAA,IAASqX,cAAe8M,aAAyCnkB,GAF5D,GAHA,CAMjB,EA4EIokB,eApEJ,SAASC,oBACL,IAOIC,EAPJ,OAAKhe,OAAO6V,MAGRc,EAAiB3W,OAAO6V,KAAKc,iBAI7BqH,EAASrH,IAAiBC,kBAAkBoH,SACtB,KAAXA,EAGRA,GAFM,GAJA,GAJA,CAWjB,EA0DIC,YA5PJ,SAASC,eAAexkB,GACpB,IAAoBmW,EAShBsO,EANJ,OADItV,EAAKN,gBADG7O,EAAG8O,QAKVkB,uBAAuBb,IAGxBsV,EAAiB1U,+BAAiC,KAAOZ,EAAGuV,aAfpC,6BAgBrB,CACHC,SAAU,OAACzd,EAAKiI,EAAGc,aAAad,EAAGyV,eAAsC,EAAS1d,EAAGpX,aAAe,GACpG2W,QAAS,OAAC6B,EAAK6G,EAAGc,aAAad,EAAG0V,cAAqC,EAASvc,EAAGxY,aAAe,GAClGg1B,eAAgBL,EAAiB,OAACjc,EAAK2G,EAAGc,aAAawU,EAAeM,6BAAoD,EAASvc,EAAG1Y,WAAa,GACnJk1B,UAAW,OAAC9O,EAAK/G,EAAGc,aAAad,EAAG8V,gBAAuC,EAAS/O,EAAGpmB,aAAe,GACtGo1B,iBAAkBT,EAAiB,OAACtO,EAAKhH,EAAGc,aAAawU,EAAeU,+BAAsD,EAAShP,EAAGrmB,WAAa,GACvJs1B,wBAAyB,OAAChP,EAAKjH,EAAGc,aAAad,EAAGkW,gCAAuD,EAASjP,EAAGtmB,aAAe,MA9ClG,GAFf,CAkD3B,EA0OIw1B,gBAtOJ,SAASC,mBAAmBvlB,GACxB,IACImP,EAAKN,gBADG7O,EAAG8O,OAEf,IAAKK,EACD,OA1DmB,EA4DvB,IAAKa,uBAAuBb,GACxB,OA3DkC,EA6DlCqW,EAAarW,EAAGsW,yBAApB,IACIC,EAAoBvW,EAAGwW,uBACvBC,EAAwB,GAExB9b,EAAa,GACb+b,EAAa,GACbC,EAAsB,GACtBC,EAAmB,GAEvB,GAAIL,EACA,IAAK,IAAIpgB,EAAK,EAAG4B,EAAKpd,OAAOiI,KAAK2zB,GAAoBpgB,EAAK4B,EAAG7c,OAAQib,IAAM,CACxE,IAAI0gB,EAAgB9e,EAAG5B,GACvBwE,EAAW5b,KAAK,GAAGS,OAAOq3B,EAAe,KAAKr3B,OAAO+2B,EAAkBM,IACnF,CAII,IADA,IACS1d,EAAK,EAAG2d,EADDtW,0BAA0BR,GACA7G,EAAK2d,EAAY57B,OAAQie,IAAM,CACrE,IACI4d,EAAO/W,EAAGgX,EADCF,EAAY3d,IAE3Bud,EAAW33B,KAAK,GAAGS,OAAOw3B,EAAU,KAAKx3B,OAAOu3B,GAAMv3B,OAAO8f,uBAAuB3Y,IAAIowB,GAAQ,IAAIv3B,OAAOwgB,EAAGc,aAAaiW,IAAS,IAC5I,CAEI,GAAIV,EACA,IAAK,IAAIhd,EAAK,EAAG4d,EAAeZ,EAAYhd,EAAK4d,EAAa/7B,OAAQme,IAAM,CACxE,IAAI6B,EAAS+b,EAAa5d,GAC1B,KA/DoB,8BA+Df6B,GAAwC0F,gCA9D1B,uBA+Dd1F,IA6FN7D,cAAgBE,aA9Ff,CAIA,IAAI2f,EAAYlX,EAAGuV,aAAara,GAChC,GAAKgc,EAIL,IAAK,IAAInQ,EAAK,EAAGC,EAAKxG,0BAA0B0W,GAAYnQ,EAAKC,EAAG9rB,OAAQ6rB,IAAM,CAC9E,IAAIiQ,EACAD,EAAOG,EAAUF,EADNhQ,EAAGD,IAElB4P,EAAoB53B,KAAK,GAAGS,OAAOw3B,EAAU,KAAKx3B,OAAOu3B,GAAMv3B,OAAO+f,qBAAqB5Y,IAAIowB,GAAQ,IAAIv3B,OAAOwgB,EAAGc,aAAaiW,IAAS,IAC3J,MAPgBN,EAAsB13B,KAAKmc,EAH3C,CAWA,CAGI,IAAK,IAAI+L,EAAK,EAAGkQ,EAAgB3X,YAAayH,EAAKkQ,EAAcj8B,OAAQ+rB,IAErE,IADA,IAAIhH,EAAakX,EAAclQ,GACtBC,EAAK,EAAGkQ,EAAmB3X,eAAgByH,EAAKkQ,EAAiBl8B,OAAQgsB,IAAM,CACpF,IAAIhH,EAAgBkX,EAAiBlQ,GACjC/G,EAAkBJ,mBAAmBC,EAAIC,EAAYC,GACzD0W,EAAiB73B,KAAK,GAAGS,OAAOygB,EAAY,KAAKzgB,OAAO0gB,EAAe,KAAK1gB,OAAO2gB,EAAgBhF,KAAK,MACpH,CAKI,OAFAwb,EAAoB9Q,OACpB6Q,EAAW7Q,OACJ,CACH0Q,kBAAmB5b,EACnB+b,WAAYA,EACZE,iBAAkBA,EAClBP,WAAYA,EACZM,oBAAqBA,EACrBF,sBAAuBA,EAE/B,GAqKA,SAASY,mBAAmB5qB,GACxB,OAAOuI,YAAYC,QAASxI,EAAS,GACzC,CAGA,SAAS6qB,cAAcxgB,GACnB,IACIygB,EAkCR,SAASC,yBAAyBC,GAC9B,OAAOxlB,MAAM,IAAO,IAAOwlB,EAAqB,KACpD,CApC6BD,CADrBC,EAIR,SAASC,uBAAuB5gB,GAI5B,OAAI4B,YACO,GAGPnB,YACOC,mBAAuBU,sBAAwBT,iBAA0B,GAAN,IAE1EwX,EAAW,UAAWnY,EAAWmY,SAAWnY,EAAWmY,SAAS/xB,MAAQ,GAExE,OAAOkM,KAAK6lB,GAKL,GAGP,OAAO7lB,KAAK6lB,GAKL,GAGJ,GACX,CAlC8ByI,CAAuB5gB,IAEjD,MAAO,CAAE6gB,MAAOF,EAAqBG,QAJnB,4CAI4CC,QAAQ,MAAO,GAAGr4B,OAAO+3B,IAC3F,CAsDA,SAASO,eAAehhB,GACpB,OAAO9C,WAnBX,SAAS+jB,4BAA4BjhB,GAEjC,IADA,IAAItZ,EAAS,GACJ2Y,EAAK,EAAGtF,EAAKlW,OAAOiI,KAAKkU,GAAY+O,OAAQ1P,EAAKtF,EAAG3V,OAAQib,IAAM,CACxE,IAAI6hB,EAAennB,EAAGsF,GAElBjZ,EAAQ,UADR+6B,EAAYnhB,EAAWkhB,IACQ,QAAUv2B,KAAKC,UAAUu2B,EAAU/6B,OACtEM,GAAU,GAAGgC,OAAOhC,EAAS,IAAM,IAAIgC,OAAOw4B,EAAaH,QAAQ,YAAa,QAAS,KAAKr4B,OAAOtC,EAC7G,CACI,OAAOM,CACX,CAUsBu6B,CAA4BjhB,GAClD,CA+BA,SAASohB,kBAAkBC,GAGvB,OAnhGJ,SAASC,+BAA+BC,EAAiBC,QAC7B,IAApBA,IAA8BA,EAAkBrE,KACpD,IAAIsE,EAAsBphB,OAAOohB,oBACjC,OAAIA,EAIO,IAAIz7B,SAAQ,SAAUC,GAAW,OAAOw7B,EAAoBj9B,KAAK6b,QAAQ,WAAc,OAAOpa,MAAc,CAAEy7B,QAASF,OAGvHzoB,KAAK7H,KAAK4W,IAAIyZ,EAAiBC,GAE9C,CAugGWF,CAFyBD,OAAV,IAAlBA,EAA4C,GAEVA,EAA+B,EAAhBA,EACzD,CAQA,SAASM,UAAUC,EAAepW,GACXxR,KAAKF,MACxB,MAAO,CACHpQ,IAAK,SAAUiM,GACX,OAAOhQ,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAesb,EAAYtZ,EAC3B,OAAOG,YAAYnC,MAAM,SAAUqV,GAC/B,OAAQA,EAAG7S,OACP,KAAK,EAED,OADY8S,KAAKF,MACV,CAAC,EAAa8nB,KACzB,KAAK,EAQD,OAPA5hB,EAAajG,EAAG5S,OAChBT,EAnD5B,SAASm7B,kBAAkB7hB,GACvB,IAAI8hB,EAEAC,EAAavB,cAAcxgB,GAE/B,MAAO,CACHgiB,aAIIA,GAAA,OAFIF,OADmBrnB,IAAnBqnB,EACiBd,eAAet8B,KAAKsb,YAElC8hB,CACV,EACDE,aAAcA,CAAAA,GACVF,EAAiBE,CACpB,EACDD,WAAYA,EACZ/hB,WAAYA,EACZ0e,QA1hGM,QA4hGd,CAgCqCmD,CAAkB7hB,GACvBwL,GAAU7V,MAAAA,GAAkDA,EAAQ6V,MAKjE,CAAC,EAAc9kB,GAElD,GACA,GACS,EAET,CA+CA,IAAI6M,MAAQ,CAAE0uB,KAxBd,SAASA,KAAKtsB,GACV,IAAIoE,EAEJ,YADgB,IAAZpE,IAAsBA,EAAU,IAC7BhQ,UAAUjB,UAAM,OAAQ,GAAQ,WACnC,IAAI28B,EAAe7V,EACnB,OAAO3kB,YAAYnC,MAAM,SAAUuc,GAC/B,OAAQA,EAAG/Z,OACP,KAAK,EAKD,OAJI,OAAC6S,EAAKpE,EAAQusB,cAAwCnoB,GA3B9E,SAASooB,UAEL,KAAI9hB,OAAO+hB,YAA+B,MAAjBlxB,KAAKC,UAG9B,IACI,IAAIkxB,EAAU,IAAIC,eAClBD,EAAQE,KAAK,MAAO,0CAA0C75B,OAnlGxD,QAmlGwE,oBAAoB,GAClG25B,EAAQG,MAMhB,CAJI,MAAOhpB,GAIX,CACA,CAawB2oB,GAEJd,EAAgB1rB,EAAQ0rB,cAAe7V,EAAQ7V,EAAQ6V,MAChD,CAAC,EAAa4V,kBAAkBC,IAC3C,KAAK,EAGD,OAFApgB,EAAG9Z,OAEI,CAAC,EAAcw6B,UADNpB,mBAAmB,CAAE1X,MAAO,CAAI2C,EAAAA,MAAOA,IACRA,IAEnE,GACA,GACA,EAI0BwV,eAAgBA,eAAgByB,wBA7H1D,SAASA,wBAAwBziB,GAC7B,OAAOrV,KAAKC,UAAUoV,GAAY,SAAU0iB,EAAMt8B,GAC9C,OAAIA,aAAiBkD,MA9gF7B,SAASq5B,cAAcnpB,GACnB,IAAIO,EACJ,OAAOnW,SAAS,CAAE4P,KAAMgG,EAAMhG,KAAMiqB,QAASjkB,EAAMikB,QAASmF,MAAO,OAAC7oB,EAAKP,EAAMopB,YAAmC,EAAS7oB,EAAGzG,MAAM,OAASkG,EACjJ,CA4gFmBmpB,CAAcv8B,GAElBA,CACV,GAAE,EACP,GC9gGA,SAAsBy8B,qBAAWC,qBAAAA,MAAAp+B,KAAAP,UAIhC,CAAA,SAAA2+B,oCAAAA,aAAAC,kBAAAC,wBAAAA,OAJM,SAAAC,IAAA,IAAAv8B,SAAAs8B,sBAAAA,MAAA,SAAAE,GAAA,oBAAA,OAAAA,UAAAA,WAAA,OAAA,OAAAA,YACcC,QAAAA,QAAoB,OAA7B,OAAFC,EAAEF,UAAAA,YACaE,EAAAA,MAAQ,OAAjB,OAAN18B,EAAMw8B,UAAAA,IAAAA,eACLx8B,IAAAA,OAAgB,OAAA,UAAA,OAAAw8B,EAAAA,UAAAD,UAC1BH,MAAAp+B,KAAAP,UACD,CAAA,SAAsBk/B,kBAAiBC,EAAAC,GAAAC,OAAAA,mBAAAA,QAAAA,MAAA9+B,KAAAP,sGAqBtC,SAAAq/B,0CAAAA,mBAAAT,kBAAAC,sBAAAA,MArBM,SAAAS,EAAiCtmB,EAAOumB,OAAIC,EAAAC,mBAAAZ,wBAAAA,OAAA,SAAAa,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,OAAA,OAAAA,YACxBhB,cAAa,OAiBR,OAjBtBiB,EAAQD,WAGVH,MAAAA,OAAI,EAAJA,IAAAA,iBAAyBK,SACzBJ,EAAU,IAAII,QACdL,EAAAA,iBAAqB,SAACt9B,EAAOtB,GACzB6+B,UAAY7+B,EAAKsB,OAIrBu9B,EAAU,IAAII,SAAQL,MAAAA,OAAI,EAAJA,IAAAA,QAAiB,CAAA,GAG3CC,YAAAA,KAA2BG,GACrBF,EAAYI,eAAAA,gBACXN,EAAAA,GAAI,CAAA,EAAA,SACPC,uBACwBE,EAAAA,SAAAA,KAErBI,MAAM9mB,EAAOymB,IAAa,OAAA,UAAA,OAAAC,EAAAA,UAAAJ,UACpCD,MAAA9+B,KAAAP,UACD,CAAA,SAAA+/B,UAAA,IAAAC,EAAA,CAAA,YAAA,eAAA,gBAAA,UAAA,SAAA,QAAA,OAAA,WAAA,eAAA,UAAA,gBAAA,QAAA,UAAA,iBAAA,OAAA,eAAA,WAAA,SAAA,OAAA,UAAA,MAAA,QAAA,OAAA,OAAA,UAAA,SAAA,OAAA,cAAA,eAAA,YAAA,eAAA,MAAA,QAAA,OAAAD,QAAA,WAAA,OAAAC,CAAA,IAAA,CAAsBC,SAAAA,2BAA0BC,UAAAC,0CAAA5/B,KAAAP,UAoB/C,CAAA,SAAAmgC,mDAAAA,4BAAAvB,kBAAAC,sBAAAA,MApBM,SAAAuB,EAA0CpnB,OAAKumB,EAAAc,EAAAZ,EAAAa,YAAAC,EAAAvgC,iBAAA6+B,+BAAA,SAAA2B,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,IAAAA,OAAA,OAAmB,OAAjBjB,IAAIgB,gBAAAjqB,IAAAiqB,KAAAA,KAAG,GAAIF,IAAME,UAAAA,UAAAjqB,EAAAkqB,YAC9C9B,cAAa,OASR,OATtBiB,EAAQa,EAAAA,MAERhB,EAAU,IAAII,QAAQL,EAAAA,YAC5BC,aAA2BG,GAC3BH,IAAAA,QAAAA,KAAyBa,GAEnBZ,EAAYI,eAAAA,gBACXN,EAAAA,GAAI,CAAA,EAAA,SACPC,gBADO,OAEiBgB,EAAAA,OAAAA,IAAAA,SAGDV,MAAM9mB,EAAOymB,GAAa,QAAnC,OAARa,EAAQE,UAAAA,EAAAA,gBACPF,GAAQ,QAGsB,MAHtBE,IAAAA,SAAAA,EAAAA,GAAAA,aAGsBA,EAAAA,GAAA,QAAA,OAAA,KAAA,OAAAA,IAAAA,WAAAJ,EAAA,KAAA,CAAA,sBAG5C7/B,KAAAP,orDC/CD2gC,QAAAC,EAAAC,GAAA,IAAAC,EAAAC,UAAA,OAAAJ,QAAA,SAAAK,EAAAC,GAAA,OAAAH,EAAAE,GAAA,IAAA,GAAAJ,EAAAC,EAAA,sTAEA,IAAMK,SAASvsB,4BACTwsB,UAAYxZ,KAAKhT,6BACVysB,cAAAA,UA6RDC,EAAAA,IADPC,EA7BQC,IATAC,EATAC,EAVAC,EAtCAC,EADRC,EAjDAC,EAAAA,EAtCoBC,EAPRC,IAPAC,IAdLC,EADPC,EAAAA,IA5BOC,IAHCC,IAJAC,EAJCC,EAAAA,IAJAC,IAPFC,EAHAC,IAdCC,IADRC,qBAAAC,cAHD,SAAAxB,kBAAcyB,qBAAAzB,GACV7gC,OAAA,MAAe,KACfA,OAAA,MAAyB,OAC5B,CAAA,uBAAAoiC,EAAA/D,kBAAAC,+BACD,SAAAC,IAAAqC,IAAAA,EAAA2B,EAAA3B,EAAAA,SAAAtC,+BAAA,SAAAE,GAAA,cAAA,OAAAA,EAAAA,KAAAA,EAAAA,MAAA,OAAA,OAAAA,YACyBgE,OAAAA,SAAAA,MAA0B,QAAA,uCAG3B,IAAI5pB,WAAW,yBAE1B,oBAAuB,OALpB,OAAZ5Y,aAAYw+B,IAAAA,MAAAA,IAAAA,QAMYgE,OAAAA,iBAAAA,KAAgCxiC,OAAA,QAAA,OAAuB,OAAhE,OAAT4gC,EAASpC,EAAAA,KAAAA,IAAAA,QACUgE,8BAAiCxiC,sBAAwB,OAAlE,OAAVuiC,EAAU/D,UAAAA,iBACT,WACQx+B,aAAsB4gC,2BACrB5gC,aAAsBuiC,IAAtB,QACf,QAAA,YAAA,OAAA/D,eAAAD,EAAAv+B,UACJ,kBAbQoiC,IAAAA,MAAApiC,KAAAP,cAAA,qBAAA0iC,EAAA9D,kBAAAC,+BAcT,SAAAS,mBAAAT,+BAAA,SAAAa,GAAA,IAAA,IAAAsD,EAAAC,IAAA,OAAAvD,UAAAA,WAAA,OAAA,OAAAA,IAAAA,QACiBqD,SAAAA,cAA0B,4BAAwC,oBAAuB,OAAA,OAAArD,IAAAA,aAAAA,IAAAA,OAAA,OAAA,YAAA,OAAAA,eAAAJ,EAfjGT,KAgBR,WAFO6D,OAAAA,EAAAA,EAAAA,MAAAniC,KAAAP,cAAA,iBAAAyiC,EAAA7D,kBAAAC,sBAAAA,MAGR,SAAAuB,EAAS8C,EAAQrzB,GAAIszB,IAAAC,EAAAC,EAAAF,EAAAA,eAAAtE,+BAAA,SAAA2B,GAAA,IAAA,IAAA8C,EAAAC,IAAA,OAAA/C,UAAAA,WAAA,OAGoC,OAD/CgD,GADAL,EAAU,IAAI7pB,qBACczJ,GAC5BuzB,EAAKL,eAAuB,IAAI5pB,gBAAeqnB,IAAAA,QACzBuC,uBAAsB,QAAA,QAAmBK,GAAMF,EAAQM,GAAW,OAA3E,OAAbH,EAAa7C,UAAAA,iBACZ,eAAE6C,KAAeD,IAAI,OAAA,OAAA,KAAA,OAAA5C,IAAAA,WAAAJ,EARxBvB,KASP,SANOM,EAAAC,GAAAqD,OAAAA,EAAAA,MAAAliC,KAAAP,cAAA,OAAA,gBAAAwiC,EAAA5D,kBAAAC,+BAOR,SAAA4E,EAAWC,OAAGC,aAAA9E,+BAAA,SAAA+E,GAAA,cAAA,OAAAA,EAAAA,KAAAA,EAAAA,MAAA,OACkC,OAAtCD,EAAYpjC,OAAA,MAAsBmjC,GAAIE,YAC/Bb,eAAAA,iBAAgCY,EAAW,oBAAA,SAA6C,UAAY,OAAA,OAAAC,mBAAAA,WAAA,OAAA,UAAA,OAAAA,eAAAH,EAAAljC,KAT7Gs+B,KAUP,SAHSqB,UAAAsC,IAAAA,MAAAjiC,KAAAP,UAAJ6jC,IAAI,uBAAAtB,EAAA3D,kBAAAC,wBAAAA,OAIV,SAAAiF,EAAWJ,GAAG,IAAAC,SAAA9E,sBAAAA,MAAA,SAAAkF,GAAA,IAAA,IAAAC,EAAArD,UAAA,OAAAoD,UAAAA,WAAA,OACkC,OAAtCJ,EAAYpjC,KAAA0jC,iBAAsBP,GAAIK,YAC/BhB,SAAAA,MAAAA,iBAAiCY,EAAW,iCAA6C,GAAA,OAAY,OAAA,OAAAI,EAAAA,cAAAA,IAAAA,OAAA,OAAA,UAAA,OAAAA,IAAAA,WAAAD,EAAAvjC,KAN5Gs+B,KAOT,SAHSqF,UAAA3B,UAAAhiC,KAAAP,cAAA,mBAAAsiC,EAAAA,EAAAA,EAAA1D,kBAAAC,+BAIV,SAAAsF,EAAUhD,EAAW+B,OAAMkB,MAAAvF,OAAAA,wBAAAA,OAAA,SAAAwF,GAAA,cAAA,OAAAA,UAAAA,IAAAA,OAAA,OAAA,OAAAA,EAAAA,OACMtB,OAAAA,qBAA+BG,GAAO,OAA/C,OAAdkB,EAAcC,UAAAA,EAAAA,OACPtB,iBAAAA,MAAsB,QAAA,MAAsB5B,EAAWiD,GAAe,OAAA,OAAAC,YAAAA,KAAAA,EAAAA,MAAA,OAAA,UAAA,OAAAA,eAAAF,OACtF,SAHQG,EAAAC,GAAAjC,OAAAA,EAAAA,EAAAA,MAAA/hC,KAAAP,UAAHwkC,IAAG,uBAAAnC,EAAAzD,kBAAAC,wBAAAA,OAIT,SAAA4F,EAAU3B,EAAYO,UAAaxE,sBAAAA,MAAA,SAAA6F,GAAA,oBAAA,OAAAA,EAAAA,KAAAA,WAAA,OAAA,OAAAA,YAClB3B,iBAAAA,MAAsB,kBAAsBD,EAAYO,GAAc,OAAA,OAAAqB,YAAAA,KAAAA,WAAA,OAAA,YAAA,OAAAA,eAAAD,OACtF,SAFQE,EAAAC,UAAAvC,IAAAA,MAAA9hC,KAAAP,cAAA,qBAAAoiC,EAAAxD,kBAAAC,+BAGT,SAAAgG,EAASC,EAAcj1B,OAAIqzB,EAAAG,EAAAD,EAAA2B,EAAAC,MAAAnG,OAAAA,+BAAA,SAAAoG,GAAA,cAAA,OAAAA,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACF1kC,KAAA2kC,KAAS,OAAlB,OAANhC,EAAM+B,IAAAA,MAAAA,YACwB1kC,KAAA4kC,GAAQjC,EAAQrzB,GAAK,OAAhC,OAAgCu1B,EAAAH,EAAAA,KAAjD5B,EAAa+B,IAAAA,MAAEhC,EAAEgC,EAAAA,GAAAH,aACD1kC,aAAUukC,GAAa,QAAhC,OAAT3D,EAAS8D,UAAAA,aACe1kC,aAAS4gC,EAAW+B,GAAO,QAImC,OAJtF6B,EAAeE,IAAAA,OACfD,EAAe,IAAI7rB,WAAW4rB,UAA6B3B,UAAgBC,EAAAA,aACjF2B,IAAiB,IAAI7rB,WAAW4rB,MAChCC,UAAiB5B,EAAI2B,WACrBC,IAAAA,MAAiB,IAAI7rB,WAAWkqB,GAAgB0B,EAAAA,WAA6B3B,WAAe6B,IAAAA,aACrFI,KAAKt+B,SAAAA,QAAAA,MAAAA,OAAMu+B,mBAAiBN,MAAc,QAAA,OAAA,KAAA,OAAAC,IAAAA,WAAAJ,EAAAtkC,UACpD,SAVOglC,EAAAC,GAAApD,OAAAA,EAAAA,MAAA7hC,KAAAP,UAAFylC,IAAE,qBAAAtD,EAAAvD,kBAAAC,wBAAAA,OAWR,SAAA6G,EAASC,EAAeC,OAAaC,EAAAxC,EAAAyC,EAAAC,MAAAlH,OAAAA,+BAAA,SAAAmH,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,IAAAA,OAAA,OAIqC,OAJrCA,YAEvBhB,EAAe7rB,aAAAA,MAAgBwO,KAAKie,IAAgB,SAAA9kC,UAAKA,IAAAA,YACzD+kC,EAAkBb,IAAAA,aAClB3B,EAAgB2B,cAAwBA,WAAoBgB,YACzCzlC,OAAA,MAAUolC,GAAc,OAAjC,OAAV7C,EAAUkD,UAAAA,YACWzlC,OAAA,MAASuiC,EAAY+C,GAAgB,OAA9C,OAAZC,EAAYE,EAAAA,KAAAA,IAAAA,SACUzlC,KAAA4lC,GAAQL,EAAczC,GAAc,QAA7C,OAAb0C,EAAaC,EAAAA,KAAAA,IAAAA,eACZD,GAAa,QAG6B,MAH7BC,EAAAA,QAAAA,EAAAA,GAAAA,IAAAA,SAId,IAAI7gC,QAAJ,MAA8B,QAAA,YAAA,OAAA6gC,eAAAN,EAAAnlC,KAAA,CAAA,aAE3C,SAdO6lC,EAAAC,UAAAlE,IAAAA,MAAA5hC,KAAAP,UAAFsmC,IAAE,kBAeR,SAAIvX,GACOsW,IAAAA,EAAAA,EAAAA,OAAAA,KAAKt+B,uBAAAA,OAAMu+B,mBAAiB,IAAInsB,WAAW4V,KAhB9C,GAiBP,iBAAAmT,EAAAA,EAAAA,EAAAtD,kBAAAC,wBAAAA,OACD,SAAA0H,EAAST,EAAczC,GAAa,IAAAH,EAAAE,EAAAoD,EAAAC,SAAA5H,sBAAAA,MAAA,SAAA6H,GAAA,oBAAA,OAAAA,IAAAA,MAAAA,WAAA,OAAA,OAAAA,YAAAA,YAEP3D,OAAAA,OAAAA,iBAA+B+C,EAAc,iBAA4B,UAAY,OAI3C,OAJzD5C,EAAMwD,UACNtD,EAAKC,gBACL1jB,EAAM0jB,iBACNsD,EAAatD,EAAAA,UACbmD,EAAkB,IAAIrtB,WAAU,WAAAmsB,mBAAKqB,GAAUrB,mBAAK3lB,KAAK+mB,IAAAA,SACjC3D,OAAAA,SAAAA,MAAsB,QAAA,QAAuBK,GAAMF,EAAQsD,GAAgB,QAApF,OAAfC,EAAeC,UAAAA,sBACVE,qBAAqBH,IAAgB,QAAA,MAAAC,EAAAA,QAAAA,EAAAA,GAAAA,EAAAA,SAG1C,IAAIvhC,cAAkC,QAAA,UAAA,OAAAuhC,IAAAA,WAAAH,EAAA,KAAA,CAAA,QAZnD1H,KAcA,SAbOgI,EAAAC,GAAA5E,OAAAA,EAAAA,EAAAA,MAAA3hC,KAAAP,cAAA,uBAAAiiC,EAAArD,kBAAAC,+BAcR,SAAAkI,EAAc5F,EAAW6F,OAAS3D,aAAAxE,+BAAA,SAAAoI,GAAA,cAAA,OAAAA,EAAAA,KAAAA,WAAA,OAAA,OAAAA,EAAAA,OACZ1mC,aAAqB4gC,GAAU,OAAxC,OAAHxgC,EAAGsmC,EAAAA,KAAAA,YACmBlE,uBAAsB,cAE/CpiC,OAAS2Y,qBAAqB0tB,IAAW,OAFzB,OAAb3D,EAAa4D,UAAAA,IAAAA,aAGZ1mC,aAAyB8iC,IAAc,OAAA,YAAA,OAAA4D,eAAAF,EAAAxmC,KAnB1Cs+B,KAoBP,SANYqI,EAAAC,UAAAlF,UAAA1hC,KAAAP,cAAA,0BAAAgiC,EAAApD,kBAAAC,+BAOb,SAAAuI,EAActE,EAAY8C,OAAaG,MAAAlH,OAAAA,wBAAAA,OAAA,SAAAwI,GAAA,cAAA,OAAAA,UAAAA,IAAAA,OAAA,OAAA,OAAAA,YACjB9mC,KAAA+mC,iBAAsBxE,GAAW,OAA1C,OAAHniC,EAAG0mC,UAAAA,EAAAA,OACmBtE,iBAAAA,MAAsB,QAAA,MAE/CpiC,EAAKJ,KAAAgnC,oBAAyB3B,IAAe,OAF7B,OAAbG,EAAasB,UAAAA,EAAAA,iBAGZ,IAAIT,eAAJ,MAAyBb,IAAc,OAAA,OAAA,KAAA,OAAAsB,EAAAA,UAAAD,EAAA7mC,UACjD,SANYinC,EAAAC,UAAAzF,IAAAA,MAAAzhC,KAAAP,cAAA,mBAAA+hC,EAAAnD,kBAAAC,sBAAAA,MAOb,SAAA6I,EAAsBhE,UAAG7E,sBAAAA,MAAA,SAAA8I,GAAA,IAAA,IAAAC,EAAAjH,UAAA,OAAAgH,EAAAA,KAAAA,EAAAA,MAAA,OAAA,OAAAA,iBACd5E,SAAAA,QAAAA,aAAgCxiC,aAAsBmjC,GAAM,6BAG1D,WAAY,OAAA,OAAA,KAAA,OAAAiE,EAAAA,UAAAD,EAAAnnC,UACxB,SALoBsnC,GAAA9F,OAAAA,EAAAA,cAAAxhC,KAAAP,cAAA,OAAA,YAAA8hC,EAAAlD,kBAAAC,sBAAAA,MAMrB,SAAAiJ,EAAuBpE,GAAG7E,IAAAA,EAAAA,QAAAA,OAAAA,+BAAA,SAAAkJ,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,EAAAA,KAAAA,EAAAA,MAAA,OAAA,OAAAA,iBACfhF,SAAAA,MAAAA,iBAAiCxiC,aAAsBmjC,GAAM,6BAG3D,WAAY,OAAA,OAAA,KAAA,OAAAqE,IAAAA,WAAAD,EAAAvnC,UACxB,SALqB2nC,GAAApG,OAAAA,EAAAA,cAAAvhC,KAAAP,cAAA,8BAMtB,SAAiB+uB,EAAQjoB,GAAMqhC,IAAAA,EAAAA,EACrBC,EAAS7nC,aAAyBwuB,YAC/B,OAAA,MAAiBjoB,YAAjB,MAAqBqhC,OAAAA,EAAUC,UAAa,kBAAbD,EAAAA,gCAAkDrhC,aAE7F,kBACD,SAAoBioB,GAIhB,IAHIsZ,IAAAA,EAAAA,EAAAA,KACE5uB,EAAQ,IAAIN,WAAW4V,GACvBuZ,EAAM7uB,IAAAA,MACH3Z,IAAOA,EAAIwoC,EAAKxoC,IACrBuoC,GAAUthC,SAAAA,MAAoB0S,EAAM3Z,IAEjCoc,OAAAA,SAAAA,MAAYmsB,KACtB,kBACD,SAAoBD,GAIhB,YAHMG,EAAersB,SAAAA,MAAYksB,GAC3BE,EAAMC,EAAAA,OACN9uB,EAAQ,IAAIN,WAAWmvB,GACpBxoC,IAAOA,EAAIwoC,EAAKxoC,IACrB2Z,EAAM3Z,GAAKyoC,UAAwBzoC,UAEhC2Z,IAAAA,QACV,OAAA,WACD,SAAiBiqB,WACP0E,EAAS1E,EAAAA,QAAY,+DACpBnjC,aAAyB6nC,EAHnC,GAIA,iBAAAvG,EAAAA,EAAAA,EAAAjD,kBAAAC,wBAAAA,OACD,SAAA2J,QAAAC,EAAAC,EAAAC,EAAA9oC,EAAA+oC,EAAAC,aAAAhK,+BAAA,SAAAiK,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,IAAAA,MAAAA,WAAA,OAAA,OAAAA,EAAAA,OACmCvoC,KAAA0oC,MAAU,OAAnB,OAAtB1oC,aAAsBuoC,UAAAA,EAAAA,OACO/F,OAAAA,eAA0B,QAAA,uCAAkE,IAAI5pB,WAAW,yBAA8C,mBAAmB,OAC3I,OADxDsvB,EAAcK,UACdJ,EAAKnoC,OAAA,MAAkBA,OAAA,QAAA,OAAiCuoC,YACvC/F,eAAAA,iBAAgC0F,EAAAA,WAAyB,OAEnD,OAFvBS,EAAQJ,IAAAA,MACRH,EAAKtD,KAAKt+B,OAAAA,aAAAA,MAAAA,OAAMu+B,mBAAiB,IAAInsB,WAAW+vB,MAChDrpC,EAAIkjC,iBAAmB+F,aACXvoC,eAAU,QAEW,OAFjC4oC,EAAGL,UAEHM,GADAjG,EAAU,IAAI7pB,qBACWzZ,EAAIspC,GAAIL,IAAAA,SACT/F,OAAAA,eAAmB,QAAA,MAA+B0F,IAAAA,MAA2BW,GAAQ,QAC3C,OADlER,EAAeE,IAAAA,MACfD,EAAKxD,KAAKt+B,iBAAAA,MAAAA,OAAMu+B,mBAAiB,IAAInsB,WAAWyvB,MAAkBE,iBACjE,IAAEJ,KAAIC,KAAIE,IAAIhpC,IAAG,QAAA,OAAA,KAAA,OAAAipC,IAAAA,WAAAN,EAAAjoC,UAC3B,kBAbOshC,UAAAthC,KAAAP,cAAA,kBAcR,SAAa4rB,UACFyZ,KAAKgE,SAASC,mBAAmB1d,IAfpC,GAgBP,gBACD,SAAGvc,EAAMpN,EAAOsnC,GACNC,IAAAA,EAAAA,EAAAA,EAAO,IAAI3zB,KACjB2zB,UAAaA,EAAAA,aAAkBD,OACzBE,SAAuBD,IAAAA,QAC7B5+B,SAAAA,OAAkByE,MAAapN,MAAcwnC,WAChD,gBACD,SAAGp6B,GAGC,IAFMq6B,IAAAA,EAAAA,EAAAA,EAASr6B,MACTs6B,EAAK/+B,WAAAA,mBACF9K,IAAOA,EAAI6pC,UAAW7pC,KAE3B,IADA,IAAIgB,EAAI6oC,EAAG7pC,SACJgB,IAAAA,UACHA,EAAIA,IAAAA,QAAeA,EAAAA,eACnBA,IAAAA,MAAU4oC,GACV,OAAO5oC,IAAAA,MAAY4oC,UAAe5oC,WAEnC,OAAA,OAEX,gBACA,SAAGuO,GACCzE,SAAAA,OAAkByE,yBAFtB,GAIA,gBACA,WAEI,IADMu6B,IAAAA,EAAAA,EAAAA,EAAUh/B,WAAAA,MAAAA,WACP9K,IAAOA,EAAI8pC,EAAAA,OAAgB9pC,KAChC,IAAM61B,EAASiU,EAAQ9pC,GAEjBuP,GAAe,GADfw6B,EAAQlU,gBACYA,IAAAA,QAAiBkU,GAASlU,EACpD/qB,SAAAA,OAAkByE,+CAEzB,OAAA,YAAAuyB,EAAAhD,kBAAAC,sBAAAA,MACD,SAAAiL,IAAAC,IAAA1G,EAAA2G,EAAA1J,EAAA2J,EAAAC,EAAAH,EAAAA,QAAAlL,OAAAA,wBAAAA,OAAA,SAAAsL,GAAA,cAAA,OAAAA,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACoC5pC,KAAA6pC,KAAS,OAOE,OAPFL,EAAAI,IAAAA,MACnCjM,EAAU,IADRwK,EAAEqB,EAAAA,MAAIA,EAAAA,MAAIA,EAAAA,KAAGA,EAAAA,GAOfM,EAAc7jC,OAAAA,MAAe03B,GAAQiM,aACf5pC,KAAAklC,GAAQtE,UAAWkJ,GAAY,QAG1D,OAHKhH,EAAa8G,UACbH,EAAiB,aACN3G,GAChB8G,aAAAA,aAE0BjL,kBAAkBgC,gBAA2B,uBAEvD,6BAGH16B,KAAAA,UAAewjC,KACvB,YANI1J,EAAQ6J,EAAAA,MAOT7J,GAAW6J,CAAAA,aAAA,MAAA,MACN,IAAIhlC,cAAoC,QAAA,OAAAglC,EAAAA,QAEvB7J,IAAAA,QAAe,SAApC2J,EAAYE,YACEF,EAAAA,WAA0BA,IAAAA,gBAC1C1pC,KAAA+pC,OAAaL,YAAAA,SACPC,EAAyB3pC,KAAAgqC,aAAkBhqC,OAAA,eACjDA,KAAA+pC,OAAaJ,MAIhBC,IAAAA,SAAA,MAAA,QAAAA,EAAAA,QAAAA,EAAAA,GAAAA,cAGqC,QAAA,YAAA,OAAAA,IAAAA,WAAAL,EAAAvpC,KAAA,CAAA,SApC7Cs+B,KAsCA,kBArCQ+C,UAAAA,MAAArhC,KAAAP,cAAA,mBAAA2hC,EAAA/C,kBAAAC,sBAAAA,MAsCT,SAAA2L,QAAA1pC,EAAAjB,EAAA4qC,mBAAA5L,+BAAA,SAAA6L,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,UACQ5pC,EAAIP,KAAAoqC,QACJ9qC,EAAIU,KAAAoqC,QACH7pC,GAAMjB,EAAC6qC,CAAAA,IAAAA,QAAA,MAAA,OAAAA,qBACC,OAEM,OAAfE,EAAQjjB,KAAK7mB,GAAE4pC,YACGnqC,KAAA+lC,GAAQsE,EAAO/qC,GAAE,OAA1B,OAAT4qC,EAASC,EAAAA,KAAAA,iBACND,GAAS,OAAA,YAAA,OAAAC,eAAAF,EAAAjqC,UACnB,WATQohC,OAAAA,EAAAA,MAAAphC,KAAAP,cAAA,kBAAA0hC,EAAA9C,kBAAAC,sBAAAA,MAUT,SAAAgM,EAAUh7B,OAAIi7B,EAAAzH,YAAAxE,OAAAA,wBAAAA,OAAA,SAAAkM,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACIxqC,eAAU,UAApBV,EAACkrC,EAAAA,KACDD,EAAQnjB,KAAK9nB,GACZA,GAACkrC,YAAA,MAAA,OAAAA,EAAAA,SAAAA,SACO,OAAA,OAAAA,YAEaxqC,KAAAklC,GAAQqF,EAAOj7B,GAAK,OAA7B,OAAbwzB,EAAa0H,EAAAA,KAAAA,iBACV1H,GAAa,QAAA,OAAA,KAAA,OAAA0H,eAAAF,EAAAtqC,UACvB,SARQ2qC,GAAAxJ,OAAAA,EAAAA,QAAAA,MAAAnhC,KAAAP,cAAA,kBAAAyhC,EAAA7C,kBAAAC,sBAAAA,MAST,SAAAsM,EAAU9H,OAAaviC,EAAAilC,mBAAAlH,wBAAAA,OAAA,SAAAuM,GAAA,cAAA,OAAAA,UAAAA,WAAA,UACftqC,EAAIP,KAAAoqC,QACFS,CAAAA,IAAAA,QAAA,MAAA,OAAAA,qBACO,OAEM,OAAfR,EAAQjjB,KAAK7mB,GAAEsqC,EAAAA,OACO7qC,KAAA+lC,GAAQsE,EAAOvH,GAAc,OAAtC,OAAb0C,EAAaqF,EAAAA,KAAAA,EAAAA,cACVrF,GAAa,OAAA,YAAA,OAAAqF,EAAAA,UAAAD,EAAA5qC,UACvB,SARQ8qC,GAAA5J,OAAAA,EAAAA,QAAAA,MAAAlhC,KAAAP,UAAHsrC,IAAG,uBAAA9J,EAAA5C,kBAAAC,wBAAAA,OAST,SAAA0M,IAAAjL,IAAAA,EAAAA,EAAAA,SAAAzB,+BAAA,SAAA2M,GAAA,cAAA,OAAAA,IAAAA,MAAAA,WAAA,OAAA,OAAAA,YAAAA,YAE+BtM,kBAAkBgC,gBAAsC,uBAElE,6BAGH,OACR,WANIZ,EAAQkL,EAAAA,MAOTlL,IAAWkL,YAAA,MAAA,MACN,IAAIrmC,qCAAoC,OAAA,OAAAqmC,YAEvBlL,IAAAA,QAAe,OAAxBkL,EAAAA,KACQA,aAAA,MAAA,QAAAA,EAAAA,QAAAA,EAAAA,GAAAA,aAGY,QAAA,YAAA,OAAAA,eAAAD,EAAA,KAAA,CAAA,aAE7C,kBAlBQ/J,EAAAA,MAAAjhC,KAAAP,cAAA,OAAA,YAAAuhC,EAAA3C,kBAAAC,sBAAAA,MAmBT,SAAA4M,IAAA5M,OAAAA,sBAAAA,MAAA,SAAA6M,GAAA,IAAA,IAAAC,EAAAhL,UAAA,OAAA+K,UAAAA,IAAAA,OAAA,UACSnrC,KAAAqrC,KAASF,CAAAA,EAAAA,OAAA,MAAA,OAAAA,IAAAA,QACJnrC,eAAU,OAAA,UAAA,OAAAmrC,EAAAA,UAAAD,EAAAlrC,UAEvB,WAJQghC,OAAAA,EAAAA,QAAAA,MAAAhhC,KAAAP,cAAA,gBAKT,oBACQO,KAAAoqC,UAAgBpqC,KAAAoqC,WAIvB,qBAAArJ,EAAA1C,kBAAAC,+BACD,SAAAgN,QAAAC,aAAAjN,+BAAA,SAAAkN,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,WAAA,OACQD,KAAY,WACRvrC,KAAAoqC,WAAgBmB,EAAW,OAAAC,YACzB,IAAIlqC,SAAQ,SAAAC,UAAWiT,WAAWjT,MAAlC,IADyBiqC,EAAAA,OAAA,MACuB,OACtDD,IAAUC,EAAAA,OAAA,MAAA,OAAA,YAAA,OAAAA,EAAAA,UAAAF,EAAAtrC,UAEjB,WANO+gC,OAAAA,EAAAA,QAAA/gC,KAAAP,cAAA,OAAA,YAAAqhC,EAAAA,EAAAA,EAAAzC,kBAAAC,+BAOR,SAAAqN,QAAA3pC,MAAAs8B,OAAAA,wBAAAA,OAAA,SAAAsN,GAAA,IAAA,IAAAC,EAAAC,IAAA,OAAAF,UAAAA,EAAAA,MAAA,OAAA,OAAAA,IAAAA,QACqBnN,gBAAoB,OAA7B,OAAFC,EAAEkN,UAAAA,EAAAA,OACalN,IAAAA,QAAQ,OAAjB,OAAN18B,EAAM4pC,IAAAA,MAAAA,YAAAA,KACL5pC,WAAgB,OAAA,YAAA,OAAA4pC,eAAAD,OAC1B,WAJQ7K,OAAAA,EAAAA,EAAAA,MAAA9gC,KAAAP,iBApSAohC,wcCLbkL,QAAAC,EAAAC,GAAA,IAAAC,EAAAC,UAAA,OAAAJ,QAAA,SAAAK,EAAAC,GAAA,OAAAH,EAAAE,GAAA,IAAA,GAAAJ,EAAAC,EAAA,6UAEA,IAAMtL,SAASvsB,YAAAA,OACFk4B,wBAAaC,EAAAA,YAAAlK,OAAAA,cAAA,SAAAiK,IAAAhK,qBAAAgK,EAAAjK,GAAA,CAAA,sBAAAkK,EAAAA,QAAAA,EAAAlO,kBAAAC,wBAAAA,OACtB,SAAAC,EAAciO,EAAU7O,OAAOmC,EAAA2M,EAAA1M,MAAA2M,EAAAjtC,iBAAA6+B,wBAAAA,OAAA,SAAAE,GAAA,IAAA,IAAAmO,EAAAC,IAAA,OAAApO,UAAAA,EAAAA,MAAA,OAEyC,OAFvCqO,MAAWH,gBAAA32B,IAAA22B,OAAAA,KAAS5M,IAAM4M,IAAAA,MAAAA,UAAA32B,EAAAyoB,EAAAA,OAE7CiO,EAAUI,EAAcnN,2BAA6BH,MAAKf,YACzCiO,aAAW9L,yBAAsB6L,GAAY,UAAA,aAEvD,kBAAA,WACHvmC,aAAe03B,IACtBmC,GAAO,WAJJC,EAAQvB,WAKTuB,IAAWvB,YAAA,MAAA,MACNuB,EAAQ,OAAA,OAAAvB,aAELuB,IAAAA,QAAe,QAAA,OAAAvB,YAAAA,KAAAA,IAAAA,OAAA,QAAA,MAAAA,aAAAA,EAAAA,GAAAA,IAAAA,SAAAA,EAAAA,GAAA,QAAA,OAAA,KAAA,OAAAA,eAAAD,EAAA,KAAA,CAAA,aAKnC,SAhBYK,EAAAC,UAAA0N,IAAAA,MAAAvsC,KAAAP,UAAPk+B,IAAO,kBAiBb,SAAWA,EAASmP,UAAc9sC,0BAA2B29B,KAAemP,KAAO,kBACnF,SAAenP,EAASmP,GAAc,IAAAC,EAAAC,EAAA,OAAAhtC,oBAA+B29B,KAAemP,KAAO,uBAC3F,SAAUnP,EAASxM,GAAmB,OAAAnxB,KAAAgtC,EAAA,qCAA8C7b,EAAUwM,WAAsB,2BACpH,SAAcA,EAASmP,GAAc,OAAA9sC,KAAAgtC,EAAA,sBAA8BrP,KAAemP,KAAO,kBACzF,SAAYnP,EAASmP,GAAc,IAAAG,EAAAD,EAAA,OAAAhtC,KAAA29B,UAAA,KAA4BA,KAAemP,KAAO,kBACrF,SAAUnP,EAASmP,GAAc,IAAAI,EAAAF,EAAA,OAAAhtC,OAAA,QAAA,KAA0B29B,KAAemP,EADW,u5BCwDlF,SAASK,oBAAoBC,GAC1BnE,IAAAA,EAAAA,QACAoE,GADApE,EAAO,IAAI3zB,KAAK83B,MACRnE,UAAAA,UAAAA,aACRD,EAAUC,sDACNoE,SAAV,MAAmBrE,EAEvB,4FAeO,SAASsE,eAAerE,GACvBA,IAAAA,EAAAA,QAAAA,OAAQ,MAARA,MAEAsE,EAAU,IAAIj4B,KAAK2zB,MAChBsE,sCAAwDA,IAAAA,wCAA6DA,YA6EhI,sTAAA,0+yDCpLA,IAAM5M,SAASvsB,4BACFo5B,mBAAqB,SAACC,EAAmBC,EAAevc,EAAUwc,EAAYC,EAAaC,EAAiBC,EAAeC,EAAgBC,EAAcC,EAAaC,EAAiBC,EAAeC,EAAWC,EAAsBC,mBAAuBC,OAAAA,EAAIC,kBAAAA,mBAAAC,uBAAA,uBAC5Qd,EAAaY,EAAIG,iBAAAA,kBAAAD,uBAII9N,wBAAAA,gBACwFxP,IAAAA,oDASlGwc,EAAaY,EAAII,iBAAAA,kBAAAF,uBAGLtd,GAAAA,uGAAAA,gBAAAA,EAAAA,kBAEwCA,YAElDod,EAAIK,iBAAAA,kBAAAH,uBAAA,iBACNb,EAAcW,EAAIM,iBAAAA,kBAAAJ,uBAAA,GAAA,mwCAAA,OAAA,OAAA,OAAA,0CAAA,6dAAA,OAAA,kbAAA,OAAA,qYAAA,0bAAA,OAAA,OAAA,OAAA,yDAAA,OAAA,gQAAA,OAAA,OAAA,wRAUwBf,WAaFvc,kBAoC2FA,qCA+BCA,kBA+BDA,6BA8BdA,IAAAA,uBAmBjGA,6BAGImd,EAAqBC,EAAIO,iBAAAA,kBAAAL,uBAGdtd,8PAAAA,OAAAA,OAAAA,GACC,SAACtvB,UAAMwsC,EAAqBxsC,EAAAA,OAAAA,MAJnB0sC,gBAoBTpd,EAAAA,SAIgD0c,MAAAA,SAAAA,IAAAA,aAMhD1c,eA3MtBod,KA+M6EV,MAAAA,OAAAA,EAAAA,mBAMvD1c,gBAKI0c,MAAAA,OAAAA,EAAAA,EAAAA,QAAgCU,EAAIQ,iBAAAA,kBAAAN,oMAEhCtd,qCACWid,WAEXjd,EAAAA,IAL4Bod,WAO9BV,MAAAA,OAAAA,EAAAA,EAAAA,QAAgCU,EAAIS,iBAAAA,kBAAAP,uBAAA,wBACTtd,2BACC,KAA5B0c,MAAAA,OAAe,EAAfA,WAAiCU,EAAIU,iBAAAA,kBAAAR,uBAAA,UAAA,cACZtd,aADQod,kBASvCpd,WD7F3C,SAAS+d,aAAaxtC,EAAOytC,EAAche,wBAC1CzvB,MAAAA,MAEEM,SAASmvB,EAAoBzvB,EAAQA,EAAQytC,SAC/Che,OAAqBge,EACd3iC,KAAAA,MAAWxK,aAAXwK,QAAsC,8BAGtC4iC,GAAwDC,EAAAC,eAA5BttC,IAAAA,WAAAA,kBAAfutC,EAAWF,KACzBG,EAAmBJ,IAAAA,MAAoB,wCACnCI,eAAoBD,IAGtC,CCmFoGL,CAAapB,MAAAA,OAAAA,EAAAA,UAA2BK,EAAehd,GAAa+c,WAMtH/c,EAAAA,IArPtBod,KAwPkBV,MAAAA,GAAAA,0BAA2DU,EAAIkB,kBAAAA,mBAAAhB,uBAanDtd,4LAAAA,uHAAAA,uJAAAA,OAAAA,OAAAA,yPAAAA,MAb+Cod,YAebpd,wEAIPA,oBAKQA,EAAAA,IAxBYod,KA0BnCP,MAAAA,GAAAA,OAAY0B,EAAZ1B,mBAAA0B,EAAAA,mBAGuBve,EAAAA,IA7BYod,KA8BFP,MAAAA,GAAAA,OAAY2B,EAAZ3B,IAAAA,eAAA2B,EAAAA,mBAEVxe,WACU6c,MAAAA,GAAAA,OAAY4B,EAAZ5B,mBAAA4B,EAAAA,IAAAA,eAEVze,EAAAA,IAnCYod,KAoCFP,MAAAA,GAAAA,OAAY6B,EAAZ7B,mBAA0B,EAA1B6B,iBAEV1e,6BACS6c,MAAAA,OAAY,EAAZA,IAAAA,MAAiCP,EAAoBI,MAAAA,OAAAA,EAAAA,EAAAA,aAEzFG,MAAAA,GAAAA,OAAY8B,EAAZ9B,IAAAA,WAAA8B,EAAAA,WAAyCvB,EAAIwB,kBAAAA,mBAAAtB,gEAG/BT,MAAAA,GAAAA,OAAYgC,EAAZhC,IAAAA,iBAAAgC,IAAAA,MAAgDhC,MAAAA,GAAAA,OAAYiC,EAAZjC,IAAAA,eAAAiC,EAAAA,IAAAA,iBAOtB9e,8CACnB6c,MAAAA,OAAAA,EAAAA,EAAAA,MAIvCH,MAAAA,GAAAA,0BAAkDU,EAAI2B,kBAAAA,mBAAAzB,uBAAA,8KAAA,OAAA,OAAA,OAAA,iDAa5Ctd,iCAEkCA,MAfMod,cAmBjCpd,EAAAA,gBAgB4BA,sDACUA,WAE7B6c,MAAAA,OAAY,EAAZA,EAAAA,wBAgBmB7c,WAEf6c,MAAAA,OAAY,EAAZA,EAAAA,gBACAA,MAAAA,OAAY,EAAZA,mBAkBe7c,EAAAA,IA3EKod,KA6E5BP,MAAAA,OAAY,EAAZA,IAAAA,OAOtBH,MAAAA,GAAAA,OAAesC,EAAftC,EAAAA,gBAAAsC,YAAAA,MAAuD5B,EAAI6B,kBAAAA,mBAAA3B,uBAajDtd,GAAAA,OAAAA,OAAAA,cAAAA,IAAAA,+BAEkCA,gBAA6D0c,MAAAA,GAAAA,OAAewC,EAAfxC,IAAAA,YAA8B,EAA9BwC,YAAAA,yBAA8FxC,MAAAA,GAAAA,OAAeyC,EAAfzC,gBAAAyC,EAAAA,wBAEvMzC,MAAAA,SAAAA,oBAAmDU,EAAIgC,kBAAAA,mBAAA9B,uBAY7Ctd,qIAAAA,IAAAA,kCAEkCA,qDAY1DA,MA/aNod,YAmbqDpd,qBAEvC0c,MAAAA,OAAe,EAAfA,IAAAA,eAIuC1c,EAAAA,IAzbrDod,KA2bcV,MAAAA,SAAAA,iBAMAA,MAAAA,OAAe,EAAfA,mBAQR1c,EAAAA,IAzcNod,YAgdsBpd,iCAGAA,gBAndtBod,cAsdsBpd,WAKZ2c,MAAAA,OAAa,EAAbA,EAAAA,UAAAA,OAA2B,SAAC0C,OAAGC,aAAKlC,EAAImC,kBAAAA,mBAAAjC,uBAMxB+B,GAAAA,OAAAA,mCAAAA,MAAAA,SAAAA,IAAAA,MAEJA,MAAAA,GAAAA,UAAkBjC,EAAIoC,kBAAAA,mBAAAlC,uBAGZtd,qBAAAA,iLAAAA,kBAA2Cqf,MAAAA,GAAAA,OAAGI,EAAHJ,gBAAAI,EAAAA,IAAAA,OAA+BJ,MAAAA,GAAAA,OAAGK,EAAHL,IAAAA,YAAe,EAAfK,wBAAsCL,MAAAA,GAAAA,OAAGM,EAAHN,gBAAe,EAAfM,IAAAA,QAAAA,YAAwCN,MAAAA,GAAAA,OAAGO,EAAHP,kBAAAO,IAAAA,QAAAA,iBAC9JP,MAAAA,GAAAA,OAAGQ,EAAHR,kBAAAQ,IAAAA,gBAAqC7f,MAArC,MAA6Eqf,MAAAA,GAAAA,OAAGC,EAAHD,EAAAA,iBAAe,EAAfC,eAAA,cAAqCtf,EAAAA,IAArC,cAA6EA,eAK9Jqf,UAAAA,KAAiB,SAACS,GAAY1C,IAAAA,EAAAA,EAAAA,OAAAA,EAAI2C,kBAAAA,mBAAAzC,uBAAA,GAAA,OAAA,OAClCwC,MAAAA,GAAAA,EAAAA,QAAmB1C,EAAI4C,kBAAAA,mBAAA1C,uBAAA,oHAAA,OAC+BwC,MAAAA,SAAAA,UAAmBA,MAAAA,SAAAA,IAAAA,UAFzET,ID9d3C,SAASY,mBAAmBnI,EAAM9X,GACjC,IAEYkgB,EAFZC,EAAAC,QAAA,IAACtI,EACD,OAAO,QAGPA,aAAgB3zB,KAChBk8B,EAAMvI,IAAAA,QACNwI,EAAQxI,cACRoI,EAAOpI,EAAAA,mBAGFyI,cAAAA,QAAOzI,OAAsB,OAASA,aAAmBA,KAAQ,OAAUA,GAChFuI,EAAMvI,EAAAA,OACNwI,EAAQxI,aACRoI,EAAOpI,IAAAA,eAGF,CAAA,UAAOA,MACZ,YAQO,QARD0I,EAAS,IAAIr8B,KAAK2zB,GACpB5yB,MAAMs7B,aACN,OAAO,KACXH,EAAMG,EAAAA,UACNF,EAAQE,IAAAA,UACRN,EAAOM,YAKLC,EAAKJ,IAAAA,UAAAA,aAAX,IACMK,EAAKJ,2BACLK,EAAOT,IAAAA,sBAENlgB,KAAiBntB,OAAM4tC,OAAN5tC,OAAY6tC,UAAU7tC,OAAQ6tC,SAAR7tC,MAAc4tC,UAApC5tC,MAAkB8tC,EAE9C,CCockDV,CAAmBZ,MAAAA,OAAAA,EAAAA,EAAAA,SAAerf,UAGlCqf,IAAAA,aAAsBrf,IAAAA,YAAqCqf,0BAAuBrf,SAAvBqf,YAA6Drf,6BAgBpIA,EAAAA,IAvgBVod,cA2gB2Cpd,WAA0D4gB,OAAAA,EAEnFhE,EAAeF,MAAAA,OAAe,EAAfA,IAAAA,aAAfkE,EAAAA,UACClE,MAAAA,OAAAA,EAAAA,iBAI2C1c,qBAAkD6gB,OAAAA,EAG9FjE,EAAeF,MAAAA,SAAAA,IAAAA,aAAyB,EAAxCmE,EAAAA,SACCnE,MAAAA,SAAAA,mBAIsD1c,EAAAA,IA1hBzEod,KA4hBkBV,MAAAA,OAAe,EAAfA,IAAAA,MAGRA,MAAAA,GAAAA,UAA8BU,EAAI0D,kBAAAA,mBAAAxD,uBAAA,2PAE6Btd,wBAEvD0c,MAAAA,OAAAA,EAAAA,IAAAA,mBAOuD1c,MA1iBzEod,QA2iBc2D,OAAAA,EAAApE,gBAAkB,EAAlBoE,EAAAA,oBAAAA,QAAqD3D,EAAI4D,kBAAAA,mBAAA1D,uBAAA,UAAA,OAAA2D,OAAAA,EAEzDtE,IAAAA,YAAkB,EAAlBsE,IAAAA,QAAAA,OAA4C,SAACC,EAAmBxjC,GAAKyjC,IAAAC,EAAAD,EAAAA,EAAK/D,OAAAA,EAAIiE,kBAAAA,mBAAA/D,ybAQ1D6D,OAAAA,EAAAxE,kBAAAwE,EAAAA,sBAAAA,OAAqD/D,EAAIkE,kBAAAA,mBAAAhE,kCAAI5/B,cAAkBsiB,EAAAA,IAAlBtiB,WAA8DsiB,eDrkBlL,SAASuhB,sBAAsBzJ,EAAM9X,GACpC,IAcEsgB,EAdFkB,EAAApB,QAAA,OAACtI,QAAiBlzB,IAATkzB,GAGT2J,EAAQ,IAAIt9B,KAAK2zB,UACjB9X,EAEOyhB,IAAAA,QAAAA,KAAkC,mCAAA,QAOvCpB,EAAMoB,YAAAA,0BACNnB,EAAQmB,IAAAA,QAAAA,KAA8B,SAAA,OACtCvB,EAAOuB,EAAAA,iBACb5uC,OAAUwtC,eAAOC,gBAAUJ,KAfhB,IAiBf,CCujB8DqB,CAAqBH,OAAAA,EAACF,IAAAA,QAAAA,eAAAE,EAAAA,UAAkDphB,UAExEA,uBDzjBvD,SAAS0hB,mBAAmBC,OAM/B/3B,EAEIiuB,mBAPQ,MAAR8J,MAGAC,EAAY,IAAIz9B,KAAKw9B,KAAAA,eAEzB/3B,EADc,IAAIzF,KAAKw9B,EAAKA,IAAAA,WAALA,SACZE,QAAoBD,IAAAA,QAC3B1F,EAAQ7gC,aAAWuO,QACnBiuB,EAAUx8B,OAAAA,MAAYuO,YACnBsyB,IAAAA,UAAAA,iBAA0CrE,EAAAA,2BCijBS6J,CAAmBR,EAAAA,iBAI3BA,YAAAA,QAAAA,OAAmC,SAACY,EAAKC,GAAMC,IAAAC,EAAAD,EAAAA,SAAK5E,EAAI8E,kBAAAA,mBAAA5E,uBACxD5/B,m3EAAAA,89BAAAA,SAAAA,EAAY0/B,EAAI+E,kBAAAA,mBAAA7E,uBAAA,+nCAAA,oGAQItd,WAAqDgiB,OAAAA,EACrDpF,EAAekF,IAAAA,aAAfE,EAAAA,UD/gBnE,SAASI,sBAAsBx4B,iBAC5BsyB,EAAQ7gC,OAAAA,MAAWuO,MAAXvO,WAAAA,gBAEd,OADMw8B,GAAWjuB,MAAD5V,WAAAquC,gBAChB,GAAAxvC,OAAUqpC,SAAV,MAAmBrE,GC6gBmDuK,CAAsBlB,kBAA+Ba,gBAyBjD/F,oBAAoB8F,MAAAA,OAAAA,EAAAA,IAAAA,OAKjBA,MAAAA,OAAAA,EAAAA,UAAkBQ,OAAAA,EACzB1F,EAAekF,MAAAA,SAAAA,iBAAmB,EAAlCQ,UAWc9S,SAA8BsS,MAAAA,SAAAA,UAUpCA,MAAAA,OAAAA,EAAAA,WAA6BA,MAAAA,OAAG,EAAHA,YAAyBA,MAAAA,OAAAA,EAAAA,YAAoBS,OAAAA,EAAArB,IAAAA,QAAAqB,OAAmCA,EAAnCA,EAAAA,aAAkDR,SAAO,EAAzDQ,aAAmEN,OAAAA,EAAIf,IAAAA,QAAJe,OAAuCA,EAAnCA,EAAAA,aAAkDF,SAAlDE,EAAAA,WASnJjG,oBAAoB8F,MAAAA,OAAAA,EAAAA,IAAAA,OAInBA,MAAAA,SAAAA,IAAAA,MAAgBU,OAAAA,EACvB5F,EAAekF,MAAAA,OAAG,EAAHA,IAAAA,aAAiB,EAAhCU,IAAAA,UAjGhCvB,KA8GE7D,EAAIqF,kBAAAA,mBAAAnF,uBAAA,UAe9BoF,OAAAA,EAAA/F,kBAAA+F,EAAAA,sBAAAA,OAAqDtF,EAAIuF,kBAAAA,mBAAArF,uBAAA,8EAAA,OAAAsF,OAAAA,EAEzDjG,EAAAA,WAAAiG,EAAAA,IAAAA,MAAAA,KAA4C,SAAC1B,EAAmBxjC,GAAU0/B,IAAAA,EAAAA,EAAAA,OAAAA,EAAIyF,kBAAAA,mBAAAvF,uBAC9E4D,GAAAA,OAAAA,OAAAA,IAAAA,QAAAA,QAAAA,OAAmC,SAACY,EAAKC,GAAMe,IAAAC,EAAAD,EAAAA,SAAK1F,EAAI4F,kBAAAA,mBAAA1F,uBAAA,omHAAA,OAAA,uCAAA,OAAA,OAAA,kQAAA,WAAA,OAAA,sKAAAwF,OAAAA,EAcLlG,EAAekF,iBAAkB,EAAjCgB,UAA2CG,OAAAA,EAAMrG,EAAekF,IAAAA,eAAfmB,IAAAA,MAM1DzT,SAA8BsS,IAAAA,aAKc9hB,+BAzBlCod,KA2B0B0E,EAAAA,+BAIQ9hB,EAAAA,IA/BlCod,KAiCZ0E,IAAAA,MAAwBA,EAAAA,aD7nB3E,SAASoB,aAAapL,GACnBqL,IAAAA,EAAAA,QAECA,MAFM,GAAA,OAAA,OAAA,OAAA,OAAA,OAAA,cACG,IAAIh/B,KAAK2zB,aAG7B,CCspB+DoL,CAAapB,IAAAA,OAAyB3F,eAAe2F,WAQtD9F,oBAAoB8F,EAAAA,eAIpB3F,eAAe2F,IAAAA,OAGfA,IAAAA,aAAyBsB,OAAAA,EAAGxG,EAAekF,EAAAA,qBAAfsB,EAAAA,IAAAA,gBAG5BpjB,WAA+C8hB,EAAAA,uBAK/CA,EAAAA,UDlsBvD,SAASuB,YAAYvB,GACpBA,IAKA5F,EALA4F,EAAAA,QAAAA,OAAO,MAAPA,MAEAF,EAAY,IAAIz9B,KAAK29B,IAAAA,OAErBl4B,EADU,IAAIzF,KAAK29B,IAAAA,SACRD,QAAoBD,IAAAA,QAC/B1F,EAAQ7gC,KAAAA,MAAWuO,QACnBiuB,EAAUx8B,OAAAA,MAAYuO,YACnBsyB,IAAAA,UAAAA,iBAA0CrE,EAAAA,aAAAA,cCusBSwL,CAAYvB,GAKZ9F,oBAAoB8F,WAIpB3F,eAAe2F,EAAAA,cAGfwB,OAAAA,EAAA1G,EAAekF,iBAAgB,EAA/BwB,iBAAoDxB,EAAAA,mBAGpD9hB,mBAA+C8hB,IAAAA,oBAYzC9hB,cACNujB,OAAAA,EAAArC,YAAAqC,OAAmCA,EAAnCA,UAAkDxB,SAAO,EAAzDwB,IAAAA,aAA8EC,OAAAA,EAAAtC,YAAAsC,OAAmCA,EAAnCA,UAAkDzB,SAAO,EAAzDyB,IAAAA,OAAiFpG,EAAIqG,kBAAAA,mBAAAnG,uBAAA,GAAA,kFAAAoG,OAAAA,EAC3JxC,IAAAA,QAD2JwC,OACxHA,EAAnCA,IAAAA,MAAkD3B,SAAO,EAAzD2B,cACJC,OAAAA,EACIzC,YADJyC,OACuCA,EAAnCA,UAAkD5B,SAAO,EAAzD4B,IAAAA,aAGF3jB,sBArIEod,QAsIRwG,OAAAA,EAAA1C,EAAAA,oBAAA0C,OAAmCA,EAAnCA,UAAkD7B,WAAlD6B,iBAA4EC,OAAAA,EAAA3C,EAAAA,oBAAA2C,OAAmCA,EAAnCA,EAAAA,aAAkD9B,SAAO,EAAzD8B,IAAAA,OAA6EzG,EAAI0G,kBAAAA,mBAAAxG,uBAAA,GAAA,kFAAAyG,OAAAA,EACrJ7C,YADqJ6C,OAClHA,EAAnCA,IAAAA,MAAkDhC,SAAO,EAAzDgC,IAAAA,eAERC,OAAAA,EAAA9C,YAAA8C,OAAmCA,EAAnCA,EAAAA,aAAkDjC,SAAlDiC,EAAAA,EAAAA,WAA6E5G,EAAI6G,kBAAAA,mBAAA3G,uBAC3Etd,yFAAAA,kCACJod,EAAI8G,kBAAAA,mBAAA5G,uBAAA,UAAA,OAAAyF,OAAAA,EAAY7B,YAAZ6B,OAA+CA,EAAnCA,EAAAA,aAAkDhB,SAAlDgB,EAAAA,IAAAA,OA3I9D7B,SA2JE9D,EAAI+G,kBAAAA,mBAAA7G,uCAMkFtd,EAAAA,IA90B9Eod,cAg1BUpd,WACuC8c,MAAAA,GAAAA,OAAWsH,EAAXtH,EAAAA,kBAAwB,EAAxBsH,IAAAA,aACtCpkB,mBAC2C8c,MAAAA,GAAAA,OAAWuH,EAAXvH,kBAAAuH,EAAAA,uBAC3CrkB,MAp1BXod,YAq1BUpd,0CAIoEA,4BAExE8c,MAAAA,GAAAA,OAAWwH,EAAXxH,EAAAA,wBAAAwH,EAAAA,EAAAA,KAAoC,SAACC,GAAgBnH,IAAAA,EAAAA,EAAAA,OAAAA,EAAIoH,kBAAAA,mBAAAlH,uBAAA,kBAAQiH,UAAjED,WAIwEtkB,IAAAA,qBAE5E8c,MAAAA,GAAAA,OAAW2H,EAAX3H,gBAAyB,EAAzB2H,EAAAA,KAA+B,SAACC,UAAgBtH,EAAIuH,kBAAAA,mBAAArH,uBAAA,kBAAQoH,EAAAA,mBAGL1kB,YAKrEod,EAAIwH,kBAAAA,mBAAAtH,uBAAA,GAAA,OAAA,wFAEMtd,IAAAA,gCACwCA,aAn4B3D,ECCP,ICAS5sB,EAAE,CAAC+B,WAAU,EAAGC,KAAKC,OAAOC,UAAUpH,IAAEqH,SAAQ,EAAGC,WAAW9E,KAAGrB,IAAE,CAACnB,EAAEkF,EAAE1C,EAAErB,KAAK,IAAMw1C,KAAKx2C,EAAEwB,SAASzB,GAAGiB,EAAEuE,IAAIzF,EAAE2E,WAAW2C,oBAAoB5B,IAAIzF,GAAG,QAAG,IAASD,GAAG2E,WAAW2C,oBAAoB1B,IAAI3F,EAAED,EAAE,IAAIwI,KAAKxI,EAAE4F,IAAI1E,EAAEsO,KAAKzP,GAAG,aAAaG,EAAE,CAAC,IAAW+E,EAAG/D,EAAFsO,KAAI,MAAM,CAAC5J,GAAAA,CAAI1E,GAAG,IAAMhB,EAAEqC,EAAEmD,IAAIlF,KAAKE,MAAM6B,EAAEqD,IAAIpF,KAAKE,KAAKQ,GAAGR,KAAK0H,cAAcnD,EAAE/E,EAAEH,EAAE,EAAE2/B,IAAAA,CAAKn9B,GAAG,YAAO,IAASA,GAAG7B,KAAKoB,EAAEmD,OAAE,EAAOlF,GAAGwC,CAAC,EAAE,CAAC,GAAG,WAAWrC,EAAgG,MAAMoF,MAAM,mCAAmCpF,GAA7I,CAAC,IAAW+E,EAAG/D,EAAFsO,KAAI,OAAO,SAAStO,GAAG,IAAMhB,EAAEQ,KAAKuE,GAAG1C,EAAE/B,KAAKE,KAAKQ,GAAGR,KAAK0H,cAAcnD,EAAE/E,EAAEH,EAAE,CAAC,GAAoD,SAASG,EAAEH,GAAG,MAAM,CAACwC,EAAE0C,KAAI,MAAA,iBAAiBA,EAAE/D,IAAEnB,EAAEwC,EAAE0C,IAAKlF,EAAkJA,EAAnImB,EAAqIqB,EAAjIhC,eAAe0E,GAAU1C,EAAE6C,YAAY2C,eAAe9C,EAAE/D,EAAE,IAAInB,EAAEkM,SAAQ,GAAIlM,GAAGmB,EAAErB,OAAOsB,yBAAyBoB,EAAE0C,QAAG,GAA3I,IAAElF,EAAemB,EAAyI,CCApwB,SAASA,EAAEA,GAAG,OAAOnB,EAAE,IAAImB,EAAE8G,OAAM,EAAGhB,WAAU,GAAI,saCHvD,IAAMq6B,OAASvsB,4BACF6hC,qBAAAA,sBAAoBC,EAAA7X,kBAAAC,wBAAAA,OAAG,SAAAC,EAAO4X,EAAchlB,EAAU2O,OAAMsW,EAAArW,aAAAzB,wBAAAA,OAAA,SAAAE,GAAA,IAAA,IAAA6X,EAAAC,IAAA,OAAA9X,UAAAA,WAAA,OAIpE,OAHK4X,EAAc,cACFD,IAAAA,oBACJhlB,GACbqN,IAAAA,QAAAA,EAAAA,OAE0BkB,gCAA0B,MAAIiB,oCAAmC,UAAA,aAE3E,kBAAA,WACH16B,KAAAA,UAAemwC,IACtBtW,GAAO,WAJJC,EAAQvB,WAKTuB,IAAWvB,IAAAA,QAAA,MAAA,MACNuB,EAAQ,OAAA,OAAAvB,EAAAA,OAELuB,YAAe,OAAA,OAAAvB,iBAAAA,WAAA,QAAA,MAAAA,IAAAA,SAAAA,EAAAA,GAAAA,aAAAA,EAAAA,GAAA,QAAA,OAAA,KAAA,OAAAA,eAAAD,EAAA,KAAA,CAAA,oBAKnC,SAnBgCK,EAAAC,EAAAc,UAAAuW,IAAAA,MAAAl2C,KAAAP,UAAA,GAApBw2C,scAoBM,MAAAM,IAAAA,EAAAA,UAAAlY,kBAAAC,+BAAG,SAAAS,IAAA,IAAAgB,SAAAzB,sBAAAA,MAAA,SAAAa,GAAA,IAAA,IAAAqX,EAAAC,UAAA,OAAAtX,UAAAA,IAAAA,OAAA,OAAA,OAAAA,IAAAA,QACKI,SAAKv7B,OAAI28B,eAA2B,iBAEzD,OAFY,OAARZ,EAAQZ,IAAAA,MAAAA,EAAAA,SAAAA,KAGPY,IAAAA,SAAe,OAAA,UAAA,OAAAZ,eAAAJ,EAJPT,IAAA,EAAA,GAAA,MAMYoY,IAAAA,EAAAA,UAAArY,kBAAAC,+BAAG,SAAAuB,EAAO1O,EAAU2O,GAAMsW,IAAAA,EAAArW,EAAAqW,EAAAA,EAAA9X,OAAAA,+BAAA,SAAA2B,GAAA,IAAA,IAAA0W,EAAAC,IAAA,OAAA3W,IAAAA,MAAAA,IAAAA,OAAA,OAGpD,OAFKmW,EAAc,UACNjlB,GACb8O,IAAAA,QAAAA,YAE0BP,8BAA0B17B,OAAI28B,SAAJ,MAA2C,UAAA,aAE/E,6BACH16B,OAAAA,MAAemwC,IACtBtW,GAAO,WAJJC,EAAQE,EAAAA,MAKTF,IAAWE,IAAAA,QAAA,MAAA,MACNF,EAAQ,OAAA,OAAAE,IAAAA,QAELF,YAAe,OAAA,OAAAE,IAAAA,eAAAA,IAAAA,OAAA,QAAA,MAAAA,IAAAA,SAAAA,EAAAA,GAAAA,aAAAA,EAAAA,GAAA,QAAA,OAAA,KAAA,OAAAA,eAAAJ,EAAA,KAAA,CAAA,cAnBjB,GAyBK,MAAAgX,IAAAA,EAAAA,UAAAxY,kBAAAC,+BAAG,SAAA4E,EAAO4T,EAAUhX,GAAMC,IAAAA,EAAAA,EAAAA,EAAAzB,OAAAA,+BAAA,SAAA+E,GAAA,IAAA,IAAA0T,EAAAC,IAAA,OAAA3T,UAAAA,WAAA,OAAA,OAAAA,EAAAA,OAAAA,EAAAA,OAEnB3D,qCAA8BiB,wCAA8BmW,GAAY,UAAA,aAElF,qCACVhX,GAAO,WAHJC,EAAQsD,WAITtD,IAAWsD,EAAAA,OAAA,MAAA,MACNtD,EAAQ,OAAA,OAAAsD,YAELtD,IAAAA,QAAe,OAAA,OAAAsD,EAAAA,gBAAAA,WAAA,QAAA,MAAAA,IAAAA,SAAAA,EAAAA,GAAAA,aAAAA,EAAAA,GAAA,QAAA,OAAA,KAAA,OAAAA,eAAAH,EAAA,KAAA,CAAA,QATZ5E,IAAA,EAAA,yBAeED,kBAAAC,wBAAAA,OAAG,SAAAiF,EAAO5F,OAAOoC,aAAAzB,+BAAA,SAAAkF,GAAA,IAAA,IAAAyT,EAAAC,IAAA,OAAA1T,EAAAA,KAAAA,WAAA,OACI,OAArC4S,EAAcnwC,KAAAA,UAAe03B,GAAQ6F,IAAAA,QACpBjE,iBAASoB,eAA0C,uBAE7D,kBAAA,WAGHyV,IACR,OANY,OAARrW,EAAQyD,EAAAA,KAAAA,iBAOPzD,aAAe,OAAA,UAAA,OAAAyD,IAAAA,WAAAD,EATAjF,IAAnB,KC/DP,IAAA6Y,UAAAC,QAAA,SAAAC,UAAA,IAAAC,EAAA,CAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,WAAA,UAAA,WAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,YAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,iBAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,YAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,cAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,eAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,eAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,gBAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,eAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,OAAA,UAAA,UAAA,UAAA,aAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,OAAA,UAAA,UAAA,UAAA,UAAA,cAAA,UAAA,UAAA,UAAA,eAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,WAAA,OAAAD,QAAA,WAAA,OAAAC,CAAA,IAAA,CAAA,SAAAF,QAAAG,EAAAC,GAAA,IAAAC,EAAAJ,UAAA,OAAAD,QAAA,SAAAM,EAAAC,GAAA,OAAAF,EAAAC,GAAA,IAAA,GAAAH,EAAAC,EAAA,OAAA,IAAA,IAAAI,EAAAR,QAAAS,EAAAR,YAAA,IAAA,GAAA,SAAApjC,SAAA2jC,EAAA,OAAA3jC,SAAA2jC,EAAA,MAAA,GAAA3jC,SAAA2jC,EAAA,MAAA,IAAA3jC,SAAA2jC,EAAA,MAAA,GAAA3jC,SAAA2jC,EAAA,MAAA,GAAA3jC,SAAA2jC,EAAA,MAAA,IAAA3jC,SAAA2jC,EAAA,MAAA,EAAA3jC,SAAA2jC,EAAA,MAAA,GAAA3jC,SAAA2jC,EAAA,MAAA,GAAA3jC,SAAA2jC,EAAA,MAAA,KAAA3jC,SAAA2jC,EAAA,MAAA,IAAA3jC,SAAA2jC,EAAA,MAAA,GAAA,MAAAC,EAAAt0C,KAAAs0C,EAAA3jC,QAAA,CAAA,MAAA4jC,GAAAD,EAAAt0C,KAAAs0C,EAAA3jC,QAAA,CAAA,KAAO,IAAM6jC,OAAS,qCAAA,2BAAA,qBAAA,qBAAA,WAMX,cAAA,mBAAA,mBAAA,mBAAA,2HAAA,WAaD,cAAA,mGAAA,mBAAA,mBAAA,iDAaA,oDAAA,+CAAA,mBAAA,mBAAA,mBAAA,+CAaG,0CAAA,mBAAA,mBAAA,mBAAA,iGAaF,0CAAA,+FAAA,oDAaF,kJAAA,mBAAA,mBAAA,aAaG,0FAAA,mBAAA,iCAAA,gEAaD,iCAAA,mBAAA,iCAAA,0FAAA,mBAAA,aAaC,cAAA,wGAAA,mBAAA,iCAAA,8BAaF,6DAAA,mBAAA,+GAaC,cAAA,mBAAA,mBAAA,+FAAA,iCAAA,4BAaE,4BAAA,gIAAA,iCAAA,WAaH,qFAAA,mBAAA,mBAAA,mBAAA,oEAaA,iCAAA,mBAAA,iCAAA,wGAAA,UAaD,cAAA,gIAAA,qDAaC,oDAAA,mBAAA,iCAAA,uGAaE,cAAA,mBAAA,mBAAA,2JAaA,cAAA,mBAAA,mBAAA,wGAAA,mBAAA,mBAAA,aAaA,0FAAA,mBAAA,mBAAA,mBAAA,kEAaC,iCAAA,mBAAA,mBAAA,mBAAA,6GAAA,WAaH,2HAAA,iCAAA,4CAaA,+CAAA,mBAAA,mBAAA,mBAAA,8kBClRH,SAASC,aAAaC,iBAGzB,IACI,IAEUr2B,EAFJ+vB,EAAS1rC,OAAAA,MAAWgyC,MACtBvG,QAAOC,YAKP,OAJM/vB,EAAOvX,WAAAA,WACblL,SAAAA,MAAewyC,KAAfxyC,OAA+B,SAAA+2C,GAAkBK,IAAAA,EAAAA,EAAhBn2C,GAAgBm2C,EAAAjH,eAAA4G,SAAXx0C,EAAK60C,KACvC30B,yCAAsCxhB,GAAOsB,MAKzD,MAAOG,GAAAA,CAmBS,SAAVq2C,EAAWC,EAAKC,WAGZ53C,GAFA63C,EAAMpkC,SAASkkC,EAAAA,oBACfG,EAAM9rC,kBAAkB4rC,GACpB5rC,iBAAcA,KAAAA,OAAa6rC,OAAaC,KAC5C11C,EAAI4J,OAAAA,UAAcA,gBAAc6rC,UAAoBC,IACpDzxC,EAAI2F,iBAAcA,oBAAa6rC,GAAcC,gCAC1B93C,QAAYoC,MAAUiE,eAApC9C,SAtBf,CAyBe,SAATw0C,EAAUJ,EAAKC,GACXC,IAAAA,EAAAA,EAEA73C,GAFA63C,EAAMpkC,SAASkkC,EAAAA,oBACfG,EAAM9rC,kBAAkB4rC,GACpB5rC,OAAAA,UAAcA,OAAAA,SAAa6rC,OAAaC,KAC5C11C,EAAI4J,KAAAA,QAAcA,KAAAA,OAAc6rC,UAAoBC,IACpDzxC,EAAI2F,iBAAcA,oBAAa6rC,GAAcC,gBACnD,gBAAyB93C,QAAYoC,MAAUiE,KAApC,UAAA9C,UAKTy0C,EAAS,IACLN,EAHJO,GAlCeR,EAkCQA,IAhCrBA,EAAAA,cACOA,GAGGF,OAAOE,IAOdF,gCAyBAG,EAAQO,UACRP,EAAQO,UACRP,EAAQO,UACRP,EAAQO,UACRA,MACAF,EAAOE,UACPF,EAAOE,UACPF,EAAOE,UACPF,EAAOE,UACPF,EAAOE,OA/ClB,IAkDM72B,EAAOvX,WAAAA,MAEblL,SAAAA,MAAeq5C,GAAfr5C,SAA+B,SAAAu3C,WAAEt2C,GAAgBy2C,EAAAvH,eAAAoH,SAAXh1C,EAAKm1C,KACvCj1B,UAAAA,2BAAsB5d,OAAgB5D,GAAOsB,mmFCjErD,IAAMg3C,cAAgB,IAAI7X,cACpB8X,cAAgB,IAAIrM,cACtBsM,aAAUC,YAAAA,UA2LaC,EAAAA,IADtBC,IA7BkBC,EAXHC,EAAAA,EADfC,EAAAA,EA/BAC,EAAAA,YAvED,SAAAP,EAAYQ,EAAgBC,OAAgB74B,YAAA8hB,OAAAA,qBAAAsW,IACxCp4B,EAAA84B,WAAAt5C,KAAA44C,MACAp4B,MAAsB44B,EACtB54B,UAAsB64B,EACtB74B,aACAA,mBACAA,EAAAA,kBACAA,aACAA,IAAAA,sBACAA,EAAAA,UACAA,aACAA,EAAAA,iBACAA,aACAA,aACAA,EAAAA,eACAA,EAAAA,kBACAA,aACAA,IAAAA,SACAA,aACAA,UAAuB,KACvBA,EAAAA,cAAqB,KACrBA,IAAAA,MAAsB,GACtBA,IAAAA,MAAoB,KACpBA,EAAAA,WAAkB,KAClBA,kBACAA,IAAAA,QACAA,cACAA,UAAe,iDAKfA,IAAAA,MAAsBk4B,cACtBl4B,EAAAA,eAAsBm4B,cAAcn4B,sTACvC+4B,CAAAX,EA7EqCzlC,KA6ErCkvB,aAAAuW,EAAA,CAAA,oBArED,kBACW54C,kBAoEV,MAlED,SAAa0B,OAKC83C,YAJJC,EAAWz5C,OAAA,MAEbA,KAAA05C,mBAEMF,EADY,IAAIG,gBAAgBh+B,wBAChBi+B,cACDJ,IAAkBx5C,aAEnCA,KAAA65C,UAAiBL,GAKjBx5C,OAAA,MAAiB0B,EAGZ1B,eACDA,eACAA,kBAMRA,OAAA,MAAiB0B,EAGrB1B,KAAA0H,yBAA+B+xC,EAuClC,GAtCA,gBACD,WACW,IAAAK,EAAAC,EAAA,WAAA/5C,qBAA2BA,KAAAmxB,aAA+BnxB,YAFpE,GAGA,OAAA,WAoCD,mBACIg6C,cAAApB,SAAA54C,KAAAg6C,CAAA,IACAh6C,KAAAi6C,QAAej6C,aACfA,OAAA,cAEAA,iBACH,kCACD,eAOUw5C,MALDx5C,gBAKCw5C,EADY,IAAIG,gBAAgBh+B,SAAAA,MAAAA,QAChBi+B,cAGlB55C,aAAiBw5C,EAEjBx5C,eAAA,OAEMA,eAENA,eACAA,OAAA,UAlBP,GAoBA,kBACD,WACUk6C,IAAAA,EAAAA,EAAAA,EAAa,IAAIC,IAAIx+B,SAAAA,MAAAA,MACrBy+B,EAAS,IAAIT,gBAAgBO,WAEnCE,IAAAA,aAAuBp6C,cAEjBq6C,KAAMr2C,OAAMk2C,iBAAN,MAA6BE,IAAAA,SACzCz+B,SAAAA,cAA4B,MAAQ0+B,EARvC,GAUA,2BAAAlB,EAAAA,EAAAA,EAAA9a,kBAAAC,+BACD,SAAAC,EAAmB+b,OAAkBC,aAAAjc,+BAAA,SAAAE,GAAA,IAAA,IAAAgc,EAAAC,IAAA,OAAAjc,EAAAA,KAAAA,WAAA,OACM,OAAvCwb,cAAApB,iBAAA54C,KAAAg6C,CAAA,CAAmBM,IAAoB9b,IAAAA,QACjCx+B,eAAiB,YACnBA,OAAA,QACAg4C,aAAah4C,cACbA,OAAA,SAIAA,eACM06C,EAAkBrwC,4BACxBqwC,mBACAA,UAAuB16C,KAAA26C,eACvBtwC,SAAAA,aAA0BqwC,MAIpBA,EAAkBrwC,yCAExBqwC,EAAAA,OACArwC,KAAAA,yBAA0BqwC,SAG1B16C,cACaqK,yBACbuX,mBAAqC5hB,OAAA,OACxC,OAAA,OAAA,KAAA,OAAAw+B,IAAAA,WAAAD,EAAAv+B,UACJ,SA3BiB4+B,UAAAua,UAAAn5C,KAAAP,cAAA,kBA4BlB,SAAQ66C,GACJN,cAAApB,EAAAoB,EAAAA,KAAAh6C,KAAAg6C,CAAA,CAAcM,MACjB,mBAAApB,EAAAA,EAAAA,EAAA7a,kBAAAC,+BACD,SAAAS,IAAA,IAAAqb,SAAA9b,sBAAAA,MAAA,SAAAa,GAAA,oBAAA,OAAAA,UAAAA,WAAA,UACQib,EAAS,IAAIT,gBAAgBh+B,wBACjC3b,KAAA29B,QAAe,WACAyc,EAAAA,mCACIA,YAAAA,wBACAA,YAAAA,WAEfp6C,OAAA,QAAA,OAA0BA,OAAA,MAAA46C,eAA8B56C,KAAA29B,gBAA0B,OAAAwB,IAAAA,QAC5En/B,OAAA,MAAmBA,OAAA,OADyDm/B,YAAA,MAC5C,OAAA,YAAA,OAAAA,EAAAA,UAAAJ,EAAA/+B,KAT7Cs+B,KAWA,kBAVe4a,UAAAl5C,KAAAP,cAAA,OAAA,YAAAw5C,EAAAA,EAAAA,EAAA5a,kBAAAC,+BAWhB,SAAAuB,EAAoBlC,kBAAOW,+BAAA,SAAA2B,GAAA,IAAA,IAAA4a,EAAAC,IAAA,OAAA7a,EAAAA,KAAAA,EAAAA,MAAA,UAClBjgC,KAAAo5C,eAAA/N,KAAwBpL,CAAAA,EAAAA,OAAA,MAAA,OAAAA,EAAAA,OACnBjgC,uBAAyB,OAEnCA,OAAA,MAAuB29B,GAAS,OAAA,UAAA,OAAAsC,eAAAJ,EAAA7/B,UACnC,SALkB6+B,GAAAoa,OAAAA,EAAAA,EAAAA,MAAAj5C,KAAAP,UAAbs7C,IAAa,uBAAA/B,EAAA3a,kBAAAC,+BAMnB,SAAA4E,EAAqB5zB,OAAIwzB,aAAAxE,+BAAA,SAAA+E,GAAA,IAAA,IAAA2X,EAAAC,IAAA,OAAA5X,EAAAA,KAAAA,IAAAA,OAAA,OAAA,OAAAA,EAAAA,OACOrjC,aAAAk7C,IAAwBj1C,aAAeqJ,IAAM,OAAtD,OAAbwzB,EAAaO,UAAAA,iBACZ,aACUP,IAChB,OAAA,UAAA,OAAAO,eAAAH,EAAAljC,UACJ,SALmB2/B,UAAAqZ,EAAAA,MAAAh5C,KAAAP,UAAd07C,IAAc,6BAMpB,WAAkBC,IAAAA,EAAAA,EAAAA,EAAAC,EAAAr7C,KACVs7C,IACJF,OAAAA,EAAIp7C,eAAJo7C,IAAAA,QAAAA,OAAoC,SAAC5K,EAAK3hC,OAG9B0sC,MAFJ/K,oBAEI+K,EAAWF,oBAAAA,OAAgC,SAAC7K,kBAAQA,aAAAA,MAAuBA,EAAAA,OAAa8K,QAExFC,UAAsB/K,EAEtB6K,UAAAA,QAAAA,OAAkCxsC,MAEtCysC,KAGA9K,UAAY3hC,IApBJ,GAuBnB,oCAAAkqC,EAAA1a,kBAAAC,+BACD,SAAAiF,EAAwB5F,GAAO6d,IAAAC,EAAAC,EAAAC,EAAAH,EAAAA,EAAAld,OAAAA,wBAAAA,OAAA,SAAAkF,GAAA,IAAA,IAAAoY,EAAAC,IAAA,OAAArY,UAAAA,WAAA,OACJ,OAAvBxjC,gBAAuBwjC,IAAAA,QACOxjC,OAAA,MAAoB29B,GAAQ,OAArC,OAAjB6d,EAAiBhY,UAAAA,EAAAA,OAAAA,EAAAA,OAECxjC,qBAAkCw7C,EAAmBx7C,cAAa,OAA3E,OAAHy7C,EAAGjY,UAAAA,IAAAA,SACkBxjC,qBAAwBy7C,WAAc,WAA3DK,EAAYtY,EAAAA,MACdkY,EAAUz1C,aAAW61C,MACrBJ,MAQuB,OAPnBC,EAAW11C,OAAAA,MAAWy1C,IAAAA,eAC1B17C,aAAqB27C,EACrB37C,gBACAA,OAAA,MAAuB07C,UACvB17C,OAAA,MAAoBiG,aAAWy1C,EAAAA,mBAC/B17C,KAAA+7C,WAAkB91C,aAAWy1C,IAAAA,QAAAA,OAE7B17C,eAAuBwjC,aACjBxjC,KAAAg8C,mBATaxY,aAAA,MASU,QAAAA,EAAAA,QAAA,MAAA,WAAAA,IAAAA,SAAAA,EAAAA,GAAAA,mBAI7BA,EAAAA,KAAAA,MACyB,OAAzBxjC,aAAAi8C,KAAyBzY,IAAAA,SACnBxjC,OAAA,gBAFcwjC,aAAA,MAEW,QAAA,OAAAA,aACzBxjC,OAAA,MAAuB29B,GAAQ,QAIjB,OAJiB6F,IAAAA,SAIzCxjC,gBAAwBwjC,EAAAA,WAAA,QAAA,UAAA,OAAAA,IAAAA,WAAAD,EAAAvjC,KAAA,CAAA,cA5B/Bs+B,KA8BA,SA7BsBqF,UAAAoV,UAAA/4C,KAAAP,cAAA,mBAAAq5C,EAAAA,EAAAA,EAAAza,kBAAAC,+BA8BvB,SAAAsF,IAAA,IAAAuS,EAAAsF,EAAAS,SAAA5d,sBAAAA,MAAA,SAAAwF,GAAA,IAAA,IAAAqY,EAAAC,UAAA,OAAAtY,UAAAA,IAAAA,OAAA,OAWO,OAVCqS,EAAe,GACnBkG,OAAAA,EAAIr8C,aAAAs8C,OAAJD,mBAAqD,SAACE,GAClDA,IAAAA,EAAAA,EAAAA,EAAAA,UAAAA,QAAAA,OAA+B,SAACtJ,WACvBkD,IAAAA,MAAsBlD,EAAAA,gBACvBkD,UAAkBlD,IAAAA,OAEjBkD,UAAsBlD,IAAAA,QACvBkD,UAAkBlD,IAAAA,MAL1BsJ,GADJF,IASGvY,IAAAA,QAAAA,YAEiBmS,qBAAqBE,EAAcn2C,OAAA,YAAuBA,KAAAi6C,SAAa,QAAnFwB,EAAG3X,EAAAA,gBAEH9jC,aAAsBy7C,IAAAA,MACtBz7C,OAAA,MAAmBy7C,YAAAA,cACbe,SAAqBf,2BAAoCx1C,KAAAA,MAAWw1C,EAAAA,iBAAwBA,UAAAA,SAClGz7C,OAAA,MAAsBw8C,eACtBx8C,OAAA,MAAoBw8C,cAGpBx8C,OAAA,gBACAk8C,OAAAA,EAAIT,IAAAA,QAAAS,IAAAA,QACAl8C,KAAAwwB,MAAairB,IAAAA,mBACTz7C,gBACAg4C,aAAah4C,KAAAwwB,OACbxwB,gBAGX8jC,IAAAA,SAAA,MAAA,QAAAA,IAAAA,SAAAA,EAAAA,GAAAA,IAAAA,SAGoB,QAAA,UAAA,OAAAA,EAAAA,UAAAF,EAAA5jC,KAAA,CAAA,QA/DNs+B,KAiEtB,kBAnCqBwa,UAAA94C,KAAAP,UAAhBu8C,IAAgB,kBAoCtB,WACUS,IAAAA,EAAAA,EAAAA,EAAU9gC,eAAAA,OACVy+B,EAAS,IAAIT,gBAAgB,WACpB35C,OAAA,4BACIA,KAAA29B,8BACA39B,KAAA29B,QAAA+e,gBAGf18C,OAAA,OACAo6C,IAAAA,aAA0Bp6C,cAE9B2b,OAAAA,WAAAA,iBAA0B8gC,eAAWz8C,KAAA28C,0BAAqBvC,eAC7D,kBACD,SAAqBwC,WACjB58C,OAAA,MAAgB48C,EAChB58C,KAAAg8C,mBAEAh8C,OAAA,QACAA,cANH,GAOA,kBACD,WACWwtC,IAAAA,EAAAA,EAAAA,OAAAA,mBAAmBxtC,KAAAytC,kBAAwBztC,aAAoBA,aAAeA,aAAiBA,KAAA4tC,YAAkB5tC,aAAsBA,aAAoBA,OAAA,MAAqBA,aAAmBA,KAAA+7C,WAAiB/7C,aAAuBA,OAAA,MAAmBA,qBAAoBA,MAAOA,KAAAquC,qBAAA5zB,KAA+Bza,MAAOA,OAAA,MAFzU,MAhRS64C,oBACa,CACnBgE,0lrEjBRwnB,EAACx9C,KAAKwC,KAAW0C,EAAE,IAAIlF,EAAEK,OAAOL,EAAE,GAAGwC,EAAE0U,SAAS1U,EAAEvC,EAAEiF,IAAI1C,EAAAA,MAAO,IAAG,IAA+OvC,EAAxOqF,aAAa,OAA2NrF,EAAlNuF,QAAQ,GAAG,iBAAuMvF,EAApL,OAAoLA,EAA3K,MAAMsF,MAAM,mEAA+JtF,EAA1F,uFAA2F,EAA1PuC,GAA4PxC,EAAEkF,EAAE,IAAIlF,EAAE,IAAW,IAAIG,IAAE+E,EAAElF,EAAEC,MiBS38Bw9C,CAAGtO,gBAAAA,iBAAAC,uBAAA,mGAINoK,kZA+QL54C,WAAW,CACP6f,EAAS,MAAQ9Z,UACjBnF,qBAAAA,KAA0B1B,SAC3By5C,WAAAA,mCAA2C,GAC9C34C,WAAW,CACP6f,EAAS,MAAQtZ,SACjB3F,qBAAAA,KAA0B1B,SAC3By5C,qBAAAA,gBAHH34C,UAGiC,GACjCA,WAAW,CACP6f,EAAS,MAAQtZ,SACjB3F,0BAA0B1B,SAC3By5C,+CAAwC,GAC3C34C,WAAW,CACP6f,EAAS,MAAQtZ,SACjB3F,qBAAAA,KAA0B1B,SAC3By5C,WAAAA,8BAA8B,GACjC34C,WAAW,CACP6f,EAAS,MAAQtZ,SACjB3F,yBAA0B2F,SAC3BoyC,qCAHH34C,UAGyC,GACzCA,WAAW,CACP6f,EAAS,MAAQtZ,SACjB3F,0BAA0B1B,SAC3By5C,qBAAAA,gBAHH34C,UAGmC,GACnCA,WAAW,CACP6f,EAAS,MAAQtZ,SACjB3F,qBAAAA,KAA0B1B,SAC3By5C,WAAAA,oBAHH34C,UAGkC,GAClCA,WAAW,CACP6f,EAAS,MAAQtZ,SACjB3F,0BAA0B1B,SAC3By5C,qBAAAA,2BAAuC,GAC1C34C,WAAW,CACP6f,EAAS,MAAQ9Z,UACjBnF,yBAA0B1B,SAC3By5C,WAAAA,8BAA4C,GAC/C34C,WAAW,CACP6f,EAAS,MAAQ9Z,UACjBnF,yBAA0B1B,SAC3By5C,qBAAAA,+BAA2C,GAC9C34C,WAAW,CACP6f,EAAS,MAAQtZ,SACjB3F,yBAA0B2F,QAC1B3F,+BAAgC,CAAC2F,UAClCoyC,qBAAAA,gBAJH34C,KAIqC,MACrCA,WAAW,CACPqH,IACAzG,qBAAAA,KAA0B2F,SAC3BoyC,qBAAAA,0BAAiC,GACpC34C,WAAW,CACPqH,IACAzG,qBAAAA,KAA0BmF,UAC3B4yC,qBAAAA,0BAAoC,GACvC34C,WAAW,CACPqH,IACAzG,yBAA0BmF,UAC3B4yC,WAAAA,oBAHH34C,UAGwC,GACxCA,WAAW,CACPqH,IACAzG,0BAA0B1B,SAC3By5C,qBAAAA,0BAAyC,GAC5C34C,WAAW,CACPqH,IACAzG,yBAA0B1B,SAC3By5C,gDAAuC,GAC1C34C,WAAW,CACPqH,IACAzG,qBAAAA,KAA0BiD,QAC3B80C,iDAAwC,GAC3C34C,WAAW,CACPqH,IACAzG,0BAA0B1B,SAC3By5C,+CAAsC,GACzC34C,WAAW,CACPqH,IACAzG,0BAA0B1B,SAC3By5C,WAAAA,8BAAoC,GACvC34C,WAAW,CACPqH,IACAzG,qBAAAA,KAA0B2F,SAC3BoyC,qBAAAA,gBAHH34C,UAGwC,GACxCA,WAAW,CACPqH,IACAzG,yBAA0BuF,SAC3BwyC,qBAAAA,gBAHH34C,UAGyC,GACzCA,WAAW,CACPqH,IACAzG,yBAA0B2F,SAC3BoyC,+CAAwC,GAC3C34C,WAAW,CACPqH,IACAzG,qBAAAA,KAA0B1B,SAC3By5C,+CAAiC,GACpCA,WAAa34C,WAAW,CNzXhBZ,IAAG,CAACwC,EAAE0C,cAAcA,EAAEA,EAAEwC,gBAAgB,KAAKg2C,eAAeC,OAAO39C,EAAEwC,EAAC,IAAKk7C,eAAeC,OAAO39C,EAAEwC,EAAC,EM0XxGo7C,gBACAp8C,0BAAgC,CAACggC,cAC7ByL,iBACLsM", "x_google_ignoreList": [0, 1, 2, 3, 4, 6, 12, 13, 14]}