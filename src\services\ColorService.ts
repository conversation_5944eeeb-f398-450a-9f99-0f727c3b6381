import { colors } from "../interface/DefaultColors";


/**
 * Thi<PERSON><PERSON> lập các biến CSS đại diện cho màu "nmt"
 * @param baseColor Tên màu trong Tailwind (vd: 'blue', 'rose') hoặc mã màu hex (vd: '#3b82f6')
 */
export function setnmtColors(baseColor: string) {
    console.log('baseColor', baseColor);
    // Nếu baseColor là object chứa các giá trị màu đã được tính toán sẵn
    try {
        const parsed = JSON.parse(baseColor) as Record<string, string>;
        if (typeof parsed === 'object') {
            const root = document.documentElement;
            Object.entries(parsed).forEach(([key, value]) => {
                root.style.setProperty(`--color-nmt-${key}`, value);
            });
            return;
        }
    } catch (e) {
    }

    // Lấy màu hex từ tên màu Tailwind hoặc chuỗi hex
    const getHexColor = (baseColor: string): string => {
        // Kiểm tra xem baseColor có phải là mã hex không
        if (baseColor.startsWith("#")) {
            return baseColor;
        }

        // Kiểm tra xem baseColor có phải là tên màu trong Tailwind không
        const color = colors[baseColor as keyof typeof colors];
        if (color) {
            // Lấy màu chính (500) từ danh sách màu
            return color["500"];
        }
        // Nếu không phải tên màu hợp lệ, trả về màu mặc định (màu chính của Tailwind)
        // return colors.blue["500"]; // Màu xanh dương mặc định
        return colors.orange["500"]; // Màu cam mặc định
    };

    // Làm sáng màu
    const lighten = (hex: string, percent: number): string => {
        const num = parseInt(hex.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const r = Math.min(255, Math.max(0, (num >> 16) + amt));
        const g = Math.min(255, Math.max(0, ((num >> 8) & 0xff) + amt));
        const b = Math.min(255, Math.max(0, (num & 0xff) + amt));
        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
    };

    // Làm tối màu
    const darken = (hex: string, percent: number): string => {
        const num = parseInt(hex.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const r = Math.min(255, Math.max(0, (num >> 16) - amt));
        const g = Math.min(255, Math.max(0, ((num >> 8) & 0xff) - amt));
        const b = Math.min(255, Math.max(0, (num & 0xff) - amt));
        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
    };

    // Lấy màu gốc
    const hexColor = getHexColor(baseColor);

    // Các sắc độ màu
    const shades: Record<string, string> = {
        "50": lighten(hexColor, 50),
        "100": lighten(hexColor, 40),
        "200": lighten(hexColor, 30),
        "300": lighten(hexColor, 20),
        "400": lighten(hexColor, 10),
        "500": hexColor,
        "600": darken(hexColor, 10),
        "700": darken(hexColor, 20),
        "800": darken(hexColor, 30),
        "900": darken(hexColor, 40),
        "950": darken(hexColor, 50),
    };

    // Lấy root element
    const root = document.documentElement;

    // Gán vào biến CSS
    Object.entries(shades).forEach(([key, value]) => {
        root.style.setProperty(`--color-nmt-${key}`, value);
    });
}
